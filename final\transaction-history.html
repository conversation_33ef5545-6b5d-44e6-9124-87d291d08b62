<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Transaction History - Crypto Wallet</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
            min-height: 100vh;
        }

        .stats-card {
            background: rgba(30, 41, 59, 0.4);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(59, 130, 246, 0.2);
            border-radius: 1rem;
            padding: 1.5rem;
            box-shadow: 0 20px 50px rgba(0, 0, 0, 0.5);
        }

        .transaction-item {
            padding: 1rem;
            border-radius: 0.75rem;
            background: rgba(30, 41, 59, 0.3);
            border: 1px solid rgba(59, 130, 246, 0.1);
            transition: all 0.3s ease;
            margin-bottom: 0.75rem;
        }

        .transaction-item:hover {
            background: rgba(30, 41, 59, 0.5);
            border-color: rgba(59, 130, 246, 0.2);
            transform: translateX(4px);
        }

        .filter-dropdown {
            background: rgba(30, 41, 59, 0.6);
            border: 1px solid rgba(59, 130, 246, 0.3);
            border-radius: 0.5rem;
            padding: 0.5rem 0.75rem;
            color: white;
            font-size: 0.75rem;
            font-weight: 500;
            transition: all 0.3s ease;
            appearance: none;
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%2306b6d4' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
            background-position: right 0.5rem center;
            background-repeat: no-repeat;
            background-size: 1rem 1rem;
            padding-right: 2rem;
        }

        .filter-dropdown:focus {
            outline: none;
            border-color: rgba(6, 182, 212, 0.6);
            box-shadow: 0 0 0 2px rgba(6, 182, 212, 0.2);
            background: rgba(30, 41, 59, 0.8);
        }

        .filter-dropdown:hover {
            border-color: rgba(6, 182, 212, 0.4);
            background: rgba(30, 41, 59, 0.7);
        }

        .date-input {
            background: rgba(30, 41, 59, 0.4);
            border: 1px solid rgba(59, 130, 246, 0.2);
            border-radius: 0.5rem;
            padding: 0.75rem 1rem;
            color: white;
            font-size: 0.875rem;
            transition: all 0.3s ease;
        }

        .date-input:focus {
            outline: none;
            border-color: rgba(59, 130, 246, 0.5);
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .date-input:hover {
            border-color: rgba(59, 130, 246, 0.3);
            background: rgba(30, 41, 59, 0.6);
        }

        /* Custom date picker styling */
        .date-input::-webkit-calendar-picker-indicator {
            filter: invert(1);
            cursor: pointer;
        }

        .date-input::-webkit-datetime-edit {
            color: white;
        }

        .date-input::-webkit-datetime-edit-fields-wrapper {
            color: white;
        }

        .date-input::-webkit-datetime-edit-text {
            color: #94a3b8;
        }

        /* Custom Scrollbar */
        .scrollbar-thin::-webkit-scrollbar {
            width: 6px;
        }

        .scrollbar-thin::-webkit-scrollbar-track {
            background: rgba(30, 41, 59, 0.3);
            border-radius: 3px;
        }

        .scrollbar-thin::-webkit-scrollbar-thumb {
            background: rgba(71, 85, 105, 0.8);
            border-radius: 3px;
        }

        .scrollbar-thin::-webkit-scrollbar-thumb:hover {
            background: rgba(71, 85, 105, 1);
        }

        /* Bottom Navigation Styles */
        .accent {
            color: #06b6d4 !important;
        }

        .hover\:accent:hover {
            color: #06b6d4 !important;
        }
    </style>
</head>

<body class="text-white">
    <div class="min-h-screen flex flex-col">
        <!-- Header -->
        <div class="sticky top-0 z-50 bg-slate-800/95 backdrop-blur-sm border-b border-slate-700">
            <div class="flex items-center justify-between px-6 py-4">
                <div class="flex items-center gap-4">
                    <button onclick="goBack()"
                        class="w-10 h-10 rounded-full bg-slate-700 hover:bg-slate-600 flex items-center justify-center transition-colors">
                        <i class="fas fa-arrow-left text-slate-300"></i>
                    </button>
                    <div>
                        <h1 class="text-xl font-bold text-white">Transaction History</h1>
                        <p class="text-sm text-slate-400">Complete transaction records</p>
                    </div>
                </div>
                <div class="flex items-center gap-2">
                    <button onclick="goToProfile()"
                        class="w-10 h-10 rounded-full bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-500 hover:to-cyan-500 flex items-center justify-center transition-all duration-300 border-2 border-blue-400/30">
                        <i class="fas fa-user text-white"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-grow overflow-y-auto px-4 sm:px-6 py-4 sm:py-6">
            <!-- Ultra Sleek Filter Section -->
            <div class="bg-slate-800/40 backdrop-blur-sm border border-slate-700/50 rounded-xl p-4 mb-6">
                <!-- Inline Filter Controls -->
                <div class="flex items-center gap-4 mb-3">
                    <div class="flex items-center gap-2 text-sm font-medium text-white">
                        <i class="fas fa-filter text-blue-400 text-xs"></i>
                        <span>Filters</span>
                    </div>
                    <div class="flex-1 grid grid-cols-2 gap-3">
                        <select onchange="filterByType(this.value)" class="filter-dropdown text-xs" id="typeFilter">
                            <option value="all">🏷️ All Types</option>
                            <option value="deposit">💰 Deposits</option>
                            <option value="withdrawal">💸 Withdrawals</option>
                            <option value="profit">📈 Daily Profits</option>
                            <option value="network">👥 Network Income</option>
                            <option value="bonus">🎁 Bonus Income</option>
                            <option value="transfer">🔄 Transfers</option>
                        </select>
                        <select onchange="filterByDateRange(this.value)" class="filter-dropdown text-xs"
                            id="dateFilter">
                            <option value="1month">📅 Last Month</option>
                            <option value="3months">📅 Last 3 Months</option>
                            <option value="6months">📅 Last 6 Months</option>
                            <option value="custom">🗓️ Custom Range</option>
                        </select>
                    </div>
                </div>

                <!-- Custom Date Range (Hidden by default) -->
                <div class="hidden" id="customDateRange">
                    <div
                        class="bg-gradient-to-r from-slate-800/40 to-slate-700/40 rounded-lg p-4 border border-blue-500/20 backdrop-blur-sm">
                        <div class="flex items-center gap-2 mb-3">
                            <i class="fas fa-calendar-range text-blue-400"></i>
                            <span class="text-sm font-medium text-white">Custom Date Range</span>
                        </div>
                        <div class="grid grid-cols-1 sm:grid-cols-2 gap-3 mb-4">
                            <div>
                                <label class="text-xs text-slate-400 block mb-2">Start Date</label>
                                <input type="date" id="startDate" class="date-input w-full">
                            </div>
                            <div>
                                <label class="text-xs text-slate-400 block mb-2">End Date</label>
                                <input type="date" id="endDate" class="date-input w-full">
                            </div>
                        </div>
                        <div class="flex gap-2">
                            <button onclick="applyCustomDateRange()"
                                class="bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-500 hover:to-cyan-500 text-white font-medium py-2 px-4 rounded-lg text-sm transition-all duration-300 flex items-center gap-2 flex-1">
                                <i class="fas fa-check"></i>
                                Apply
                            </button>
                            <button onclick="clearCustomDateRange()"
                                class="bg-slate-600/60 hover:bg-slate-500/80 text-white font-medium py-2 px-4 rounded-lg text-sm transition-all duration-300 border border-slate-500/30 flex items-center gap-2">
                                <i class="fas fa-times"></i>
                                Clear
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Ultra Compact Active Filters -->
                <div class="flex items-center justify-between pt-2 border-t border-slate-600/30">
                    <div class="flex items-center gap-2">
                        <div class="flex items-center gap-1">
                            <span class="text-xs text-slate-500">•</span>
                            <span
                                class="text-xs bg-blue-500/10 text-blue-300 px-2 py-0.5 rounded-md border border-blue-500/20 font-medium"
                                id="activeTypeFilter">All Types</span>
                        </div>
                        <div class="flex items-center gap-1">
                            <span class="text-xs text-slate-500">•</span>
                            <span
                                class="text-xs bg-cyan-500/10 text-cyan-300 px-2 py-0.5 rounded-md border border-cyan-500/20 font-medium"
                                id="activeDateFilter">Last Month</span>
                        </div>
                    </div>
                    <button onclick="clearAllFilters()"
                        class="text-xs text-slate-500 hover:text-red-400 transition-colors flex items-center gap-1 hover:bg-red-500/10 px-2 py-1 rounded-md">
                        <i class="fas fa-times text-xs"></i>
                        <span>Clear</span>
                    </button>
                </div>
            </div>

            <!-- Transaction List -->
            <div class="stats-card">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="font-bold text-white">All Transactions</h3>
                    <div class="flex items-center gap-2">
                        <span class="text-xs text-slate-400" id="transactionCount">Showing transactions</span>
                    </div>
                </div>

                <div class="max-h-96 overflow-y-auto space-y-3 pr-2 scrollbar-thin" id="transactionList">
                    <!-- Transactions will be loaded here -->
                </div>
            </div>
        </div>

        <!-- Sticky Bottom Navigation Bar -->
        <div class="sticky bottom-0 z-40 flex-shrink-0">
            <div class="bg-slate-800/95 backdrop-blur-sm mx-4 mb-4 rounded-2xl border border-slate-700">
                <div class="flex justify-around items-center h-16">
                    <a href="1.html" class="flex flex-col items-center gap-1 text-slate-400 hover:accent">
                        <i class="fas fa-home"></i>
                        <span class="text-xs">Home</span>
                    </a>
                    <a href="wallet.html" class="flex flex-col items-center gap-1 accent">
                        <i class="fas fa-wallet"></i>
                        <span class="text-xs">Wallet</span>
                    </a>
                    <a href="team.html" class="flex flex-col items-center gap-1 text-slate-400 hover:accent">
                        <i class="fas fa-users"></i>
                        <span class="text-xs">Team</span>
                    </a>
                    <a href="profile.html" class="flex flex-col items-center gap-1 text-slate-400 hover:accent">
                        <i class="fas fa-user"></i>
                        <span class="text-xs">Profile</span>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Sample transaction data with more entries and varied dates
        const allTransactions = [
            { id: 1, type: 'profit', title: 'Daily Profit', amount: 0.75, date: '2025-07-05', timestamp: '2025-07-05 00:01', status: 'Standard Package', icon: 'fas fa-coins', color: 'cyan' },
            { id: 2, type: 'network', title: 'Network Income', amount: 2.50, date: '2025-07-04', timestamp: '2025-07-04 18:22', status: 'Referral Bonus', icon: 'fas fa-users', color: 'blue' },
            { id: 3, type: 'deposit', title: 'Deposit', amount: 100.00, date: '2025-07-04', timestamp: '2025-07-04 14:30', status: 'Bank Transfer', icon: 'fas fa-arrow-down', color: 'green' },
            { id: 4, type: 'bonus', title: 'Bonus Income', amount: 5.25, date: '2025-07-04', timestamp: '2025-07-04 12:15', status: 'Welcome Bonus', icon: 'fas fa-gift', color: 'purple' },
            { id: 5, type: 'withdrawal', title: 'Withdrawal', amount: -25.00, date: '2025-07-03', timestamp: '2025-07-03 16:45', status: 'Bank Account', icon: 'fas fa-arrow-up', color: 'red' },
            { id: 6, type: 'profit', title: 'Daily Profit', amount: 0.75, date: '2025-07-03', timestamp: '2025-07-03 00:01', status: 'Standard Package', icon: 'fas fa-coins', color: 'cyan' },
            { id: 7, type: 'transfer', title: 'Transfer Out', amount: -50.00, date: '2025-07-02', timestamp: '2025-07-02 09:15', status: 'To User @john_doe', icon: 'fas fa-exchange-alt', color: 'orange' },
            { id: 8, type: 'network', title: 'Network Income', amount: 10.00, date: '2025-07-01', timestamp: '2025-07-01 20:30', status: 'Level 2 Bonus', icon: 'fas fa-users', color: 'blue' },
            { id: 9, type: 'deposit', title: 'Deposit', amount: 200.00, date: '2025-06-30', timestamp: '2025-06-30 11:20', status: 'Crypto Transfer', icon: 'fas fa-arrow-down', color: 'green' },
            { id: 10, type: 'bonus', title: 'Bonus Income', amount: 15.00, date: '2025-06-25', timestamp: '2025-06-25 14:30', status: 'Monthly Bonus', icon: 'fas fa-gift', color: 'purple' },
            { id: 11, type: 'withdrawal', title: 'Withdrawal', amount: -75.00, date: '2025-06-20', timestamp: '2025-06-20 10:15', status: 'Crypto Wallet', icon: 'fas fa-arrow-up', color: 'red' },
            { id: 12, type: 'profit', title: 'Daily Profit', amount: 0.75, date: '2025-06-15', timestamp: '2025-06-15 00:01', status: 'Standard Package', icon: 'fas fa-coins', color: 'cyan' },
            { id: 13, type: 'deposit', title: 'Deposit', amount: 500.00, date: '2025-05-10', timestamp: '2025-05-10 16:45', status: 'Bank Transfer', icon: 'fas fa-arrow-down', color: 'green' },
            { id: 14, type: 'network', title: 'Network Income', amount: 25.00, date: '2025-04-15', timestamp: '2025-04-15 12:30', status: 'Team Bonus', icon: 'fas fa-users', color: 'blue' },
            { id: 15, type: 'transfer', title: 'Transfer In', amount: 100.00, date: '2025-03-20', timestamp: '2025-03-20 09:45', status: 'From User @alice', icon: 'fas fa-exchange-alt', color: 'orange' },
            { id: 16, type: 'deposit', title: 'Deposit', amount: 300.00, date: '2025-01-05', timestamp: '2025-01-05 14:20', status: 'Initial Deposit', icon: 'fas fa-arrow-down', color: 'green' }
        ];

        let currentTypeFilter = 'all';
        let currentDateFilter = '1month';
        let customStartDate = null;
        let customEndDate = null;
        let displayedTransactions = 10;

        function goBack() {
            window.history.back();
        }

        function goToProfile() {
            window.location.href = 'profile.html';
        }

        function exportTransactions() {
            alert('Transaction export functionality will be implemented here.\n\nFormats available:\n• PDF Report\n• CSV Export\n• Excel Spreadsheet');
        }

        function filterByType(type) {
            currentTypeFilter = type;

            // Update dropdown selection
            document.getElementById('typeFilter').value = type;

            // Update active filter display
            const typeNames = {
                'all': 'All Types',
                'deposit': 'Deposits',
                'withdrawal': 'Withdrawals',
                'profit': 'Daily Profits',
                'network': 'Network Income',
                'bonus': 'Bonus Income',
                'transfer': 'Transfers'
            };
            document.getElementById('activeTypeFilter').textContent = typeNames[type];

            // Reset displayed count and render
            displayedTransactions = 10;
            renderTransactions();
        }

        function filterByDateRange(range) {
            currentDateFilter = range;

            // Update dropdown selection
            document.getElementById('dateFilter').value = range;

            // Show/hide custom date range
            const customDiv = document.getElementById('customDateRange');
            if (range === 'custom') {
                customDiv.classList.remove('hidden');
                // Smooth scroll to custom date section
                setTimeout(() => {
                    customDiv.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
                }, 100);
            } else {
                customDiv.classList.add('hidden');
                customStartDate = null;
                customEndDate = null;
            }

            // Update active filter display
            const dateNames = {
                '1month': 'Last Month',
                '3months': 'Last 3 Months',
                '6months': 'Last 6 Months',
                'custom': 'Custom Range'
            };
            document.getElementById('activeDateFilter').textContent = dateNames[range];

            // Reset displayed count and render
            displayedTransactions = 10;
            renderTransactions();
        }

        function toggleDateFilter() {
            const customDiv = document.getElementById('customDateRange');
            if (currentDateFilter === 'custom') {
                customDiv.classList.toggle('hidden');
            } else {
                filterByDateRange('custom');
            }
        }

        function applyCustomDateRange() {
            const startDate = document.getElementById('startDate').value;
            const endDate = document.getElementById('endDate').value;

            if (!startDate || !endDate) {
                alert('Please select both start and end dates');
                return;
            }

            if (new Date(startDate) > new Date(endDate)) {
                alert('Start date must be before end date');
                return;
            }

            customStartDate = startDate;
            customEndDate = endDate;

            // Update active filter display
            document.getElementById('activeDateFilter').textContent = `${startDate} to ${endDate}`;

            // Reset displayed count and render
            displayedTransactions = 10;
            renderTransactions();
        }

        function clearCustomDateRange() {
            document.getElementById('startDate').value = '';
            document.getElementById('endDate').value = '';
            customStartDate = null;
            customEndDate = null;
            filterByDateRange('1month');
        }

        function clearAllFilters() {
            filterByType('all');
            filterByDateRange('1month');
        }



        function getDateRangeFilter() {
            const now = new Date();
            let startDate;

            switch (currentDateFilter) {
                case '1month':
                    startDate = new Date(now.getFullYear(), now.getMonth() - 1, now.getDate());
                    break;
                case '3months':
                    startDate = new Date(now.getFullYear(), now.getMonth() - 3, now.getDate());
                    break;
                case '6months':
                    startDate = new Date(now.getFullYear(), now.getMonth() - 6, now.getDate());
                    break;
                case 'custom':
                    if (customStartDate && customEndDate) {
                        return {
                            start: new Date(customStartDate),
                            end: new Date(customEndDate + 'T23:59:59')
                        };
                    }
                    return null;
                default:
                    return null;
            }

            return { start: startDate, end: now };
        }

        function renderTransactions() {
            const container = document.getElementById('transactionList');
            let filteredTransactions = allTransactions;

            // Filter by type
            if (currentTypeFilter !== 'all') {
                filteredTransactions = filteredTransactions.filter(t => t.type === currentTypeFilter);
            }

            // Filter by date range
            const dateRange = getDateRangeFilter();
            if (dateRange) {
                filteredTransactions = filteredTransactions.filter(t => {
                    const transactionDate = new Date(t.date);
                    return transactionDate >= dateRange.start && transactionDate <= dateRange.end;
                });
            }

            const transactionsToShow = filteredTransactions;
            const totalFiltered = filteredTransactions.length;

            // Update transaction count
            const countElement = document.getElementById('transactionCount');
            if (countElement) {
                countElement.textContent = `Showing ${totalFiltered} transactions`;
            }

            container.innerHTML = transactionsToShow.map(transaction => `
                <div class="transaction-item flex justify-between items-center" data-type="${transaction.type}">
                    <div class="flex items-center gap-3">
                        <div class="w-10 h-10 rounded-full bg-${transaction.color}-500/20 flex items-center justify-center border border-${transaction.color}-500/30">
                            <i class="${transaction.icon} text-${transaction.color}-400"></i>
                        </div>
                        <div>
                            <p class="font-semibold text-white">${transaction.title}</p>
                            <p class="text-xs text-slate-400">${transaction.timestamp}</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <p class="font-semibold text-${transaction.color}-400">${transaction.amount > 0 ? '+' : ''}$${Math.abs(transaction.amount).toFixed(2)}</p>
                        <p class="text-xs text-${transaction.color}-300">${transaction.status}</p>
                    </div>
                </div>
            `).join('');
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function () {
            // Set default dropdown values
            document.getElementById('typeFilter').value = 'all';
            document.getElementById('dateFilter').value = '1month';

            // Set default date values for custom range
            const today = new Date();
            const lastMonth = new Date(today.getFullYear(), today.getMonth() - 1, today.getDate());

            document.getElementById('endDate').value = today.toISOString().split('T')[0];
            document.getElementById('startDate').value = lastMonth.toISOString().split('T')[0];

            renderTransactions();
        });
    </script>
</body>

</html>