<?php
session_start();

if (!isset($_SESSION['user_id']) || !$_SESSION['is_admin']) {
    header('Location: login.html');
    exit();
}

include 'dbcon.php';

$package_id = $_GET['id'] ?? null;

if (!$package_id) {
    header('Location: package_manager.php?error=invalid_package');
    exit();
}

// Fetch package details (using is_deleted instead of deleted_at)
$stmt = $conn->prepare('SELECT * FROM packages WHERE id = ? AND is_deleted = 0');
$stmt->bind_param('i', $package_id);
$stmt->execute();
$result = $stmt->get_result();
$package = $result->fetch_assoc();
$stmt->close();

if (!$package) {
    header('Location: package_manager.php?error=package_not_found');
    exit();
}

// Handle form submission for update
if ($_SERVER['REQUEST_METHOD'] === 'POST' && $_POST['action'] === 'update') {
    $package_name = $_POST['package_name'];
    $package_amount = $_POST['package_amount'];
    $profit_percentage = $_POST['profit_percentage'];
    $admin_id = $_SESSION['user_id'];

    // Ensure we have a valid admin user ID
    if (!$admin_id) {
        $admin_id = 1; // Fallback to default admin
    }

    // Verify that the admin user exists
    $check_user = $conn->prepare('SELECT id FROM users WHERE id = ?');
    $check_user->bind_param('i', $admin_id);
    $check_user->execute();
    $user_result = $check_user->get_result();

    if ($user_result->num_rows === 0) {
        // Admin user doesn't exist, set updated_by to NULL
        $stmt = $conn->prepare('UPDATE packages SET package_name = ?, package_amount = ?, profit_percentage = ?, updated_by = NULL WHERE id = ?');
        $stmt->bind_param('sddi', $package_name, $package_amount, $profit_percentage, $package_id);
    } else {
        // Admin user exists, use it
        $stmt = $conn->prepare('UPDATE packages SET package_name = ?, package_amount = ?, profit_percentage = ?, updated_by = ? WHERE id = ?');
        $stmt->bind_param('sddii', $package_name, $package_amount, $profit_percentage, $admin_id, $package_id);
    }
    $check_user->close();

    $stmt->execute();
    $stmt->close();

    header('Location: package_manager.php?success=package_updated');
    exit();
}

// Page configuration for admin layout
$page_title = "Edit Package";
ob_start();
?>
<!-- Page Header -->
<div class="flex flex-col lg:flex-row justify-between items-start lg:items-center space-y-4 lg:space-y-0 mb-6">
    <div>
        <h1 class="text-3xl font-bold text-gray-900">Edit Package</h1>
        <p class="text-gray-600 mt-1">Update package details and settings</p>
    </div>
    <div class="flex items-center space-x-2 bg-blue-50 px-4 py-2 rounded-lg border border-blue-200">
        <i class="fas fa-edit text-blue-600"></i>
        <span class="text-sm font-medium text-blue-700">Edit Mode</span>
    </div>
</div>

<!-- Edit Package Form -->
<div class="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
    <div class="px-6 py-4 bg-gradient-to-r from-gray-50 to-gray-100 border-b border-gray-200">
        <div class="flex items-center space-x-3">
            <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                <i class="fas fa-box text-blue-600 text-sm"></i>
            </div>
            <div>
                <h3 class="text-lg font-semibold text-gray-900">Package Information</h3>
                <p class="text-sm text-gray-600">Update the package details below</p>
            </div>
        </div>
    </div>

    <div class="p-6">
        <form action="edit_package.php?id=<?php echo htmlspecialchars($package['id']); ?>" method="POST" class="space-y-6">
            <input type="hidden" name="action" value="update">

            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <!-- Package Name -->
                <div class="space-y-2">
                    <label for="package_name" class="block text-sm font-semibold text-gray-700">
                        <i class="fas fa-tag mr-2 text-blue-600"></i>Package Name
                    </label>
                    <input type="text"
                        name="package_name"
                        id="package_name"
                        value="<?php echo htmlspecialchars($package['package_name']); ?>"
                        required
                        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                        placeholder="Enter package name">
                </div>

                <!-- Package Amount -->
                <div class="space-y-2">
                    <label for="package_amount" class="block text-sm font-semibold text-gray-700">
                        <i class="fas fa-dollar-sign mr-2 text-green-600"></i>Amount ($)
                    </label>
                    <input type="number"
                        name="package_amount"
                        id="package_amount"
                        value="<?php echo htmlspecialchars($package['package_amount']); ?>"
                        step="0.01"
                        min="0"
                        required
                        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                        placeholder="0.00">
                </div>

                <!-- Profit Percentage -->
                <div class="space-y-2">
                    <label for="profit_percentage" class="block text-sm font-semibold text-gray-700">
                        <i class="fas fa-percentage mr-2 text-purple-600"></i>Profit (%)
                    </label>
                    <input type="number"
                        name="profit_percentage"
                        id="profit_percentage"
                        value="<?php echo htmlspecialchars($package['profit_percentage']); ?>"
                        step="0.01"
                        min="0"
                        max="100"
                        required
                        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                        placeholder="0.00">
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="flex flex-col sm:flex-row justify-between items-center space-y-3 sm:space-y-0 sm:space-x-4 pt-6 border-t border-gray-200">
                <a href="package_manager.php"
                    class="w-full sm:w-auto inline-flex items-center justify-center px-6 py-3 border border-gray-300 rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-all duration-200">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Back to Packages
                </a>

                <button type="submit"
                    class="w-full sm:w-auto inline-flex items-center justify-center px-6 py-3 border border-transparent rounded-lg text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200">
                    <i class="fas fa-save mr-2"></i>
                    Save Changes
                </button>
            </div>
        </form>
    </div>
</div>

<?php
// Capture the page content
$page_content = ob_get_clean();

// Close database connection
$conn->close();

// Include the admin layout
include 'admin/includes/layout.php';
?>