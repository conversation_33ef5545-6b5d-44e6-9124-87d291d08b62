<?php
// Test complete password reset flow
include 'dbcon.php';
include 'email_helper.php';

echo "<h2>🧪 Testing Complete Password Reset Flow (Asia/Kolkata Timezone)</h2>";

// Show current timezone
echo "<p><strong>🕐 Current Timezone:</strong> " . date_default_timezone_get() . "</p>";
echo "<p><strong>🕐 Current Time (IST):</strong> " . date('Y-m-d H:i:s T') . "</p>";

// Test email (change this to your email for testing)
$test_email = '<EMAIL>';

echo "<h3>Step 1: Check if user exists</h3>";
$stmt = $conn->prepare('SELECT id, name FROM users WHERE email = ? AND is_active = 1');
$stmt->bind_param('s', $test_email);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows > 0) {
    $user = $result->fetch_assoc();
    echo "<p>✅ User found: " . htmlspecialchars($user['name']) . "</p>";

    echo "<h3>Step 2: Generate reset token</h3>";
    $reset_token = bin2hex(random_bytes(32));
    $expires_at = date('Y-m-d H:i:s', strtotime('+1 hour'));

    echo "<p>🔑 Token: " . substr($reset_token, 0, 20) . "...</p>";
    echo "<p>⏰ Expires: " . $expires_at . "</p>";

    echo "<h3>Step 3: Store token in database</h3>";
    $stmt = $conn->prepare('INSERT INTO password_resets (email, token, expires_at) VALUES (?, ?, ?) ON DUPLICATE KEY UPDATE token = VALUES(token), expires_at = VALUES(expires_at), created_at = NOW()');
    $stmt->bind_param('sss', $test_email, $reset_token, $expires_at);

    if ($stmt->execute()) {
        echo "<p>✅ Token stored successfully</p>";

        echo "<h3>Step 4: Generate reset URL</h3>";
        $reset_url = "http://localhost/netvis/ui/reset_password.php?token=" . $reset_token;
        echo "<p>🔗 Reset URL: <a href='" . htmlspecialchars($reset_url) . "' target='_blank'>" . htmlspecialchars($reset_url) . "</a></p>";

        echo "<h3>Step 5: Test token validation</h3>";
        $current_time = date('Y-m-d H:i:s');
        $stmt = $conn->prepare('SELECT email, expires_at FROM password_resets WHERE token = ?');
        $stmt->bind_param('s', $reset_token);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result->num_rows > 0) {
            $row = $result->fetch_assoc();
            $is_valid = strtotime($row['expires_at']) > strtotime($current_time);
            echo "<p>✅ Token found in database</p>";
            echo "<p>🕐 Current time: " . $current_time . "</p>";
            echo "<p>🕐 Expires at: " . $row['expires_at'] . "</p>";
            echo "<p>✅ Token is " . ($is_valid ? "VALID" : "EXPIRED") . "</p>";

            if ($is_valid) {
                $time_remaining = strtotime($row['expires_at']) - strtotime($current_time);
                echo "<p>⏱️ Time remaining: " . round($time_remaining / 60) . " minutes</p>";
            }
        } else {
            echo "<p>❌ Token not found</p>";
        }

        echo "<h3>Step 6: Create email content</h3>";
        $subject = 'Password Reset Link - CryptoApp';
        $html_message = createPasswordResetLinkEmail($user['name'], $reset_url);
        echo "<p>✅ Email content created</p>";
        echo "<p>📧 Subject: " . htmlspecialchars($subject) . "</p>";

        echo "<h3>Step 7: Test email sending (optional)</h3>";
        echo "<p>💡 To test email sending, uncomment the line below in the code:</p>";
        echo "<p><code>// sendCryptoAppEmail(\$test_email, \$user['name'], \$subject, \$html_message, 'password_reset');</code></p>";

        // Uncomment this line to actually send the email:
        // if (sendCryptoAppEmail($test_email, $user['name'], $subject, $html_message, 'password_reset')) {
        //     echo "<p>✅ Email sent successfully!</p>";
        // } else {
        //     echo "<p>❌ Failed to send email</p>";
        // }

        echo "<h3>🎉 Test Results</h3>";
        echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px;'>";
        echo "<p><strong>✅ All components are working correctly!</strong></p>";
        echo "<p>🔐 Token generation: Working</p>";
        echo "<p>💾 Database storage: Working</p>";
        echo "<p>🕐 Timezone handling: Working</p>";
        echo "<p>🔗 URL generation: Working</p>";
        echo "<p>✉️ Email template: Working</p>";
        echo "</div>";

        echo "<h3>📋 Next Steps</h3>";
        echo "<ol>";
        echo "<li>Click the reset URL above to test the reset password page</li>";
        echo "<li>Try entering a new password</li>";
        echo "<li>Verify that the password reset works</li>";
        echo "<li>Test the forgot password form on your website</li>";
        echo "</ol>";
    } else {
        echo "<p>❌ Failed to store token: " . $conn->error . "</p>";
    }
} else {
    echo "<p>❌ User not found with email: " . htmlspecialchars($test_email) . "</p>";
    echo "<p>💡 Please update the \$test_email variable with a valid user email</p>";
}

$conn->close();
?>

<style>
    body {
        font-family: Arial, sans-serif;
        margin: 20px;
    }

    h2,
    h3 {
        color: #333;
    }

    p {
        margin: 10px 0;
    }

    a {
        color: #007cba;
    }

    code {
        background: #f8f9fa;
        padding: 2px 4px;
        border-radius: 3px;
    }

    ol {
        margin-left: 20px;
    }
</style>