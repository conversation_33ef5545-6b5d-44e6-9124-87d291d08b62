<?php
session_start();

// Check if user is admin
if (!isset($_SESSION['user_id']) || !$_SESSION['is_admin']) {
    header("Location: login.php");
    exit();
}

include 'dbcon.php';

$message = '';
$error = '';

if ($_POST) {
    try {
        // Add fields to users table for plan expiration tracking
        $alterQueries = [
            "ALTER TABLE users ADD COLUMN total_earnings_received DECIMAL(20,8) DEFAULT 0.00000000 COMMENT 'Total earnings received from this plan'",
            "ALTER TABLE users ADD COLUMN plan_status ENUM('active', 'expired') DEFAULT 'active' COMMENT 'Plan status - expires when earnings reach 2x package amount'",
            "ALTER TABLE users ADD COLUMN plan_expired_at TIMESTAMP NULL DEFAULT NULL COMMENT 'When the plan expired'"
        ];

        foreach ($alterQueries as $query) {
            try {
                if ($conn->query($query)) {
                    $message .= "✅ Successfully executed: " . substr($query, 0, 50) . "...<br>";
                } else {
                    // Check if column already exists
                    if (strpos($conn->error, 'Duplicate column name') !== false) {
                        $message .= "ℹ️ Column already exists: " . substr($query, 0, 50) . "...<br>";
                    } else {
                        $error .= "❌ Error: " . $conn->error . "<br>";
                    }
                }
            } catch (Exception $e) {
                if (strpos($e->getMessage(), 'Duplicate column name') !== false) {
                    $message .= "ℹ️ Column already exists: " . substr($query, 0, 50) . "...<br>";
                } else {
                    $error .= "❌ Exception: " . $e->getMessage() . "<br>";
                }
            }
        }

        if (empty($error)) {
            $message .= "<br>✅ <strong>Database schema updated successfully!</strong><br>";
            $message .= "📊 New fields added to users table:<br>";
            $message .= "• total_earnings_received - Tracks total earnings from current plan<br>";
            $message .= "• plan_status - 'active' or 'expired'<br>";
            $message .= "• plan_expired_at - Timestamp when plan expired<br>";
        }
    } catch (Exception $e) {
        $error = "Error updating database: " . $e->getMessage();
    }
}

// Check current table structure
$describe = "DESCRIBE users";
$result = $conn->query($describe);
$columns = [];
while ($row = $result->fetch_assoc()) {
    $columns[] = $row;
}

$conn->close();
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Add Plan Expiration Fields - NetVis</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>

<body class="bg-gray-100">
    <?php include 'admin_nav.php'; ?>

    <main class="max-w-6xl mx-auto py-6 sm:px-6 lg:px-8">
        <div class="bg-white shadow-lg rounded-lg p-8">
            <div class="mb-6">
                <h2 class="text-3xl font-bold text-gray-800">Add Plan Expiration Fields</h2>
                <p class="text-gray-600 mt-2">Add database fields to track plan expiration when earnings reach 2x package amount.</p>
            </div>

            <?php if ($message): ?>
                <div class="bg-green-50 border border-green-200 text-green-800 px-4 py-3 rounded-lg mb-6">
                    <?php echo $message; ?>
                </div>
            <?php endif; ?>

            <?php if ($error): ?>
                <div class="bg-red-50 border border-red-200 text-red-800 px-4 py-3 rounded-lg mb-6">
                    <?php echo $error; ?>
                </div>
            <?php endif; ?>

            <!-- Plan Expiration System Info -->
            <div class="bg-blue-50 p-6 rounded-lg mb-6">
                <h3 class="text-blue-800 font-semibold mb-3">🎯 Plan Expiration System</h3>
                <div class="text-blue-700 text-sm space-y-2">
                    <p><strong>Purpose:</strong> Stop users from receiving profits and commissions when their total earnings reach 2x their package amount.</p>
                    <p><strong>Example:</strong> User has $100 package → Plan expires when they've earned $200 total</p>
                    <p><strong>Effects:</strong> No more daily profits, network commissions, or bonuses until they upgrade their plan</p>
                </div>
            </div>

            <!-- Database Fields to Add -->
            <div class="mb-6">
                <h3 class="text-xl font-semibold text-gray-800 mb-4">Database Fields to Add</h3>

                <div class="space-y-4">
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h4 class="font-semibold text-gray-800">total_earnings_received</h4>
                        <p class="text-sm text-gray-600">DECIMAL(20,8) - Tracks cumulative earnings from current plan</p>
                        <p class="text-xs text-gray-500">Includes daily profits, network commissions, and bonuses</p>
                    </div>

                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h4 class="font-semibold text-gray-800">plan_status</h4>
                        <p class="text-sm text-gray-600">ENUM('active', 'expired') - Current plan status</p>
                        <p class="text-xs text-gray-500">Changes to 'expired' when earnings reach 2x package amount</p>
                    </div>

                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h4 class="font-semibold text-gray-800">plan_expired_at</h4>
                        <p class="text-sm text-gray-600">TIMESTAMP - When the plan expired</p>
                        <p class="text-xs text-gray-500">NULL for active plans, set when plan expires</p>
                    </div>
                </div>
            </div>

            <!-- Add Fields Button -->
            <form method="POST" class="mb-6">
                <button type="submit" class="w-full bg-indigo-600 text-white py-3 px-4 rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 font-medium">
                    Add Plan Expiration Fields to Database
                </button>
            </form>

            <!-- Current Table Structure -->
            <div class="mb-6">
                <h3 class="text-xl font-semibold text-gray-800 mb-4">Current Users Table Structure</h3>

                <div class="overflow-x-auto border border-gray-200 rounded-lg">
                    <table class="min-w-full bg-white">
                        <thead class="bg-gray-100">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-600 uppercase">Field</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-600 uppercase">Type</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-600 uppercase">Null</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-600 uppercase">Default</th>
                            </tr>
                        </thead>
                        <tbody class="divide-y divide-gray-200">
                            <?php foreach ($columns as $column): ?>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                        <?php echo $column['Field']; ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <?php echo $column['Type']; ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <?php echo $column['Null']; ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <?php echo $column['Default'] ?? 'NULL'; ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Next Steps -->
            <div class="bg-yellow-50 p-6 rounded-lg">
                <h4 class="text-yellow-800 font-semibold mb-2">📋 Next Steps After Adding Fields:</h4>
                <ol class="text-yellow-700 text-sm space-y-1 list-decimal list-inside">
                    <li>Update the earnings calculation script to check plan status</li>
                    <li>Modify profit distribution to skip expired plans</li>
                    <li>Add UI indicators to show plan expiration status</li>
                    <li>Create upgrade prompts for expired plans</li>
                    <li>Update wallet displays to show plan status</li>
                </ol>
            </div>

            <!-- Quick Links -->
            <div class="mt-6 flex space-x-4">
                <a href="calculate_earnings.php" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
                    Calculate Earnings
                </a>
                <a href="admin.php" class="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700">
                    Admin Dashboard
                </a>
                <a href="package_manager.php" class="bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700">
                    Package Manager
                </a>
            </div>
        </div>
    </main>
</body>

</html>