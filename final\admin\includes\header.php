<!-- <PERSON><PERSON> -->
<header class="bg-white shadow-lg border-b border-gray-200 sticky top-0 z-50 transition-all duration-300 ease-in-out sidebar-responsive" id="mainHeader">
    <div class="flex items-center justify-between px-6 py-4">
        <!-- Left Section: Logo & Brand -->
        <div class="flex items-center space-x-4">
            <!-- Mobile Menu Toggle -->
            <button id="sidebarToggle" class="lg:hidden p-2 rounded-lg hover:bg-gray-100 transition-colors">
                <i class="fas fa-bars text-gray-600"></i>
            </button>

            <!-- Logo & Brand -->
            <div class="flex items-center space-x-3">
                <div class="w-10 h-10 rounded-xl flex items-center justify-center shadow-lg logo-glow relative overflow-hidden">
                    <svg class="w-8 h-8 logo-svg" viewBox="0 0 4091.27 4091.73" xmlns="http://www.w3.org/2000/svg">
                        <g>
                            <circle cx="2045.635" cy="2045.865" r="2045.635" class="logo-bg" />
                            <path class="logo-symbol" fill-rule="nonzero"
                                d="M2947.77 1754.38c40.72,-272.26 -166.56,-418.61 -450,-516.24l91.95 -368.8 -224.5 -55.94 -89.51 358.99c-59.02,-14.72 -119.63,-28.59 -179.87,-42.34l90.16 -361.46 -224.36 -55.94 -91.92 368.68c-48.84,-11.12 -96.81,-22.11 -143.35,-33.69l0.26 -1.16 -309.59 -77.31 -59.72 239.78c0,0 166.56,38.18 163.05,40.53 90.91,22.69 107.35,82.87 104.62,130.57l-104.74 420.15c6.26,1.59 14.38,3.89 23.34,7.49 -7.49,-1.86 -15.46,-3.89 -23.73,-5.87l-146.81 588.57c-11.11,27.62 -39.31,69.07 -102.87,53.33 2.25,3.26 -163.17,-40.72 -163.17,-40.72l-111.46 256.98 292.15,72.83c54.35,13.63 107.61,27.89 159.58,41.27l-92.83 373.03 224.24,55.94 91.95 -368.8c61.35,16.61 120.71,31.97 178.91,46.43l-91.69 367.33 224.51,55.94 92.83 -372.68c382.82,72.45 670.67,43.24 792.04 -303.14 98.00 -279.47 -4.86 -440.75 -206.26 -546.25 146.69,-33.83 257.18,-130.31 286.64 -329.61l-0.07 -0.05zm-512.93 719.26c-69.38,278.78 -538.76,128.08 -690.94,90.29l123.28 -494.2c152.17,37.99 640.17,113.17 567.67,403.91zm69.43 -723.3c-63.29,253.58 -453.96,124.75 -580.69,93.16l111.77 -448.21c126.73,31.59 534.85,90.55 468.94,355.05l-0.02 0z" />
                        </g>
                    </svg>
                </div>
                <div>
                    <h1 class="text-xl font-bold text-gray-800">CryptoApp</h1>
                    <p class="text-xs text-gray-500">Admin Panel</p>
                </div>
            </div>
        </div>

        <!-- Right Section: Profile -->
        <div class="flex items-center space-x-4 ml-auto">

            <!-- Profile Dropdown -->
            <div class="relative" id="profileDropdown">
                <button class="flex items-center space-x-2 p-2 rounded-lg hover:bg-gray-100 transition-colors" onclick="toggleProfileDropdown()">
                    <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                        <i class="fas fa-user text-white text-sm"></i>
                    </div>
                    <span class="hidden md:block text-sm font-medium text-gray-700">Admin</span>
                    <i class="fas fa-chevron-down text-gray-400 text-xs"></i>
                </button>

                <!-- Dropdown Menu -->
                <div id="profileDropdownMenu" class="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 hidden">
                    <div class="py-2">
                        <a href="#" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                            <i class="fas fa-user-circle mr-3"></i>Profile
                        </a>
                        <a href="#" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                            <i class="fas fa-cog mr-3"></i>Settings
                        </a>
                        <hr class="my-2">
                        <a href="login.php" class="flex items-center px-4 py-2 text-sm text-red-600 hover:bg-red-50">
                            <i class="fas fa-sign-out-alt mr-3"></i>Logout
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</header>

<style>
    /* Header Styles */
    .logo-glow {
        background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(6, 182, 212, 0.1));
        border: 2px solid rgba(59, 130, 246, 0.2);
        transition: all 0.3s ease;
    }

    .logo-glow:hover {
        transform: scale(1.05);
        box-shadow: 0 5px 15px rgba(59, 130, 246, 0.3);
    }

    .logo-bg {
        fill: #3b82f6;
    }

    .logo-symbol {
        fill: #ffffff;
    }
</style>

<script>
    // Profile dropdown toggle
    function toggleProfileDropdown() {
        const dropdown = document.getElementById('profileDropdownMenu');
        dropdown.classList.toggle('hidden');
    }

    // Close dropdown when clicking outside
    document.addEventListener('click', function(event) {
        const dropdown = document.getElementById('profileDropdown');
        const dropdownMenu = document.getElementById('profileDropdownMenu');

        if (!dropdown.contains(event.target)) {
            dropdownMenu.classList.add('hidden');
        }
    });

    // Mobile sidebar toggle
    document.getElementById('sidebarToggle').addEventListener('click', function() {
        const sidebar = document.getElementById('sidebar');
        const overlay = document.getElementById('sidebarOverlay');

        sidebar.classList.remove('-translate-x-full');
        overlay.classList.remove('hidden');
    });
</script>