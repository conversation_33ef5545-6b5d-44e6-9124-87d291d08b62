<?php
session_start();

if (!isset($_SESSION['user_id'])) {
    header("Location: login.php");
    exit();
}

echo "<h2>User Link Test</h2>";
echo "<p>This page tests that all user-facing links work correctly and don't redirect to admin pages.</p>";

echo "<h3>✅ Fixed Links</h3>";
echo "<p>The following links have been corrected to point to user-accessible pages:</p>";

echo "<div style='background: #e8f5e8; padding: 15px; border: 1px solid #4CAF50; margin: 10px 0;'>";
echo "<h4>🔧 Changes Made:</h4>";
echo "<ul>";
echo "<li><strong>wallet.php</strong> - 'Upgrade Plan' button now links to <code>invest.php</code> instead of <code>package_manager.php</code></li>";
echo "<li><strong>package_history.php</strong> - 'Browse Packages' buttons now link to <code>invest.php</code> instead of <code>package_manager.php</code></li>";
echo "</ul>";
echo "</div>";

echo "<h3>🧪 Test Links</h3>";
echo "<p>Click these links to verify they work correctly for regular users:</p>";

echo "<div style='margin: 20px 0;'>";
echo "<h4>User Pages (Should Work):</h4>";
echo "<ul style='list-style-type: disc; margin-left: 20px;'>";
echo "<li><a href='invest.php' target='_blank' style='color: blue; text-decoration: underline;'>invest.php</a> - Package purchase/upgrade page</li>";
echo "<li><a href='wallet.php' target='_blank' style='color: blue; text-decoration: underline;'>wallet.php</a> - User wallet page</li>";
echo "<li><a href='package_history.php' target='_blank' style='color: blue; text-decoration: underline;'>package_history.php</a> - User package history</li>";
echo "<li><a href='account.php' target='_blank' style='color: blue; text-decoration: underline;'>account.php</a> - User account page</li>";
echo "</ul>";
echo "</div>";

echo "<div style='margin: 20px 0;'>";
echo "<h4>Admin Pages (Should Redirect to Login):</h4>";
echo "<ul style='list-style-type: disc; margin-left: 20px;'>";
echo "<li><a href='package_manager.php' target='_blank' style='color: red; text-decoration: underline;'>package_manager.php</a> - Admin package management</li>";
echo "<li><a href='admin.php' target='_blank' style='color: red; text-decoration: underline;'>admin.php</a> - Admin dashboard</li>";
echo "<li><a href='admin_package_history.php' target='_blank' style='color: red; text-decoration: underline;'>admin_package_history.php</a> - Admin package history</li>";
echo "</ul>";
echo "</div>";

echo "<h3>📋 Summary</h3>";
echo "<div style='background: #f0f8ff; padding: 15px; border: 1px solid #4169E1; margin: 10px 0;'>";
echo "<h4>🎯 Problem Solved:</h4>";
echo "<p>The issue was that user-facing pages were linking to admin-only pages. When regular users clicked 'Upgrade Plan' or 'Browse Packages', they were redirected to <code>package_manager.php</code> which requires admin authentication.</p>";
echo "<br>";
echo "<h4>✅ Solution Applied:</h4>";
echo "<p>Changed all user-facing links to point to <code>invest.php</code> which is the correct user page for package purchases and upgrades.</p>";
echo "<br>";
echo "<h4>🔒 Security Maintained:</h4>";
echo "<p>Admin pages still require admin authentication and will redirect unauthorized users to the login page.</p>";
echo "</div>";

echo "<h3>🚀 Next Steps</h3>";
echo "<ol>";
echo "<li><strong>Test the wallet page</strong> - Go to wallet.php and click 'Upgrade Plan' (should go to invest.php)</li>";
echo "<li><strong>Test package history</strong> - Go to package_history.php and click 'Browse Packages' (should go to invest.php)</li>";
echo "<li><strong>Verify invest.php works</strong> - Make sure users can purchase/upgrade packages</li>";
echo "</ol>";

echo "<div style='margin: 30px 0; text-align: center;'>";
echo "<a href='wallet.php' style='background: #4CAF50; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>Test Wallet Page</a>";
echo "<a href='package_history.php' style='background: #2196F3; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>Test Package History</a>";
echo "<a href='invest.php' style='background: #FF9800; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>Test Invest Page</a>";
echo "</div>";
