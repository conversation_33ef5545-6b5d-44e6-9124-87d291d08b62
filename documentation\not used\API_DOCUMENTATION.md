# NetVis Crypto Platform - API Documentation

## Overview
This document covers all API endpoints for the NetVis Crypto Platform, including request/response formats, authentication requirements, and usage examples.

## API Base URL
```
http://localhost/netvis/ui/api/
```

## Authentication
All API endpoints require user session authentication. Users must be logged in to access any API functionality.

## Core API Endpoints

### 1. Package Management APIs

#### `POST /api/purchase_package.php`
**Purpose**: Purchase or upgrade investment packages
**Authentication**: User session required
**Request Format**:
```json
{
    "package_id": 1,
    "wallet_type": "deposit" // or "withdrawal"
}
```

**Response Format**:
```json
{
    "success": true,
    "message": "Package purchased successfully",
    "package_details": {
        "package_name": "Basic Package",
        "package_amount": 500.0000,
        "profit_percentage": 1.2000,
        "earnings_limit": 1000.0000
    },
    "user_balance": {
        "deposit_wallet": 1500.0000,
        "withdrawal_wallet": 250.0000
    }
}
```

**Error Response**:
```json
{
    "success": false,
    "error": "Insufficient balance in selected wallet"
}
```

**Business Logic**:
- Validates package exists and is active
- Checks user wallet balance
- Ensures new package amount > current package (upgrade only)
- Updates user package and wallet balance
- Creates package history record
- Calculates 2x earnings limit

### 2. Staking System APIs

#### `POST /api/submit_staking_request.php`
**Purpose**: Submit staking request with dynamic coin pricing
**Authentication**: User session required
**Request Format**:
```json
{
    "stake_amount_usd": 100.0000,
    "wallet_type": "deposit" // or "withdrawal"
}
```

**Response Format**:
```json
{
    "success": true,
    "message": "Staking request submitted successfully",
    "staking_details": {
        "coins_staked": 222,
        "amount_usd": 100.0000,
        "coin_price": 0.4500,
        "freeze_end_date": "2024-07-10 12:00:00",
        "stake_type": "deposit"
    }
}
```

**Dynamic Pricing**:
- Uses current coin price from `system_settings` table
- Supports 4 decimal precision (e.g., $0.4567)
- Validates minimum amounts (multiples of 10)
- Calculates exact coin amounts with floor function

#### `POST /api/claim_staking.php`
**Purpose**: Submit claim request for unlocked staking
**Authentication**: User session required
**Request Format**:
```json
{
    "staking_id": 123
}
```

**Response Format**:
```json
{
    "success": true,
    "message": "Claim request submitted for admin approval",
    "claim_details": {
        "staking_id": 123,
        "coins_to_claim": 222,
        "amount_usd": 100.0000,
        "status": "claim_pending"
    }
}
```

### 3. Wallet Management APIs

#### `POST /api/deposit_request.php`
**Purpose**: Submit deposit request with receipt upload
**Authentication**: User session required
**Request Format**: Multipart form data
```
amount: 500.0000
receipt: [file upload]
```

**Response Format**:
```json
{
    "success": true,
    "message": "Deposit request submitted successfully",
    "request_id": 456,
    "amount": 500.0000,
    "status": "pending"
}
```

#### `POST /api/withdrawal_request.php`
**Purpose**: Submit withdrawal request
**Authentication**: User session required
**Request Format**:
```json
{
    "amount": 250.0000,
    "binance_address": "**********************************"
}
```

**Response Format**:
```json
{
    "success": true,
    "message": "Withdrawal request submitted successfully",
    "request_id": 789,
    "amount": 250.0000,
    "status": "pending"
}
```

**Validation**:
- Minimum withdrawal: $10.0000
- Amount must be multiple of 10
- Validates Binance address format
- Checks sufficient withdrawal wallet balance

### 4. Fund Transfer APIs

#### `POST /api/fund_transfer.php`
**Purpose**: Transfer funds between users (sponsor/downline only)
**Authentication**: User session required
**Request Format**:
```json
{
    "recipient_user_id": "FL123456",
    "amount": 100.0000,
    "wallet_type": "withdrawal"
}
```

**Response Format**:
```json
{
    "success": true,
    "message": "Fund transfer completed successfully",
    "transfer_details": {
        "recipient": "John Doe (FL123456)",
        "amount": 100.0000,
        "from_wallet": "withdrawal",
        "to_wallet": "deposit"
    }
}
```

**Business Rules**:
- Only transfer to direct sponsor or downline users
- Amount must be multiple of 10
- Minimum transfer: $10.0000
- Transfers from withdrawal wallet to recipient's deposit wallet

### 5. System Information APIs

#### `GET /api/get_coin_price.php`
**Purpose**: Get current staking settings and coin price
**Authentication**: User session required
**Response Format**:
```json
{
    "success": true,
    "coin_price": 0.4500,
    "min_coins": 10,
    "freeze_months": 6,
    "min_usd_amount": 10,
    "timestamp": 1625932800
}
```

#### `GET /api/get_user_stats.php`
**Purpose**: Get user statistics and earnings summary
**Authentication**: User session required
**Response Format**:
```json
{
    "success": true,
    "user_stats": {
        "total_earnings": 1250.0000,
        "current_package": "Premium Package",
        "package_progress": 62.5,
        "direct_referrals": 8,
        "network_size": 45,
        "active_stakings": 3
    }
}
```

### 6. Network Information APIs

#### `GET /api/get_network_tree.php`
**Purpose**: Get user's network structure
**Authentication**: User session required
**Response Format**:
```json
{
    "success": true,
    "network_tree": {
        "direct_referrals": [
            {
                "user_id": "FL123457",
                "name": "Jane Smith",
                "package": "Basic Package",
                "join_date": "2024-01-15",
                "status": "active"
            }
        ],
        "total_levels": 3,
        "total_network_size": 45
    }
}
```

## Admin-Only APIs

### 1. Admin Staking Management

#### `POST /api/admin_approve_staking_claim.php`
**Purpose**: Approve or reject staking claims
**Authentication**: Admin session required
**Request Format**:
```json
{
    "staking_id": 123,
    "action": "approve", // or "reject"
    "admin_notes": "Approved after verification"
}
```

**Response Format**:
```json
{
    "success": true,
    "message": "Staking claim approved successfully",
    "updated_balance": 350.0000
}
```

### 2. Admin Deposit/Withdrawal Management

#### `POST /api/admin_approve_deposit.php`
**Purpose**: Approve or reject deposit requests
**Authentication**: Admin session required
**Request Format**:
```json
{
    "request_id": 456,
    "action": "approve", // or "reject"
    "admin_notes": "Receipt verified"
}
```

#### `POST /api/admin_approve_withdrawal.php`
**Purpose**: Approve or reject withdrawal requests
**Authentication**: Admin session required
**Request Format**:
```json
{
    "request_id": 789,
    "action": "approve", // or "reject"
    "transaction_hash": "0x1234567890abcdef",
    "admin_notes": "Payment processed"
}
```

### 3. Admin System Management

#### `POST /api/admin_update_coin_price.php`
**Purpose**: Update staking coin price with historical tracking
**Authentication**: Admin session required
**Request Format**:
```json
{
    "new_price": 0.4750,
    "reason": "Market adjustment"
}
```

**Response Format**:
```json
{
    "success": true,
    "message": "Coin price updated successfully",
    "price_change": {
        "old_price": 0.4500,
        "new_price": 0.4750,
        "change_amount": 0.0250,
        "change_percentage": 5.56
    }
}
```

## Error Handling

### Standard Error Response Format
```json
{
    "success": false,
    "error": "Error message description",
    "error_code": "INSUFFICIENT_BALANCE", // Optional error code
    "details": { // Optional additional details
        "required_amount": 500.0000,
        "available_balance": 250.0000
    }
}
```

### Common Error Codes
- `INSUFFICIENT_BALANCE` - Not enough wallet balance
- `INVALID_PACKAGE` - Package not found or inactive
- `INVALID_AMOUNT` - Amount validation failed
- `UNAUTHORIZED` - User not logged in or insufficient privileges
- `VALIDATION_ERROR` - Input validation failed
- `SYSTEM_ERROR` - Internal server error

## Rate Limiting
- Maximum 100 requests per minute per user
- Maximum 10 requests per minute for sensitive operations (transfers, withdrawals)
- Rate limit headers included in responses

## Data Precision
- All monetary amounts use 4 decimal precision
- Calculations maintain precision throughout the system
- Rounding only applied for display purposes

## Security Features
- CSRF protection on all POST requests
- Input validation and sanitization
- SQL injection prevention
- XSS protection
- Session-based authentication
- Admin privilege verification

## Testing Endpoints

### `GET /api/test_connection.php`
**Purpose**: Test API connectivity and authentication
**Response**:
```json
{
    "success": true,
    "message": "API connection successful",
    "user_id": "FL123456",
    "timestamp": 1625932800
}
```

This API documentation provides comprehensive coverage of all endpoints with detailed request/response formats and business logic explanations.
