1. add internally fund transfer ✅
2. add fund in user side wallet by admin or user both ✅    
3. and deposit should be only done multiple of 10 and also withdrawal and transfer also should be done in multiple of 10✅
4. add stacking feature and from the day they stack it will  locked  for 6 month (more than 80% done)✅
5. when wallet balance is 2X the package wallet make id stop to receive any profit and commission✅
6. add two wallet one deposit and one withdrawal wallet in deposit only user can deposit the fund not able to withdrawal and can transfer to their  sponsor and downline and in withdrwal wallet then will  receive profit , bonus and network commission ✅
7. now check why their is some error in user side when click on claim for previous dat a and for admin side success message in red color S✅
8. Now to get level income of second level you have to compulsory have three direct member and same for 3 level to get 3 level income you should have  atleast 5 direct 
9. custom dyamica value of crypto coin is also done ✅



now do one thikn go through all the file and create one final schema  in @c:\xampp\htdocs\netvis/database\final_schema.sql  add all compulsory data like bonus maagemtn , package management and level management 'rrrrr