<?php
// backfill_user_ids.php

ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

include 'dbcon.php';

echo '<h1>Backfilling User IDs</h1>';

// --- Fetch users without a user_id ---
$sql = "SELECT id, name FROM users WHERE user_id IS NULL OR user_id = ''";
$result = $conn->query($sql);

if ($result->num_rows > 0) {
    $updated_count = 0;
    echo '<p>Found ' . $result->num_rows . ' users to update.</p><hr>';

    // Prepare the update statement
    $update_stmt = $conn->prepare('UPDATE users SET user_id = ? WHERE id = ?');

    while ($row = $result->fetch_assoc()) {
        $db_user_id = $row['id'];
        $name = $row['name'];
        $user_id_code = '';

        // --- Generate a unique user_id ---
        $is_unique = false;
        while (!$is_unique) {
            // Generate code
            $initials = strtoupper(substr(preg_replace('/\s+/', '', $name), 0, 2));
            // Use microtime for better uniqueness in a fast loop
            $timestamp_part = substr(str_replace('.', '', microtime(true)), -6);
            $user_id_code = $initials . $timestamp_part;

            // Check if it's already in use
            $check_stmt = $conn->prepare('SELECT id FROM users WHERE user_id = ?');
            $check_stmt->bind_param('s', $user_id_code);
            $check_stmt->execute();
            if ($check_stmt->get_result()->num_rows == 0) {
                $is_unique = true;
            }
            $check_stmt->close();
        }

        // --- Update the user record ---
        $update_stmt->bind_param('si', $user_id_code, $db_user_id);
        if ($update_stmt->execute()) {
            echo "<p style='color:green;'>&check; Successfully updated user #" . $db_user_id . ' with User ID: <strong>' . $user_id_code . '</strong></p>';
            $updated_count++;
        } else {
            echo "<p style='color:red;'>&cross; Error updating user #" . $db_user_id . ': ' . $conn->error . '</p>';
        }
    }

    $update_stmt->close();
    echo '<hr><h2>Update complete. ' . $updated_count . ' users were updated.</h2>';
} else {
    echo '<p>All users already have a User ID. No action needed.</p>';
}

$conn->close();
?>
