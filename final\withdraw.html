<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Withdraw Funds - Crypto Wallet</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
            min-height: 100vh;
        }

        .stats-card {
            background: rgba(30, 41, 59, 0.4);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(59, 130, 246, 0.2);
            border-radius: 0.75rem;
            padding: 1rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }

        .amount-btn {
            background: rgba(30, 41, 59, 0.4);
            border: 2px solid rgba(59, 130, 246, 0.3);
            border-radius: 0.5rem;
            padding: 0.5rem;
            color: white;
            font-weight: 600;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        @media (max-width: 640px) {
            .amount-btn {
                padding: 0.4rem;
                font-size: 0.875rem;
            }
        }

        .amount-btn:hover {
            border-color: rgba(6, 182, 212, 0.6);
            background: rgba(30, 41, 59, 0.6);
            transform: translateY(-2px);
        }

        .amount-btn.selected {
            background: linear-gradient(135deg, #3b82f6, #06b6d4);
            border-color: rgba(6, 182, 212, 0.8);
            box-shadow: 0 8px 25px rgba(6, 182, 212, 0.3);
            color: white !important;
        }

        .amount-btn.selected .text-slate-400 {
            color: rgba(255, 255, 255, 0.8) !important;
        }

        .custom-input {
            background: rgba(15, 23, 42, 0.8);
            border: 1px solid rgba(59, 130, 246, 0.3);
            border-radius: 0.75rem;
            padding: 0.875rem 1rem;
            color: white;
            font-size: 1.125rem;
            font-weight: 600;
            text-align: left;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .custom-input:focus {
            outline: none;
            border-color: rgba(6, 182, 212, 0.6);
            box-shadow: 0 0 0 3px rgba(6, 182, 212, 0.15);
            background: rgba(15, 23, 42, 0.9);
        }

        .custom-input:hover {
            border-color: rgba(6, 182, 212, 0.4);
            background: rgba(15, 23, 42, 0.85);
        }

        .custom-input::placeholder {
            color: rgba(148, 163, 184, 0.5);
        }

        .withdrawal-method {
            background: rgba(30, 41, 59, 0.3);
            border: 2px solid rgba(59, 130, 246, 0.2);
            border-radius: 0.75rem;
            padding: 1rem;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .withdrawal-method:hover {
            border-color: rgba(6, 182, 212, 0.4);
            background: rgba(30, 41, 59, 0.5);
        }

        .withdrawal-method.selected {
            border-color: rgba(6, 182, 212, 0.6);
            background: rgba(6, 182, 212, 0.1);
        }

        .account-input {
            background: rgba(30, 41, 59, 0.4);
            border: 2px solid rgba(59, 130, 246, 0.3);
            border-radius: 0.75rem;
            padding: 1rem;
            color: white;
            transition: all 0.3s ease;
        }

        .account-input:focus {
            outline: none;
            border-color: rgba(6, 182, 212, 0.6);
            box-shadow: 0 0 0 3px rgba(6, 182, 212, 0.2);
        }

        /* Bottom Navigation Styles */
        .accent {
            color: #06b6d4 !important;
        }

        .hover\:accent:hover {
            color: #06b6d4 !important;
        }
    </style>
</head>

<body class="text-white">
    <div class="min-h-screen flex flex-col">
        <!-- Header -->
        <div class="sticky top-0 z-50 bg-slate-800/95 backdrop-blur-sm border-b border-slate-700">
            <div class="flex items-center justify-between px-6 py-4">
                <div class="flex items-center gap-4">
                    <button onclick="goBack()"
                        class="w-10 h-10 rounded-full bg-slate-700 hover:bg-slate-600 flex items-center justify-center transition-colors">
                        <i class="fas fa-arrow-left text-slate-300"></i>
                    </button>
                    <div>
                        <h1 class="text-xl font-bold text-white">Withdraw Funds</h1>
                        <p class="text-sm text-slate-400">Cash out your earnings</p>
                    </div>
                </div>
                <div class="flex items-center gap-2">
                    <div class="text-right">
                        <p class="text-xs text-slate-400">Withdrawal Balance</p>
                        <p class="text-lg font-bold text-cyan-400">$450.75</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-grow overflow-y-auto px-4 sm:px-6 py-4">
            <!-- Amount Selection -->
            <div class="stats-card mb-4">
                <div class="flex items-center gap-3 mb-4">
                    <div
                        class="w-10 h-10 rounded-lg bg-gradient-to-r from-blue-600 to-cyan-600 flex items-center justify-center">
                        <i class="fas fa-dollar-sign text-white"></i>
                    </div>
                    <div>
                        <h2 class="text-lg font-bold text-white">Withdrawal Amount</h2>
                        <p class="text-xs text-slate-400">Choose amount in multiples of $10</p>
                    </div>
                </div>

                <!-- Quick Amount Buttons -->
                <div class="grid grid-cols-2 sm:grid-cols-4 gap-3 mb-4">
                    <button class="amount-btn" onclick="selectAmount(10)">
                        <div class="text-center">
                            <div class="text-base sm:text-lg font-bold">$10</div>
                            <div class="text-xs text-slate-400">Min</div>
                        </div>
                    </button>
                    <button class="amount-btn" onclick="selectAmount(20)">
                        <div class="text-center">
                            <div class="text-base sm:text-lg font-bold">$20</div>
                            <div class="text-xs text-slate-400">Quick</div>
                        </div>
                    </button>
                    <button class="amount-btn" onclick="selectAmount(40)">
                        <div class="text-center">
                            <div class="text-base sm:text-lg font-bold">$40</div>
                            <div class="text-xs text-slate-400">Most</div>
                        </div>
                    </button>
                    <button class="amount-btn" onclick="selectAmount('all')">
                        <div class="text-center">
                            <div class="text-base sm:text-lg font-bold">$450</div>
                            <div class="text-xs text-slate-400">All</div>
                        </div>
                    </button>
                </div>

                <!-- Custom Amount Input -->
                <div class="mb-4">
                    <label class="block text-sm font-medium text-slate-300 mb-3">Custom Amount</label>
                    <div class="relative">
                        <span
                            class="absolute left-4 top-1/2 transform -translate-y-1/2 text-cyan-400 text-lg font-semibold">$</span>
                        <input type="number" id="customAmount" class="custom-input w-full pl-10"
                            placeholder="Enter amount" min="10" step="10" max="450" oninput="validateCustomAmount()">
                    </div>
                    <p class="text-xs text-slate-400 mt-2 flex items-center gap-2">
                        <i class="fas fa-info-circle text-cyan-400"></i>
                        <span>Minimum $10 • Maximum $450 • Must be multiples of $10</span>
                    </p>
                </div>

                <!-- Selected Amount Display -->
                <div
                    class="bg-gradient-to-r from-slate-800/50 to-slate-700/50 rounded-lg p-4 border border-blue-500/20">
                    <div class="flex items-center justify-between">
                        <span class="text-slate-400">Withdrawal Amount:</span>
                        <span class="text-2xl font-bold text-blue-400" id="selectedAmount">$0</span>
                    </div>
                    <div class="flex items-center justify-between mt-2 text-sm">
                        <span class="text-slate-500">GAS Fee (10%):</span>
                        <span class="text-cyan-300" id="gasFee">$0.00</span>
                    </div>

                    <div class="flex items-center justify-between mt-1 text-sm border-t border-slate-600/30 pt-2">
                        <span class="text-slate-400">You'll Receive:</span>
                        <span class="text-lg font-bold text-cyan-400" id="netAmount">$0.00</span>
                    </div>
                </div>
            </div>

            <!-- Withdrawal Method -->
            <div class="stats-card mb-4">
                <div class="flex items-center gap-3 mb-3">
                    <div
                        class="w-10 h-10 rounded-lg bg-gradient-to-r from-cyan-600 to-blue-600 flex items-center justify-center">
                        <i class="fab fa-ethereum text-white"></i>
                    </div>
                    <div>
                        <h2 class="text-lg font-bold text-white">Withdrawal Method</h2>
                        <p class="text-xs text-slate-400">BEP20 network withdrawal</p>
                    </div>
                </div>

                <div class="withdrawal-method selected">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center gap-3">
                            <i class="fab fa-ethereum text-yellow-400 text-xl"></i>
                            <div>
                                <p class="font-semibold text-white">BEP20 Address</p>
                                <p class="text-xs text-slate-400">Binance Smart Chain • 10% GAS fee</p>
                            </div>
                        </div>
                        <i class="fas fa-check-circle text-cyan-400"></i>
                    </div>
                </div>
            </div>



            <!-- Withdraw Button -->
            <div class="stats-card">
                <button onclick="processWithdrawal()" id="withdrawBtn" disabled
                    class="w-full bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-500 hover:to-cyan-500 disabled:from-slate-600 disabled:to-slate-700 disabled:cursor-not-allowed text-white font-bold py-4 px-6 rounded-lg text-lg transition-all duration-300 shadow-lg flex items-center justify-center gap-3">
                    <i class="fas fa-arrow-up"></i>
                    <span>Process Withdrawal</span>
                    <span class="text-sm opacity-75" id="withdrawAmount">$0</span>
                </button>
                <p class="text-xs text-slate-500 text-center mt-3">🔒 Secure withdrawal • Processing fees apply</p>
            </div>
        </div>

        <!-- Sticky Bottom Navigation Bar -->
        <div class="sticky bottom-0 z-40 flex-shrink-0">
            <div class="bg-slate-800/95 backdrop-blur-sm mx-4 mb-4 rounded-2xl border border-slate-700">
                <div class="flex justify-around items-center h-16">
                    <a href="1.html" class="flex flex-col items-center gap-1 text-slate-400 hover:accent">
                        <i class="fas fa-home"></i>
                        <span class="text-xs">Home</span>
                    </a>
                    <a href="wallet.html" class="flex flex-col items-center gap-1 accent">
                        <i class="fas fa-wallet"></i>
                        <span class="text-xs">Wallet</span>
                    </a>
                    <a href="team.html" class="flex flex-col items-center gap-1 text-slate-400 hover:accent">
                        <i class="fas fa-users"></i>
                        <span class="text-xs">Team</span>
                    </a>
                    <a href="profile.html" class="flex flex-col items-center gap-1 text-slate-400 hover:accent">
                        <i class="fas fa-user"></i>
                        <span class="text-xs">Profile</span>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script>
        let selectedAmountValue = 0;
        let selectedWithdrawalMethod = 'bep20';
        let availableBalance = 450.75;

        function goBack() {
            window.history.back();
        }

        function selectAmount(amount) {
            if (amount === 'all') {
                // Round down to nearest $10 multiple
                selectedAmountValue = Math.floor(availableBalance / 10) * 10;
            } else {
                selectedAmountValue = amount;
            }

            // Clear custom input
            document.getElementById('customAmount').value = '';

            // Update UI
            updateSelectedAmount();
            updateAmountButtons();
            updateWithdrawButton();
        }

        function validateCustomAmount() {
            const input = document.getElementById('customAmount');
            let value = parseFloat(input.value) || 0;

            // Set limits
            if (value > availableBalance) {
                value = availableBalance;
                input.value = value.toFixed(2);
            }

            selectedAmountValue = value;

            // Clear amount button selections
            document.querySelectorAll('.amount-btn').forEach(btn => btn.classList.remove('selected'));

            updateSelectedAmount();
            updateWithdrawButton();
        }

        function updateSelectedAmount() {
            // Calculate 10% GAS fee
            const gasFee = selectedAmountValue * 0.10;
            const netAmount = selectedAmountValue - gasFee;

            document.getElementById('selectedAmount').textContent = `$${selectedAmountValue.toFixed(2)}`;
            document.getElementById('gasFee').textContent = `$${gasFee.toFixed(2)}`;
            document.getElementById('netAmount').textContent = `$${Math.max(0, netAmount).toFixed(2)}`;
        }

        function updateAmountButtons() {
            document.querySelectorAll('.amount-btn').forEach(btn => {
                btn.classList.remove('selected');
            });

            // Find and select the matching button
            const buttons = document.querySelectorAll('.amount-btn');
            buttons.forEach(btn => {
                const btnText = btn.textContent;
                if (btnText.includes('All') && selectedAmountValue === Math.floor(availableBalance / 10) * 10) {
                    btn.classList.add('selected');
                } else {
                    const amount = parseInt(btnText.replace('$', ''));
                    if (amount === selectedAmountValue) {
                        btn.classList.add('selected');
                    }
                }
            });
        }

        function updateWithdrawButton() {
            const btn = document.getElementById('withdrawBtn');
            const amountSpan = document.getElementById('withdrawAmount');

            if (selectedAmountValue >= 10) {
                btn.disabled = false;
                amountSpan.textContent = `$${selectedAmountValue.toFixed(2)}`;
            } else {
                btn.disabled = true;
                amountSpan.textContent = '$0';
            }
        }

        function selectWithdrawalMethod(method) {
            selectedWithdrawalMethod = method;

            // Update UI
            document.querySelectorAll('.withdrawal-method').forEach(wm => {
                wm.classList.remove('selected');
                const icon = wm.querySelector('.fa-check-circle, .fa-circle');
                icon.className = 'fas fa-circle text-slate-600';
            });

            // Select the clicked method
            event.currentTarget.classList.add('selected');
            const selectedIcon = event.currentTarget.querySelector('.fa-circle');
            selectedIcon.className = 'fas fa-check-circle text-cyan-400';

            // Update account fields based on method
            updateAccountFields(method);

            // Recalculate fees
            updateSelectedAmount();
        }

        function updateAccountFields(method) {
            const subtitle = document.getElementById('accountSubtitle');
            const fields = document.getElementById('bankFields');

            switch (method) {
                case 'bank':
                    subtitle.textContent = 'Enter your bank account information';
                    fields.innerHTML = `
                        <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-slate-400 mb-2">Account Holder Name</label>
                                <input type="text" class="account-input w-full" placeholder="John Doe">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-slate-400 mb-2">Account Number</label>
                                <input type="text" class="account-input w-full" placeholder="**********">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-slate-400 mb-2">Routing Number</label>
                                <input type="text" class="account-input w-full" placeholder="*********">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-slate-400 mb-2">Bank Name</label>
                                <input type="text" class="account-input w-full" placeholder="Chase Bank">
                            </div>
                        </div>
                    `;
                    break;
                case 'crypto':
                    subtitle.textContent = 'Enter your cryptocurrency wallet address';
                    fields.innerHTML = `
                        <div>
                            <label class="block text-sm font-medium text-slate-400 mb-2">Wallet Address</label>
                            <input type="text" class="account-input w-full" placeholder="**********************************">
                        </div>
                        <div class="mt-4">
                            <label class="block text-sm font-medium text-slate-400 mb-2">Cryptocurrency Type</label>
                            <select class="account-input w-full">
                                <option>Bitcoin (BTC)</option>
                                <option>Ethereum (ETH)</option>
                                <option>USDT (Tether)</option>
                            </select>
                        </div>
                    `;
                    break;
                case 'paypal':
                    subtitle.textContent = 'Enter your PayPal account information';
                    fields.innerHTML = `
                        <div>
                            <label class="block text-sm font-medium text-slate-400 mb-2">PayPal Email</label>
                            <input type="email" class="account-input w-full" placeholder="<EMAIL>">
                        </div>
                    `;
                    break;
            }
        }

        function processWithdrawal() {
            if (selectedAmountValue < 10) {
                alert('Please select an amount of at least $10');
                return;
            }

            let fee = 0;
            let methodName = '';

            switch (selectedWithdrawalMethod) {
                case 'bank':
                    fee = 2;
                    methodName = 'Bank Transfer';
                    break;
                case 'crypto':
                    fee = selectedAmountValue * 0.03;
                    methodName = 'Cryptocurrency';
                    break;
                case 'paypal':
                    fee = selectedAmountValue * 0.029;
                    methodName = 'PayPal';
                    break;
            }

            const netAmount = selectedAmountValue - fee;

            let confirmMessage = `Withdrawal Details:\n\n`;
            confirmMessage += `Method: ${methodName}\n`;
            confirmMessage += `Amount: $${selectedAmountValue.toFixed(2)}\n`;
            confirmMessage += `Processing Fee: $${fee.toFixed(2)}\n`;
            confirmMessage += `You'll Receive: $${netAmount.toFixed(2)}\n`;
            confirmMessage += `\nConfirm this withdrawal?`;

            if (confirm(confirmMessage)) {
                alert(`Withdrawal of $${selectedAmountValue.toFixed(2)} via ${methodName} has been processed successfully!\n\nYou'll receive $${netAmount.toFixed(2)} in your account.`);
                // In a real app, this would redirect back to wallet
                window.location.href = 'wallet.html';
            }
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function () {
            updateWithdrawButton();
        });
    </script>
</body>

</html>