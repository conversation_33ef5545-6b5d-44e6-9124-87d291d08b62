<?php
session_start();

// Admin authentication check
if (!isset($_SESSION['user_id']) || !$_SESSION['is_admin']) {
    header("Location: login.html");
    exit();
}

// Include your existing database configuration
require_once 'dbcon.php';
require_once 'settings_helper.php';

$message = '';
$error = '';

// Get dynamic staking configuration from database
$staking_settings = getStakingSettings($conn);
$COIN_PRICE = $staking_settings['coin_price'];
$MIN_STAKE_COINS = $staking_settings['min_coins'];
$FREEZE_MONTHS = $staking_settings['freeze_months'];

// Page configuration
$page_title = "Admin Staking Management";
$additional_css = '
<style>
    /* Enhanced form styling */
    .staking-form {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 1rem;
        padding: 2rem;
        margin-bottom: 2rem;
        color: white;
    }

    .form-input {
        background: rgba(255, 255, 255, 0.1);
        border: 2px solid rgba(255, 255, 255, 0.2);
        color: white;
        border-radius: 0.5rem;
        padding: 0.75rem 1rem;
        width: 100%;
        transition: all 0.3s ease;
    }

    .form-input::placeholder {
        color: rgba(255, 255, 255, 0.7);
    }

    .form-input:focus {
        outline: none;
        border-color: rgba(255, 255, 255, 0.5);
        background: rgba(255, 255, 255, 0.15);
        box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.1);
    }

    .form-label {
        color: rgba(255, 255, 255, 0.9);
        font-weight: 600;
        margin-bottom: 0.5rem;
        display: block;
    }

    .submit-btn {
        background: rgba(255, 255, 255, 0.2);
        color: white;
        border: 2px solid rgba(255, 255, 255, 0.3);
        padding: 0.75rem 2rem;
        border-radius: 0.5rem;
        font-weight: 600;
        transition: all 0.3s ease;
        cursor: pointer;
    }

    .submit-btn:hover {
        background: rgba(255, 255, 255, 0.3);
        border-color: rgba(255, 255, 255, 0.5);
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    }

    /* Enhanced table styling */
    .admin-table {
        width: 100%;
        border-collapse: separate;
        border-spacing: 0;
        background: white;
    }

    .admin-table thead {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    .admin-table thead th {
        color: white;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.05em;
        padding: 1rem 1.5rem;
        font-size: 0.75rem;
        border: none;
        position: relative;
        text-align: left;
    }

    .admin-table tbody tr {
        transition: all 0.2s ease;
        border-bottom: 1px solid #e2e8f0;
    }

    .admin-table tbody tr:hover {
        background-color: #f8fafc;
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    .admin-table tbody tr:last-child {
        border-bottom: none;
    }

    .admin-table tbody td {
        padding: 1rem 1.5rem;
        vertical-align: middle;
        border: none;
        background-color: white;
    }

    .admin-table tbody tr:hover td {
        background-color: #f8fafc;
    }

    /* Status badges */
    .status-badge {
        display: inline-flex;
        align-items: center;
        padding: 0.25rem 0.75rem;
        border-radius: 9999px;
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.05em;
    }

    .status-active {
        background-color: #d1fae5;
        color: #065f46;
        border: 1px solid #10b981;
    }

    .status-completed {
        background-color: #ddd6fe;
        color: #5b21b6;
        border: 1px solid #8b5cf6;
    }

    /* User search styling */
    .user-search-results {
        background: rgba(255, 255, 255, 0.95);
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-radius: 0.5rem;
        max-height: 200px;
        overflow-y: auto;
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        z-index: 10;
        margin-top: 0.25rem;
    }

    .user-search-item {
        padding: 0.75rem 1rem;
        cursor: pointer;
        border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        color: #374151;
        transition: all 0.2s ease;
    }

    .user-search-item:hover {
        background-color: rgba(102, 126, 234, 0.1);
    }

    .user-search-item:last-child {
        border-bottom: none;
    }
</style>
';

// Handle AJAX search request for users
if (isset($_GET['search']) && !empty($_GET['search'])) {
    $search = '%' . mysqli_real_escape_string($conn, $_GET['search']) . '%';
    $query = "SELECT id, user_id, name, email, phone, is_active, deposit_wallet_balance, withdrawal_wallet_balance FROM users WHERE email LIKE '$search' OR name LIKE '$search' OR user_id LIKE '$search' LIMIT 10";
    $result = mysqli_query($conn, $query);

    $users = [];
    if ($result) {
        while ($row = mysqli_fetch_assoc($result)) {
            $users[] = $row;
        }
    }

    header('Content-Type: application/json');
    echo json_encode($users);
    exit;
}

// Handle staking form submission
if ($_POST) {
    $email = filter_var($_POST['email'] ?? '', FILTER_SANITIZE_EMAIL);
    $stake_coins = intval($_POST['stake_coins'] ?? 0);
    $stake_type = $_POST['stake_type'] ?? 'deposit'; // deposit or withdrawal wallet

    // Validation
    if (empty($email) || !filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $error = 'Please enter a valid email address.';
    } elseif ($stake_coins < $MIN_STAKE_COINS) {
        $error = "Minimum staking amount is $MIN_STAKE_COINS coins.";
    } elseif ($stake_coins % $MIN_STAKE_COINS !== 0) {
        $error = "Staking amount must be in multiples of $MIN_STAKE_COINS coins.";
    } else {
        // Check if user exists
        $email_escaped = mysqli_real_escape_string($conn, $email);
        $query = "SELECT id, user_id, name, deposit_wallet_balance, withdrawal_wallet_balance FROM users WHERE email = '$email_escaped'";
        $result = mysqli_query($conn, $query);

        if ($result && mysqli_num_rows($result) > 0) {
            $user = mysqli_fetch_assoc($result);
            $stake_amount_usd = $stake_coins * $COIN_PRICE;

            // Check wallet balance
            $wallet_balance = ($stake_type === 'deposit') ? $user['deposit_wallet_balance'] : $user['withdrawal_wallet_balance'];

            if ($wallet_balance < $stake_amount_usd) {
                $error = "Insufficient balance in " . ucfirst($stake_type) . " wallet. Available: $" . number_format($wallet_balance, 2);
            } else {
                // Calculate freeze end date (6 months from now)
                $freeze_end_date = date('Y-m-d H:i:s', strtotime("+$FREEZE_MONTHS months"));

                // Start transaction
                mysqli_begin_transaction($conn);

                try {
                    // Deduct from wallet
                    $wallet_field = $stake_type . '_wallet_balance';
                    $new_balance = $wallet_balance - $stake_amount_usd;
                    $update_query = "UPDATE users SET $wallet_field = $new_balance WHERE id = " . $user['id'];

                    if (!mysqli_query($conn, $update_query)) {
                        throw new Exception('Failed to update wallet balance');
                    }

                    // Insert staking record
                    $insert_query = "INSERT INTO staking_records (user_id, coins_staked, amount_usd, stake_type, freeze_end_date, status, created_at) 
                                   VALUES (" . $user['id'] . ", $stake_coins, $stake_amount_usd, '$stake_type', '$freeze_end_date', 'active', NOW())";

                    if (!mysqli_query($conn, $insert_query)) {
                        throw new Exception('Failed to create staking record');
                    }

                    // Log transaction in wallet history
                    $description = "Staked $stake_coins coins for 6 months (Freeze until: $freeze_end_date)";
                    $log_query = "INSERT INTO wallet_history (user_id, amount, type, wallet_type, description) 
                                VALUES (" . $user['id'] . ", -$stake_amount_usd, 'staking', '$stake_type', '$description')";
                    mysqli_query($conn, $log_query);

                    // Commit transaction
                    mysqli_commit($conn);

                    $user_display = htmlspecialchars($user['name']) . " (ID: " . htmlspecialchars($user['user_id']) . ")";
                    $message = "Successfully staked $stake_coins coins ($" . number_format($stake_amount_usd, 2) . ") for $user_display. Freeze period ends on: " . date('M d, Y', strtotime($freeze_end_date));
                } catch (Exception $e) {
                    mysqli_rollback($conn);
                    $error = 'Staking failed: ' . $e->getMessage();
                }
            }
        } else {
            $error = 'No user found with this email address.';
        }
    }
}

// Start output buffering to capture the page content
ob_start();
?>

<!-- Page Header -->
<div class="flex flex-col lg:flex-row justify-between items-start lg:items-center space-y-4 lg:space-y-0 mb-6">
    <div>
        <h1 class="text-3xl font-bold text-gray-900">Admin Staking Management</h1>
        <p class="text-gray-600 mt-1">Create and manage user staking investments</p>
    </div>
    <div class="flex items-center space-x-4">
        <div class="flex items-center space-x-2 bg-blue-50 px-4 py-2 rounded-lg border border-blue-200">
            <i class="fas fa-coins text-blue-600"></i>
            <span class="text-sm font-medium text-blue-700">Staking Manager</span>
        </div>
        <a href="admin_change_password.php" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 transition-colors">
            <i class="fas fa-key mr-2"></i>
            Password Management
        </a>
    </div>
</div>

<!-- Staking Info Cards -->
<div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
    <div class="admin-card p-6">
        <div class="flex items-center">
            <div class="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center">
                <i class="fas fa-coins text-green-600 text-xl"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">Current Coin Price</p>
                <p class="text-2xl font-bold text-gray-900">$<?php echo number_format($COIN_PRICE, 4); ?></p>
            </div>
        </div>
    </div>

    <div class="admin-card p-6">
        <div class="flex items-center">
            <div class="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">
                <i class="fas fa-layer-group text-blue-600 text-xl"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">Minimum Stake</p>
                <p class="text-2xl font-bold text-gray-900"><?php echo number_format($MIN_STAKE_COINS); ?> coins</p>
            </div>
        </div>
    </div>

    <div class="admin-card p-6">
        <div class="flex items-center">
            <div class="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center">
                <i class="fas fa-clock text-purple-600 text-xl"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">Freeze Period</p>
                <p class="text-2xl font-bold text-gray-900"><?php echo $FREEZE_MONTHS; ?> months</p>
            </div>
        </div>
    </div>
</div>

<!-- Messages -->
<?php if ($message): ?>
    <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-lg mb-6">
        <div class="flex items-center">
            <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
            </svg>
            <?php echo htmlspecialchars($message); ?>
        </div>
    </div>
<?php endif; ?>

<?php if ($error): ?>
    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg mb-6">
        <div class="flex items-center">
            <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
            </svg>
            <?php echo htmlspecialchars($error); ?>
        </div>
    </div>
<?php endif; ?>

<!-- Form -->
<form method="POST" class="space-y-6">
    <!-- User Email Search -->
    <div>
        <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
            User Email Address
        </label>
        <div class="relative">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207"></path>
                </svg>
            </div>
            <input type="email" id="email" name="email" required
                value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>"
                class="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                placeholder="Type to search users by email, name, or ID..."
                autocomplete="off">

            <!-- Search Results Dropdown -->
            <div id="searchResults" class="absolute z-10 w-full bg-white border border-gray-300 rounded-lg shadow-lg mt-1 hidden max-h-60 overflow-y-auto">
                <!-- Results will be populated here -->
            </div>
        </div>

        <!-- Selected User Info -->
        <div id="userInfo" class="mt-3 p-3 bg-blue-50 border border-blue-200 rounded-lg hidden">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                            <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium text-blue-900" id="selectedUserName"></p>
                        <p class="text-sm text-blue-700" id="selectedUserEmail"></p>
                    </div>
                </div>
                <div class="text-right">
                    <p class="text-xs text-blue-600">Deposit: $<span id="depositBalance">0.00</span></p>
                    <p class="text-xs text-blue-600">Withdrawal: $<span id="withdrawalBalance">0.00</span></p>
                </div>
            </div>
        </div>
    </div>

    <!-- Wallet Type Selection -->
    <div>
        <label class="block text-sm font-medium text-gray-700 mb-2">
            Wallet Type
        </label>
        <div class="grid grid-cols-2 gap-4">
            <label class="flex items-center p-3 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50">
                <input type="radio" name="stake_type" value="deposit" checked class="mr-3">
                <div>
                    <div class="font-medium text-gray-900">Deposit Wallet</div>
                    <div class="text-sm text-gray-500">Stake from deposit wallet</div>
                </div>
            </label>
            <label class="flex items-center p-3 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50">
                <input type="radio" name="stake_type" value="withdrawal" class="mr-3">
                <div>
                    <div class="font-medium text-gray-900">Withdrawal Wallet</div>
                    <div class="text-sm text-gray-500">Stake from withdrawal wallet</div>
                </div>
            </label>
        </div>
    </div>

    <!-- Staking Amount -->
    <div>
        <label for="stake_coins" class="block text-sm font-medium text-gray-700 mb-2">
            Number of Coins to Stake
        </label>
        <div class="relative">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                </svg>
            </div>
            <input type="number" id="stake_coins" name="stake_coins" required min="<?php echo $MIN_STAKE_COINS; ?>" step="<?php echo $MIN_STAKE_COINS; ?>"
                value="<?php echo htmlspecialchars($_POST['stake_coins'] ?? ''); ?>"
                class="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                placeholder="Enter coins in multiples of <?php echo $MIN_STAKE_COINS; ?>">
        </div>
        <div class="mt-2 text-sm text-gray-600">
            <div>Minimum: <?php echo $MIN_STAKE_COINS; ?> coins (Must be in multiples of <?php echo $MIN_STAKE_COINS; ?>)</div>
            <div id="stakeValue" class="font-medium text-blue-600"></div>
        </div>
    </div>

    <!-- Submit Button -->
    <button type="submit"
        class="w-full bg-gradient-to-r from-green-500 to-blue-600 text-white py-3 px-4 rounded-lg font-medium hover:from-green-600 hover:to-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all duration-200 transform hover:scale-105">
        Stake Coins (6 Month Freeze)
    </button>
</form>

</div>
</main>

<script>
    const coinPrice = <?php echo $COIN_PRICE; ?>;
    const minStakeCoins = <?php echo $MIN_STAKE_COINS; ?>;

    // Calculate stake value in real-time
    document.getElementById('stake_coins').addEventListener('input', function() {
        const coins = parseInt(this.value) || 0;
        const value = coins * coinPrice;
        const stakeValueDiv = document.getElementById('stakeValue');

        if (coins > 0) {
            stakeValueDiv.textContent = `Total Value: $${value.toFixed(2)} USD`;

            if (coins % minStakeCoins !== 0) {
                stakeValueDiv.className = 'font-medium text-red-600';
                stakeValueDiv.textContent += ` (Must be multiple of ${minStakeCoins})`;
            } else {
                stakeValueDiv.className = 'font-medium text-blue-600';
            }
        } else {
            stakeValueDiv.textContent = '';
        }
    });

    // User search functionality
    let searchTimeout;
    const emailInput = document.getElementById('email');
    const searchResults = document.getElementById('searchResults');
    const userInfo = document.getElementById('userInfo');

    emailInput.addEventListener('input', function() {
        clearTimeout(searchTimeout);
        const query = this.value.trim();

        if (query.length < 2) {
            searchResults.classList.add('hidden');
            return;
        }

        searchTimeout = setTimeout(() => {
            fetch(`?search=${encodeURIComponent(query)}`)
                .then(response => response.json())
                .then(users => {
                    displaySearchResults(users);
                })
                .catch(error => {
                    console.error('Search error:', error);
                    searchResults.classList.add('hidden');
                });
        }, 300);
    });

    function displaySearchResults(users) {
        if (users.length === 0) {
            searchResults.innerHTML = '<div class="p-3 text-gray-500 text-center">No users found</div>';
            searchResults.classList.remove('hidden');
            return;
        }

        const resultsHTML = users.map(user => `
                <div class="p-3 hover:bg-gray-50 cursor-pointer border-b border-gray-100 last:border-b-0"
                     onclick="selectUser('${user.email}', '${user.name}', '${user.user_id}', ${user.deposit_wallet_balance}, ${user.withdrawal_wallet_balance})">
                    <div class="flex justify-between items-center">
                        <div>
                            <div class="font-medium text-gray-900">${user.name}</div>
                            <div class="text-sm text-gray-600">${user.email}</div>
                            <div class="text-xs text-gray-500">ID: ${user.user_id}</div>
                        </div>
                        <div class="text-right text-xs">
                            <div class="text-green-600">D: $${parseFloat(user.deposit_wallet_balance).toFixed(2)}</div>
                            <div class="text-blue-600">W: $${parseFloat(user.withdrawal_wallet_balance).toFixed(2)}</div>
                        </div>
                    </div>
                </div>
            `).join('');

        searchResults.innerHTML = resultsHTML;
        searchResults.classList.remove('hidden');
    }

    function selectUser(email, name, userId, depositBalance, withdrawalBalance) {
        emailInput.value = email;
        searchResults.classList.add('hidden');

        document.getElementById('selectedUserName').textContent = name;
        document.getElementById('selectedUserEmail').textContent = `${email} (ID: ${userId})`;
        document.getElementById('depositBalance').textContent = parseFloat(depositBalance).toFixed(2);
        document.getElementById('withdrawalBalance').textContent = parseFloat(withdrawalBalance).toFixed(2);

        userInfo.classList.remove('hidden');
    }

    // Hide search results when clicking outside
    document.addEventListener('click', function(e) {
        if (!emailInput.contains(e.target) && !searchResults.contains(e.target)) {
            searchResults.classList.add('hidden');
        }
    });

    // Form validation
    document.querySelector('form').addEventListener('submit', function(e) {
        const coins = parseInt(document.getElementById('stake_coins').value) || 0;

        if (coins < minStakeCoins) {
            e.preventDefault();
            alert(`Minimum staking amount is ${minStakeCoins} coins!`);
            return false;
        }

        if (coins % minStakeCoins !== 0) {
            e.preventDefault();
            alert(`Staking amount must be in multiples of ${minStakeCoins} coins!`);
            return false;
        }
    });

    // Auto-hide success message after 5 seconds
    const successMessage = document.querySelector('.bg-green-100');
    if (successMessage) {
        setTimeout(() => {
            successMessage.style.opacity = '0';
            setTimeout(() => successMessage.remove(), 300);
        }, 5000);
    }
</script>
</body>

</html>