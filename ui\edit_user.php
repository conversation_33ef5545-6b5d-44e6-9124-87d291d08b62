<?php
session_start();

if (!isset($_SESSION['user_id']) || !$_SESSION['is_admin']) {
    header('Location: login.php');
    exit();
}

include 'dbcon.php';

$user_id_to_edit = $_GET['id'] ?? null;

if (!$user_id_to_edit) {
    header('Location: admin.php');
    exit();
}

// Handle form submission for updating a user
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'update') {
    $name = $_POST['name'];
    $email = $_POST['email'];
    $phone = $_POST['phone'];
    $sponser_id = $_POST['sponser_id'];
    $binanace_address = $_POST['binanace_address'];
    $is_admin = isset($_POST['is_admin']) ? 1 : 0;
    $new_password = $_POST['password'];

    // Prevent admin from removing their own admin status
    if ($user_id_to_edit == $_SESSION['user_id'] && !$is_admin) {
        // Optionally, add an error message here
    } else {
        if (!empty($new_password)) {
            $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
            $stmt = $conn->prepare('UPDATE users SET name = ?, email = ?, phone = ?, sponser_id = ?, binanace_address = ?, is_admin = ?, password = ? WHERE id = ?');
            $stmt->bind_param('sssssisi', $name, $email, $phone, $sponser_id, $binanace_address, $is_admin, $hashed_password, $user_id_to_edit);
        } else {
            $stmt = $conn->prepare('UPDATE users SET name = ?, email = ?, phone = ?, sponser_id = ?, binanace_address = ?, is_admin = ? WHERE id = ?');
            $stmt->bind_param('sssssi', $name, $email, $phone, $sponser_id, $binanace_address, $is_admin, $user_id_to_edit);
        }
        $stmt->execute();
        $stmt->close();
    }

    header('Location: admin.php');
    exit();
}

// Fetch user details
$stmt = $conn->prepare('SELECT * FROM users WHERE id = ? AND deleted_at IS NULL');
$stmt->bind_param('i', $user_id_to_edit);
$stmt->execute();
$result = $stmt->get_result();
$user = $result->fetch_assoc();
$stmt->close();

if (!$user) {
    echo 'User not found.';
    exit();
}

$conn->close();
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin - Edit User</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>

<body class="bg-gray-100">
    <nav class="bg-white shadow-md">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex">
                    <div class="flex-shrink-0 flex items-center">
                        <a href="admin.php" class="text-xl font-bold">Crypto Admin</a>
                    </div>
                </div>
                <div class="flex items-center">
                    <a href="admin.php" class="text-gray-700 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium">User Management</a>
                    <a href="package_manager.php" class="text-gray-700 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium">Package Master</a>
                    <a href="logout.php" class="text-gray-700 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium">Logout</a>
                </div>
            </div>
        </div>
    </nav>

    <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div class="bg-white shadow-md rounded-lg p-8">
            <h2 class="text-2xl font-bold mb-6">Edit User</h2>
            <form action="edit_user.php?id=<?php echo htmlspecialchars($user['id']); ?>" method="POST">
                <input type="hidden" name="action" value="update">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="name" class="block text-sm font-medium text-gray-700">Full Name</label>
                        <input type="text" name="name" id="name" value="<?php echo htmlspecialchars($user['name']); ?>" required class="mt-1 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
                    </div>
                    <div>
                        <label for="email" class="block text-sm font-medium text-gray-700">Email Address</label>
                        <input type="email" name="email" id="email" value="<?php echo htmlspecialchars($user['email']); ?>" required class="mt-1 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
                    </div>
                    <div>
                        <label for="phone" class="block text-sm font-medium text-gray-700">Phone Number</label>
                        <input type="text" name="phone" id="phone" value="<?php echo htmlspecialchars($user['phone']); ?>" class="mt-1 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
                    </div>
                    <div>
                        <label for="sponser__id" class="block text-sm font-medium text-gray-700">Sponser ID</label>
                        <input type="text" name="sponser_id" id="sponser_id" value="<?php echo htmlspecialchars($user['sponser_id']); ?>" class="mt-1 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
                    </div>
                    <div>
                        <label for="binanace_address" class="block text-sm font-medium text-gray-700">Binance Address</label>
                        <input type="text" name="binanace_address" id="binanace_address" value="<?php echo htmlspecialchars($user['binanace_address']); ?>" class="mt-1 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
                    </div>
                    <div>
                        <label for="password" class="block text-sm font-medium text-gray-700">New Password (leave blank to keep current)</label>
                        <input type="password" name="password" id="password" class="mt-1 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
                    </div>
                    <div class="flex items-center">
                        <input type="checkbox" name="is_admin" id="is_admin" value="1" <?php echo $user['is_admin'] ? 'checked' : ''; ?> <?php if ($user['id'] == $_SESSION['user_id']) echo 'disabled'; ?> class="h-4 w-4 text-indigo-600 border-gray-300 rounded">
                        <label for="is_admin" class="ml-2 block text-sm text-gray-900">Is Admin</label>
                    </div>
                </div>
                <div class="mt-6 flex justify-between">
                    <button type="submit" class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">Save Changes</button>
                    <a href="admin.php" class="inline-flex justify-center py-2 px-4 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">Cancel</a>
                </div>
            </form>
        </div>
    </div>
</body>

</html>