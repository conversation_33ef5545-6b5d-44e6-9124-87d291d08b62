<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contact Support - Crypto Wallet</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <style>
        body {
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
            min-height: 100vh;
        }

        .stats-card {
            background: rgba(30, 41, 59, 0.4);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(59, 130, 246, 0.2);
            border-radius: 0.75rem;
            padding: 1rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }

        .contact-card {
            background: rgba(15, 23, 42, 0.8);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(59, 130, 246, 0.3);
            border-radius: 0.75rem;
            padding: 1rem;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .contact-card:hover {
            border-color: rgba(6, 182, 212, 0.6);
            background: rgba(15, 23, 42, 0.9);
            transform: translateY(-2px);
        }

        .input-field {
            background: rgba(15, 23, 42, 0.8);
            border: 1px solid rgba(59, 130, 246, 0.3);
            border-radius: 0.5rem;
            padding: 0.75rem;
            color: white;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .input-field:focus {
            outline: none;
            border-color: rgba(6, 182, 212, 0.6);
            box-shadow: 0 0 0 3px rgba(6, 182, 212, 0.15);
            background: rgba(15, 23, 42, 0.9);
        }

        .input-field::placeholder {
            color: rgba(148, 163, 184, 0.5);
        }

        .btn-primary {
            background: linear-gradient(135deg, #3b82f6, #06b6d4);
            border: none;
            border-radius: 0.5rem;
            padding: 0.75rem 1.5rem;
            color: white;
            font-weight: 600;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #2563eb, #0891b2);
            transform: translateY(-1px);
        }

        /* Bottom Navigation Styles */
        .accent {
            color: #06b6d4 !important;
        }

        .hover\:accent:hover {
            color: #06b6d4 !important;
        }
    </style>
</head>

<body class="text-white">
    <div class="min-h-screen flex flex-col">
        <!-- Header -->
        <div class="sticky top-0 z-50 bg-slate-800/95 backdrop-blur-sm border-b border-slate-700">
            <div class="flex items-center justify-between px-6 py-4">
                <div class="flex items-center gap-4">
                    <button onclick="goBack()"
                        class="w-10 h-10 rounded-full bg-slate-700 hover:bg-slate-600 flex items-center justify-center transition-colors">
                        <i class="fas fa-arrow-left text-slate-300"></i>
                    </button>
                    <div>
                        <h1 class="text-xl font-bold text-white">Contact Support</h1>
                        <p class="text-sm text-slate-400">Get in touch with our team</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-grow overflow-y-auto px-4 sm:px-6 py-4">
            <!-- Contact Methods -->
            <div class="stats-card mb-4">
                <div class="flex items-center gap-2 mb-4">
                    <div
                        class="w-10 h-10 rounded-lg bg-gradient-to-r from-blue-600 to-cyan-600 flex items-center justify-center">
                        <i class="fas fa-headset text-white"></i>
                    </div>
                    <div>
                        <h2 class="text-lg font-bold text-white">Contact Methods</h2>
                        <p class="text-xs text-slate-400">Choose your preferred way to reach us</p>
                    </div>
                </div>

                <div class="space-y-3">
                    <div class="contact-card" onclick="sendEmail()">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center gap-3">
                                <i class="fas fa-envelope text-blue-400 text-xl"></i>
                                <div>
                                    <p class="text-base font-semibold text-white">Email Support</p>
                                    <p class="text-xs text-slate-400"><EMAIL></p>
                                    <p class="text-xs text-cyan-400">Response within 24 hours</p>
                                </div>
                            </div>
                            <i class="fas fa-external-link-alt text-slate-400"></i>
                        </div>
                    </div>

                    <div class="contact-card" onclick="openTelegram()">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center gap-3">
                                <i class="fab fa-telegram text-cyan-400 text-xl"></i>
                                <div>
                                    <p class="text-base font-semibold text-white">Telegram Support</p>
                                    <p class="text-xs text-slate-400">@CryptoWalletSupport</p>
                                    <p class="text-xs text-blue-400">Fast response • Online now</p>
                                </div>
                            </div>
                            <i class="fas fa-external-link-alt text-slate-400"></i>
                        </div>
                    </div>

                    <div class="contact-card" onclick="callSupport()">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center gap-3">
                                <i class="fas fa-phone text-cyan-400 text-xl"></i>
                                <div>
                                    <p class="text-base font-semibold text-white">Phone Support</p>
                                    <p class="text-xs text-slate-400">+1 (555) 123-CRYPTO</p>
                                    <p class="text-xs text-blue-400">Mon-Fri 9AM-6PM EST</p>
                                </div>
                            </div>
                            <i class="fas fa-external-link-alt text-slate-400"></i>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Contact Form -->
            <div class="stats-card mb-4">
                <div class="flex items-center gap-2 mb-4">
                    <div
                        class="w-10 h-10 rounded-lg bg-gradient-to-r from-blue-600 to-cyan-600 flex items-center justify-center">
                        <i class="fas fa-paper-plane text-white"></i>
                    </div>
                    <div>
                        <h2 class="text-lg font-bold text-white">Quick Contact Form</h2>
                        <p class="text-xs text-slate-400">Send us a message directly</p>
                    </div>
                </div>

                <form id="contactForm" class="space-y-4">
                    <!-- Name -->
                    <div>
                        <label class="block text-sm font-medium text-slate-300 mb-2">Your Name</label>
                        <input type="text" id="contactName" class="input-field w-full" value="John Doe"
                            placeholder="Enter your name">
                    </div>

                    <!-- Email -->
                    <div>
                        <label class="block text-sm font-medium text-slate-300 mb-2">Email Address</label>
                        <input type="email" id="contactEmail" class="input-field w-full" value="<EMAIL>"
                            placeholder="Enter your email">
                    </div>

                    <!-- Subject -->
                    <div>
                        <label class="block text-sm font-medium text-slate-300 mb-2">Subject</label>
                        <select id="contactSubject" class="input-field w-full">
                            <option value="">Select a subject</option>
                            <option value="wallet">Wallet & Deposits</option>
                            <option value="withdrawal">Withdrawal Issues</option>
                            <option value="referral">Referral System</option>
                            <option value="security">Security Concerns</option>
                            <option value="technical">Technical Issues</option>
                            <option value="account">Account Problems</option>
                            <option value="other">Other</option>
                        </select>
                    </div>

                    <!-- Priority -->
                    <div>
                        <label class="block text-sm font-medium text-slate-300 mb-2">Priority Level</label>
                        <select id="contactPriority" class="input-field w-full">
                            <option value="low">Low - General inquiry</option>
                            <option value="medium" selected>Medium - Standard support</option>
                            <option value="high">High - Urgent issue</option>
                            <option value="critical">Critical - Account compromised</option>
                        </select>
                    </div>

                    <!-- Message -->
                    <div>
                        <label class="block text-sm font-medium text-slate-300 mb-2">Message</label>
                        <textarea id="contactMessage" rows="5" class="input-field w-full"
                            placeholder="Please describe your issue or question in detail..."></textarea>
                        <p class="text-xs text-slate-500 mt-1">Minimum 20 characters required</p>
                    </div>

                    <!-- Attachment -->
                    <div>
                        <label class="block text-sm font-medium text-slate-300 mb-2">Attachment (Optional)</label>
                        <div class="border-2 border-dashed border-slate-600 rounded-lg p-4 text-center">
                            <i class="fas fa-cloud-upload-alt text-slate-400 text-2xl mb-2"></i>
                            <p class="text-sm text-slate-400">Click to upload or drag and drop</p>
                            <p class="text-xs text-slate-500">PNG, JPG, PDF up to 5MB</p>
                            <input type="file" id="contactAttachment" class="hidden" accept=".png,.jpg,.jpeg,.pdf">
                        </div>
                    </div>

                    <button type="button" onclick="submitContactForm()" class="btn-primary w-full">
                        <i class="fas fa-paper-plane mr-2"></i>
                        Send Message
                    </button>
                </form>
            </div>

            <!-- Support Hours -->
            <div class="stats-card mb-4">
                <div class="flex items-center gap-2 mb-4">
                    <div
                        class="w-10 h-10 rounded-lg bg-gradient-to-r from-blue-600 to-cyan-600 flex items-center justify-center">
                        <i class="fas fa-clock text-white"></i>
                    </div>
                    <div>
                        <h2 class="text-lg font-bold text-white">Support Hours</h2>
                        <p class="text-xs text-slate-400">When our team is available</p>
                    </div>
                </div>

                <div class="space-y-3">
                    <div
                        class="bg-gradient-to-r from-slate-800/50 to-slate-700/50 rounded-lg p-3 border border-cyan-500/20">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-semibold text-cyan-400">Email & Telegram</p>
                                <p class="text-xs text-slate-300">24/7 Support Available</p>
                            </div>
                            <span class="text-xs text-cyan-400 bg-cyan-500/20 px-2 py-1 rounded-full">Online</span>
                        </div>
                    </div>

                    <div
                        class="bg-gradient-to-r from-slate-800/50 to-slate-700/50 rounded-lg p-3 border border-blue-500/20">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-semibold text-blue-400">Phone Support</p>
                                <p class="text-xs text-slate-300">Monday - Friday: 9:00 AM - 6:00 PM EST</p>
                            </div>
                            <span class="text-xs text-blue-400 bg-blue-500/20 px-2 py-1 rounded-full">Business
                                Hours</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Emergency Contact -->
            <div class="stats-card mb-4">
                <div class="bg-gradient-to-r from-red-600/20 to-pink-600/20 rounded-lg p-3 border border-red-500/30">
                    <div class="flex items-start gap-2">
                        <i class="fas fa-exclamation-triangle text-red-400 text-lg mt-0.5"></i>
                        <div>
                            <p class="text-sm font-semibold text-red-400">Emergency Contact</p>
                            <p class="text-xs text-slate-300 mb-2">If your account has been compromised or you suspect
                                unauthorized access:</p>
                            <div class="space-y-1">
                                <p class="text-xs text-slate-300">• Email: <EMAIL></p>
                                <p class="text-xs text-slate-300">• Telegram: @CryptoWalletEmergency</p>
                                <p class="text-xs text-slate-300">• Phone: +1 (555) 911-HELP</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sticky Bottom Navigation Bar -->
        <div class="sticky bottom-0 z-40 flex-shrink-0">
            <div class="bg-slate-800/95 backdrop-blur-sm mx-4 mb-4 rounded-2xl border border-slate-700">
                <div class="flex justify-around items-center h-16">
                    <a href="1.html" class="flex flex-col items-center gap-1 text-slate-400 hover:accent">
                        <i class="fas fa-home"></i>
                        <span class="text-xs">Home</span>
                    </a>
                    <a href="wallet.html" class="flex flex-col items-center gap-1 text-slate-400 hover:accent">
                        <i class="fas fa-wallet"></i>
                        <span class="text-xs">Wallet</span>
                    </a>
                    <a href="team.html" class="flex flex-col items-center gap-1 text-slate-400 hover:accent">
                        <i class="fas fa-users"></i>
                        <span class="text-xs">Team</span>
                    </a>
                    <a href="profile.html" class="flex flex-col items-center gap-1 accent">
                        <i class="fas fa-user"></i>
                        <span class="text-xs">Profile</span>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script>
        function goBack() {
            window.history.back();
        }

        function sendEmail() {
            window.open('mailto:<EMAIL>?subject=Support Request&body=Hello, I need help with...');
        }

        function openTelegram() {
            window.open('https://t.me/CryptoWalletSupport', '_blank');
        }

        function callSupport() {
            window.open('tel:+15551234567');
        }

        function submitContactForm() {
            const name = document.getElementById('contactName').value;
            const email = document.getElementById('contactEmail').value;
            const subject = document.getElementById('contactSubject').value;
            const priority = document.getElementById('contactPriority').value;
            const message = document.getElementById('contactMessage').value;

            if (!name || !email || !subject || !message) {
                Swal.fire({
                    icon: 'error',
                    title: 'Missing Information',
                    text: 'Please fill in all required fields',
                    background: '#1e293b',
                    color: '#ffffff',
                    confirmButtonColor: '#3b82f6',
                    iconColor: '#3b82f6'
                });
                return;
            }

            if (message.length < 20) {
                Swal.fire({
                    icon: 'error',
                    title: 'Message Too Short',
                    text: 'Please provide more details (minimum 20 characters)',
                    background: '#1e293b',
                    color: '#ffffff',
                    confirmButtonColor: '#3b82f6',
                    iconColor: '#3b82f6'
                });
                return;
            }

            // Simulate form submission
            Swal.fire({
                icon: 'success',
                title: 'Message Sent!',
                html: `
                    <div class="text-center space-y-2">
                        <p>Your message has been sent successfully.</p>
                        <p class="text-sm text-slate-400">Ticket ID: <strong class="text-cyan-400">#TK${Math.floor(Math.random() * 1000).toString().padStart(3, '0')}</strong></p>
                        <p class="text-sm text-slate-400">Expected response time: <strong class="text-cyan-400">${priority === 'critical' ? '1 hour' : priority === 'high' ? '4 hours' : '24 hours'}</strong></p>
                    </div>
                `,
                background: '#1e293b',
                color: '#ffffff',
                confirmButtonColor: '#06b6d4',
                iconColor: '#06b6d4',
                timer: 3000,
                showConfirmButton: false
            }).then(() => {
                // Clear form
                document.getElementById('contactForm').reset();
                document.getElementById('contactName').value = 'John Doe';
                document.getElementById('contactEmail').value = '<EMAIL>';
            });
        }

        // File upload handling
        document.addEventListener('DOMContentLoaded', function () {
            const uploadArea = document.querySelector('.border-dashed');
            const fileInput = document.getElementById('contactAttachment');

            uploadArea.addEventListener('click', () => fileInput.click());

            uploadArea.addEventListener('dragover', (e) => {
                e.preventDefault();
                uploadArea.classList.add('border-cyan-400', 'bg-cyan-400/10');
            });

            uploadArea.addEventListener('dragleave', (e) => {
                e.preventDefault();
                uploadArea.classList.remove('border-cyan-400', 'bg-cyan-400/10');
            });

            uploadArea.addEventListener('drop', (e) => {
                e.preventDefault();
                uploadArea.classList.remove('border-cyan-400', 'bg-cyan-400/10');
                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    fileInput.files = files;
                    updateFileDisplay(files[0]);
                }
            });

            fileInput.addEventListener('change', (e) => {
                if (e.target.files.length > 0) {
                    updateFileDisplay(e.target.files[0]);
                }
            });

            function updateFileDisplay(file) {
                const uploadArea = document.querySelector('.border-dashed');
                uploadArea.innerHTML = `
                    <i class="fas fa-file text-green-400 text-2xl mb-2"></i>
                    <p class="text-sm text-green-400">${file.name}</p>
                    <p class="text-xs text-slate-500">${(file.size / 1024 / 1024).toFixed(2)} MB</p>
                `;
            }
        });
    </script>
</body>

</html>