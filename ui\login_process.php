<?php
session_start();

include 'dbcon.php';

// Get data from form
$email = $_POST['email'] ?? '';
$password = $_POST['password'] ?? '';
$remember = isset($_POST['remember']) ? true : false;

// Basic validation
if (empty($email) || empty($password)) {
    echo 'Please fill in all fields';
    exit;
}

$sql = 'SELECT * FROM users WHERE email = ? AND is_active = 1';
$stmt = $conn->prepare($sql);
$stmt->bind_param('s', $email);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows > 0) {
    $user = $result->fetch_assoc();
    if (password_verify($password, $user['password'])) {
        // Set session variables
        $_SESSION['user_id'] = $user['user_id']; // Use correct column name
        $_SESSION['user_name'] = $user['full_name']; // Use correct column name
        $_SESSION['is_admin'] = $user['is_admin'];

        // Set admin-specific session variables if user is admin
        if ($user['is_admin'] == 1) {
            $_SESSION['admin_logged_in'] = true;
            $_SESSION['admin_id'] = $user['user_id'];
        }

        // Handle remember me functionality
        if ($remember) {
            // Generate a secure random token
            $session_token = bin2hex(random_bytes(32));
            $expires_at = date('Y-m-d H:i:s', strtotime('+30 days')); // Remember for 30 days

            // Store the session token in database
            $stmt = $conn->prepare('INSERT INTO user_sessions (user_id, session_token, expires_at) VALUES (?, ?, ?)');
            $stmt->bind_param('iss', $user['id'], $session_token, $expires_at);

            if ($stmt->execute()) {
                // Set secure cookie
                setcookie('remember_token', $session_token, [
                    'expires' => strtotime('+30 days'),
                    'path' => '/',
                    'secure' => false, // Set to true in production with HTTPS
                    'httponly' => true,
                    'samesite' => 'Strict'
                ]);
            }
            $stmt->close();
        }

        // Redirect based on user role
        if ($user['is_admin'] == 1) {
            // Check if coming from admin dashboard redirect
            if (isset($_GET['redirect']) && $_GET['redirect'] === 'admin_dashboard') {
                header('Location: admin_dashboard.php');
            } else {
                header('Location: admin_dashboard.php'); // Default admin redirect to dashboard
            }
        } else {
            header('Location: account.php');
        }
        exit();
    } else {
        echo 'Invalid email or password';
    }
} else {
    echo 'Invalid email or password';
}

$stmt->close();
$conn->close();
