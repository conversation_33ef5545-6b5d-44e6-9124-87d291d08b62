<?php
session_start();
header('Content-Type: application/json');

if (!isset($_SESSION['user_id']) || !$_SESSION['is_admin']) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Unauthorized access.']);
    exit();
}

$admin_id = $_SESSION['user_id'];
$data = json_decode(file_get_contents('php://input'), true);

$request_id = $data['request_id'] ?? null;
$action = $data['action'] ?? null; // 'approve' or 'reject'
$notes = $data['notes'] ?? '';

if (!$request_id || !$action || !in_array($action, ['approve', 'reject'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Invalid request data.']);
    exit();
}

$servername = "localhost";
$username = "root";
$password = "";
$dbname = "crypto";

$conn = new mysqli($servername, $username, $password, $dbname);
if ($conn->connect_error) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Database connection failed.']);
    exit();
}

// --- Main Logic ---
$conn->begin_transaction();

try {
    // 1. Fetch the request and lock the row
    $sql_select = "SELECT * FROM deposit_requests WHERE id = ? AND status = 'pending' FOR UPDATE";
    $stmt_select = $conn->prepare($sql_select);
    $stmt_select->bind_param("i", $request_id);
    $stmt_select->execute();
    $result = $stmt_select->get_result();
    $request = $result->fetch_assoc();
    $stmt_select->close();

    if (!$request) {
        throw new Exception('Request not found or already processed.');
    }

    $user_id = $request['user_id'];
    $amount = $request['amount'];

    // 2. Perform action
    if ($action === 'approve') {
        // Add fund to user's deposit wallet
        $sql_update_user = "UPDATE users SET deposit_wallet_balance = deposit_wallet_balance + ? WHERE id = ?";
        $stmt_update_user = $conn->prepare($sql_update_user);
        $stmt_update_user->bind_param("di", $amount, $user_id);
        if (!$stmt_update_user->execute()) throw new Exception('Failed to update user balance.');
        $stmt_update_user->close();

        // Log the transaction in wallet_history
        $description = "Deposit request #{$request_id} approved by admin #{$admin_id}";
        $sql_log = "INSERT INTO wallet_history (user_id, amount, type, wallet_type, description, related_user_id) VALUES (?, ?, 'deposit', 'deposit', ?, ?)";
        $stmt_log = $conn->prepare($sql_log);
        $stmt_log->bind_param("idsi", $user_id, $amount, $description, $admin_id);
        if (!$stmt_log->execute()) throw new Exception('Failed to log transaction.');
        $stmt_log->close();
    }

    // 3. Update the request status
    $new_status = ($action === 'approve') ? 'approved' : 'rejected';
    $sql_update_request = "UPDATE deposit_requests SET status = ?, reviewed_by = ?, reviewed_at = NOW(), admin_notes = ? WHERE id = ?";
    $stmt_update_request = $conn->prepare($sql_update_request);
    $stmt_update_request->bind_param("sisi", $new_status, $admin_id, $notes, $request_id);
    if (!$stmt_update_request->execute()) throw new Exception('Failed to update request status.');
    $stmt_update_request->close();

    // Commit transaction
    $conn->commit();

    echo json_encode(['success' => true]);

} catch (Exception $e) {
    $conn->rollback();
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => $e->getMessage()]);
} finally {
    $conn->close();
}
?>
