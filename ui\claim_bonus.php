<?php
session_start();

header('Content-Type: application/json');

if (!isset($_SESSION['user_id'])) {
    echo json_encode(['error' => 'User not authenticated']);
    exit();
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST' || !isset($_POST['bonus_id'])) {
    echo json_encode(['error' => 'Invalid request']);
    exit();
}

include 'dbcon.php';

$current_user_id = $_SESSION['user_id'];
$bonus_id = (int) $_POST['bonus_id'];

$conn->begin_transaction();

try {
    $check_sql = 'SELECT id FROM bonus_claims WHERE user_id = ? AND bonus_id = ?';
    $stmt = $conn->prepare($check_sql);
    $stmt->bind_param('ii', $current_user_id, $bonus_id);
    $stmt->execute();
    $result = $stmt->get_result();
    if ($result->num_rows > 0) {
        throw new Exception('You have already claimed this bonus.');
    }
    $stmt->close();

    $insert_sql = "INSERT INTO bonus_claims (user_id, bonus_id, status) VALUES (?, ?, 'pending')";
    $stmt = $conn->prepare($insert_sql);
    $stmt->bind_param('ii', $current_user_id, $bonus_id);
    if (!$stmt->execute()) {
        throw new Exception('Failed to record your claim. Please try again.');
    }
    $stmt->close();

    $conn->commit();
    echo json_encode(['success' => true]);
} catch (Exception $e) {
    $conn->rollback();
    echo json_encode(['error' => $e->getMessage()]);
}

$conn->close();
?>
