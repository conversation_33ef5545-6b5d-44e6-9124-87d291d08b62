<?php
session_start();

if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit();
}

include 'dbcon.php';

$user_id = $_SESSION['user_id'];

// Fetch user's current balance, package, and package amount
$user_sql = 'SELECT u.deposit_wallet_balance, u.withdrawal_wallet_balance, u.package, p.package_amount as current_package_amount FROM users u LEFT JOIN packages p ON u.package = p.package_name WHERE u.id = ?';
$stmt = $conn->prepare($user_sql);
$stmt->bind_param('i', $user_id);
$stmt->execute();
$user_result = $stmt->get_result();
$user = $user_result->fetch_assoc();
$current_deposit_balance = $user['deposit_wallet_balance'];
$current_withdrawal_balance = $user['withdrawal_wallet_balance'];
$current_package = $user['package'];
$current_package_amount = $user['current_package_amount'] ?? 0;

// Fetch all active packages
$packages_sql = 'SELECT id, package_name, package_amount, profit_percentage FROM packages WHERE is_active = 1 ORDER BY package_amount ASC';
$packages_result = $conn->query($packages_sql);

$conn->close();
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invest</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>

<body class="bg-gray-100 font-sans">
    <?php include 'user_nav.php'; ?>

    <main class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div class="bg-white shadow-lg rounded-lg p-8">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-3xl font-bold text-gray-800">Choose Your Plan</h2>
                <div class="text-right">
                    <div class="mb-2">
                        <h3 class="text-sm font-semibold text-gray-700">Deposit Wallet Balance</h3>
                        <p class="text-xl font-bold text-green-600">$<?php echo number_format($current_deposit_balance, 4); ?></p>
                    </div>
                    <div>
                        <h3 class="text-sm font-semibold text-gray-700">Withdrawal Wallet Balance</h3>
                        <p class="text-xl font-bold text-blue-600">$<?php echo number_format($current_withdrawal_balance, 4); ?></p>
                    </div>
                </div>
            </div>

            <div id="feedback-message" class="hidden p-4 mb-4 text-sm rounded-lg" role="alert">
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <?php while ($package = $packages_result->fetch_assoc()): ?>
                    <div class="border rounded-lg p-6 shadow-sm hover:shadow-lg transition-shadow duration-200 flex flex-col <?php echo ($package['package_name'] == $current_package) ? 'border-indigo-500' : 'border-gray-200'; ?>">
                        <h3 class="text-2xl font-bold text-gray-900"><?php echo htmlspecialchars($package['package_name']); ?></h3>
                        <p class="text-4xl font-extrabold text-indigo-600 my-4">$<?php echo number_format($package['package_amount'], 2); ?></p>
                        <p class="text-gray-600 mb-4">Daily Profit: <span class="font-semibold"><?php echo htmlspecialchars($package['profit_percentage']); ?>%</span></p>
                        <div class="flex-grow"></div>
                        <?php
                        $is_current = ($package['package_name'] == $current_package);
                        $is_upgrade = ($package['package_amount'] > $current_package_amount);
                        ?>
                        <?php if ($is_current): ?>
                            <button disabled class="w-full mt-4 bg-gray-400 text-white font-bold py-2 px-4 rounded cursor-not-allowed">Current Plan</button>
                        <?php elseif ($is_upgrade): ?>
                            <button data-package-id="<?php echo $package['id']; ?>" data-package-amount="<?php echo $package['package_amount']; ?>" data-package-name="<?php echo htmlspecialchars($package['package_name']); ?>" class="purchase-btn w-full mt-4 bg-indigo-600 hover:bg-indigo-700 text-white font-bold py-2 px-4 rounded transition-colors duration-200">Purchase Plan</button>
                        <?php else: ?>
                            <button disabled class="w-full mt-4 bg-gray-300 text-gray-500 font-bold py-2 px-4 rounded cursor-not-allowed">Not an Upgrade</button>
                        <?php endif; ?>
                    </div>
                <?php endwhile; ?>
            </div>
        </div>
    </main>

    <!-- Wallet Selection Modal -->
    <div id="walletModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900" id="modalTitle">Select Payment Wallet</h3>
                    <button type="button" class="text-gray-400 hover:text-gray-600" id="closeModalBtn">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>

                <div class="mb-4">
                    <p class="text-sm text-gray-600 mb-4">Choose which wallet to use for purchasing <span id="selectedPackageName" class="font-semibold"></span>:</p>
                    <p class="text-lg font-bold text-gray-800 mb-4">Package Cost: $<span id="selectedPackageAmount"></span></p>
                </div>

                <div class="space-y-3">
                    <!-- Deposit Wallet Option -->
                    <label class="flex items-center p-3 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50" id="depositWalletOption">
                        <input type="radio" name="wallet_type" value="deposit" class="mr-3" checked>
                        <div class="flex-1">
                            <div class="flex justify-between items-center">
                                <div>
                                    <div class="font-medium text-gray-900">Deposit Wallet</div>
                                    <div class="text-sm text-gray-500">Use funds from deposit wallet</div>
                                </div>
                                <div class="text-right">
                                    <div class="text-lg font-bold text-green-600">$<?php echo number_format($current_deposit_balance, 4); ?></div>
                                    <div class="text-xs text-gray-500">Available</div>
                                </div>
                            </div>
                        </div>
                    </label>

                    <!-- Withdrawal Wallet Option -->
                    <label class="flex items-center p-3 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50" id="withdrawalWalletOption">
                        <input type="radio" name="wallet_type" value="withdrawal" class="mr-3">
                        <div class="flex-1">
                            <div class="flex justify-between items-center">
                                <div>
                                    <div class="font-medium text-gray-900">Withdrawal Wallet</div>
                                    <div class="text-sm text-gray-500">Use funds from withdrawal wallet</div>
                                </div>
                                <div class="text-right">
                                    <div class="text-lg font-bold text-blue-600">$<?php echo number_format($current_withdrawal_balance, 4); ?></div>
                                    <div class="text-xs text-gray-500">Available</div>
                                </div>
                            </div>
                        </div>
                    </label>
                </div>

                <div class="mt-6 flex space-x-3">
                    <button type="button" id="cancelPurchase" class="flex-1 px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors">
                        Cancel
                    </button>
                    <button type="button" id="confirmPurchaseBtn" class="flex-1 px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 transition-colors">
                        Purchase Plan
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const purchaseButtons = document.querySelectorAll('.purchase-btn');
            const feedbackMessage = document.getElementById('feedback-message');
            const depositBalance = <?php echo $current_deposit_balance; ?>;
            const withdrawalBalance = <?php echo $current_withdrawal_balance; ?>;

            let selectedPackageId = null;
            let selectedPackageAmount = 0;

            purchaseButtons.forEach(button => {
                button.addEventListener('click', function() {
                    selectedPackageId = this.dataset.packageId;
                    selectedPackageAmount = parseFloat(this.dataset.packageAmount);
                    const packageName = this.dataset.packageName;

                    // Check if either wallet has sufficient balance
                    if (depositBalance < selectedPackageAmount && withdrawalBalance < selectedPackageAmount) {
                        showFeedback('Insufficient balance in both wallets to purchase this plan.', 'error');
                        return;
                    }

                    // Show wallet selection modal
                    showWalletModal(packageName, selectedPackageAmount);
                });
            });

            // Add event listeners for modal buttons
            document.getElementById('closeModalBtn').addEventListener('click', closeWalletModal);
            document.getElementById('cancelPurchase').addEventListener('click', closeWalletModal);
            document.getElementById('confirmPurchaseBtn').addEventListener('click', confirmPurchase);

            function purchasePackage(packageId, walletType) {
                fetch('/netvis/ui/api/purchase_package.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            package_id: packageId,
                            wallet_type: walletType
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            showFeedback('Package purchased successfully! The page will now reload.', 'success');
                            setTimeout(() => window.location.reload(), 2000);
                        } else {
                            showFeedback(data.error || 'An unknown error occurred.', 'error');
                        }
                    })
                    .catch(error => {
                        console.error('Error purchasing package:', error);
                        showFeedback('A network error occurred. Please try again.', 'error');
                    });
            }

            function showWalletModal(packageName, packageAmount) {
                document.getElementById('selectedPackageName').textContent = packageName;
                document.getElementById('selectedPackageAmount').textContent = packageAmount.toFixed(2);

                // Update wallet option states based on balance
                const depositOption = document.getElementById('depositWalletOption');
                const withdrawalOption = document.getElementById('withdrawalWalletOption');
                const depositRadio = depositOption.querySelector('input[type="radio"]');
                const withdrawalRadio = withdrawalOption.querySelector('input[type="radio"]');

                // Disable options with insufficient balance
                if (depositBalance < packageAmount) {
                    depositOption.classList.add('opacity-50', 'cursor-not-allowed');
                    depositRadio.disabled = true;
                } else {
                    depositOption.classList.remove('opacity-50', 'cursor-not-allowed');
                    depositRadio.disabled = false;
                }

                if (withdrawalBalance < packageAmount) {
                    withdrawalOption.classList.add('opacity-50', 'cursor-not-allowed');
                    withdrawalRadio.disabled = true;
                } else {
                    withdrawalOption.classList.remove('opacity-50', 'cursor-not-allowed');
                    withdrawalRadio.disabled = false;
                }

                // Select the first available option
                if (!depositRadio.disabled) {
                    depositRadio.checked = true;
                } else if (!withdrawalRadio.disabled) {
                    withdrawalRadio.checked = true;
                }

                document.getElementById('walletModal').classList.remove('hidden');
            }

            function closeWalletModal() {
                document.getElementById('walletModal').classList.add('hidden');
            }

            function confirmPurchase() {
                const selectedWallet = document.querySelector('input[name="wallet_type"]:checked');
                if (!selectedWallet) {
                    showFeedback('Please select a wallet to proceed.', 'error');
                    return;
                }

                const walletType = selectedWallet.value;
                const selectedBalance = walletType === 'deposit' ? depositBalance : withdrawalBalance;

                if (selectedBalance < selectedPackageAmount) {
                    showFeedback(`Insufficient balance in ${walletType} wallet.`, 'error');
                    return;
                }

                closeWalletModal();
                purchasePackage(selectedPackageId, walletType);
            }

            function showFeedback(message, type) {
                feedbackMessage.textContent = message;
                if (type === 'success') {
                    feedbackMessage.className = 'p-4 mb-4 text-sm text-green-800 rounded-lg bg-green-50';
                } else {
                    feedbackMessage.className = 'p-4 mb-4 text-sm text-red-800 rounded-lg bg-red-50';
                }
                feedbackMessage.classList.remove('hidden');
            }
        });
    </script>
</body>

</html>