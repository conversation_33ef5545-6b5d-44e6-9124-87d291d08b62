<?php
session_start();

// Check if user is logged in as admin
// if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
//     header('Location: login.php');
//     exit();
// }

// Include database connection
include '../../ui/dbcon.php';

// Set page title
$page_title = 'Dashboard';

// Fetch dashboard statistics
try {
    // Users Statistics
    $total_users_query = "SELECT COUNT(*) as total FROM users WHERE is_admin = 0";
    $total_users_result = $conn->query($total_users_query);
    $total_users = $total_users_result->fetch_assoc()['total'];

    $active_users_query = "SELECT COUNT(*) as active FROM users WHERE is_admin = 0 AND plan_status = 'active'";
    $active_users_result = $conn->query($active_users_query);
    $active_users = $active_users_result->fetch_assoc()['active'];

    $new_users_today_query = "SELECT COUNT(*) as new_today FROM users WHERE is_admin = 0 AND DATE(created_at) = CURDATE()";
    $new_users_today_result = $conn->query($new_users_today_query);
    $new_users_today = $new_users_today_result->fetch_assoc()['new_today'];

    // Financial Statistics
    $total_deposits_query = "SELECT COALESCE(SUM(amount), 0) as total FROM deposit_requests WHERE status = 'approved'";
    $total_deposits_result = $conn->query($total_deposits_query);
    $total_deposits = $total_deposits_result->fetch_assoc()['total'];

    $total_withdrawals_query = "SELECT COALESCE(SUM(amount), 0) as total FROM withdrawal_requests WHERE status = 'approved'";
    $total_withdrawals_result = $conn->query($total_withdrawals_query);
    $total_withdrawals = $total_withdrawals_result->fetch_assoc()['total'];

    $pending_deposits_query = "SELECT COUNT(*) as pending FROM deposit_requests WHERE status = 'pending'";
    $pending_deposits_result = $conn->query($pending_deposits_query);
    $pending_deposits = $pending_deposits_result->fetch_assoc()['pending'];

    $pending_withdrawals_query = "SELECT COUNT(*) as pending FROM withdrawal_requests WHERE status = 'pending'";
    $pending_withdrawals_result = $conn->query($pending_withdrawals_query);
    $pending_withdrawals = $pending_withdrawals_result->fetch_assoc()['pending'];

    // Package Statistics
    $total_packages_query = "SELECT COUNT(*) as total FROM packages WHERE status = 'active'";
    $total_packages_result = $conn->query($total_packages_query);
    $total_packages = $total_packages_result->fetch_assoc()['total'];

    $total_investments_query = "SELECT COALESCE(SUM(package_amount), 0) as total FROM package_history";
    $total_investments_result = $conn->query($total_investments_query);
    $total_investments = $total_investments_result->fetch_assoc()['total'];

    // Staking Statistics
    $active_staking_query = "SELECT COUNT(*) as active, COALESCE(SUM(amount_usd), 0) as total_amount FROM staking_records WHERE status = 'active'";
    $active_staking_result = $conn->query($active_staking_query);
    $staking_data = $active_staking_result->fetch_assoc();
    $active_staking_count = $staking_data['active'];
    $total_staking_amount = $staking_data['total_amount'];

    // Recent Activities
    $recent_users_query = "SELECT user_id, full_name, created_at FROM users WHERE is_admin = 0 ORDER BY created_at DESC LIMIT 5";
    $recent_users_result = $conn->query($recent_users_query);

    $recent_deposits_query = "SELECT dr.*, u.full_name FROM deposit_requests dr JOIN users u ON dr.user_id = u.user_id ORDER BY dr.created_at DESC LIMIT 5";
    $recent_deposits_result = $conn->query($recent_deposits_query);

    $recent_withdrawals_query = "SELECT wr.*, u.full_name FROM withdrawal_requests wr JOIN users u ON wr.user_id = u.user_id ORDER BY wr.created_at DESC LIMIT 5";
    $recent_withdrawals_result = $conn->query($recent_withdrawals_query);

    // Monthly Growth Data for Charts
    $monthly_users_query = "SELECT DATE_FORMAT(created_at, '%Y-%m') as month, COUNT(*) as count FROM users WHERE is_admin = 0 AND created_at >= DATE_SUB(NOW(), INTERVAL 12 MONTH) GROUP BY DATE_FORMAT(created_at, '%Y-%m') ORDER BY month";
    $monthly_users_result = $conn->query($monthly_users_query);
    $monthly_users_data = [];
    while ($row = $monthly_users_result->fetch_assoc()) {
        $monthly_users_data[] = $row;
    }

    $monthly_deposits_query = "SELECT DATE_FORMAT(created_at, '%Y-%m') as month, COALESCE(SUM(amount), 0) as total FROM deposit_requests WHERE status = 'approved' AND created_at >= DATE_SUB(NOW(), INTERVAL 12 MONTH) GROUP BY DATE_FORMAT(created_at, '%Y-%m') ORDER BY month";
    $monthly_deposits_result = $conn->query($monthly_deposits_query);
    $monthly_deposits_data = [];
    while ($row = $monthly_deposits_result->fetch_assoc()) {
        $monthly_deposits_data[] = $row;
    }
} catch (Exception $e) {
    error_log("Dashboard Error: " . $e->getMessage());
    // Set default values if queries fail
    $total_users = $active_users = $new_users_today = 0;
    $total_deposits = $total_withdrawals = 0;
    $pending_deposits = $pending_withdrawals = 0;
    $total_packages = $total_investments = 0;
    $active_staking_count = $total_staking_amount = 0;
    $monthly_users_data = $monthly_deposits_data = [];
}

// Start output buffering for page content
ob_start();
?>

<!-- Dashboard Content -->
<div class="space-y-6 fade-in">
    <!-- Page Header -->
    <div class="flex flex-col lg:flex-row justify-between items-start lg:items-center space-y-4 lg:space-y-0">
        <div>
            <h1 class="text-3xl font-bold text-gray-900">Dashboard</h1>
            <p class="text-gray-600 mt-1">Welcome back! Here's what's happening with your platform.</p>
        </div>
        <div class="flex items-center space-x-3">
            <div class="flex items-center space-x-2 bg-green-50 px-4 py-2 rounded-lg border border-green-200">
                <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                <span class="text-sm font-medium text-green-700">System Online</span>
            </div>
            <div class="text-sm text-gray-500">
                Last updated: <?php echo date('M d, Y H:i'); ?>
            </div>
        </div>
    </div>

    <!-- Key Metrics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <!-- Total Users -->
        <div class="admin-card p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600">Total Users</p>
                    <p class="text-3xl font-bold text-gray-900"><?php echo number_format($total_users); ?></p>
                    <p class="text-sm text-green-600 mt-1">
                        <i class="fas fa-arrow-up mr-1"></i>
                        <?php echo $new_users_today; ?> new today
                    </p>
                </div>
                <div class="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">
                    <i class="fas fa-users text-blue-600 text-xl"></i>
                </div>
            </div>
        </div>

        <!-- Active Users -->
        <div class="admin-card p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600">Active Users</p>
                    <p class="text-3xl font-bold text-gray-900"><?php echo number_format($active_users); ?></p>
                    <p class="text-sm text-gray-500 mt-1">
                        <?php echo $total_users > 0 ? round(($active_users / $total_users) * 100, 1) : 0; ?>% of total
                    </p>
                </div>
                <div class="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center">
                    <i class="fas fa-user-check text-green-600 text-xl"></i>
                </div>
            </div>
        </div>

        <!-- Total Deposits -->
        <div class="admin-card p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600">Total Deposits</p>
                    <p class="text-3xl font-bold text-gray-900">$<?php echo number_format($total_deposits, 2); ?></p>
                    <p class="text-sm text-orange-600 mt-1">
                        <i class="fas fa-clock mr-1"></i>
                        <?php echo $pending_deposits; ?> pending
                    </p>
                </div>
                <div class="w-12 h-12 bg-emerald-100 rounded-xl flex items-center justify-center">
                    <i class="fas fa-arrow-down text-emerald-600 text-xl"></i>
                </div>
            </div>
        </div>

        <!-- Total Withdrawals -->
        <div class="admin-card p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600">Total Withdrawals</p>
                    <p class="text-3xl font-bold text-gray-900">$<?php echo number_format($total_withdrawals, 2); ?></p>
                    <p class="text-sm text-red-600 mt-1">
                        <i class="fas fa-clock mr-1"></i>
                        <?php echo $pending_withdrawals; ?> pending
                    </p>
                </div>
                <div class="w-12 h-12 bg-red-100 rounded-xl flex items-center justify-center">
                    <i class="fas fa-arrow-up text-red-600 text-xl"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Secondary Metrics -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <!-- Investment Statistics -->
        <div class="admin-card p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-900">Investment Stats</h3>
                <i class="fas fa-chart-pie text-purple-600"></i>
            </div>
            <div class="space-y-4">
                <div class="flex justify-between items-center">
                    <span class="text-sm text-gray-600">Active Packages</span>
                    <span class="font-semibold text-gray-900"><?php echo number_format($total_packages); ?></span>
                </div>
                <div class="flex justify-between items-center">
                    <span class="text-sm text-gray-600">Total Investments</span>
                    <span class="font-semibold text-gray-900">$<?php echo number_format($total_investments, 2); ?></span>
                </div>
                <div class="flex justify-between items-center">
                    <span class="text-sm text-gray-600">Avg. Investment</span>
                    <span class="font-semibold text-gray-900">
                        $<?php echo $total_users > 0 ? number_format($total_investments / $total_users, 2) : '0.00'; ?>
                    </span>
                </div>
            </div>
        </div>

        <!-- Staking Statistics -->
        <div class="admin-card p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-900">Staking Stats</h3>
                <i class="fas fa-coins text-yellow-600"></i>
            </div>
            <div class="space-y-4">
                <div class="flex justify-between items-center">
                    <span class="text-sm text-gray-600">Active Stakes</span>
                    <span class="font-semibold text-gray-900"><?php echo number_format($active_staking_count); ?></span>
                </div>
                <div class="flex justify-between items-center">
                    <span class="text-sm text-gray-600">Total Staked</span>
                    <span class="font-semibold text-gray-900">$<?php echo number_format($total_staking_amount, 2); ?></span>
                </div>
                <div class="flex justify-between items-center">
                    <span class="text-sm text-gray-600">Avg. Stake</span>
                    <span class="font-semibold text-gray-900">
                        $<?php echo $active_staking_count > 0 ? number_format($total_staking_amount / $active_staking_count, 2) : '0.00'; ?>
                    </span>
                </div>
            </div>
        </div>

        <!-- Platform Health -->
        <div class="admin-card p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-900">Platform Health</h3>
                <i class="fas fa-heartbeat text-pink-600"></i>
            </div>
            <div class="space-y-4">
                <div class="flex justify-between items-center">
                    <span class="text-sm text-gray-600">System Status</span>
                    <span class="badge badge-success">Online</span>
                </div>
                <div class="flex justify-between items-center">
                    <span class="text-sm text-gray-600">Database</span>
                    <span class="badge badge-success">Connected</span>
                </div>
                <div class="flex justify-between items-center">
                    <span class="text-sm text-gray-600">Last Backup</span>
                    <span class="text-sm text-gray-500">2 hours ago</span>
                </div>
            </div>
        </div>
    </div>

    <?php
    // Get the page content
    $page_content = ob_get_clean();

    // Include the layout
    include 'includes/layout.php';
    ?>