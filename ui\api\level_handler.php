<?php
session_start();
header('Content-Type: application/json');

if (!isset($_SESSION['user_id']) || !$_SESSION['is_admin']) {
    http_response_code(403);
    echo json_encode(['error' => 'Unauthorized']);
    exit();
}

include '../dbcon.php';

$method = $_SERVER['REQUEST_METHOD'];

if ($method === 'GET') {
    $sql = 'SELECT id, level_number, distribution_percentage FROM levels WHERE level_number IN (1, 2, 3) ORDER BY level_number';
    $result = $conn->query($sql);
    $levels = [];
    while ($row = $result->fetch_assoc()) {
        $levels[] = $row;
    }
    echo json_encode($levels);
} elseif ($method === 'POST') {
    $input = json_decode(file_get_contents('php://input'), true);
    $levels_data = $input['levels'] ?? null;

    if (is_array($levels_data)) {
        $conn->begin_transaction();
        try {
            $stmt = $conn->prepare('UPDATE levels SET distribution_percentage = ? WHERE level_number = ?');
            foreach ($levels_data as $level) {
                if (isset($level['percentage']) && isset($level['number'])) {
                    $stmt->bind_param('di', $level['percentage'], $level['number']);
                    $stmt->execute();
                }
            }
            $stmt->close();
            $conn->commit();
            echo json_encode(['success' => true, 'message' => 'Levels updated successfully.']);
        } catch (Exception $e) {
            $conn->rollback();
            http_response_code(500);
            echo json_encode(['error' => 'Failed to update levels: ' . $e->getMessage()]);
        }
    } else {
        http_response_code(400);
        echo json_encode(['error' => 'Invalid data provided.']);
    }
} else {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed.']);
}

$conn->close();
