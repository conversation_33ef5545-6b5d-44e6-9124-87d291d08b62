<?php
include 'dbcon.php';

echo "<h2>🔍 Deposit & Withdrawal Tables Structure Check</h2>";

// Check deposit_requests table structure
echo "<h3>📋 Deposit Requests Table:</h3>";
$desc_query = "DESCRIBE deposit_requests";
$desc_result = $conn->query($desc_query);

if ($desc_result) {
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>";
    echo "<tr style='background: #f3f4f6;'><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    
    $deposit_columns = [];
    while ($row = $desc_result->fetch_assoc()) {
        $deposit_columns[] = $row['Field'];
        echo "<tr>";
        echo "<td><strong>" . $row['Field'] . "</strong></td>";
        echo "<td>" . $row['Type'] . "</td>";
        echo "<td>" . $row['Null'] . "</td>";
        echo "<td>" . $row['Key'] . "</td>";
        echo "<td>" . ($row['Default'] ?? 'NULL') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Find date columns
    $date_columns = array_filter($deposit_columns, function($col) {
        return stripos($col, 'date') !== false || stripos($col, 'time') !== false || stripos($col, 'created') !== false || stripos($col, 'updated') !== false;
    });
    
    echo "<h4>📅 Date/Time Columns in deposit_requests:</h4>";
    if (!empty($date_columns)) {
        echo "<ul>";
        foreach ($date_columns as $col) {
            echo "<li><strong>$col</strong></li>";
        }
        echo "</ul>";
    } else {
        echo "<p>No obvious date/time columns found.</p>";
    }
}

// Check withdrawal_requests table structure
echo "<h3>📋 Withdrawal Requests Table:</h3>";
$desc_query = "DESCRIBE withdrawal_requests";
$desc_result = $conn->query($desc_query);

if ($desc_result) {
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>";
    echo "<tr style='background: #f3f4f6;'><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    
    $withdrawal_columns = [];
    while ($row = $desc_result->fetch_assoc()) {
        $withdrawal_columns[] = $row['Field'];
        echo "<tr>";
        echo "<td><strong>" . $row['Field'] . "</strong></td>";
        echo "<td>" . $row['Type'] . "</td>";
        echo "<td>" . $row['Null'] . "</td>";
        echo "<td>" . $row['Key'] . "</td>";
        echo "<td>" . ($row['Default'] ?? 'NULL') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Find date columns
    $date_columns = array_filter($withdrawal_columns, function($col) {
        return stripos($col, 'date') !== false || stripos($col, 'time') !== false || stripos($col, 'created') !== false || stripos($col, 'updated') !== false;
    });
    
    echo "<h4>📅 Date/Time Columns in withdrawal_requests:</h4>";
    if (!empty($date_columns)) {
        echo "<ul>";
        foreach ($date_columns as $col) {
            echo "<li><strong>$col</strong></li>";
        }
        echo "</ul>";
    } else {
        echo "<p>No obvious date/time columns found.</p>";
    }
}

// Show sample data from both tables
echo "<h3>📊 Sample Data:</h3>";

echo "<h4>💰 Sample Deposit Requests (First 3 rows):</h4>";
$sample_deposits = $conn->query("SELECT * FROM deposit_requests LIMIT 3");
if ($sample_deposits && $sample_deposits->num_rows > 0) {
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%; font-size: 0.8em;'>";
    echo "<tr style='background: #f3f4f6;'>";
    foreach ($deposit_columns as $col) {
        echo "<th>$col</th>";
    }
    echo "</tr>";
    
    while ($row = $sample_deposits->fetch_assoc()) {
        echo "<tr>";
        foreach ($deposit_columns as $col) {
            $value = $row[$col] ?? '';
            if (strlen($value) > 15) {
                $value = substr($value, 0, 15) . '...';
            }
            echo "<td>" . htmlspecialchars($value) . "</td>";
        }
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>No deposit data found.</p>";
}

echo "<h4>💸 Sample Withdrawal Requests (First 3 rows):</h4>";
$sample_withdrawals = $conn->query("SELECT * FROM withdrawal_requests LIMIT 3");
if ($sample_withdrawals && $sample_withdrawals->num_rows > 0) {
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%; font-size: 0.8em;'>";
    echo "<tr style='background: #f3f4f6;'>";
    foreach ($withdrawal_columns as $col) {
        echo "<th>$col</th>";
    }
    echo "</tr>";
    
    while ($row = $sample_withdrawals->fetch_assoc()) {
        echo "<tr>";
        foreach ($withdrawal_columns as $col) {
            $value = $row[$col] ?? '';
            if (strlen($value) > 15) {
                $value = substr($value, 0, 15) . '...';
            }
            echo "<td>" . htmlspecialchars($value) . "</td>";
        }
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>No withdrawal data found.</p>";
}

// Suggest corrected queries
echo "<div style='background: #dcfce7; border: 2px solid #16a34a; border-radius: 12px; padding: 20px; margin: 20px 0;'>";
echo "<h3 style='color: #15803d; margin-top: 0;'>🔧 Suggested Query Fixes</h3>";

// Determine the correct date column for deposits
$deposit_date_column = 'id'; // fallback to id for ordering
if (in_array('request_date', $deposit_columns)) {
    $deposit_date_column = 'request_date';
} elseif (in_array('created_date', $deposit_columns)) {
    $deposit_date_column = 'created_date';
} elseif (in_array('date', $deposit_columns)) {
    $deposit_date_column = 'date';
} elseif (in_array('timestamp', $deposit_columns)) {
    $deposit_date_column = 'timestamp';
}

// Determine the correct date column for withdrawals
$withdrawal_date_column = 'id'; // fallback to id for ordering
if (in_array('request_date', $withdrawal_columns)) {
    $withdrawal_date_column = 'request_date';
} elseif (in_array('created_date', $withdrawal_columns)) {
    $withdrawal_date_column = 'created_date';
} elseif (in_array('date', $withdrawal_columns)) {
    $withdrawal_date_column = 'date';
} elseif (in_array('timestamp', $withdrawal_columns)) {
    $withdrawal_date_column = 'timestamp';
}

echo "<h4 style='color: #166534;'>📝 Corrected Queries:</h4>";

echo "<div style='background: #f0fdf4; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
echo "<h5 style='color: #166534; margin-top: 0;'>Recent Deposits Query:</h5>";
echo "<code style='display: block; background: white; padding: 10px; border-radius: 4px; color: #166534;'>";
echo "SELECT dr.*, u.name FROM deposit_requests dr JOIN users u ON dr.user_id = u.id ORDER BY dr.$deposit_date_column DESC LIMIT 5";
echo "</code>";
echo "</div>";

echo "<div style='background: #f0fdf4; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
echo "<h5 style='color: #166534; margin-top: 0;'>Recent Withdrawals Query:</h5>";
echo "<code style='display: block; background: white; padding: 10px; border-radius: 4px; color: #166534;'>";
echo "SELECT wr.*, u.name FROM withdrawal_requests wr JOIN users u ON wr.user_id = u.id ORDER BY wr.$withdrawal_date_column DESC LIMIT 5";
echo "</code>";
echo "</div>";

echo "</div>";

$conn->close();
?>

<style>
body { 
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; 
    margin: 20px; 
    background: #f8fafc;
    line-height: 1.6;
}
h2, h3, h4, h5 { color: #1f2937; }
table { 
    font-size: 0.9em; 
    max-width: 100%; 
    overflow-x: auto;
}
th, td { 
    padding: 6px 10px; 
    text-align: left; 
    border: 1px solid #e5e7eb;
}
th { 
    background: #f9fafb; 
    font-weight: 600;
}
code { 
    background: #f1f5f9; 
    padding: 2px 6px; 
    border-radius: 4px; 
    font-family: monospace;
    font-size: 0.9em;
}
</style>
