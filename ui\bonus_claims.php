<?php
session_start();

if (!isset($_SESSION['user_id']) || !$_SESSION['is_admin']) {
    header('Location: login.html');
    exit();
}

include 'dbcon.php';

// Fetch all bonus claims with user and bonus details
$sql = '
    SELECT 
        bc.id, 
        u.name AS user_name, 
        u.user_id, 
        b.bonus_name, 
        b.bonus_fund AS amount, 
        bc.status, 
        bc.claimed_at
    FROM bonus_claims bc
    JOIN users u ON bc.user_id = u.id
    JOIN bonuses b ON bc.bonus_id = b.id
    ORDER BY bc.claimed_at DESC
';
$result = $conn->query($sql);

// Page configuration
$page_title = "Bonus Claims Management";
$additional_css = '
<style>
    /* Enhanced table styling */
    .admin-table {
        width: 100%;
        border-collapse: separate;
        border-spacing: 0;
        background: white;
    }

    .admin-table thead {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    .admin-table thead th {
        color: white;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.05em;
        padding: 1rem 1.5rem;
        font-size: 0.75rem;
        border: none;
        position: relative;
        text-align: left;
    }

    .admin-table thead th:last-child {
        text-align: center;
    }

    .admin-table tbody tr {
        transition: all 0.2s ease;
        border-bottom: 1px solid #e2e8f0;
    }

    .admin-table tbody tr:hover {
        background-color: #f8fafc;
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    .admin-table tbody tr:last-child {
        border-bottom: none;
    }

    .admin-table tbody td {
        padding: 1rem 1.5rem;
        vertical-align: middle;
        border: none;
        background-color: white;
    }

    .admin-table tbody tr:hover td {
        background-color: #f8fafc;
    }

    /* Status badges */
    .status-badge {
        display: inline-flex;
        align-items: center;
        padding: 0.25rem 0.75rem;
        border-radius: 9999px;
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.05em;
    }

    .status-pending {
        background-color: #fef3c7;
        color: #92400e;
        border: 1px solid #fbbf24;
    }

    .status-approved {
        background-color: #d1fae5;
        color: #065f46;
        border: 1px solid #10b981;
    }

    .status-rejected {
        background-color: #fee2e2;
        color: #991b1b;
        border: 1px solid #ef4444;
    }

    /* Action buttons */
    .action-btn {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 2rem;
        height: 2rem;
        border-radius: 0.375rem;
        font-weight: 500;
        font-size: 0.875rem;
        transition: all 0.2s ease;
        border: none;
        cursor: pointer;
    }

    .action-btn-approve {
        background: rgba(34, 197, 94, 0.1);
        color: #22c55e;
        border: 1px solid rgba(34, 197, 94, 0.2);
    }

    .action-btn-approve:hover {
        background: rgba(34, 197, 94, 0.2);
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(34, 197, 94, 0.2);
    }

    .action-btn-reject {
        background: rgba(239, 68, 68, 0.1);
        color: #ef4444;
        border: 1px solid rgba(239, 68, 68, 0.2);
    }

    .action-btn-reject:hover {
        background: rgba(239, 68, 68, 0.2);
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(239, 68, 68, 0.2);
    }
</style>
';

// Start output buffering to capture the page content
ob_start();

?>

<!-- Page Header -->
<div class="flex flex-col lg:flex-row justify-between items-start lg:items-center space-y-4 lg:space-y-0 mb-6">
    <div>
        <h1 class="text-3xl font-bold text-gray-900">Bonus Claims Management</h1>
        <p class="text-gray-600 mt-1">Review and manage user bonus claim requests</p>
    </div>
    <div class="flex items-center space-x-2 bg-blue-50 px-4 py-2 rounded-lg border border-blue-200">
        <i class="fas fa-gift text-blue-600"></i>
        <span class="text-sm font-medium text-blue-700">Bonus Claims</span>
    </div>
</div>
<!-- Statistics Section -->
<?php
// Calculate bonus claim statistics
$stats_sql = "SELECT
    COUNT(*) as total_claims,
    SUM(b.bonus_fund) as total_amount,
    COUNT(CASE WHEN bc.status = 'pending' THEN 1 END) as pending_count,
    COUNT(CASE WHEN bc.status = 'approved' THEN 1 END) as approved_count,
    COUNT(CASE WHEN bc.status = 'rejected' THEN 1 END) as rejected_count
    FROM bonus_claims bc
    JOIN bonuses b ON bc.bonus_id = b.id";

$stats_result = $conn->query($stats_sql);
$stats = $stats_result->fetch_assoc();
?>

<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-6">
    <div class="admin-card p-6">
        <div class="flex items-center">
            <div class="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">
                <i class="fas fa-gift text-blue-600 text-xl"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">Total Claims</p>
                <p class="text-2xl font-bold text-gray-900"><?php echo number_format($stats['total_claims']); ?></p>
            </div>
        </div>
    </div>

    <div class="admin-card p-6">
        <div class="flex items-center">
            <div class="w-12 h-12 bg-yellow-100 rounded-xl flex items-center justify-center">
                <i class="fas fa-clock text-yellow-600 text-xl"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">Pending</p>
                <p class="text-2xl font-bold text-gray-900"><?php echo number_format($stats['pending_count']); ?></p>
            </div>
        </div>
    </div>

    <div class="admin-card p-6">
        <div class="flex items-center">
            <div class="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center">
                <i class="fas fa-check text-green-600 text-xl"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">Approved</p>
                <p class="text-2xl font-bold text-gray-900"><?php echo number_format($stats['approved_count']); ?></p>
            </div>
        </div>
    </div>

    <div class="admin-card p-6">
        <div class="flex items-center">
            <div class="w-12 h-12 bg-red-100 rounded-xl flex items-center justify-center">
                <i class="fas fa-times text-red-600 text-xl"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">Rejected</p>
                <p class="text-2xl font-bold text-gray-900"><?php echo number_format($stats['rejected_count']); ?></p>
            </div>
        </div>
    </div>

    <div class="admin-card p-6">
        <div class="flex items-center">
            <div class="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center">
                <i class="fas fa-dollar-sign text-purple-600 text-xl"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">Total Value</p>
                <p class="text-2xl font-bold text-gray-900">$<?php echo number_format($stats['total_amount'] ?? 0, 2); ?></p>
            </div>
        </div>
    </div>
</div>

<!-- Bonus Claims Table -->
<div class="admin-card p-0 overflow-hidden">
    <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center space-x-3">
            <div class="w-8 h-8 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-lg flex items-center justify-center">
                <i class="fas fa-gift text-white text-sm"></i>
            </div>
            <div>
                <h3 class="text-lg font-semibold text-gray-900">Bonus Claims</h3>
                <p class="text-sm text-gray-600">Review and manage user bonus claim requests</p>
            </div>
        </div>
    </div>

    <div class="overflow-x-auto">
        <table class="admin-table">
            <thead>
                <tr>
                    <th>User</th>
                    <th>Bonus</th>
                    <th>Reward Amount</th>
                    <th>Status</th>
                    <th>Claimed At</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                <?php if ($result->num_rows > 0): ?>
                    <?php while ($row = $result->fetch_assoc()): ?>
                        <tr>
                            <td>
                                <div class="flex items-center space-x-3">
                                    <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                        <i class="fas fa-user text-blue-600 text-xs"></i>
                                    </div>
                                    <div>
                                        <div class="text-sm font-medium text-gray-900"><?php echo htmlspecialchars($row['user_name']); ?></div>
                                        <div class="text-xs text-gray-500">ID: <?php echo htmlspecialchars($row['user_id']); ?></div>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div class="flex items-center space-x-2">
                                    <div class="w-6 h-6 bg-purple-100 rounded flex items-center justify-center">
                                        <i class="fas fa-gift text-purple-600 text-xs"></i>
                                    </div>
                                    <span class="text-sm font-medium text-gray-900">
                                        <?php echo htmlspecialchars($row['bonus_name']); ?>
                                    </span>
                                </div>
                            </td>
                            <td>
                                <div class="flex items-center justify-center space-x-2">
                                    <div class="w-6 h-6 bg-green-100 rounded flex items-center justify-center">
                                        <i class="fas fa-dollar-sign text-green-600 text-xs"></i>
                                    </div>
                                    <span class="text-lg font-bold text-green-600">
                                        $<?php echo number_format($row['amount'], 2); ?>
                                    </span>
                                </div>
                            </td>
                            <td>
                                <span class="status-badge status-<?php echo $row['status']; ?>">
                                    <?php echo ucfirst($row['status']); ?>
                                </span>
                            </td>
                            <td>
                                <div class="text-sm text-gray-900">
                                    <div class="font-medium"><?php echo date('M j, Y', strtotime($row['claimed_at'])); ?></div>
                                    <div class="text-xs text-gray-500"><?php echo date('g:i A', strtotime($row['claimed_at'])); ?></div>
                                </div>
                            </td>
                            <td>
                                <?php if ($row['status'] === 'pending'): ?>
                                    <div class="flex items-center justify-center space-x-2">
                                        <button onclick="updateClaim(<?php echo $row['id']; ?>, 'approved')"
                                            class="action-btn action-btn-approve" title="Approve Claim">
                                            <i class="fas fa-check"></i>
                                        </button>
                                        <button onclick="updateClaim(<?php echo $row['id']; ?>, 'rejected')"
                                            class="action-btn action-btn-reject" title="Reject Claim">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                <?php else: ?>
                                    <div class="flex items-center justify-center">
                                        <span class="text-xs text-gray-500">No actions</span>
                                    </div>
                                <?php endif; ?>
                            </td>
                        </tr>
                    <?php endwhile; ?>
                <?php else: ?>
                    <tr>
                        <td colspan="6" style="text-align: center; padding: 3rem;">
                            <div class="flex flex-col items-center">
                                <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                                    <i class="fas fa-gift text-gray-400 text-2xl"></i>
                                </div>
                                <h3 class="text-lg font-medium text-gray-900 mb-2">No Bonus Claims Found</h3>
                                <p class="text-sm text-gray-500 mb-4">No bonus claims have been submitted yet.</p>
                            </div>
                        </td>
                    </tr>
                <?php endif; ?>
            </tbody>
        </table>
    </div>
</div>

<?php
// Capture the page content
$page_content = ob_get_clean();

// Close database connection
$conn->close();

// Include the admin layout
include 'admin/includes/layout.php';
?>

<script>
    function updateClaim(claimId, newStatus) {
        if (!confirm(`Are you sure you want to ${newStatus} this claim?`)) return;

        fetch('api/update_bonus_claim.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `claim_id=${claimId}&status=${newStatus}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Claim updated successfully!');
                    location.reload();
                } else {
                    alert('Error: ' + (data.error || 'An unknown error occurred.'));
                }
            })
            .catch(error => {
                console.error('Error updating claim:', error);
                alert('An error occurred while updating the claim.');
            });
    }
</script>