<?php
session_start();

if (!isset($_SESSION['user_id'])) {
    header("Location: login.php");
    exit();
}

include 'dbcon.php';

$user_id = $_SESSION['user_id'];

// Get user's package history
$history_sql = "SELECT ph.*, 
                CASE 
                    WHEN ph.status = 'active' THEN DATEDIFF(NOW(), ph.purchased_at)
                    WHEN ph.duration_days IS NOT NULL THEN ph.duration_days
                    ELSE DATEDIFF(COALESCE(ph.expired_at, NOW()), ph.purchased_at)
                END as calculated_duration,
                CASE 
                    WHEN ph.status = 'active' THEN (ph.total_earnings_received / ph.earnings_limit) * 100
                    ELSE 100
                END as progress_percentage
                FROM package_history ph 
                WHERE ph.user_id = ? 
                ORDER BY ph.purchased_at DESC";
$stmt = $conn->prepare($history_sql);
$stmt->bind_param("i", $user_id);
$stmt->execute();
$history_result = $stmt->get_result();
$package_history = $history_result->fetch_all(MYSQLI_ASSOC);
$stmt->close();

// Get current user info
$user_sql = "SELECT name, user_id as public_id, package FROM users WHERE id = ?";
$stmt = $conn->prepare($user_sql);
$stmt->bind_param("i", $user_id);
$stmt->execute();
$user_result = $stmt->get_result();
$user_info = $user_result->fetch_assoc();
$stmt->close();

// Calculate summary statistics
$total_invested = 0;
$total_earned = 0;
$active_packages = 0;
$expired_packages = 0;

foreach ($package_history as $record) {
    $total_invested += $record['package_amount'];
    $total_earned += $record['total_earnings_received'];

    if ($record['status'] === 'active') {
        $active_packages++;
    } else {
        $expired_packages++;
    }
}

$conn->close();
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Package History - NetVis</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>

<body class="bg-gray-100">
    <?php include 'user_nav.php'; ?>

    <main class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div class="bg-white shadow-lg rounded-lg p-8">
            <div class="mb-6">
                <h2 class="text-3xl font-bold text-gray-800">Package History</h2>
                <p class="text-gray-600 mt-2">Complete history of your package purchases and their performance.</p>
            </div>

            <!-- Summary Statistics -->
            <div class="mb-8 grid grid-cols-1 md:grid-cols-4 gap-6">
                <div class="bg-blue-50 p-6 rounded-lg text-center">
                    <h3 class="text-lg font-medium text-blue-600 mb-2">Total Invested</h3>
                    <p class="text-3xl font-bold text-blue-800">$<?php echo number_format($total_invested, 2); ?></p>
                </div>
                <div class="bg-green-50 p-6 rounded-lg text-center">
                    <h3 class="text-lg font-medium text-green-600 mb-2">Total Earned</h3>
                    <p class="text-3xl font-bold text-green-800">$<?php echo number_format($total_earned, 2); ?></p>
                </div>
                <div class="bg-yellow-50 p-6 rounded-lg text-center">
                    <h3 class="text-lg font-medium text-yellow-600 mb-2">Active Packages</h3>
                    <p class="text-3xl font-bold text-yellow-800"><?php echo $active_packages; ?></p>
                </div>
                <div class="bg-red-50 p-6 rounded-lg text-center">
                    <h3 class="text-lg font-medium text-red-600 mb-2">Expired Packages</h3>
                    <p class="text-3xl font-bold text-red-800"><?php echo $expired_packages; ?></p>
                </div>
            </div>

            <!-- Package History Table -->
            <div class="mb-6">
                <h3 class="text-xl font-semibold text-gray-800 mb-4">Package Purchase History</h3>

                <?php if (empty($package_history)): ?>
                    <div class="bg-gray-50 p-8 rounded-lg text-center">
                        <div class="text-gray-400 mb-4">
                            <svg class="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                            </svg>
                        </div>
                        <h4 class="text-lg font-medium text-gray-600 mb-2">No Package History</h4>
                        <p class="text-gray-500 mb-4">You haven't purchased any packages yet.</p>
                        <a href="invest.php" class="inline-flex items-center px-4 py-2 bg-indigo-600 text-white text-sm font-medium rounded-md hover:bg-indigo-700">
                            Browse Packages
                        </a>
                    </div>
                <?php else: ?>
                    <div class="overflow-x-auto border border-gray-200 rounded-lg">
                        <table class="min-w-full bg-white">
                            <thead class="bg-gray-100">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">Package</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-600 uppercase tracking-wider">Amount</th>
                                    <th class="px-6 py-3 text-center text-xs font-medium text-gray-600 uppercase tracking-wider">Purchased</th>
                                    <th class="px-6 py-3 text-center text-xs font-medium text-gray-600 uppercase tracking-wider">Duration</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-600 uppercase tracking-wider">Earnings</th>
                                    <th class="px-6 py-3 text-center text-xs font-medium text-gray-600 uppercase tracking-wider">Progress</th>
                                    <th class="px-6 py-3 text-center text-xs font-medium text-gray-600 uppercase tracking-wider">Status</th>
                                </tr>
                            </thead>
                            <tbody class="divide-y divide-gray-200">
                                <?php foreach ($package_history as $record): ?>
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div>
                                                <div class="text-sm font-medium text-gray-900"><?php echo htmlspecialchars($record['package_name']); ?></div>
                                                <div class="text-sm text-gray-500"><?php echo $record['profit_percentage']; ?>% daily profit</div>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-right">
                                            <div class="text-sm font-medium text-gray-900">$<?php echo number_format($record['package_amount'], 2); ?></div>
                                            <div class="text-sm text-gray-500">Limit: $<?php echo number_format($record['earnings_limit'], 2); ?></div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-center text-sm text-gray-500">
                                            <?php echo date('M j, Y', strtotime($record['purchased_at'])); ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-center">
                                            <div class="text-sm font-medium text-gray-900"><?php echo $record['calculated_duration']; ?> days</div>
                                            <?php if ($record['expired_at']): ?>
                                                <div class="text-sm text-gray-500">Ended: <?php echo date('M j, Y', strtotime($record['expired_at'])); ?></div>
                                            <?php endif; ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-right">
                                            <div class="text-sm font-medium text-gray-900">$<?php echo number_format($record['total_earnings_received'], 2); ?></div>
                                            <?php
                                            $roi = $record['package_amount'] > 0 ? ($record['total_earnings_received'] / $record['package_amount']) * 100 : 0;
                                            ?>
                                            <div class="text-sm text-gray-500"><?php echo number_format($roi, 1); ?>% ROI</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-center">
                                            <div class="w-full bg-gray-200 rounded-full h-2 mb-1">
                                                <div class="<?php echo $record['status'] === 'expired' ? 'bg-red-500' : 'bg-green-500'; ?> h-2 rounded-full"
                                                    style="width: <?php echo min($record['progress_percentage'], 100); ?>%"></div>
                                            </div>
                                            <div class="text-xs text-gray-500"><?php echo number_format($record['progress_percentage'], 1); ?>%</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-center">
                                            <?php
                                            $status_classes = [
                                                'active' => 'bg-green-100 text-green-800',
                                                'expired' => 'bg-red-100 text-red-800',
                                                'upgraded' => 'bg-blue-100 text-blue-800'
                                            ];
                                            $status_class = $status_classes[$record['status']] ?? 'bg-gray-100 text-gray-800';
                                            ?>
                                            <span class="px-2 py-1 text-xs font-medium rounded-full <?php echo $status_class; ?>">
                                                <?php echo ucfirst($record['status']); ?>
                                            </span>
                                            <?php if ($record['expiry_reason']): ?>
                                                <div class="text-xs text-gray-400 mt-1">
                                                    <?php
                                                    $reason_text = [
                                                        'limit_reached' => 'Limit reached',
                                                        'upgraded' => 'Upgraded',
                                                        'manual' => 'Manual'
                                                    ];
                                                    echo $reason_text[$record['expiry_reason']] ?? $record['expiry_reason'];
                                                    ?>
                                                </div>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Performance Insights -->
            <?php if (!empty($package_history)): ?>
                <div class="bg-gray-50 p-6 rounded-lg">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">Performance Insights</h3>

                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                        <div>
                            <h4 class="font-medium text-gray-700">Overall ROI</h4>
                            <p class="text-lg font-bold text-gray-900">
                                <?php echo $total_invested > 0 ? number_format(($total_earned / $total_invested) * 100, 1) : 0; ?>%
                            </p>
                        </div>
                        <div>
                            <h4 class="font-medium text-gray-700">Average Package Size</h4>
                            <p class="text-lg font-bold text-gray-900">
                                $<?php echo count($package_history) > 0 ? number_format($total_invested / count($package_history), 2) : 0; ?>
                            </p>
                        </div>
                        <div>
                            <h4 class="font-medium text-gray-700">Total Packages</h4>
                            <p class="text-lg font-bold text-gray-900"><?php echo count($package_history); ?></p>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Quick Actions -->
            <div class="mt-6 flex space-x-4">
                <a href="invest.php" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
                    Browse Packages
                </a>
                <a href="wallet.php" class="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700">
                    View Wallet
                </a>
                <a href="dashboard.php" class="bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700">
                    Dashboard
                </a>
            </div>
        </div>
    </main>
</body>

</html>