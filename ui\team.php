<?php
session_start();

if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit();
}

include 'dbcon.php';

$current_user_id = $_SESSION['user_id'];

// Fetch all packages for the filter dropdown
$packages_result = $conn->query('SELECT id, package_name FROM packages ORDER BY package_name');
$packages = [];
if ($packages_result) {
    while ($pkg = $packages_result->fetch_assoc()) {
        $packages[] = $pkg;
    }
}

// Fetch all active users with their package names
$sql = '
    SELECT 
        u.id, u.name, u.user_id, u.sponser_id, u.reg_date, p.package_name as package_name
    FROM users u
    LEFT JOIN packages p ON u.package = p.package_name
    WHERE u.is_active = 1 AND u.deleted_at IS NULL
';
$result = $conn->query($sql);

$users_by_id = [];
if ($result) {
    while ($row = $result->fetch_assoc()) {
        $users_by_id[$row['id']] = $row;
        $users_by_id[$row['id']]['children'] = [];
    }
}

// Build the full hierarchy
foreach ($users_by_id as $id => &$user) {
    if ($user['sponser_id'] !== null && isset($users_by_id[$user['sponser_id']])) {
        $users_by_id[$user['sponser_id']]['children'][] = &$user;
    }
}
unset($user);

// Get the logged-in user's specific downline tree
$user_tree = [];
if (isset($users_by_id[$current_user_id])) {
    $user_tree = $users_by_id[$current_user_id]['children'];
}

// --- RENDER FUNCTIONS ---

function render_tree_view($nodes)
{
    if (empty($nodes))
        return;
    echo '<ul>';
    foreach ($nodes as $node) {
        $tooltip = htmlspecialchars($node['name'] . ' (ID: ' . ($node['user_id'] ?? 'N/A') . ')');
        echo '<li><div class="node-content" title="' . $tooltip . '"><svg class="w-8 h-8 mx-auto text-gray-500" fill="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" viewBox="0 0 24 24" stroke="currentColor"><path d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path></svg></div>';
        if (!empty($node['children'])) {
            render_tree_view($node['children']);
        }
        echo '</li>';
    }
    echo '</ul>';
}

// New function to render table rows for the list view
function render_list_view_rows($nodes, $level = 1)
{
    if (empty($nodes))
        return;
    foreach ($nodes as $node) {
        echo '<tr data-level="' . $level . '" data-package="' . htmlspecialchars($node['package_name'] ?? 'N/A') . '">';
        echo '<td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">' . $level . '</td>';
        echo '<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">' . htmlspecialchars($node['user_id'] ?? 'N/A') . '</td>';
        echo '<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">' . htmlspecialchars($node['name']) . '</td>';
        echo '<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">' . htmlspecialchars($node['package_name'] ?? 'N/A') . '</td>';
        echo '</tr>';
        if (!empty($node['children'])) {
            render_list_view_rows($node['children'], $level + 1);
        }
    }
}

function render_single_card_for_drilldown($node, $level)
{
    $has_children = !empty($node['children']);
    $package_name = htmlspecialchars($node['package_name'] ?? 'N/A');
    $user_id = htmlspecialchars($node['user_id'] ?? 'N/A');
    $name = htmlspecialchars($node['name']);
    $level_text = 'Level ' . $level;

    echo '<div class="w-full p-2 card-item-wrapper" data-level="' . $level . '" data-package="' . $package_name . '">';

    $data_attribute = $has_children ? 'data-target-view="user_' . $node['id'] . '"' : '';

    $card_classes = 'rounded-lg shadow-md p-4 flex items-center space-x-4 card-item';
    if ($has_children) {
        // Highlight cards with children and make them clickable
        $card_classes .= ' bg-indigo-50 cursor-pointer hover:bg-indigo-100';
    } else {
        $card_classes .= ' bg-white';
    }

    echo '<div class="' . $card_classes . '" ' . $data_attribute . '>';
    echo '<div class="flex-shrink-0"><svg class="w-10 h-10 text-gray-400" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path></svg></div>';
    echo '<div class="flex-grow"><div class="text-lg font-semibold text-gray-800">' . $name . '</div><div class="text-sm text-gray-500">ID: ' . $user_id . '</div></div>';
    echo '<div class="text-right flex-shrink-0"><div class="px-3 py-1 text-sm font-semibold text-indigo-600 bg-indigo-100 rounded-full">' . $level_text . '</div><div class="mt-1 text-xs text-gray-600">' . $package_name . '</div></div>';

    // Arrow is removed as per user request, color now indicates children.

    echo '</div></div>';
}

function render_drilldown_views($nodes, $parent_id, $users_by_id, $level = 1, $is_top_level = true)
{
    $view_id = 'user_' . $parent_id;

    echo '<div class="team-level-view' . ($is_top_level ? '' : ' hidden') . ' flex flex-wrap w-full" data-view-id="' . $view_id . '">';

    if (!$is_top_level) {
        $parent_node = $users_by_id[$parent_id];
        $parent_name = htmlspecialchars($parent_node['name']);
        $grandparent_id = $parent_node['sponser_id'];
        echo '<div class="w-full p-2"><button class="back-button mb-4 flex items-center text-sm font-semibold text-indigo-600 hover:text-indigo-800" data-target-view="user_' . $grandparent_id . '"><svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path></svg>Back</button><h2 class="text-xl font-bold text-gray-800">Team of ' . $parent_name . '</h2></div>';
    }

    foreach ($nodes as $node) {
        render_single_card_for_drilldown($node, $level);
    }

    echo '</div>';

    foreach ($nodes as $node) {
        if (!empty($node['children'])) {
            render_drilldown_views($node['children'], $node['id'], $users_by_id, $level + 1, false);
        }
    }
}

?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Team - Professional UI</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            font-family: 'Inter', sans-serif;
        }

        @import url('https://rsms.me/inter/inter.css');

        .tree-container {
            text-align: center;
            padding: 20px;
            overflow-x: auto;
            white-space: nowrap;
            background-color: #f9fafb;
            border-radius: 1rem;
        }

        .tree {
            display: inline-block
        }

        .tree ul {
            padding-top: 20px;
            position: relative;
            transition: all .5s
        }

        .tree li {
            float: left;
            text-align: center;
            list-style-type: none;
            position: relative;
            padding: 20px 5px 0;
            transition: all .5s
        }

        .tree li::after,
        .tree li::before {
            content: '';
            position: absolute;
            top: 0;
            right: 50%;
            border-top: 2px solid #d1d5db;
            width: 50%;
            height: 20px
        }

        .tree li::after {
            right: auto;
            left: 50%;
            border-left: 2px solid #d1d5db
        }

        .tree li:only-child::after,
        .tree li:only-child::before {
            display: none
        }

        .tree li:only-child {
            padding-top: 0
        }

        .tree li:first-child::before {
            border: 0 none
        }

        .tree li:last-child::after {
            border: 0 none
        }

        .tree li:last-child::before {
            border-right: 2px solid #d1d5db;
            border-radius: 0 5px 0 0
        }

        .tree li:first-child::after {
            border-radius: 5px 0 0 0
        }

        .tree ul ul::before {
            content: '';
            position: absolute;
            top: 0;
            left: 50%;
            border-left: 2px solid #d1d5db;
            width: 0;
            height: 20px
        }

        .tree li .node-content {
            border: 1px solid #e5e7eb;
            padding: 8px;
            color: #374151;
            background-color: #fff;
            display: inline-block;
            border-radius: 50%;
            transition: all .3s;
            min-width: 50px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, .05);
            cursor: pointer;
        }

        .tree li .node-content:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, .1);
            transform: scale(1.1);
            border-color: #a5b4fc;
        }

        .tab-button {
            font-weight: 600;
            color: #6b7280;
            border-bottom: 3px solid transparent;
            transition: all 0.3s;
        }

        .tab-button.active {
            color: #4f46e5;
            border-bottom-color: #4f46e5;
        }

        .card-item.expanded .expand-icon svg {
            transform: rotate(180deg);
        }

        .filter-button {
            background-color: #eef2ff;
            color: #4338ca;
            border: 1px solid #c7d2fe;
            font-weight: 600;
            transition: all 0.2s;
        }

        .filter-button.active,
        .filter-button:hover {
            background-color: #4f46e5;
            color: #fff;
        }
    </style>
</head>

<body class="bg-gray-50">
    <?php include 'user_nav.php'; ?>

    <main class="container mx-auto p-4 sm:p-6 lg:p-8">
        <div class="bg-white rounded-2xl shadow-lg p-6 sm:p-8">
            <h1 class="text-3xl font-extrabold text-gray-900 text-center">My Team Network</h1>
            <p class="text-center text-gray-500 mt-2 mb-8">Visualize your referral network with our interactive tools.</p>

            <div class="tabs flex justify-center border-b border-gray-200 mb-6">
                <button class="tab-button text-base py-3 px-6" data-view="tree">Tree View</button>
                <button class="tab-button text-base py-3 px-6" data-view="list">List View</button>
                <button class="tab-button text-base py-3 px-6" data-view="card">Card View</button>
            </div>

            <div id="filter-container" class="flex flex-col sm:flex-row justify-center items-center space-y-4 sm:space-y-0 sm:space-x-4 my-6 bg-gray-100 p-3 rounded-xl hidden">
                <div>
                    <label for="level-filter" class="text-sm font-medium text-gray-700 sr-only sm:not-sr-only">Level</label>
                    <select id="level-filter" class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md">
                        <option value="all">All Levels</option>
                        <option value="1">Level 1</option>
                        <option value="2">Level 2</option>
                        <option value="3">Level 3</option>
                    </select>
                </div>
                <div>
                    <label for="package-filter" class="text-sm font-medium text-gray-700 sr-only sm:not-sr-only">Package</label>
                    <select id="package-filter" class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md">
                        <option value="all">All Packages</option>
                        <?php foreach ($packages as $pkg): ?>
                            <option value="<?php echo htmlspecialchars($pkg['package_name']); ?>"><?php echo htmlspecialchars($pkg['package_name']); ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>
            </div>

            <div id="view-container">
                <div id="tree-view" class="view-content hidden">
                    <div class="tree-container">
                        <div class="tree"><?php render_tree_view($user_tree); ?></div>
                    </div>
                </div>

                <div id="list-view" class="view-content hidden">
                    <div class="overflow-x-auto rounded-lg border border-gray-200">
                        <table id="team-list-table" class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Level</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User ID</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Package</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <?php render_list_view_rows($user_tree); ?>
                            </tbody>
                        </table>
                    </div>
                </div>

                <div id="card-view" class="view-content hidden">
                    <?php render_drilldown_views($user_tree, $current_user_id, $users_by_id); ?>
                </div>
            </div>
        </div>
    </main>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const tabButtons = document.querySelectorAll('.tab-button');
            const views = ['tree', 'list', 'card'];
            const filterContainer = document.getElementById('filter-container');
            const levelFilter = document.getElementById('level-filter');
            const packageFilter = document.getElementById('package-filter');
            const cardViewContainer = document.getElementById('card-view');

            function applyFilters() {
                const selectedLevel = levelFilter.value;
                const selectedPackage = packageFilter.value;
                const activeView = localStorage.getItem('teamView') || 'tree';

                if (activeView === 'list') {
                    document.querySelectorAll('#team-list-table tbody tr').forEach(row => {
                        const levelMatch = (selectedLevel === 'all') || (row.dataset.level === selectedLevel);
                        const packageMatch = (selectedPackage === 'all') || (row.dataset.package === selectedPackage);
                        row.style.display = (levelMatch && packageMatch) ? '' : 'none';
                    });
                } else if (activeView === 'card') {
                    const visibleTeamView = cardViewContainer.querySelector('.team-level-view:not(.hidden)');
                    if (visibleTeamView) {
                        visibleTeamView.querySelectorAll('.card-item-wrapper').forEach(wrapper => {
                            const levelMatch = (selectedLevel === 'all') || (wrapper.dataset.level === selectedLevel);
                            const packageMatch = (selectedPackage === 'all') || (wrapper.dataset.package === selectedPackage);
                            wrapper.style.display = (levelMatch && packageMatch) ? '' : 'none';
                        });
                    }
                }
            }

            function switchDrilldownView(targetViewId) {
                cardViewContainer.querySelectorAll('.team-level-view').forEach(v => v.classList.add('hidden'));
                const targetView = cardViewContainer.querySelector(`.team-level-view[data-view-id='${targetViewId}']`);
                if (targetView) {
                    targetView.classList.remove('hidden');
                    applyFilters(); // Re-apply filters to the new view
                }
            }

            function switchTabView(viewName) {
                views.forEach(v => document.getElementById(v + '-view').classList.add('hidden'));
                document.getElementById(viewName + '-view').classList.remove('hidden');
                tabButtons.forEach(b => b.classList.toggle('active', b.dataset.view === viewName));
                localStorage.setItem('teamView', viewName);

                if (viewName === 'list' || viewName === 'card') {
                    filterContainer.classList.remove('hidden');
                    applyFilters();
                } else {
                    filterContainer.classList.add('hidden');
                }
            }

            // --- Event Listeners ---
            levelFilter.addEventListener('change', applyFilters);
            packageFilter.addEventListener('change', applyFilters);
            tabButtons.forEach(b => b.addEventListener('click', () => switchTabView(b.dataset.view)));

            // Delegated event listener for drill-down card view
            cardViewContainer.addEventListener('click', (e) => {
                const card = e.target.closest('.card-item[data-target-view]');
                const backButton = e.target.closest('.back-button[data-target-view]');

                if (card) {
                    switchDrilldownView(card.dataset.targetView);
                } else if (backButton) {
                    switchDrilldownView(backButton.dataset.targetView);
                }
            });

            // --- Initial Setup ---
            const initialView = localStorage.getItem('teamView') || 'tree';
            switchTabView(initialView);
        });
    </script>
</body>

</html>