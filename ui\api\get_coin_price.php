<?php
// Prevent any output before JSON
ini_set('display_errors', 0);
error_reporting(0);

session_start();
header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Please log in to access this feature.']);
    exit();
}

include '../dbcon.php';
include '../settings_helper.php';

// Check database connection
if ($conn->connect_error) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Database connection failed.']);
    exit();
}

try {
    // Get current staking settings
    $staking_settings = getStakingSettings($conn);
    
    echo json_encode([
        'success' => true,
        'coin_price' => $staking_settings['coin_price'],
        'min_coins' => $staking_settings['min_coins'],
        'freeze_months' => $staking_settings['freeze_months'],
        'min_usd_amount' => $staking_settings['min_usd_amount'],
        'timestamp' => time()
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false, 
        'message' => 'Failed to get coin price: ' . $e->getMessage()
    ]);
}

$conn->close();
?>
