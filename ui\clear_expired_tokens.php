<?php
// Clear expired password reset tokens
include 'dbcon.php';

// Set timezone to ensure consistency
date_default_timezone_set('UTC');

echo "<h2>🧹 Clearing Expired Password Reset Tokens</h2>";

// Show current tokens before cleanup
$result = $conn->query("SELECT COUNT(*) as total FROM password_resets");
$total_before = $result->fetch_assoc()['total'];
echo "<p>📊 Total tokens before cleanup: " . $total_before . "</p>";

// Delete expired tokens
$current_time = date('Y-m-d H:i:s');
$stmt = $conn->prepare("DELETE FROM password_resets WHERE expires_at <= ?");
$stmt->bind_param('s', $current_time);
$stmt->execute();
$expired_deleted = $stmt->affected_rows;
echo "<p>🗑️ Expired tokens deleted: " . $expired_deleted . "</p>";

// Show remaining tokens
$result = $conn->query("SELECT COUNT(*) as total FROM password_resets");
$total_after = $result->fetch_assoc()['total'];
echo "<p>📊 Total tokens after cleanup: " . $total_after . "</p>";

// Show current server time
echo "<p>⏰ Current server time (UTC): " . $current_time . "</p>";

// Show remaining valid tokens
if ($total_after > 0) {
    echo "<h3>📋 Remaining Valid Tokens:</h3>";
    $result = $conn->query("SELECT id, email, LEFT(token, 20) as token_preview, expires_at, created_at FROM password_resets ORDER BY created_at DESC");
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>ID</th><th>Email</th><th>Token Preview</th><th>Expires At</th><th>Created At</th></tr>";
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($row['id']) . "</td>";
        echo "<td>" . htmlspecialchars($row['email']) . "</td>";
        echo "<td>" . htmlspecialchars($row['token_preview']) . "...</td>";
        echo "<td>" . htmlspecialchars($row['expires_at']) . "</td>";
        echo "<td>" . htmlspecialchars($row['created_at']) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
}

$conn->close();
echo "<p>✅ Cleanup completed!</p>";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h2, h3 { color: #333; }
p { margin: 10px 0; }
table { margin: 10px 0; }
th, td { padding: 8px; text-align: left; }
th { background-color: #f2f2f2; }
</style>
