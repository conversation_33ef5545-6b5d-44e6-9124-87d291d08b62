<?php
include 'dbcon.php';

echo "<h2>🔍 Users Table Column Check</h2>";

// Check users table structure
$desc_query = "DESCRIBE users";
$desc_result = $conn->query($desc_query);

if ($desc_result) {
    echo "<h3>📋 Users Table Columns:</h3>";
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>";
    echo "<tr style='background: #f3f4f6;'><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    
    $columns = [];
    while ($row = $desc_result->fetch_assoc()) {
        $columns[] = $row['Field'];
        echo "<tr>";
        echo "<td><strong>" . $row['Field'] . "</strong></td>";
        echo "<td>" . $row['Type'] . "</td>";
        echo "<td>" . $row['Null'] . "</td>";
        echo "<td>" . $row['Key'] . "</td>";
        echo "<td>" . ($row['Default'] ?? 'NULL') . "</td>";
        echo "<td>" . ($row['Extra'] ?? '') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h3>🎯 Available Name Columns:</h3>";
    $name_columns = array_filter($columns, function($col) {
        return stripos($col, 'name') !== false;
    });
    
    if (!empty($name_columns)) {
        echo "<ul>";
        foreach ($name_columns as $col) {
            echo "<li><strong>$col</strong></li>";
        }
        echo "</ul>";
    } else {
        echo "<p>No columns containing 'name' found.</p>";
    }
    
    echo "<h3>🔧 Suggested Query Fixes:</h3>";
    
    // Check for common name column variations
    $possible_name_columns = ['name', 'username', 'first_name', 'last_name', 'user_name', 'display_name'];
    $found_name_column = null;
    
    foreach ($possible_name_columns as $col) {
        if (in_array($col, $columns)) {
            $found_name_column = $col;
            break;
        }
    }
    
    if ($found_name_column) {
        echo "<div style='background: #dcfce7; border: 2px solid #16a34a; border-radius: 8px; padding: 15px; margin: 10px 0;'>";
        echo "<h4 style='color: #15803d; margin-top: 0;'>✅ Solution Found!</h4>";
        echo "<p style='color: #166534;'>Use column: <strong>$found_name_column</strong></p>";
        echo "<p style='color: #166534;'><strong>Corrected Query:</strong></p>";
        echo "<code style='background: #f0fdf4; padding: 10px; display: block; border-radius: 4px; color: #166534;'>";
        echo "SELECT user_id, $found_name_column, created_at FROM users WHERE is_admin = 0 ORDER BY created_at DESC LIMIT 5";
        echo "</code>";
        echo "</div>";
    } else {
        echo "<div style='background: #fef3c7; border: 2px solid #f59e0b; border-radius: 8px; padding: 15px; margin: 10px 0;'>";
        echo "<h4 style='color: #92400e; margin-top: 0;'>⚠️ Manual Fix Needed</h4>";
        echo "<p style='color: #b45309;'>No standard name column found. You may need to:</p>";
        echo "<ul style='color: #b45309;'>";
        echo "<li>Use a combination of columns (e.g., CONCAT(first_name, ' ', last_name))</li>";
        echo "<li>Use an alternative identifier column</li>";
        echo "<li>Add a name column to the table</li>";
        echo "</ul>";
        echo "</div>";
    }
    
    // Show sample data
    echo "<h3>📊 Sample User Data (First 3 rows):</h3>";
    $sample_query = "SELECT * FROM users WHERE is_admin = 0 LIMIT 3";
    $sample_result = $conn->query($sample_query);
    
    if ($sample_result && $sample_result->num_rows > 0) {
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%; font-size: 0.9em;'>";
        echo "<tr style='background: #f3f4f6;'>";
        foreach ($columns as $col) {
            echo "<th>$col</th>";
        }
        echo "</tr>";
        
        while ($row = $sample_result->fetch_assoc()) {
            echo "<tr>";
            foreach ($columns as $col) {
                $value = $row[$col] ?? '';
                if (strlen($value) > 20) {
                    $value = substr($value, 0, 20) . '...';
                }
                echo "<td>" . htmlspecialchars($value) . "</td>";
            }
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>No user data found or query failed.</p>";
    }
    
} else {
    echo "<p style='color: red;'>❌ Failed to get table structure: " . $conn->error . "</p>";
}

$conn->close();
?>

<style>
body { 
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; 
    margin: 20px; 
    background: #f8fafc;
    line-height: 1.6;
}
h2, h3, h4 { color: #1f2937; }
table { 
    font-size: 0.9em; 
    max-width: 100%; 
    overflow-x: auto;
}
th, td { 
    padding: 8px 12px; 
    text-align: left; 
    border: 1px solid #e5e7eb;
}
th { 
    background: #f9fafb; 
    font-weight: 600;
}
code { 
    background: #f1f5f9; 
    padding: 2px 6px; 
    border-radius: 4px; 
    font-family: monospace;
    font-size: 0.9em;
}
</style>
