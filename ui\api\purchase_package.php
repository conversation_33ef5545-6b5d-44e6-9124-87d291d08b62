<?php
// Prevent any output before JSO<PERSON>
ini_set('display_errors', 0);
error_reporting(0);

session_start();

header('Content-Type: application/json');

if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['error' => 'User not authenticated.']);
    exit();
}

// Include database connection
include '../dbcon.php';

// Check database connection
if ($conn->connect_error) {
    http_response_code(500);
    echo json_encode(['error' => 'Database connection failed.']);
    exit();
}

$data = json_decode(file_get_contents('php://input'), true);
$package_id = $data['package_id'] ?? null;
$wallet_type = $data['wallet_type'] ?? 'deposit'; // Default to deposit wallet

if (!$package_id) {
    http_response_code(400);
    echo json_encode(['error' => 'Package ID is required.']);
    exit();
}

// Validate wallet type
if (!in_array($wallet_type, ['deposit', 'withdrawal'])) {
    http_response_code(400);
    echo json_encode(['error' => 'Invalid wallet type.']);
    exit();
}

$user_id = $_SESSION['user_id'];

// Start transaction
$conn->begin_transaction();

try {
    // 1. Get package details
    $package_sql = "SELECT package_name, package_amount, profit_percentage FROM packages WHERE id = ? AND is_active = 1";
    $stmt = $conn->prepare($package_sql);
    $stmt->bind_param("i", $package_id);
    $stmt->execute();
    $package_result = $stmt->get_result();
    if ($package_result->num_rows === 0) {
        throw new Exception('Package not found or is inactive.');
    }
    $package = $package_result->fetch_assoc();
    $package_name = $package['package_name'];
    $package_amount = $package['package_amount'];

    // 2. Get user's current package and balance (with lock for update)
    $user_sql = "SELECT u.package, u.deposit_wallet_balance, u.withdrawal_wallet_balance, p.package_amount as current_package_amount FROM users u LEFT JOIN packages p ON u.package = p.package_name WHERE u.id = ? FOR UPDATE";
    $stmt = $conn->prepare($user_sql);
    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    $user_result = $stmt->get_result();
    $user = $user_result->fetch_assoc();
    $current_balance = ($wallet_type === 'deposit') ? $user['deposit_wallet_balance'] : $user['withdrawal_wallet_balance'];
    $current_package_amount = $user['current_package_amount'] ?? 0;

    // 3. Check if it's an upgrade
    if ($package_amount <= $current_package_amount) {
        throw new Exception('This package is not an upgrade. Please select a package with a higher value.');
    }

    // 4. Check if balance is sufficient
    if ($current_balance < $package_amount) {
        throw new Exception("Insufficient {$wallet_type} wallet balance.");
    }

    // 5. Mark previous package as upgraded (if exists)
    if (!empty($user['package']) && $user['package'] !== $package_name) {
        $expire_previous_sql = "UPDATE package_history SET status = 'upgraded', expired_at = NOW(), duration_days = DATEDIFF(NOW(), purchased_at), expiry_reason = 'upgraded' WHERE user_id = ? AND status = 'active'";
        $stmt_expire = $conn->prepare($expire_previous_sql);
        $stmt_expire->bind_param("i", $user_id);
        $stmt_expire->execute();
        $stmt_expire->close();
    }

    // 6. Deduct amount and update package (reset plan status for upgrades)
    $new_balance = $current_balance - $package_amount;
    $wallet_field = $wallet_type . '_wallet_balance';
    $update_user_sql = "UPDATE users SET {$wallet_field} = ?, package = ?, total_earnings_received = 0, plan_status = 'active', plan_expired_at = NULL WHERE id = ?";
    $stmt = $conn->prepare($update_user_sql);
    $stmt->bind_param("dsi", $new_balance, $package_name, $user_id);
    $stmt->execute();

    // 7. Record new package in history
    $earnings_limit = $package_amount * 2;
    $insert_history_sql = "INSERT INTO package_history (user_id, package_name, package_amount, profit_percentage, wallet_type, earnings_limit, status, notes) VALUES (?, ?, ?, ?, ?, ?, 'active', 'Package purchased')";
    $stmt_history = $conn->prepare($insert_history_sql);
    $stmt_history->bind_param("isddsd", $user_id, $package_name, $package_amount, $package['profit_percentage'], $wallet_type, $earnings_limit);
    $stmt_history->execute();
    $stmt_history->close();

    // 8. Log transaction in wallet
    $investment_amount = -$package_amount;
    $description = "Investment in '{$package_name}' package from {$wallet_type} wallet";
    $type = 'investment';
    $insert_wallet_sql = "INSERT INTO wallet_history (user_id, amount, type, wallet_type, description) VALUES (?, ?, ?, ?, ?)";
    $stmt = $conn->prepare($insert_wallet_sql);
    $stmt->bind_param("idsss", $user_id, $investment_amount, $type, $wallet_type, $description);
    $stmt->execute();

    // Commit the transaction
    $conn->commit();

    echo json_encode(['success' => true]);
} catch (Exception $e) {
    $conn->rollback();
    http_response_code(400);
    echo json_encode(['error' => $e->getMessage()]);
} finally {
    if (isset($stmt)) {
        $stmt->close();
    }
    $conn->close();
}
