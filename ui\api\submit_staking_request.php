<?php
// Prevent any output before JSO<PERSON>
ini_set('display_errors', 0);
error_reporting(0);

session_start();
header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Please log in to stake coins.']);
    exit();
}

include '../dbcon.php';
include '../settings_helper.php';

// Check database connection
if ($conn->connect_error) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Database connection failed.']);
    exit();
}

$user_id = $_SESSION['user_id'];
$data = json_decode(file_get_contents('php://input'), true);

$wallet_type = $data['wallet_type'] ?? '';
$stake_amount_usd = floatval($data['stake_amount_usd'] ?? 0);

// Get dynamic staking settings from database
$staking_settings = getStakingSettings($conn);
$COIN_PRICE = $staking_settings['coin_price'];
$MIN_STAKE_AMOUNT = $staking_settings['min_usd_amount'];

// Validation
if (empty($wallet_type) || !in_array($wallet_type, ['deposit', 'withdrawal'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Please select a valid wallet type.']);
    exit();
}

if ($stake_amount_usd < $MIN_STAKE_AMOUNT) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => "Minimum staking amount is \$$MIN_STAKE_AMOUNT."]);
    exit();
}

if ($stake_amount_usd % $MIN_STAKE_AMOUNT !== 0) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => "Staking amount must be in multiples of \$$MIN_STAKE_AMOUNT."]);
    exit();
}

// Recalculate coins based on the USD amount (coins can be any amount, but USD must be multiple of 10)
$stake_coins = floor($stake_amount_usd / $COIN_PRICE);

try {
    // Start transaction
    $conn->begin_transaction();

    // 1. Get user's current wallet balance
    $wallet_field = $wallet_type . '_wallet_balance';
    $sql_get_balance = "SELECT $wallet_field as balance, name FROM users WHERE id = ?";
    $stmt_get_balance = $conn->prepare($sql_get_balance);

    if (!$stmt_get_balance) {
        throw new Exception('Failed to prepare balance query: ' . $conn->error);
    }

    $stmt_get_balance->bind_param("i", $user_id);

    if (!$stmt_get_balance->execute()) {
        throw new Exception('Failed to get user balance: ' . $stmt_get_balance->error);
    }

    $balance_result = $stmt_get_balance->get_result();

    if ($balance_result->num_rows === 0) {
        throw new Exception('User not found.');
    }

    $user_data = $balance_result->fetch_assoc();
    $current_balance = $user_data['balance'];
    $user_name = $user_data['name'];

    $stmt_get_balance->close();

    // 2. Check if user has sufficient balance
    if ($current_balance < $stake_amount_usd) {
        throw new Exception("Insufficient balance in $wallet_type wallet. Available: $" . number_format($current_balance, 2));
    }

    // 3. Calculate freeze end date (6 months from now)
    $freeze_end_date = date('Y-m-d H:i:s', strtotime('+6 months'));

    // 4. Deduct amount from user's wallet
    $new_balance = $current_balance - $stake_amount_usd;
    $sql_update_balance = "UPDATE users SET $wallet_field = ? WHERE id = ?";
    $stmt_update_balance = $conn->prepare($sql_update_balance);

    if (!$stmt_update_balance) {
        throw new Exception('Failed to prepare balance update: ' . $conn->error);
    }

    $stmt_update_balance->bind_param("di", $new_balance, $user_id);

    if (!$stmt_update_balance->execute()) {
        throw new Exception('Failed to update wallet balance: ' . $stmt_update_balance->error);
    }

    $stmt_update_balance->close();

    // 5. Insert staking record
    $sql_insert_stake = "INSERT INTO staking_records (user_id, coins_staked, amount_usd, stake_type, freeze_end_date, status, created_at) 
                        VALUES (?, ?, ?, ?, ?, 'active', NOW())";
    $stmt_insert_stake = $conn->prepare($sql_insert_stake);

    if (!$stmt_insert_stake) {
        throw new Exception('Failed to prepare staking insert: ' . $conn->error);
    }

    $stmt_insert_stake->bind_param("iidss", $user_id, $stake_coins, $stake_amount_usd, $wallet_type, $freeze_end_date);

    if (!$stmt_insert_stake->execute()) {
        throw new Exception('Failed to create staking record: ' . $stmt_insert_stake->error);
    }

    $stmt_insert_stake->close();

    // 6. Log transaction in wallet history
    $description = "Staked \$$stake_amount_usd ($stake_coins coins) for 6 months (Unlock: " . date('M j, Y', strtotime($freeze_end_date)) . ")";
    $negative_amount = -$stake_amount_usd;
    $log_sql = "INSERT INTO wallet_history (user_id, amount, type, wallet_type, description) VALUES (?, ?, 'staking', ?, ?)";
    $stmt_log = $conn->prepare($log_sql);

    if (!$stmt_log) {
        throw new Exception('Failed to prepare wallet history: ' . $conn->error);
    }

    $stmt_log->bind_param("idss", $user_id, $negative_amount, $wallet_type, $description);

    if (!$stmt_log->execute()) {
        throw new Exception('Failed to log transaction: ' . $stmt_log->error);
    }

    $stmt_log->close();

    // 7. Commit transaction
    $conn->commit();

    // Return success response
    http_response_code(200);
    echo json_encode([
        'success' => true,
        'message' => 'Coins staked successfully.',
        'data' => [
            'coins_staked' => $stake_coins,
            'amount_usd' => $stake_amount_usd,
            'wallet_type' => $wallet_type,
            'freeze_end_date' => $freeze_end_date,
            'new_balance' => $new_balance
        ]
    ]);
    exit();
} catch (Exception $e) {
    $conn->rollback();
    error_log('Staking Request Error: ' . $e->getMessage());
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
    exit();
} finally {
    // Close statements safely
    if (isset($stmt_get_balance) && $stmt_get_balance !== false) {
        $stmt_get_balance->close();
    }
    if (isset($stmt_update_balance) && $stmt_update_balance !== false) {
        $stmt_update_balance->close();
    }
    if (isset($stmt_insert_stake) && $stmt_insert_stake !== false) {
        $stmt_insert_stake->close();
    }
    if (isset($stmt_log) && $stmt_log !== false) {
        $stmt_log->close();
    }
    if (isset($conn) && $conn !== false) {
        $conn->close();
    }
}
