<!-- Ad<PERSON>er -->
<footer class="bg-white border-t border-gray-200 mt-auto">
    <div class="px-6 py-4">
        <div class="flex flex-col lg:flex-row justify-between items-center space-y-2 lg:space-y-0">
            <!-- Left Section: Copyright & System Status -->
            <div class="flex items-center space-x-4">
                <p class="text-sm text-gray-600">
                    © 2024 <span class="font-semibold text-gray-800">CryptoApp</span>. All rights reserved.
                </p>
                <div class="hidden md:flex items-center space-x-1">
                    <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                    <span class="text-xs text-gray-500">System Online</span>
                </div>
                <div class="text-xs text-gray-400 border-l border-gray-200 pl-4">
                    v2.1.0
                </div>
            </div>

            <!-- Right Section: Quick Stats -->
            <div class="flex items-center space-x-6 text-xs text-gray-500">
                <div class="flex items-center space-x-1">
                    <i class="fas fa-users text-blue-500"></i>
                    <span>Active Users: <strong class="text-gray-700">1,234</strong></span>
                </div>
                <div class="flex items-center space-x-1">
                    <i class="fas fa-dollar-sign text-green-500"></i>
                    <span>Total Volume: <strong class="text-gray-700">$2.5M</strong></span>
                </div>
                <div class="flex items-center space-x-1">
                    <i class="fas fa-arrow-up text-orange-500"></i>
                    <span>Withdrawals: <strong class="text-gray-700">$125K</strong></span>
                </div>
            </div>
        </div>
    </div>
</footer>

<style>
    /* Footer Styles */
    footer {
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
    }

    footer a:hover i {
        transform: scale(1.1);
        transition: transform 0.2s ease;
    }

    /* Responsive adjustments */
    @media (max-width: 768px) {
        footer .flex-col {
            text-align: center;
        }

        footer .space-x-4>* {
            margin-bottom: 0.5rem;
        }
    }
</style>