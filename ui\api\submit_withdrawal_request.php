<?php
// Prevent any output before JSO<PERSON>
ini_set('display_errors', 0);
error_reporting(0);

session_start();
header('Content-Type: application/json');

if (!isset($_SESSION['user_id'])) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Unauthorized access.']);
    exit();
}

include '../dbcon.php';

// Check database connection
if ($conn->connect_error) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Database connection failed.']);
    exit();
}

$user_id = $_SESSION['user_id'];
$amount = $_POST['amount'] ?? null;
$binance_address = $_POST['binance_address'] ?? null;
$password = $_POST['password'] ?? null;

// --- Validation ---
if (empty($amount)) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Amount is required.']);
    exit();
}

if (!is_numeric($amount)) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Amount must be a valid number.']);
    exit();
}

if ($amount <= 0) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Amount must be a positive number.']);
    exit();
}

if ($amount < 10) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Minimum withdrawal amount is $10.']);
    exit();
}

if (fmod((float)$amount, 10) != 0) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Amount must be a multiple of 10.']);
    exit();
}

if (empty($binance_address)) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Binance address is required.']);
    exit();
}

if (empty($password)) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Password is required for verification.']);
    exit();
}

// Validate Binance address format (basic validation)
if (strlen($binance_address) < 20 || strlen($binance_address) > 50) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Invalid Binance address format.']);
    exit();
}

try {
    // 1. Verify user's password and get withdrawal balance
    $user_sql = "SELECT password, withdrawal_wallet_balance FROM users WHERE id = ?";
    $stmt = $conn->prepare($user_sql);
    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    $user_result = $stmt->get_result();

    if ($user_result->num_rows === 0) {
        throw new Exception('User not found.');
    }

    $user = $user_result->fetch_assoc();
    $stmt->close();

    // 2. Verify password
    if (!password_verify($password, $user['password'])) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Invalid password.']);
        exit();
    }

    // 3. Check withdrawal balance
    if ($user['withdrawal_wallet_balance'] < $amount) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Insufficient withdrawal wallet balance.']);
        exit();
    }

    // 4. Start transaction
    $conn->begin_transaction();

    // 5. Deduct amount from withdrawal wallet
    $new_balance = $user['withdrawal_wallet_balance'] - $amount;
    $update_sql = "UPDATE users SET withdrawal_wallet_balance = ? WHERE id = ?";
    $stmt = $conn->prepare($update_sql);
    $stmt->bind_param("di", $new_balance, $user_id);
    if (!$stmt->execute()) {
        throw new Exception('Failed to update withdrawal balance.');
    }
    $stmt->close();

    // 6. Create withdrawal request
    $insert_sql = "INSERT INTO withdrawal_requests (user_id, amount, binance_address, status, requested_at) VALUES (?, ?, ?, 'pending', NOW())";
    $stmt = $conn->prepare($insert_sql);
    $stmt->bind_param("ids", $user_id, $amount, $binance_address);
    if (!$stmt->execute()) {
        throw new Exception('Failed to create withdrawal request.');
    }
    $stmt->close();

    // 7. Log transaction in wallet history
    $description = "Withdrawal request to Binance address: " . substr($binance_address, 0, 10) . "...";
    $negative_amount = -$amount;
    $log_sql = "INSERT INTO wallet_history (user_id, amount, type, wallet_type, description) VALUES (?, ?, 'withdrawal', 'withdrawal', ?)";
    $stmt = $conn->prepare($log_sql);

    if (!$stmt) {
        throw new Exception('Failed to prepare wallet history statement: ' . $conn->error);
    }

    $stmt->bind_param("ids", $user_id, $negative_amount, $description);

    if (!$stmt->execute()) {
        throw new Exception('Failed to log transaction: ' . $stmt->error);
    }

    $stmt->close();

    // 8. Commit transaction
    $conn->commit();

    // Return success response
    http_response_code(200);
    header('Content-Type: application/json');
    echo json_encode([
        'success' => true,
        'message' => 'Withdrawal request submitted successfully.',
        'status' => 'success'
    ]);
    exit();
} catch (Exception $e) {
    $conn->rollback();
    error_log('Withdrawal Request Error: ' . $e->getMessage());
    http_response_code(400);
    header('Content-Type: application/json');
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage(),
        'status' => 'error'
    ]);
    exit();
} finally {
    if (isset($stmt) && $stmt !== false) {
        $stmt->close();
    }
    if (isset($conn) && $conn !== false) {
        $conn->close();
    }
}
