<?php
session_start();

if (!isset($_SESSION['user_id']) || !$_SESSION['is_admin']) {
    header("Location: login.html");
    exit();
}

include 'dbcon.php';

// Get search and filter parameters
$search_name = isset($_GET['search_name']) ? trim($_GET['search_name']) : '';
$search_username = isset($_GET['search_username']) ? trim($_GET['search_username']) : '';
$filter_package = isset($_GET['filter_package']) ? trim($_GET['filter_package']) : '';

// Build the WHERE clause based on search and filter parameters
$where_conditions = ["u.is_admin = 0"];

if (!empty($search_name)) {
    $where_conditions[] = "u.name LIKE '%" . $conn->real_escape_string($search_name) . "%'";
}

if (!empty($search_username)) {
    $where_conditions[] = "u.user_id LIKE '%" . $conn->real_escape_string($search_username) . "%'";
}

if (!empty($filter_package)) {
    if ($filter_package === 'no_package') {
        $where_conditions[] = "(u.package IS NULL OR u.package = '')";
    } else {
        $where_conditions[] = "u.package = '" . $conn->real_escape_string($filter_package) . "'";
    }
}

$where_clause = implode(' AND ', $where_conditions);

// Fetch users with search and filter applied
$users_sql = "SELECT u.id, u.user_id, u.name, u.email, u.package, u.deposit_wallet_balance, u.withdrawal_wallet_balance, p.package_amount as current_package_amount
              FROM users u
              LEFT JOIN packages p ON u.package = p.package_name
              WHERE $where_clause
              ORDER BY u.id DESC";
$users_result = $conn->query($users_sql);

// Fetch all packages for the dropdown (including filter dropdown)
$packages_sql = "SELECT id, package_name, package_amount FROM packages WHERE is_active = 1 AND is_deleted = 0 ORDER BY package_amount ASC";
$packages_result = $conn->query($packages_sql);
$packages = [];
while ($row = $packages_result->fetch_assoc()) {
    $packages[] = $row;
}

// Get unique packages that users currently have (for filter dropdown)
$user_packages_sql = "SELECT DISTINCT u.package FROM users u WHERE u.is_admin = 0 AND u.package IS NOT NULL AND u.package != '' ORDER BY u.package";
$user_packages_result = $conn->query($user_packages_sql);
$user_packages = [];
while ($row = $user_packages_result->fetch_assoc()) {
    $user_packages[] = $row['package'];
}

// Page configuration
$page_title = "Investment Management";
$additional_css = '
<style>
    /* Enhanced table styling */
    .admin-table {
        width: 100%;
        border-collapse: separate;
        border-spacing: 0;
        background: white;
    }

    .admin-table thead {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    .admin-table thead th {
        color: white;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.05em;
        padding: 1rem 1.5rem;
        font-size: 0.75rem;
        border: none;
        position: relative;
        text-align: left;
    }

    .admin-table thead th:last-child {
        text-align: center;
    }

    .admin-table tbody tr {
        transition: all 0.2s ease;
        border-bottom: 1px solid #e2e8f0;
    }

    .admin-table tbody tr:hover {
        background-color: #f8fafc;
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    .admin-table tbody tr:last-child {
        border-bottom: none;
    }

    .admin-table tbody td {
        padding: 1rem 1.5rem;
        vertical-align: middle;
        border: none;
        background-color: white;
    }

    .admin-table tbody tr:hover td {
        background-color: #f8fafc;
    }

    /* Action buttons */
    .action-btn {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        padding: 0.5rem 1rem;
        border-radius: 0.375rem;
        font-weight: 500;
        font-size: 0.875rem;
        transition: all 0.2s ease;
        border: none;
        cursor: pointer;
        text-decoration: none;
    }

    .action-btn-primary {
        background: rgba(99, 102, 241, 0.1);
        color: #6366f1;
        border: 1px solid rgba(99, 102, 241, 0.2);
    }

    .action-btn-primary:hover {
        background: rgba(99, 102, 241, 0.2);
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(99, 102, 241, 0.2);
    }

    /* Modal styling */
    .modal-overlay {
        background: rgba(0, 0, 0, 0.5);
        backdrop-filter: blur(4px);
    }

    .modal-content {
        background: white;
        border-radius: 1rem;
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    }

    .modal-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 1.5rem;
        border-radius: 1rem 1rem 0 0;
    }

    .modal-body {
        padding: 1.5rem;
    }

    .modal-footer {
        background: #f8fafc;
        padding: 1rem 1.5rem;
        border-radius: 0 0 1rem 1rem;
        border-top: 1px solid #e2e8f0;
    }

    .form-input {
        width: 100%;
        padding: 0.75rem 1rem;
        border: 2px solid #e2e8f0;
        border-radius: 0.5rem;
        font-size: 0.875rem;
        transition: all 0.3s ease;
    }

    .form-input:focus {
        outline: none;
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }

    .form-label {
        color: #374151;
        font-weight: 600;
        margin-bottom: 0.5rem;
        display: block;
    }

    .btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        padding: 0.75rem 1.5rem;
        border-radius: 0.5rem;
        font-weight: 600;
        transition: all 0.3s ease;
        cursor: pointer;
    }

    .btn-primary:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
    }

    .btn-secondary {
        background: white;
        color: #374151;
        border: 2px solid #e2e8f0;
        padding: 0.75rem 1.5rem;
        border-radius: 0.5rem;
        font-weight: 600;
        transition: all 0.3s ease;
        cursor: pointer;
    }

    .btn-secondary:hover {
        background: #f8fafc;
        border-color: #d1d5db;
    }

    /* Search and Filter Styling */
    .search-filter-card {
        background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        border: 1px solid #e2e8f0;
    }

    .form-input:focus {
        outline: none;
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        transform: translateY(-1px);
    }

    .search-results-info {
        background: #f0f9ff;
        border: 1px solid #bae6fd;
        color: #0369a1;
        padding: 0.75rem 1rem;
        border-radius: 0.5rem;
        font-size: 0.875rem;
        margin-bottom: 1rem;
    }

    /* Loading state for search */
    .loading {
        opacity: 0.6;
        pointer-events: none;
    }

    /* Highlight search terms */
    .highlight {
        background-color: #fef3c7;
        padding: 0.125rem 0.25rem;
        border-radius: 0.25rem;
        font-weight: 600;
    }
</style>
';

// Start output buffering to capture the page content
ob_start();

$conn->close();
?>

<!-- Page Header -->
<div class="flex flex-col lg:flex-row justify-between items-start lg:items-center space-y-4 lg:space-y-0 mb-6">
    <div>
        <h1 class="text-3xl font-bold text-gray-900">Investment Management</h1>
        <p class="text-gray-600 mt-1">Manage user packages and investment portfolios</p>
    </div>
    <div class="flex items-center space-x-2 bg-blue-50 px-4 py-2 rounded-lg border border-blue-200">
        <i class="fas fa-chart-line text-blue-600"></i>
        <span class="text-sm font-medium text-blue-700">Investment Manager</span>
    </div>
</div>

<div id="feedback-message" class="hidden p-4 mb-4 text-sm rounded-lg" role="alert"></div>

<!-- Search and Filter Controls -->
<div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6">
    <div class="flex flex-col lg:flex-row lg:items-end lg:space-x-4 space-y-4 lg:space-y-0">
        <!-- Search by Name -->
        <div class="flex-1">
            <label for="search_name" class="form-label">
                <i class="fas fa-user mr-2 text-blue-600"></i>Search by Name
            </label>
            <input type="text"
                id="search_name"
                name="search_name"
                value="<?php echo htmlspecialchars($search_name); ?>"
                placeholder="Enter user name..."
                class="form-input">
        </div>

        <!-- Search by Username -->
        <div class="flex-1">
            <label for="search_username" class="form-label">
                <i class="fas fa-id-card mr-2 text-green-600"></i>Search by User ID
            </label>
            <input type="text"
                id="search_username"
                name="search_username"
                value="<?php echo htmlspecialchars($search_username); ?>"
                placeholder="Enter user ID..."
                class="form-input">
        </div>

        <!-- Filter by Package -->
        <div class="flex-1">
            <label for="filter_package" class="form-label">
                <i class="fas fa-box mr-2 text-purple-600"></i>Filter by Package
            </label>
            <select id="filter_package" name="filter_package" class="form-input">
                <option value="">All Packages</option>
                <option value="no_package" <?php echo ($filter_package === 'no_package') ? 'selected' : ''; ?>>No Package</option>
                <?php foreach ($user_packages as $package): ?>
                    <option value="<?php echo htmlspecialchars($package); ?>"
                        <?php echo ($filter_package === $package) ? 'selected' : ''; ?>>
                        <?php echo htmlspecialchars($package); ?>
                    </option>
                <?php endforeach; ?>
            </select>
        </div>

        <!-- Action Buttons -->
        <div class="flex space-x-2">
            <button type="button" onclick="applyFilters()" class="btn-primary">
                <i class="fas fa-search mr-2"></i>Search
            </button>
            <button type="button" onclick="clearFilters()" class="btn-secondary">
                <i class="fas fa-times mr-2"></i>Clear
            </button>
        </div>
    </div>
</div>

<?php
// Count total results for display
$total_results = $users_result->num_rows;
$has_filters = !empty($search_name) || !empty($search_username) || !empty($filter_package);
?>

<!-- Results Info -->
<?php if ($has_filters): ?>
    <div class="search-results-info">
        <i class="fas fa-info-circle mr-2"></i>
        Found <strong><?php echo $total_results; ?></strong> user(s) matching your search criteria.
        <?php if ($total_results === 0): ?>
            <span class="ml-2">Try adjusting your search terms or filters.</span>
        <?php endif; ?>
    </div>
<?php endif; ?>

<!-- Investment Management Table -->
<div class="admin-card p-0 overflow-hidden">
    <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-3">
                <div class="w-8 h-8 bg-gradient-to-br from-green-500 to-teal-600 rounded-lg flex items-center justify-center">
                    <i class="fas fa-chart-line text-white text-sm"></i>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-gray-900">User Investment Portfolio</h3>
                    <p class="text-sm text-gray-600">Manage user packages and wallet balances</p>
                </div>
            </div>
            <div class="text-sm text-gray-500">
                <i class="fas fa-users mr-1"></i>
                <?php echo $total_results; ?> user(s) displayed
            </div>
        </div>
    </div>

    <div class="overflow-x-auto">
        <table class="admin-table">
            <thead>
                <tr>
                    <th>User ID</th>
                    <th>Name</th>
                    <th>Current Package</th>
                    <th>Deposit Wallet</th>
                    <th>Withdrawal Wallet</th>
                    <th>Action</th>
                </tr>
            </thead>
            <tbody>
                <?php if ($total_results > 0): ?>
                    <?php while ($user = $users_result->fetch_assoc()): ?>
                        <tr>
                            <td>
                                <div class="flex items-center space-x-3">
                                    <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                        <i class="fas fa-user text-blue-600 text-xs"></i>
                                    </div>
                                    <div class="text-sm font-medium text-gray-900">
                                        <?php echo htmlspecialchars($user['user_id']); ?>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div class="text-sm font-medium text-gray-900">
                                    <?php echo htmlspecialchars($user['name']); ?>
                                </div>
                            </td>
                            <td>
                                <div class="flex items-center space-x-2">
                                    <div class="w-6 h-6 bg-purple-100 rounded flex items-center justify-center">
                                        <i class="fas fa-box text-purple-600 text-xs"></i>
                                    </div>
                                    <span class="text-sm text-gray-900">
                                        <?php echo htmlspecialchars($user['package'] ?: 'No Package'); ?>
                                    </span>
                                </div>
                            </td>
                            <td>
                                <div class="flex items-center space-x-2">
                                    <div class="w-6 h-6 bg-green-100 rounded flex items-center justify-center">
                                        <i class="fas fa-wallet text-green-600 text-xs"></i>
                                    </div>
                                    <span class="text-lg font-bold text-green-600">
                                        $<?php echo number_format($user['deposit_wallet_balance'], 2); ?>
                                    </span>
                                </div>
                            </td>
                            <td>
                                <div class="flex items-center space-x-2">
                                    <div class="w-6 h-6 bg-orange-100 rounded flex items-center justify-center">
                                        <i class="fas fa-money-bill-wave text-orange-600 text-xs"></i>
                                    </div>
                                    <span class="text-lg font-bold text-orange-600">
                                        $<?php echo number_format($user['withdrawal_wallet_balance'], 2); ?>
                                    </span>
                                </div>
                            </td>
                            <td>
                                <div class="flex items-center justify-center">
                                    <button onclick="openModal(<?php echo $user['id']; ?>, '<?php echo htmlspecialchars($user['name']); ?>', '<?php echo htmlspecialchars($user['package']); ?>', <?php echo $user['current_package_amount'] ?? 0; ?>, <?php echo $user['deposit_wallet_balance'] ?? 0; ?>)" class="action-btn action-btn-primary">
                                        <i class="fas fa-edit mr-2"></i>
                                        Change Package
                                    </button>
                                </div>
                            </td>
                        </tr>
                    <?php endwhile; ?>
                <?php else: ?>
                    <tr>
                        <td colspan="6" class="text-center py-12">
                            <div class="flex flex-col items-center space-y-4">
                                <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center">
                                    <i class="fas fa-search text-gray-400 text-2xl"></i>
                                </div>
                                <div>
                                    <h3 class="text-lg font-medium text-gray-900 mb-2">No users found</h3>
                                    <p class="text-gray-500 text-sm">
                                        <?php if ($has_filters): ?>
                                            No users match your current search criteria. Try adjusting your filters.
                                        <?php else: ?>
                                            No users are currently registered in the system.
                                        <?php endif; ?>
                                    </p>
                                </div>
                                <?php if ($has_filters): ?>
                                    <button onclick="clearFilters()" class="btn-secondary">
                                        <i class="fas fa-times mr-2"></i>Clear Filters
                                    </button>
                                <?php endif; ?>
                            </div>
                        </td>
                    </tr>
                <?php endif; ?>
            </tbody>
        </table>
    </div>
</div>

<!-- Enhanced Modal -->
<div id="package-modal" class="fixed z-50 inset-0 overflow-y-auto hidden" aria-labelledby="modal-title" role="dialog" aria-modal="true">
    <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="modal-overlay fixed inset-0 transition-opacity" aria-hidden="true"></div>
        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
        <div class="modal-content inline-block align-bottom text-left overflow-hidden transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
            <div class="modal-header">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-white bg-opacity-20 rounded-xl flex items-center justify-center">
                        <i class="fas fa-exchange-alt text-white text-lg"></i>
                    </div>
                    <div>
                        <h3 class="text-xl font-bold text-white" id="modal-title">Change Package</h3>
                        <p class="text-blue-100 text-sm">Update user investment package</p>
                    </div>
                </div>
            </div>

            <div class="modal-body">
                <div class="mb-4">
                    <div class="flex items-center space-x-3 p-3 bg-blue-50 rounded-lg">
                        <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                            <i class="fas fa-user text-blue-600 text-sm"></i>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-900">User: <span id="modal-user-name" class="font-bold"></span></p>
                            <p class="text-xs text-gray-600">Current Package: <span id="modal-current-package" class="font-medium"></span></p>
                        </div>
                    </div>
                </div>

                <div class="mb-4">
                    <label for="package-select" class="form-label">Select New Package</label>
                    <select id="package-select" class="form-input">
                        <?php foreach ($packages as $package): ?>
                            <option value="<?php echo $package['id']; ?>" data-amount="<?php echo $package['package_amount']; ?>">
                                <?php echo htmlspecialchars($package['package_name']); ?> ($<?php echo number_format($package['package_amount'], 2); ?>)
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <div class="flex items-center space-x-3 p-3 bg-yellow-50 rounded-lg">
                    <input type="checkbox" id="deduct-wallet" checked class="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500">
                    <label for="deduct-wallet" class="text-sm text-gray-900">
                        <i class="fas fa-wallet text-yellow-600 mr-2"></i>
                        Deduct upgrade cost from user's deposit wallet
                    </label>
                </div>
            </div>

            <div class="modal-footer">
                <div class="flex items-center justify-end space-x-3">
                    <button type="button" onclick="closeModal()" class="btn-secondary">
                        <i class="fas fa-times mr-2"></i>
                        Cancel
                    </button>
                    <button type="button" id="save-btn" class="btn-primary">
                        <i class="fas fa-save mr-2"></i>
                        Save Changes
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    let currentUserId = null;

    function openModal(userId, userName, currentPackage, currentPackageAmount, depositBalance) {
        currentUserId = userId;
        document.getElementById('modal-user-name').textContent = userName;
        document.getElementById('modal-current-package').textContent = `${currentPackage || 'None'} (Balance: $${depositBalance.toFixed(4)})`;

        const packageSelect = document.getElementById('package-select');
        const options = packageSelect.options;
        let firstAvailableFound = false;

        for (let i = 0; i < options.length; i++) {
            const option = options[i];
            const optionAmount = parseFloat(option.dataset.amount);
            const upgradeCost = optionAmount - currentPackageAmount;
            const originalText = option.text.split(' (')[0]; // Reset text

            // Disable if it's not an upgrade OR if the user can't afford the upgrade
            if (upgradeCost <= 0) {
                option.disabled = true;
                option.text = originalText;
            } else if (upgradeCost > depositBalance) {
                option.disabled = true;
                option.text = `${originalText} (Cost: $${upgradeCost.toFixed(2)} - Insufficient balance)`;
            } else {
                option.disabled = false;
                option.text = `${originalText} (Upgrade cost: $${upgradeCost.toFixed(2)})`;
            }
        }

        for (let i = 0; i < options.length; i++) {
            if (!options[i].disabled) {
                packageSelect.selectedIndex = i;
                firstAvailableFound = true;
                break;
            }
        }

        document.getElementById('save-btn').disabled = !firstAvailableFound;

        document.getElementById('package-modal').classList.remove('hidden');
    }

    function closeModal() {
        document.getElementById('package-modal').classList.add('hidden');
    }

    document.getElementById('save-btn').addEventListener('click', function() {
        const packageId = document.getElementById('package-select').value;
        const deductWallet = document.getElementById('deduct-wallet').checked;

        fetch('api/admin_update_package.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    user_id: currentUserId,
                    package_id: packageId,
                    deduct_wallet: deductWallet
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showFeedback('Package updated successfully! The page will now reload.', 'success');
                    setTimeout(() => window.location.reload(), 2000);
                } else {
                    showFeedback(data.error || 'An unknown error occurred.', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showFeedback('A network error occurred.', 'error');
            });
    });

    function showFeedback(message, type) {
        const feedback = document.getElementById('feedback-message');
        feedback.textContent = message;
        feedback.className = type === 'success' ? 'p-4 mb-4 text-sm text-green-800 rounded-lg bg-green-50' : 'p-4 mb-4 text-sm text-red-800 rounded-lg bg-red-50';
        feedback.classList.remove('hidden');
        closeModal();
    }

    // Search and Filter Functions
    function applyFilters() {
        const searchName = document.getElementById('search_name').value.trim();
        const searchUsername = document.getElementById('search_username').value.trim();
        const filterPackage = document.getElementById('filter_package').value;

        // Build URL with parameters
        const params = new URLSearchParams();
        if (searchName) params.append('search_name', searchName);
        if (searchUsername) params.append('search_username', searchUsername);
        if (filterPackage) params.append('filter_package', filterPackage);

        // Redirect with filters
        const url = 'manage_investments.php' + (params.toString() ? '?' + params.toString() : '');
        window.location.href = url;
    }

    function clearFilters() {
        // Clear all input fields
        document.getElementById('search_name').value = '';
        document.getElementById('search_username').value = '';
        document.getElementById('filter_package').value = '';

        // Redirect to page without parameters
        window.location.href = 'manage_investments.php';
    }

    // Add Enter key support for search inputs
    document.addEventListener('DOMContentLoaded', function() {
        const searchInputs = ['search_name', 'search_username'];
        searchInputs.forEach(function(inputId) {
            document.getElementById(inputId).addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    applyFilters();
                }
            });
        });

        // Add change event for package filter
        document.getElementById('filter_package').addEventListener('change', function() {
            applyFilters();
        });
    });
</script>

<?php
// Capture the page content
$page_content = ob_get_clean();

// Include the admin layout
include 'admin/includes/layout.php';
?>