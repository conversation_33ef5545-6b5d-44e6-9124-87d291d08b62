<?php
session_start();

// Check if user is admin
if (!isset($_SESSION['user_id']) || !$_SESSION['is_admin']) {
    header("Location: login.html");
    exit();
}

include 'dbcon.php';
include 'settings_helper.php';

// Get price history for graphing
$period = $_GET['period'] ?? 'all';
$limit = intval($_GET['limit'] ?? 50);

$price_history = getCoinPriceHistory($conn, $limit, $period);

// Prepare data for Chart.js
$chart_data = [
    'labels' => [],
    'prices' => [],
    'changes' => []
];

foreach (array_reverse($price_history) as $record) {
    $chart_data['labels'][] = date('M j, H:i', strtotime($record['created_at']));
    $chart_data['prices'][] = floatval($record['price']);
    $chart_data['changes'][] = floatval($record['change_percentage'] ?? 0);
}

$current_price = getSetting($conn, 'staking_coin_price', 0.45);

// Page configuration
$page_title = "Coin Price Trajectory";
$additional_css = '
<style>
    /* Chart container styling */
    .chart-container {
        background: white;
        border-radius: 1rem;
        padding: 2rem;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        margin-bottom: 2rem;
    }

    /* Filter controls */
    .filter-controls {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 1rem;
        padding: 1.5rem;
        margin-bottom: 2rem;
        color: white;
    }

    .filter-btn {
        background: rgba(255, 255, 255, 0.2);
        color: white;
        border: 2px solid rgba(255, 255, 255, 0.3);
        padding: 0.5rem 1rem;
        border-radius: 0.5rem;
        font-weight: 600;
        transition: all 0.3s ease;
        cursor: pointer;
        text-decoration: none;
        display: inline-block;
        margin-right: 0.5rem;
        margin-bottom: 0.5rem;
    }

    .filter-btn:hover {
        background: rgba(255, 255, 255, 0.3);
        border-color: rgba(255, 255, 255, 0.5);
        transform: translateY(-1px);
    }

    .filter-btn.active {
        background: rgba(255, 255, 255, 0.9);
        color: #667eea;
        border-color: rgba(255, 255, 255, 0.9);
    }

    /* Statistics cards */
    .stat-card {
        background: white;
        border-radius: 0.75rem;
        padding: 1.5rem;
        box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
        transition: all 0.2s ease;
    }

    .stat-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    }

    /* Chart responsive container */
    .chart-responsive {
        position: relative;
        height: 400px;
        width: 100%;
    }

    @media (max-width: 768px) {
        .chart-responsive {
            height: 300px;
        }
    }
</style>
';

// Start output buffering to capture the page content
ob_start();
?>

<!-- Page Header -->
<div class="flex flex-col lg:flex-row justify-between items-start lg:items-center space-y-4 lg:space-y-0 mb-6">
    <div>
        <h1 class="text-3xl font-bold text-gray-900">Coin Price Trajectory</h1>
        <p class="text-gray-600 mt-1">Historical price analysis and trends</p>
    </div>
    <div class="flex items-center space-x-2 bg-blue-50 px-4 py-2 rounded-lg border border-blue-200">
        <i class="fas fa-chart-line text-blue-600"></i>
        <span class="text-sm font-medium text-blue-700">Price Analytics</span>
    </div>
</div>
<!-- Filter Controls -->
<div class="filter-controls">
    <div class="flex items-center justify-between mb-4">
        <div class="flex items-center space-x-3">
            <div class="w-10 h-10 bg-white bg-opacity-20 rounded-xl flex items-center justify-center">
                <i class="fas fa-filter text-white text-lg"></i>
            </div>
            <div>
                <h3 class="text-lg font-semibold text-white">Chart Filters</h3>
                <p class="text-blue-100 text-sm">Customize your price analysis view</p>
            </div>
        </div>
    </div>

    <div class="flex flex-wrap items-center gap-4">
        <div>
            <label class="block text-sm font-medium text-white mb-2">
                <i class="fas fa-calendar mr-2"></i>Time Period
            </label>
            <select id="periodSelect" class="bg-white bg-opacity-20 border-2 border-white border-opacity-30 text-white rounded-lg px-3 py-2 text-sm focus:outline-none focus:border-opacity-50">
                <option value="all" <?php echo $period === 'all' ? 'selected' : ''; ?>>All Time</option>
                <option value="year" <?php echo $period === 'year' ? 'selected' : ''; ?>>Last Year</option>
                <option value="month" <?php echo $period === 'month' ? 'selected' : ''; ?>>Last Month</option>
                <option value="week" <?php echo $period === 'week' ? 'selected' : ''; ?>>Last Week</option>
                <option value="day" <?php echo $period === 'day' ? 'selected' : ''; ?>>Last Day</option>
            </select>
        </div>
        <div>
            <label class="block text-sm font-medium text-white mb-2">
                <i class="fas fa-list mr-2"></i>Records Limit
            </label>
            <select id="limitSelect" class="bg-white bg-opacity-20 border-2 border-white border-opacity-30 text-white rounded-lg px-3 py-2 text-sm focus:outline-none focus:border-opacity-50">
                <option value="20" <?php echo $limit === 20 ? 'selected' : ''; ?>>20 Records</option>
                <option value="50" <?php echo $limit === 50 ? 'selected' : ''; ?>>50 Records</option>
                <option value="100" <?php echo $limit === 100 ? 'selected' : ''; ?>>100 Records</option>
                <option value="200" <?php echo $limit === 200 ? 'selected' : ''; ?>>200 Records</option>
            </select>
        </div>
    </div>
</div>

<!-- Current Price Stats -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
    <div class="stat-card">
        <div class="flex items-center">
            <div class="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">
                <i class="fas fa-coins text-blue-600 text-xl"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">Current Price</p>
                <p class="text-2xl font-bold text-gray-900">$<?php echo number_format($current_price, 4); ?></p>
            </div>
        </div>
    </div>

    <?php if (!empty($price_history)): ?>
        <?php
        $latest = $price_history[0];
        $oldest = end($price_history);
        $total_change = $current_price - $oldest['price'];
        $total_change_percent = $oldest['price'] > 0 ? ($total_change / $oldest['price']) * 100 : 0;
        $highest = max(array_column($price_history, 'price'));
        $lowest = min(array_column($price_history, 'price'));
        ?>
        <div class="stat-card">
            <div class="flex items-center">
                <div class="w-12 h-12 <?php echo $total_change >= 0 ? 'bg-green-100' : 'bg-red-100'; ?> rounded-xl flex items-center justify-center">
                    <i class="fas fa-<?php echo $total_change >= 0 ? 'arrow-up' : 'arrow-down'; ?> <?php echo $total_change >= 0 ? 'text-green-600' : 'text-red-600'; ?> text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Period Change</p>
                    <p class="text-2xl font-bold <?php echo $total_change >= 0 ? 'text-green-600' : 'text-red-600'; ?>">
                        <?php echo ($total_change >= 0 ? '+' : '') . number_format($total_change_percent, 2); ?>%
                    </p>
                </div>
            </div>
        </div>

        <div class="stat-card">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center">
                    <i class="fas fa-chart-line text-purple-600 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Highest</p>
                    <p class="text-2xl font-bold text-gray-900">$<?php echo number_format($highest, 4); ?></p>
                </div>
            </div>
        </div>

        <div class="stat-card">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-orange-100 rounded-xl flex items-center justify-center">
                    <i class="fas fa-chart-line text-orange-600 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Lowest</p>
                    <p class="text-2xl font-bold text-gray-900">$<?php echo number_format($lowest, 4); ?></p>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>

<!-- Price Chart -->
<div class="chart-container">
    <div class="flex items-center space-x-3 mb-6">
        <div class="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
            <i class="fas fa-chart-line text-white text-sm"></i>
        </div>
        <div>
            <h3 class="text-lg font-semibold text-gray-900">Price History Chart</h3>
            <p class="text-sm text-gray-600">Historical price movements over time</p>
        </div>
    </div>
    <div class="chart-responsive">
        <canvas id="priceChart"></canvas>
    </div>
</div>

<!-- Change Percentage Chart -->
<div class="chart-container">
    <div class="flex items-center space-x-3 mb-6">
        <div class="w-8 h-8 bg-gradient-to-br from-green-500 to-blue-600 rounded-lg flex items-center justify-center">
            <i class="fas fa-percentage text-white text-sm"></i>
        </div>
        <div>
            <h3 class="text-lg font-semibold text-gray-900">Price Change Percentage</h3>
            <p class="text-sm text-gray-600">Percentage changes between price updates</p>
        </div>
    </div>
    <div class="chart-responsive" style="height: 300px;">
        <canvas id="changeChart"></canvas>
    </div>
</div>

<!-- Recent Changes Table -->
<div class="bg-white border border-gray-200 rounded-lg p-6">
    <h3 class="text-xl font-semibold text-gray-800 mb-4">Recent Price Changes</h3>
    <?php if (!empty($price_history)): ?>
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Date</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Price</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Change</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Reason</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Updated By</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <?php foreach (array_slice($price_history, 0, 10) as $record): ?>
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                <?php echo date('M j, Y H:i', strtotime($record['created_at'])); ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                $<?php echo number_format($record['price'], 4); ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm">
                                <?php if ($record['change_percentage']): ?>
                                    <span class="<?php echo $record['change_percentage'] >= 0 ? 'text-green-600' : 'text-red-600'; ?>">
                                        <?php echo ($record['change_percentage'] >= 0 ? '+' : '') . number_format($record['change_percentage'], 2); ?>%
                                    </span>
                                <?php else: ?>
                                    <span class="text-gray-400">-</span>
                                <?php endif; ?>
                            </td>
                            <td class="px-6 py-4 text-sm text-gray-500 max-w-xs truncate">
                                <?php echo htmlspecialchars($record['reason'] ?? '-'); ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <?php echo htmlspecialchars($record['updated_by_name'] ?? 'System'); ?>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    <?php else: ?>
        <p class="text-gray-500">No price history available.</p>
    <?php endif; ?>
</div>
</div>
</div>

<script>
    // Chart data from PHP
    const chartData = <?php echo json_encode($chart_data); ?>;

    // Price History Chart
    const priceCtx = document.getElementById('priceChart').getContext('2d');
    const priceChart = new Chart(priceCtx, {
        type: 'line',
        data: {
            labels: chartData.labels,
            datasets: [{
                label: 'Coin Price ($)',
                data: chartData.prices,
                borderColor: 'rgb(59, 130, 246)',
                backgroundColor: 'rgba(59, 130, 246, 0.1)',
                borderWidth: 2,
                fill: true,
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: false,
                    ticks: {
                        callback: function(value) {
                            return '$' + value.toFixed(4);
                        }
                    }
                }
            },
            plugins: {
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return 'Price: $' + context.parsed.y.toFixed(4);
                        }
                    }
                }
            }
        }
    });

    // Change Percentage Chart
    const changeCtx = document.getElementById('changeChart').getContext('2d');
    const changeChart = new Chart(changeCtx, {
        type: 'bar',
        data: {
            labels: chartData.labels,
            datasets: [{
                label: 'Change %',
                data: chartData.changes,
                backgroundColor: chartData.changes.map(change =>
                    change >= 0 ? 'rgba(34, 197, 94, 0.6)' : 'rgba(239, 68, 68, 0.6)'
                ),
                borderColor: chartData.changes.map(change =>
                    change >= 0 ? 'rgb(34, 197, 94)' : 'rgb(239, 68, 68)'
                ),
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    ticks: {
                        callback: function(value) {
                            return value.toFixed(2) + '%';
                        }
                    }
                }
            },
            plugins: {
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return 'Change: ' + context.parsed.y.toFixed(2) + '%';
                        }
                    }
                }
            }
        }
    });

    // Filter controls
    document.getElementById('periodSelect').addEventListener('change', function() {
        updateChart();
    });

    document.getElementById('limitSelect').addEventListener('change', function() {
        updateChart();
    });

    function updateChart() {
        const period = document.getElementById('periodSelect').value;
        const limit = document.getElementById('limitSelect').value;
        window.location.href = `?period=${period}&limit=${limit}`;
    }
</script>

<?php
// Capture the page content
$page_content = ob_get_clean();

// Close database connection
$conn->close();

// Include the admin layout
include 'admin/includes/layout.php';
?>