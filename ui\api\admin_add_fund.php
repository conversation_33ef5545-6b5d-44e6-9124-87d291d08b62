<?php
session_start();
header('Content-Type: application/json');

// Check if user is an authenticated admin
if (!isset($_SESSION['user_id']) || !$_SESSION['is_admin']) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Unauthorized access.']);
    exit();
}

$data = json_decode(file_get_contents('php://input'), true);

$user_id = $data['user_id'] ?? null;
$amount = $data['amount'] ?? null;

if (!$user_id || !$amount) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'User ID and amount are required.']);
    exit();
}

if (!is_numeric($amount) || $amount <= 0 || fmod($amount, 10) != 0) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Invalid amount. Must be a positive multiple of 10.']);
    exit();
}

include '../dbcon.php';

$admin_id = $_SESSION['user_id'];

// Start transaction
$conn->begin_transaction();

try {
    // 1. Add fund to user's deposit wallet
    $update_sql = 'UPDATE users SET deposit_wallet_balance = deposit_wallet_balance + ? WHERE id = ?';
    $stmt_update = $conn->prepare($update_sql);
    if ($stmt_update === false)
        throw new Exception('Failed to prepare user update statement.');
    $stmt_update->bind_param('di', $amount, $user_id);
    if (!$stmt_update->execute())
        throw new Exception('Failed to update user balance.');

    if ($stmt_update->affected_rows === 0) {
        throw new Exception('User not found or balance not updated.');
    }
    $stmt_update->close();

    // 2. Log the transaction in wallet_history
    $description = "Fund added by admin (ID: {$admin_id})";
    // Use related_user_id to store the admin's ID
    $log_sql = "INSERT INTO wallet_history (user_id, amount, type, wallet_type, description, related_user_id) VALUES (?, ?, 'deposit', 'deposit', ?, ?)";
    $stmt_log = $conn->prepare($log_sql);
    if ($stmt_log === false)
        throw new Exception('Failed to prepare log statement.');
    $stmt_log->bind_param('idsi', $user_id, $amount, $description, $admin_id);
    if (!$stmt_log->execute())
        throw new Exception('Failed to log transaction.');
    $stmt_log->close();

    // Commit transaction
    $conn->commit();

    echo json_encode(['success' => true, 'message' => 'Fund added successfully.']);
} catch (Exception $e) {
    $conn->rollback();
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => $e->getMessage()]);
} finally {
    $conn->close();
}
?>
