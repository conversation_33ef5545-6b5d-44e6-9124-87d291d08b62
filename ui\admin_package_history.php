<?php
session_start();

// Admin authentication check
if (!isset($_SESSION['user_id']) || !$_SESSION['is_admin']) {
    header("Location: login.html");
    exit();
}

include 'dbcon.php';

$search_user = $_GET['search'] ?? '';
$status_filter = $_GET['status'] ?? 'all';
$limit = 50; // Records per page
$page = max(1, intval($_GET['page'] ?? 1));
$offset = ($page - 1) * $limit;

// Build WHERE clause
$where_conditions = [];
$params = [];
$param_types = '';

if (!empty($search_user)) {
    $where_conditions[] = "(u.name LIKE ? OR u.user_id LIKE ?)";
    $search_term = "%$search_user%";
    $params[] = $search_term;
    $params[] = $search_term;
    $param_types .= 'ss';
}

if ($status_filter !== 'all') {
    $where_conditions[] = "ph.status = ?";
    $params[] = $status_filter;
    $param_types .= 's';
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// Get package history with user details
$history_sql = "SELECT ph.id, ph.user_id, ph.package_name, ph.package_amount, ph.profit_percentage,
                ph.wallet_type, ph.purchased_at, ph.expired_at, ph.total_earnings_received,
                ph.earnings_limit, ph.status, ph.duration_days, ph.expiry_reason, ph.notes,
                u.name as user_name, u.user_id as user_identifier,
                CASE
                    WHEN ph.status = 'active' THEN DATEDIFF(NOW(), ph.purchased_at)
                    WHEN ph.duration_days IS NOT NULL THEN ph.duration_days
                    ELSE DATEDIFF(COALESCE(ph.expired_at, NOW()), ph.purchased_at)
                END as calculated_duration,
                CASE
                    WHEN ph.status = 'active' THEN (ph.total_earnings_received / ph.earnings_limit) * 100
                    ELSE 100
                END as progress_percentage
                FROM package_history ph
                JOIN users u ON ph.user_id = u.id
                $where_clause
                ORDER BY ph.purchased_at DESC
                LIMIT ? OFFSET ?";

$stmt = $conn->prepare($history_sql);
if (!empty($params)) {
    $params[] = $limit;
    $params[] = $offset;
    $param_types .= 'ii';
    $stmt->bind_param($param_types, ...$params);
} else {
    $stmt->bind_param('ii', $limit, $offset);
}

$stmt->execute();
$history_result = $stmt->get_result();
$package_history = $history_result->fetch_all(MYSQLI_ASSOC);
$stmt->close();

// Get total count for pagination
$count_sql = "SELECT COUNT(*) as total FROM package_history ph JOIN users u ON ph.user_id = u.id $where_clause";
$stmt = $conn->prepare($count_sql);
if (!empty($params) && count($params) > 2) {
    // Remove limit and offset from params for count query
    $count_params = array_slice($params, 0, -2);
    $count_param_types = substr($param_types, 0, -2);
    $stmt->bind_param($count_param_types, ...$count_params);
}
$stmt->execute();
$count_result = $stmt->get_result();
$total_records = $count_result->fetch_assoc()['total'];
$stmt->close();

$total_pages = ceil($total_records / $limit);

// Get summary statistics
$stats_sql = "SELECT
                COUNT(*) as total_packages,
                SUM(ph.package_amount) as total_invested,
                SUM(ph.total_earnings_received) as total_earned,
                SUM(CASE WHEN ph.status = 'active' THEN 1 ELSE 0 END) as active_packages,
                SUM(CASE WHEN ph.status = 'expired' THEN 1 ELSE 0 END) as expired_packages,
                SUM(CASE WHEN ph.status = 'upgraded' THEN 1 ELSE 0 END) as upgraded_packages
              FROM package_history ph
              JOIN users u ON ph.user_id = u.id
              $where_clause";

$stmt = $conn->prepare($stats_sql);
if (!empty($params) && count($params) > 2) {
    $stats_params = array_slice($params, 0, -2);
    $stats_param_types = substr($param_types, 0, -2);
    $stmt->bind_param($stats_param_types, ...$stats_params);
}
$stmt->execute();
$stats_result = $stmt->get_result();
$stats = $stats_result->fetch_assoc();
$stmt->close();

// Page configuration
$page_title = "Package History Management";
$additional_css = '
<style>
    /* Enhanced table styling */
    .admin-table {
        width: 100%;
        border-collapse: separate;
        border-spacing: 0;
        background: white;
    }

    .admin-table thead {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    .admin-table thead th {
        color: white;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.05em;
        padding: 1rem 1.5rem;
        font-size: 0.75rem;
        border: none;
        position: relative;
        text-align: left;
    }

    .admin-table thead th:last-child {
        text-align: center;
    }

    .admin-table tbody tr {
        transition: all 0.2s ease;
        border-bottom: 1px solid #e2e8f0;
    }

    .admin-table tbody tr:hover {
        background-color: #f8fafc;
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    .admin-table tbody tr:last-child {
        border-bottom: none;
    }

    .admin-table tbody td {
        padding: 1rem 1.5rem;
        vertical-align: middle;
        border: none;
        background-color: white;
    }

    .admin-table tbody tr:hover td {
        background-color: #f8fafc;
    }

    /* Status badges */
    .status-badge {
        display: inline-flex;
        align-items: center;
        padding: 0.25rem 0.75rem;
        border-radius: 9999px;
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.05em;
    }

    .status-active {
        background-color: #d1fae5;
        color: #065f46;
        border: 1px solid #10b981;
    }

    .status-expired {
        background-color: #fee2e2;
        color: #991b1b;
        border: 1px solid #ef4444;
    }

    .status-upgraded {
        background-color: #ddd6fe;
        color: #5b21b6;
        border: 1px solid #8b5cf6;
    }

    /* Progress bars */
    .progress-bar {
        width: 100%;
        height: 0.5rem;
        background-color: #e5e7eb;
        border-radius: 9999px;
        overflow: hidden;
    }

    .progress-fill {
        height: 100%;
        background: linear-gradient(90deg, #10b981, #059669);
        border-radius: 9999px;
        transition: width 0.3s ease;
    }

    /* Filter section */
    .filter-container {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 0.75rem;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
    }

    .filter-input {
        background: rgba(255, 255, 255, 0.1);
        border: 2px solid rgba(255, 255, 255, 0.2);
        color: white;
        border-radius: 0.5rem;
        padding: 0.75rem 1rem;
        transition: all 0.3s ease;
    }

    .filter-input::placeholder {
        color: rgba(255, 255, 255, 0.7);
    }

    .filter-input:focus {
        outline: none;
        border-color: rgba(255, 255, 255, 0.5);
        background: rgba(255, 255, 255, 0.15);
        box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.1);
    }

    .filter-select {
        background: rgba(255, 255, 255, 0.1);
        border: 2px solid rgba(255, 255, 255, 0.2);
        color: white;
        border-radius: 0.5rem;
        padding: 0.75rem 1rem;
        transition: all 0.3s ease;
    }

    .filter-select:focus {
        outline: none;
        border-color: rgba(255, 255, 255, 0.5);
        background: rgba(255, 255, 255, 0.15);
    }

    .filter-btn {
        background: rgba(255, 255, 255, 0.2);
        color: white;
        border: 2px solid rgba(255, 255, 255, 0.3);
        padding: 0.75rem 1.5rem;
        border-radius: 0.5rem;
        font-weight: 600;
        transition: all 0.3s ease;
        cursor: pointer;
    }

    .filter-btn:hover {
        background: rgba(255, 255, 255, 0.3);
        border-color: rgba(255, 255, 255, 0.5);
        transform: translateY(-1px);
    }

    /* Pagination */
    .pagination-btn {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        padding: 0.5rem 1rem;
        border-radius: 0.375rem;
        font-weight: 500;
        font-size: 0.875rem;
        transition: all 0.2s ease;
        border: 1px solid #e2e8f0;
        background: white;
        color: #374151;
        text-decoration: none;
    }

    .pagination-btn:hover {
        background: #f8fafc;
        border-color: #d1d5db;
        transform: translateY(-1px);
    }

    .pagination-btn.active {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-color: #667eea;
    }

    .pagination-btn:disabled {
        opacity: 0.5;
        cursor: not-allowed;
    }
</style>
';

// Start output buffering to capture the page content
ob_start();

$conn->close();
?>

<!-- Page Header -->
<div class="flex flex-col lg:flex-row justify-between items-start lg:items-center space-y-4 lg:space-y-0 mb-6">
    <div>
        <h1 class="text-3xl font-bold text-gray-900">Package History Management</h1>
        <p class="text-gray-600 mt-1">Complete history of all user package purchases and performance</p>
    </div>
    <div class="flex items-center space-x-2 bg-blue-50 px-4 py-2 rounded-lg border border-blue-200">
        <i class="fas fa-history text-blue-600"></i>
        <span class="text-sm font-medium text-blue-700">Package History</span>
    </div>
</div>

<!-- Summary Statistics -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-6 mb-6">
    <div class="admin-card p-6">
        <div class="flex items-center">
            <div class="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">
                <i class="fas fa-box text-blue-600 text-xl"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">Total Packages</p>
                <p class="text-2xl font-bold text-gray-900"><?php echo number_format($stats['total_packages']); ?></p>
            </div>
        </div>
    </div>

    <div class="admin-card p-6">
        <div class="flex items-center">
            <div class="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center">
                <i class="fas fa-dollar-sign text-green-600 text-xl"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">Total Invested</p>
                <p class="text-2xl font-bold text-gray-900">$<?php echo number_format($stats['total_invested'], 0); ?></p>
            </div>
        </div>
    </div>

    <div class="admin-card p-6">
        <div class="flex items-center">
            <div class="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center">
                <i class="fas fa-chart-line text-purple-600 text-xl"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">Total Earned</p>
                <p class="text-2xl font-bold text-gray-900">$<?php echo number_format($stats['total_earned'], 0); ?></p>
            </div>
        </div>
    </div>

    <div class="admin-card p-6">
        <div class="flex items-center">
            <div class="w-12 h-12 bg-yellow-100 rounded-xl flex items-center justify-center">
                <i class="fas fa-play text-yellow-600 text-xl"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">Active</p>
                <p class="text-2xl font-bold text-gray-900"><?php echo number_format($stats['active_packages']); ?></p>
            </div>
        </div>
    </div>

    <div class="admin-card p-6">
        <div class="flex items-center">
            <div class="w-12 h-12 bg-red-100 rounded-xl flex items-center justify-center">
                <i class="fas fa-times-circle text-red-600 text-xl"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">Expired</p>
                <p class="text-2xl font-bold text-gray-900"><?php echo number_format($stats['expired_packages']); ?></p>
            </div>
        </div>
    </div>

    <div class="admin-card p-6">
        <div class="flex items-center">
            <div class="w-12 h-12 bg-indigo-100 rounded-xl flex items-center justify-center">
                <i class="fas fa-arrow-up text-indigo-600 text-xl"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">Upgraded</p>
                <p class="text-2xl font-bold text-gray-900"><?php echo number_format($stats['upgraded_packages']); ?></p>
            </div>
        </div>
    </div>
</div>

<!-- Filter Section -->
<div class="filter-container">
    <div class="flex items-center justify-between mb-4">
        <div class="flex items-center space-x-3">
            <div class="w-10 h-10 bg-white bg-opacity-20 rounded-xl flex items-center justify-center">
                <i class="fas fa-filter text-white text-lg"></i>
            </div>
            <div>
                <h3 class="text-lg font-semibold text-white">Filter Package History</h3>
                <p class="text-blue-100 text-sm">Search and filter package records</p>
            </div>
        </div>
    </div>

    <form method="GET" class="flex flex-wrap gap-4 items-end">
        <div class="flex-1 min-w-64">
            <label for="search" class="block text-sm font-medium text-white mb-2">
                <i class="fas fa-search mr-2"></i>Search User
            </label>
            <input type="text" id="search" name="search" value="<?php echo htmlspecialchars($search_user); ?>"
                placeholder="Search by name or user ID..."
                class="filter-input w-full">
        </div>
        <div>
            <label for="status" class="block text-sm font-medium text-white mb-2">
                <i class="fas fa-list mr-2"></i>Status Filter
            </label>
            <select id="status" name="status" class="filter-select">
                <option value="all" <?php echo $status_filter === 'all' ? 'selected' : ''; ?>>All Status</option>
                <option value="active" <?php echo $status_filter === 'active' ? 'selected' : ''; ?>>Active</option>
                <option value="expired" <?php echo $status_filter === 'expired' ? 'selected' : ''; ?>>Expired</option>
                <option value="upgraded" <?php echo $status_filter === 'upgraded' ? 'selected' : ''; ?>>Upgraded</option>
            </select>
        </div>
        <div class="flex items-center space-x-2">
            <button type="submit" class="filter-btn">
                <i class="fas fa-search mr-2"></i>
                Filter
            </button>
            <a href="admin_package_history.php" class="filter-btn">
                <i class="fas fa-undo mr-2"></i>
                Reset
            </a>
        </div>
    </form>
</div>

<!-- Package History Table -->
<div class="admin-card p-0 overflow-hidden">
    <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center space-x-3">
            <div class="w-8 h-8 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-lg flex items-center justify-center">
                <i class="fas fa-history text-white text-sm"></i>
            </div>
            <div>
                <h3 class="text-lg font-semibold text-gray-900">Package History Records</h3>
                <p class="text-sm text-gray-600">Complete history of user package investments and performance</p>
            </div>
        </div>
    </div>

    <div class="overflow-x-auto">
        <table class="admin-table">
            <thead>
                <tr>
                    <th>User</th>
                    <th>Package</th>
                    <th>Amount</th>
                    <th>Purchased</th>
                    <th>Duration</th>
                    <th>Earnings</th>
                    <th>Progress</th>
                    <th>Status</th>
                </tr>
            </thead>
            <tbody>
                <?php if (empty($package_history)): ?>
                    <tr>
                        <td colspan="8" style="text-align: center; padding: 3rem;">
                            <div class="flex flex-col items-center">
                                <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                                    <i class="fas fa-history text-gray-400 text-2xl"></i>
                                </div>
                                <h3 class="text-lg font-medium text-gray-900 mb-2">No Package History Found</h3>
                                <p class="text-sm text-gray-500 mb-4">No package history records match your search criteria.</p>
                            </div>
                        </td>
                    </tr>
                <?php else: ?>
                    <?php foreach ($package_history as $record): ?>
                        <tr>
                            <td>
                                <div class="flex items-center space-x-3">
                                    <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                        <i class="fas fa-user text-blue-600 text-xs"></i>
                                    </div>
                                    <div>
                                        <div class="text-sm font-medium text-gray-900"><?php echo htmlspecialchars($record['user_name']); ?></div>
                                        <div class="text-xs text-gray-500">ID: <?php echo htmlspecialchars($record['user_identifier']); ?></div>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div class="flex items-center space-x-2">
                                    <div class="w-6 h-6 bg-purple-100 rounded flex items-center justify-center">
                                        <i class="fas fa-box text-purple-600 text-xs"></i>
                                    </div>
                                    <div>
                                        <div class="text-sm font-medium text-gray-900"><?php echo htmlspecialchars($record['package_name']); ?></div>
                                        <div class="text-xs text-gray-500"><?php echo $record['profit_percentage']; ?>% daily</div>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div class="text-center">
                                    <div class="text-lg font-bold text-green-600">$<?php echo number_format($record['package_amount'], 2); ?></div>
                                    <div class="text-xs text-gray-500">Limit: $<?php echo number_format($record['earnings_limit'], 2); ?></div>
                                </div>
                            </td>
                            <td>
                                <div class="text-center">
                                    <div class="text-sm font-medium text-gray-900"><?php echo date('M j, Y', strtotime($record['purchased_at'])); ?></div>
                                    <div class="text-xs text-gray-500"><?php echo date('g:i A', strtotime($record['purchased_at'])); ?></div>
                                </div>
                            </td>
                            <td>
                                <div class="text-center">
                                    <div class="text-sm font-medium text-gray-900"><?php echo $record['calculated_duration']; ?> days</div>
                                    <?php if ($record['expired_at']): ?>
                                        <div class="text-xs text-gray-500">Ended: <?php echo date('M j', strtotime($record['expired_at'])); ?></div>
                                    <?php endif; ?>
                                </div>
                            </td>
                            <td>
                                <div class="text-center">
                                    <div class="text-lg font-bold text-purple-600">$<?php echo number_format($record['total_earnings_received'], 2); ?></div>
                                    <?php
                                    $roi = $record['package_amount'] > 0 ? ($record['total_earnings_received'] / $record['package_amount']) * 100 : 0;
                                    ?>
                                    <div class="text-xs text-gray-500"><?php echo number_format($roi, 1); ?>% ROI</div>
                                </div>
                            </td>
                            <td>
                                <div class="text-center">
                                    <div class="progress-bar mb-2">
                                        <div class="progress-fill" style="width: <?php echo min($record['progress_percentage'], 100); ?>%"></div>
                                    </div>
                                    <div class="text-xs font-medium text-gray-600"><?php echo number_format($record['progress_percentage'], 1); ?>%</div>
                                </div>
                            </td>
                            <td>
                                <div class="text-center">
                                    <span class="status-badge status-<?php echo $record['status']; ?>">
                                        <?php echo ucfirst($record['status']); ?>
                                    </span>
                                    <?php if ($record['expiry_reason']): ?>
                                        <div class="text-xs text-gray-400 mt-1">
                                            <?php
                                            $reason_text = [
                                                'limit_reached' => 'Limit reached',
                                                'upgraded' => 'Upgraded',
                                                'manual' => 'Manual'
                                            ];
                                            echo $reason_text[$record['expiry_reason']] ?? $record['expiry_reason'];
                                            ?>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                <?php endif; ?>
            </tbody>
        </table>
    </div>

    <!-- Enhanced Pagination -->
    <?php if ($total_pages > 1): ?>
        <div class="px-6 py-4 border-t border-gray-200 bg-gray-50">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-2">
                    <i class="fas fa-info-circle text-blue-600"></i>
                    <span class="text-sm text-gray-700">
                        Showing <?php echo $offset + 1; ?> to <?php echo min($offset + $limit, $total_records); ?> of <?php echo number_format($total_records); ?> results
                    </span>
                </div>
                <div class="flex items-center space-x-1">
                    <?php if ($page > 1): ?>
                        <a href="?page=<?php echo $page - 1; ?>&search=<?php echo urlencode($search_user); ?>&status=<?php echo urlencode($status_filter); ?>"
                            class="pagination-btn">
                            <i class="fas fa-chevron-left mr-1"></i>
                            Previous
                        </a>
                    <?php endif; ?>

                    <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                        <a href="?page=<?php echo $i; ?>&search=<?php echo urlencode($search_user); ?>&status=<?php echo urlencode($status_filter); ?>"
                            class="pagination-btn <?php echo $i === $page ? 'active' : ''; ?>">
                            <?php echo $i; ?>
                        </a>
                    <?php endfor; ?>

                    <?php if ($page < $total_pages): ?>
                        <a href="?page=<?php echo $page + 1; ?>&search=<?php echo urlencode($search_user); ?>&status=<?php echo urlencode($status_filter); ?>"
                            class="pagination-btn">
                            Next
                            <i class="fas fa-chevron-right ml-1"></i>
                        </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>

<?php
// Capture the page content
$page_content = ob_get_clean();

// Include the admin layout
include 'admin/includes/layout.php';
?>