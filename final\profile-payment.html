<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Methods - Crypto Wallet</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <style>
        body {
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
            min-height: 100vh;
        }

        .stats-card {
            background: rgba(30, 41, 59, 0.4);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(59, 130, 246, 0.2);
            border-radius: 0.75rem;
            padding: 1rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }

        .payment-card {
            background: rgba(15, 23, 42, 0.8);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(59, 130, 246, 0.3);
            border-radius: 0.75rem;
            padding: 1rem;
            transition: all 0.3s ease;
        }

        .payment-card:hover {
            border-color: rgba(6, 182, 212, 0.6);
            background: rgba(15, 23, 42, 0.9);
            transform: translateY(-2px);
        }

        .input-field {
            background: rgba(15, 23, 42, 0.8);
            border: 1px solid rgba(59, 130, 246, 0.3);
            border-radius: 0.5rem;
            padding: 0.75rem;
            color: white;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .input-field:focus {
            outline: none;
            border-color: rgba(6, 182, 212, 0.6);
            box-shadow: 0 0 0 3px rgba(6, 182, 212, 0.15);
            background: rgba(15, 23, 42, 0.9);
        }

        .input-field:disabled {
            background: rgba(15, 23, 42, 0.5);
            border-color: rgba(59, 130, 246, 0.2);
            color: rgba(255, 255, 255, 0.5);
            cursor: not-allowed;
        }

        .input-field::placeholder {
            color: rgba(148, 163, 184, 0.5);
        }

        .btn-primary {
            background: linear-gradient(135deg, #3b82f6, #06b6d4);
            border: none;
            border-radius: 0.5rem;
            padding: 0.75rem 1.5rem;
            color: white;
            font-weight: 600;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #2563eb, #0891b2);
            transform: translateY(-1px);
        }

        .btn-primary:disabled {
            background: linear-gradient(135deg, #64748b, #475569);
            cursor: not-allowed;
            transform: none;
        }

        .btn-secondary {
            background: rgba(30, 41, 59, 0.6);
            border: 1px solid rgba(59, 130, 246, 0.3);
            border-radius: 0.5rem;
            padding: 0.75rem 1.5rem;
            color: white;
            font-weight: 600;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .btn-secondary:hover {
            border-color: rgba(6, 182, 212, 0.6);
            background: rgba(30, 41, 59, 0.8);
        }

        /* Bottom Navigation Styles */
        .accent {
            color: #06b6d4 !important;
        }

        .hover\:accent:hover {
            color: #06b6d4 !important;
        }
    </style>
</head>

<body class="text-white">
    <div class="min-h-screen flex flex-col">
        <!-- Header -->
        <div class="sticky top-0 z-50 bg-slate-800/95 backdrop-blur-sm border-b border-slate-700">
            <div class="flex items-center justify-between px-6 py-4">
                <div class="flex items-center gap-4">
                    <button onclick="goBack()" class="w-10 h-10 rounded-full bg-slate-700 hover:bg-slate-600 flex items-center justify-center transition-colors">
                        <i class="fas fa-arrow-left text-slate-300"></i>
                    </button>
                    <div>
                        <h1 class="text-xl font-bold text-white">Payment Methods</h1>
                        <p class="text-sm text-slate-400">Manage your crypto wallet address</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-grow overflow-y-auto px-4 sm:px-6 py-4">
            <!-- Current Wallet Address -->
            <div class="stats-card mb-4" id="currentWalletSection">
                <div class="flex items-center gap-2 mb-4">
                    <div class="w-10 h-10 rounded-lg bg-gradient-to-r from-green-600 to-emerald-600 flex items-center justify-center">
                        <i class="fas fa-wallet text-white"></i>
                    </div>
                    <div>
                        <h2 class="text-lg font-bold text-white">Current Wallet Address</h2>
                        <p class="text-xs text-slate-400">Your registered withdrawal address</p>
                    </div>
                </div>
                
                <div class="payment-card">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center gap-3">
                            <i class="fab fa-ethereum text-yellow-400 text-xl"></i>
                            <div>
                                <p class="text-sm font-semibold text-white">BEP20 (USDT)</p>
                                <p class="text-xs text-slate-400 font-mono break-all">******************************************</p>
                            </div>
                        </div>
                        <div class="flex items-center gap-2">
                            <span class="text-xs text-green-400 bg-green-500/20 px-2 py-1 rounded-full">Verified</span>
                            <i class="fas fa-check-circle text-green-400"></i>
                        </div>
                    </div>
                </div>
                
                <div class="bg-gradient-to-r from-yellow-600/20 to-orange-600/20 rounded-lg p-3 border border-yellow-500/30 mt-3">
                    <div class="flex items-start gap-2">
                        <i class="fas fa-lock text-yellow-400 text-sm mt-0.5"></i>
                        <div>
                            <p class="text-sm font-semibold text-yellow-400">Security Notice</p>
                            <p class="text-xs text-slate-300">For security reasons, wallet addresses cannot be changed once set. Please contact support if you need assistance.</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Add Wallet Address (First Time) -->
            <div class="stats-card mb-4" id="addWalletSection" style="display: none;">
                <div class="flex items-center gap-2 mb-4">
                    <div class="w-10 h-10 rounded-lg bg-gradient-to-r from-blue-600 to-cyan-600 flex items-center justify-center">
                        <i class="fas fa-plus text-white"></i>
                    </div>
                    <div>
                        <h2 class="text-lg font-bold text-white">Add Wallet Address</h2>
                        <p class="text-xs text-slate-400">Set your withdrawal wallet address</p>
                    </div>
                </div>

                <form id="walletForm" class="space-y-4">
                    <!-- Wallet Type -->
                    <div>
                        <label class="block text-sm font-medium text-slate-300 mb-2">Wallet Type</label>
                        <select id="walletType" class="input-field w-full">
                            <option value="BEP20">BEP20 (USDT) - Binance Smart Chain</option>
                            <option value="ERC20">ERC20 (USDT) - Ethereum</option>
                            <option value="TRC20">TRC20 (USDT) - Tron</option>
                        </select>
                    </div>

                    <!-- Wallet Address -->
                    <div>
                        <label class="block text-sm font-medium text-slate-300 mb-2">Wallet Address</label>
                        <input type="text" id="walletAddress" class="input-field w-full" placeholder="Enter your wallet address (e.g., ******************************************)">
                        <p class="text-xs text-slate-500 mt-1 flex items-center gap-1">
                            <i class="fas fa-info-circle text-cyan-400"></i>
                            <span>Double-check your address. This cannot be changed later.</span>
                        </p>
                    </div>

                    <!-- Confirm Address -->
                    <div>
                        <label class="block text-sm font-medium text-slate-300 mb-2">Confirm Wallet Address</label>
                        <input type="text" id="confirmAddress" class="input-field w-full" placeholder="Re-enter your wallet address to confirm">
                    </div>

                    <!-- Security Checkbox -->
                    <div class="flex items-start gap-3">
                        <input type="checkbox" id="securityConfirm" class="mt-1 w-4 h-4 text-blue-600 bg-slate-700 border-slate-600 rounded focus:ring-blue-500">
                        <label for="securityConfirm" class="text-sm text-slate-300">
                            I understand that this wallet address cannot be changed once set for security reasons. I have double-checked the address and confirm it is correct.
                        </label>
                    </div>

                    <button type="button" onclick="addWalletAddress()" class="btn-primary w-full">
                        <i class="fas fa-save mr-2"></i>
                        Save Wallet Address
                    </button>
                </form>
            </div>

            <!-- Wallet Information -->
            <div class="stats-card mb-4">
                <div class="flex items-center gap-2 mb-4">
                    <div class="w-10 h-10 rounded-lg bg-gradient-to-r from-purple-600 to-pink-600 flex items-center justify-center">
                        <i class="fas fa-info-circle text-white"></i>
                    </div>
                    <div>
                        <h2 class="text-lg font-bold text-white">Important Information</h2>
                        <p class="text-xs text-slate-400">Please read before proceeding</p>
                    </div>
                </div>

                <div class="space-y-3">
                    <div class="bg-gradient-to-r from-slate-800/50 to-slate-700/50 rounded-lg p-3 border border-blue-500/20">
                        <div class="flex items-start gap-2">
                            <i class="fas fa-shield-alt text-blue-400 text-sm mt-0.5"></i>
                            <div>
                                <p class="text-sm font-semibold text-blue-400">Security First</p>
                                <p class="text-xs text-slate-300">Wallet addresses are locked after first setup to prevent unauthorized changes and protect your funds.</p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-gradient-to-r from-slate-800/50 to-slate-700/50 rounded-lg p-3 border border-cyan-500/20">
                        <div class="flex items-start gap-2">
                            <i class="fas fa-clock text-cyan-400 text-sm mt-0.5"></i>
                            <div>
                                <p class="text-sm font-semibold text-cyan-400">Processing Time</p>
                                <p class="text-xs text-slate-300">Withdrawals to your wallet address are processed within 24 hours after verification.</p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-gradient-to-r from-slate-800/50 to-slate-700/50 rounded-lg p-3 border border-green-500/20">
                        <div class="flex items-start gap-2">
                            <i class="fas fa-check-circle text-green-400 text-sm mt-0.5"></i>
                            <div>
                                <p class="text-sm font-semibold text-green-400">Supported Networks</p>
                                <p class="text-xs text-slate-300">We support BEP20, ERC20, and TRC20 USDT networks for withdrawals.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Contact Support -->
            <div class="stats-card mb-4">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-base font-bold text-white">Need Help?</h3>
                        <p class="text-xs text-slate-400">Contact support for wallet address changes</p>
                    </div>
                    <button onclick="contactSupport()" class="btn-secondary">
                        <i class="fas fa-headset mr-2"></i>
                        Contact Support
                    </button>
                </div>
            </div>
        </div>

        <!-- Sticky Bottom Navigation Bar -->
        <div class="sticky bottom-0 z-40 flex-shrink-0">
            <div class="bg-slate-800/95 backdrop-blur-sm mx-4 mb-4 rounded-2xl border border-slate-700">
                <div class="flex justify-around items-center h-16">
                    <a href="1.html" class="flex flex-col items-center gap-1 text-slate-400 hover:accent">
                        <i class="fas fa-home"></i>
                        <span class="text-xs">Home</span>
                    </a>
                    <a href="wallet.html" class="flex flex-col items-center gap-1 text-slate-400 hover:accent">
                        <i class="fas fa-wallet"></i>
                        <span class="text-xs">Wallet</span>
                    </a>
                    <a href="team.html" class="flex flex-col items-center gap-1 text-slate-400 hover:accent">
                        <i class="fas fa-users"></i>
                        <span class="text-xs">Team</span>
                    </a>
                    <a href="profile.html" class="flex flex-col items-center gap-1 accent">
                        <i class="fas fa-user"></i>
                        <span class="text-xs">Profile</span>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Check if user has wallet address set (simulate with localStorage)
        let hasWalletAddress = localStorage.getItem('walletAddress') !== null;

        function goBack() {
            window.history.back();
        }

        function showAddWalletForm() {
            document.getElementById('currentWalletSection').style.display = 'none';
            document.getElementById('addWalletSection').style.display = 'block';
        }

        function addWalletAddress() {
            const walletType = document.getElementById('walletType').value;
            const walletAddress = document.getElementById('walletAddress').value;
            const confirmAddress = document.getElementById('confirmAddress').value;
            const securityConfirm = document.getElementById('securityConfirm').checked;

            if (!walletAddress || !confirmAddress) {
                Swal.fire({
                    icon: 'error',
                    title: 'Missing Information',
                    text: 'Please fill in all required fields',
                    background: '#1e293b',
                    color: '#ffffff',
                    confirmButtonColor: '#3b82f6',
                    iconColor: '#3b82f6'
                });
                return;
            }

            if (walletAddress !== confirmAddress) {
                Swal.fire({
                    icon: 'error',
                    title: 'Address Mismatch',
                    text: 'Wallet addresses do not match. Please check and try again.',
                    background: '#1e293b',
                    color: '#ffffff',
                    confirmButtonColor: '#3b82f6',
                    iconColor: '#3b82f6'
                });
                return;
            }

            if (!securityConfirm) {
                Swal.fire({
                    icon: 'warning',
                    title: 'Security Confirmation Required',
                    text: 'Please confirm that you understand the security policy',
                    background: '#1e293b',
                    color: '#ffffff',
                    confirmButtonColor: '#f59e0b',
                    iconColor: '#f59e0b'
                });
                return;
            }

            // Validate wallet address format (basic validation)
            if (walletAddress.length < 26 || !walletAddress.startsWith('0x')) {
                Swal.fire({
                    icon: 'error',
                    title: 'Invalid Address',
                    text: 'Please enter a valid wallet address',
                    background: '#1e293b',
                    color: '#ffffff',
                    confirmButtonColor: '#3b82f6',
                    iconColor: '#3b82f6'
                });
                return;
            }

            Swal.fire({
                title: 'Confirm Wallet Address',
                html: `
                    <div class="text-left space-y-3">
                        <p class="text-slate-300">Please confirm your wallet details:</p>
                        <div class="bg-slate-800 p-3 rounded">
                            <p class="text-sm text-slate-400">Type: <span class="text-cyan-400">${walletType}</span></p>
                            <p class="text-sm text-slate-400">Address: <span class="text-cyan-400 font-mono break-all">${walletAddress}</span></p>
                        </div>
                        <p class="text-red-400 text-sm">⚠️ This address cannot be changed once saved!</p>
                    </div>
                `,
                icon: 'warning',
                showCancelButton: true,
                background: '#1e293b',
                color: '#ffffff',
                confirmButtonColor: '#06b6d4',
                cancelButtonColor: '#64748b',
                confirmButtonText: 'Save Address',
                cancelButtonText: 'Cancel',
                iconColor: '#f59e0b'
            }).then((result) => {
                if (result.isConfirmed) {
                    // Save wallet address
                    localStorage.setItem('walletAddress', walletAddress);
                    localStorage.setItem('walletType', walletType);
                    
                    Swal.fire({
                        icon: 'success',
                        title: 'Wallet Address Saved!',
                        text: 'Your withdrawal address has been set successfully',
                        background: '#1e293b',
                        color: '#ffffff',
                        confirmButtonColor: '#06b6d4',
                        iconColor: '#06b6d4',
                        timer: 2000,
                        showConfirmButton: false
                    }).then(() => {
                        location.reload();
                    });
                }
            });
        }

        function contactSupport() {
            window.location.href = 'contact-support.html';
        }

        // Initialize page based on wallet address status
        document.addEventListener('DOMContentLoaded', function() {
            if (!hasWalletAddress) {
                showAddWalletForm();
            }
        });
    </script>
</body>

</html>
