<?php
session_start();

// Check if user is admin
if (!isset($_SESSION['user_id']) || !$_SESSION['is_admin']) {
    header("Location: login.html");
    exit();
}

include 'dbcon.php';

echo "<h2>Create Coin Price History Table</h2>";
echo "<p>This will create a dedicated table to track all coin price changes over time for graphing and analysis.</p>";

// Create coin price history table
$create_table_sql = "
CREATE TABLE IF NOT EXISTS `coin_price_history` (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
  `price` decimal(10,4) NOT NULL,
  `previous_price` decimal(10,4) DEFAULT NULL,
  `change_amount` decimal(10,4) DEFAULT NULL,
  `change_percentage` decimal(8,4) DEFAULT NULL,
  `updated_by` int(11) UNSIGNED NOT NULL,
  `reason` varchar(255) DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  <PERSON><PERSON>Y `fk_coin_price_updated_by` (`updated_by`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
";

if ($conn->query($create_table_sql)) {
    echo "<p>✅ Coin price history table created successfully!</p>";
    
    // Check if there's already a current price in system_settings
    $current_price_sql = "SELECT setting_value FROM system_settings WHERE setting_key = 'staking_coin_price'";
    $current_result = $conn->query($current_price_sql);
    
    if ($current_result && $current_result->num_rows > 0) {
        $current_price = $current_result->fetch_assoc()['setting_value'];
        
        // Check if we already have this price in history
        $history_check = $conn->query("SELECT COUNT(*) as count FROM coin_price_history WHERE price = $current_price");
        $history_count = $history_check->fetch_assoc()['count'];
        
        if ($history_count == 0) {
            // Insert current price as the first historical record
            $admin_id = $_SESSION['user_id'];
            $insert_initial_sql = "INSERT INTO coin_price_history (price, updated_by, reason, created_at) VALUES (?, ?, 'Initial price setup', NOW())";
            $stmt = $conn->prepare($insert_initial_sql);
            $stmt->bind_param("di", $current_price, $admin_id);
            
            if ($stmt->execute()) {
                echo "<p>✅ Current price ($" . number_format($current_price, 4) . ") added to history!</p>";
            } else {
                echo "<p>❌ Error adding current price to history: " . $stmt->error . "</p>";
            }
            $stmt->close();
        } else {
            echo "<p>ℹ️ Current price already exists in history.</p>";
        }
    } else {
        echo "<p>⚠️ No current price found in system_settings. Please set an initial price.</p>";
    }
    
} else {
    echo "<p>❌ Error creating coin price history table: " . $conn->error . "</p>";
}

// Show current history
echo "<h3>Current Price History:</h3>";
$history_sql = "SELECT cph.*, u.name as updated_by_name 
                FROM coin_price_history cph 
                LEFT JOIN users u ON cph.updated_by = u.id 
                ORDER BY cph.created_at DESC 
                LIMIT 10";
$history_result = $conn->query($history_sql);

if ($history_result && $history_result->num_rows > 0) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #f0f0f0;'>";
    echo "<th>ID</th><th>Price</th><th>Previous Price</th><th>Change</th><th>Change %</th><th>Updated By</th><th>Reason</th><th>Date</th>";
    echo "</tr>";
    
    while ($row = $history_result->fetch_assoc()) {
        $change_color = '';
        if ($row['change_amount'] > 0) {
            $change_color = 'color: green;';
        } elseif ($row['change_amount'] < 0) {
            $change_color = 'color: red;';
        }
        
        echo "<tr>";
        echo "<td>{$row['id']}</td>";
        echo "<td><strong>$" . number_format($row['price'], 4) . "</strong></td>";
        echo "<td>" . ($row['previous_price'] ? "$" . number_format($row['previous_price'], 4) : '-') . "</td>";
        echo "<td style='$change_color'>" . ($row['change_amount'] ? ($row['change_amount'] > 0 ? '+' : '') . number_format($row['change_amount'], 4) : '-') . "</td>";
        echo "<td style='$change_color'>" . ($row['change_percentage'] ? ($row['change_percentage'] > 0 ? '+' : '') . number_format($row['change_percentage'], 2) . '%' : '-') . "</td>";
        echo "<td>" . htmlspecialchars($row['updated_by_name'] ?? 'System') . "</td>";
        echo "<td>" . htmlspecialchars($row['reason'] ?? '') . "</td>";
        echo "<td>" . date('M j, Y H:i:s', strtotime($row['created_at'])) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>No price history found.</p>";
}

echo "<hr>";
echo "<h3>Next Steps:</h3>";
echo "<ol>";
echo "<li><strong>Price history table created</strong> - Ready to track all price changes</li>";
echo "<li><strong>Update admin_coin_price.php</strong> - Will now create new rows instead of updating</li>";
echo "<li><strong>4 decimal precision</strong> - All prices will use 4 decimal places</li>";
echo "<li><strong>Graphing ready</strong> - Historical data available for charts and analysis</li>";
echo "</ol>";

echo "<div style='background: #e8f5e8; padding: 15px; border: 1px solid #4CAF50; margin: 20px 0;'>";
echo "<h4>✅ New Features:</h4>";
echo "<ul>";
echo "<li><strong>Historical Tracking</strong> - Every price change creates a new record</li>";
echo "<li><strong>Change Calculation</strong> - Automatic calculation of price changes and percentages</li>";
echo "<li><strong>4 Decimal Precision</strong> - More accurate pricing (e.g., $0.4567)</li>";
echo "<li><strong>Graph Ready</strong> - Data structure perfect for plotting price trajectories</li>";
echo "<li><strong>Audit Trail</strong> - Complete history of who changed what and when</li>";
echo "</ul>";
echo "</div>";

$conn->close();
?>
