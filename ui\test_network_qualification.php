<?php
session_start();

// Check if user is admin
if (!isset($_SESSION['user_id']) || !$_SESSION['is_admin']) {
    header("Location: login.php");
    exit();
}

include 'dbcon.php';

echo "<h2>Network Income Qualification System Test</h2>";
echo "<p>This system ensures users have enough direct referrals to receive network income at each level.</p>";

// Show qualification requirements
echo "<h3>1. Qualification Requirements</h3>";
echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
echo "<tr style='background: #f0f0f0;'><th>Level</th><th>Commission %</th><th>Required Direct Referrals</th><th>Description</th></tr>";

$level_percentages = [1 => 10, 2 => 5, 3 => 3];
$qualification_requirements = [
    1 => 0,  // Level 1: No qualification needed
    2 => 3,  // Level 2: Need at least 3 direct referrals
    3 => 5,  // Level 3: Need at least 5 direct referrals
];

foreach ($qualification_requirements as $level => $required) {
    $commission = $level_percentages[$level] ?? 0;
    $description = $level == 1 ? "Always qualify" : "Need $required active direct referrals with packages";
    echo "<tr>";
    echo "<td style='text-align: center;'>Level $level</td>";
    echo "<td style='text-align: center;'>$commission%</td>";
    echo "<td style='text-align: center;'>$required</td>";
    echo "<td>$description</td>";
    echo "</tr>";
}
echo "</table>";

// Show current user qualification status
echo "<h3>2. Current User Qualification Status</h3>";
$users_sql = "SELECT u.id, u.name, u.user_id as user_identifier, u.package,
              (SELECT COUNT(*) FROM users ref WHERE ref.sponser_id = u.id AND ref.is_active = 1 AND ref.deleted_at IS NULL AND ref.package IS NOT NULL AND ref.package != '') as direct_referrals_count
              FROM users u 
              WHERE u.is_active = 1 AND u.is_admin = 0 AND u.deleted_at IS NULL AND u.package IS NOT NULL AND u.package != ''
              ORDER BY direct_referrals_count DESC
              LIMIT 15";

$users_result = $conn->query($users_sql);
if ($users_result->num_rows > 0) {
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr style='background: #f0f0f0;'><th>User</th><th>Package</th><th>Direct Referrals</th><th>Level 1</th><th>Level 2</th><th>Level 3</th></tr>";

    while ($user = $users_result->fetch_assoc()) {
        $direct_count = $user['direct_referrals_count'];

        // Check qualification for each level
        $level1_status = "✅ Qualified"; // Always qualified
        $level2_status = $direct_count >= 3 ? "✅ Qualified" : "❌ Need " . (3 - $direct_count) . " more";
        $level3_status = $direct_count >= 5 ? "✅ Qualified" : "❌ Need " . (5 - $direct_count) . " more";

        echo "<tr>";
        echo "<td>{$user['name']} ({$user['user_identifier']})</td>";
        echo "<td>{$user['package']}</td>";
        echo "<td style='text-align: center;'>$direct_count</td>";
        echo "<td style='text-align: center;'>$level1_status</td>";
        echo "<td style='text-align: center;'>$level2_status</td>";
        echo "<td style='text-align: center;'>$level3_status</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>No active users with packages found.</p>";
}

// Show recent qualification failures
echo "<h3>3. Recent Qualification Failures</h3>";
$failures_sql = "SELECT wh.*, u.name, u.user_id as user_identifier 
                 FROM wallet_history wh 
                 JOIN users u ON wh.user_id = u.id 
                 WHERE wh.type = 'qualification_failed' 
                 ORDER BY wh.created_at DESC 
                 LIMIT 10";

$failures_result = $conn->query($failures_sql);
if ($failures_result->num_rows > 0) {
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr style='background: #f0f0f0;'><th>User</th><th>Description</th><th>Date</th></tr>";
    while ($failure = $failures_result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>{$failure['name']} ({$failure['user_identifier']})</td>";
        echo "<td>{$failure['description']}</td>";
        echo "<td>" . date('M j, Y H:i:s', strtotime($failure['created_at'])) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>No qualification failures recorded yet.</p>";
}

// Test the qualification system
echo "<h3>4. Test Qualification System</h3>";
if (isset($_POST['test_qualification'])) {
    echo "<div style='background: #e8f5e8; padding: 15px; border: 1px solid #4CAF50; margin: 10px 0;'>";
    echo "<h4>Testing Qualification Logic...</h4>";

    // Get a sample user and test their qualification
    $test_user_sql = "SELECT u.id, u.name, u.user_id as user_identifier,
                      (SELECT COUNT(*) FROM users ref WHERE ref.sponser_id = u.id AND ref.is_active = 1 AND ref.deleted_at IS NULL AND ref.package IS NOT NULL AND ref.package != '') as direct_count
                      FROM users u 
                      WHERE u.is_active = 1 AND u.is_admin = 0 AND u.deleted_at IS NULL AND u.package IS NOT NULL AND u.package != ''
                      ORDER BY RAND()
                      LIMIT 1";

    $test_result = $conn->query($test_user_sql);
    if ($test_result->num_rows > 0) {
        $test_user = $test_result->fetch_assoc();
        $direct_count = $test_user['direct_count'];

        echo "<p><strong>Testing User:</strong> {$test_user['name']} ({$test_user['user_identifier']})</p>";
        echo "<p><strong>Direct Referrals:</strong> $direct_count</p>";
        echo "<br>";

        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>Level</th><th>Required Referrals</th><th>User Has</th><th>Qualification Status</th><th>Commission Eligible</th></tr>";

        foreach ($qualification_requirements as $level => $required) {
            $is_qualified = $direct_count >= $required;
            $status = $is_qualified ? "✅ Qualified" : "❌ Not Qualified";
            $commission = $is_qualified ? ($level_percentages[$level] ?? 0) . "%" : "0%";

            echo "<tr>";
            echo "<td>Level $level</td>";
            echo "<td>$required</td>";
            echo "<td>$direct_count</td>";
            echo "<td>$status</td>";
            echo "<td>$commission</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    echo "</div>";
}

echo "<form method='POST'>";
echo "<button type='submit' name='test_qualification' style='background: #2196F3; color: white; padding: 10px; border: none; cursor: pointer;'>Test Random User Qualification</button>";
echo "</form>";

// Show network structure for a specific user
echo "<h3>5. Network Structure Analysis</h3>";
if (isset($_POST['analyze_user']) && !empty($_POST['user_id'])) {
    $analyze_user_id = intval($_POST['user_id']);

    // Get user details
    $user_sql = "SELECT u.id, u.name, u.user_id as user_identifier, u.package FROM users u WHERE u.id = ?";
    $stmt = $conn->prepare($user_sql);
    $stmt->bind_param("i", $analyze_user_id);
    $stmt->execute();
    $user_result = $stmt->get_result();

    if ($user_result->num_rows > 0) {
        $user = $user_result->fetch_assoc();
        echo "<div style='background: #f0f8ff; padding: 15px; border: 1px solid #4169E1; margin: 10px 0;'>";
        echo "<h4>Analyzing: {$user['name']} ({$user['user_identifier']})</h4>";

        // Get direct referrals
        $direct_sql = "SELECT u.id, u.name, u.user_id as user_identifier, u.package 
                       FROM users u 
                       WHERE u.sponser_id = ? AND u.is_active = 1 AND u.deleted_at IS NULL AND u.package IS NOT NULL AND u.package != ''";
        $direct_stmt = $conn->prepare($direct_sql);
        $direct_stmt->bind_param("i", $analyze_user_id);
        $direct_stmt->execute();
        $direct_result = $direct_stmt->get_result();

        echo "<p><strong>Direct Referrals with Packages:</strong> {$direct_result->num_rows}</p>";

        if ($direct_result->num_rows > 0) {
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
            echo "<tr><th>Name</th><th>User ID</th><th>Package</th></tr>";
            while ($direct = $direct_result->fetch_assoc()) {
                echo "<tr>";
                echo "<td>{$direct['name']}</td>";
                echo "<td>{$direct['user_identifier']}</td>";
                echo "<td>{$direct['package']}</td>";
                echo "</tr>";
            }
            echo "</table>";
        }

        // Show qualification status
        $direct_count = $direct_result->num_rows;
        echo "<h5>Qualification Status:</h5>";
        echo "<ul>";
        echo "<li><strong>Level 1:</strong> ✅ Always qualified</li>";
        echo "<li><strong>Level 2:</strong> " . ($direct_count >= 3 ? "✅ Qualified ($direct_count >= 3)" : "❌ Not qualified ($direct_count < 3)") . "</li>";
        echo "<li><strong>Level 3:</strong> " . ($direct_count >= 5 ? "✅ Qualified ($direct_count >= 5)" : "❌ Not qualified ($direct_count < 5)") . "</li>";
        echo "</ul>";

        $direct_stmt->close();
        echo "</div>";
    }
    $stmt->close();
}

// User selection form
echo "<form method='POST'>";
echo "<label>Select User to Analyze:</label><br>";
echo "<select name='user_id' required>";
echo "<option value=''>-- Select User --</option>";

$select_users_sql = "SELECT u.id, u.name, u.user_id as user_identifier FROM users u WHERE u.is_active = 1 AND u.is_admin = 0 AND u.deleted_at IS NULL ORDER BY u.name";
$select_result = $conn->query($select_users_sql);
while ($select_user = $select_result->fetch_assoc()) {
    echo "<option value='{$select_user['id']}'>{$select_user['name']} ({$select_user['user_identifier']})</option>";
}
echo "</select><br><br>";
echo "<button type='submit' name='analyze_user' style='background: #FF9800; color: white; padding: 10px; border: none; cursor: pointer;'>Analyze User Network</button>";
echo "</form>";

echo "<hr>";
echo "<h3>Summary</h3>";
echo "<p>The qualification system ensures:</p>";
echo "<ul>";
echo "<li><strong>Level 1:</strong> All active sponsors receive 10% commission (no qualification needed)</li>";
echo "<li><strong>Level 2:</strong> Only sponsors with 3+ direct referrals receive 5% commission</li>";
echo "<li><strong>Level 3:</strong> Only sponsors with 5+ direct referrals receive 3% commission</li>";
echo "<li><strong>Qualification failures are logged</strong> in wallet_history for transparency</li>";
echo "<li><strong>Direct referrals must have active packages</strong> to count toward qualification</li>";
echo "</ul>";

$conn->close();
