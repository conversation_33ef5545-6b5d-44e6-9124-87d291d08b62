<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Login - CryptoApp</title>

    <!-- Tailwind CSS for styling -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Google Fonts: Inter -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">

    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css"
        crossorigin="anonymous" referrerpolicy="no-referrer" />

    <style>
        /* Define the custom light theme and other base styles */
        body {
            font-family: 'Inter', sans-serif;
        }

        /* Light Theme - Professional Admin Style */
        .theme-light-admin {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            color: #1e293b;
            min-height: 100vh;
        }

        .theme-light-admin .card {
            /* Enhanced Glassmorphism Effect */
            background: rgba(255, 255, 255, 0.25);
            backdrop-filter: blur(25px);
            -webkit-backdrop-filter: blur(25px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 1.5rem;
            padding: 2.5rem;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow:
                0 25px 50px rgba(0, 0, 0, 0.1),
                0 0 0 1px rgba(255, 255, 255, 0.2),
                inset 0 1px 0 rgba(255, 255, 255, 0.4),
                inset 0 -1px 0 rgba(0, 0, 0, 0.05);
            position: relative;
            overflow: hidden;
        }

        .theme-light-admin .card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg,
                    transparent,
                    rgba(255, 255, 255, 0.1),
                    transparent);
            transition: left 0.8s ease;
        }

        .theme-light-admin .card::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg,
                    rgba(255, 255, 255, 0.1) 0%,
                    rgba(255, 255, 255, 0.05) 50%,
                    rgba(255, 255, 255, 0.1) 100%);
            border-radius: 1.5rem;
            pointer-events: none;
            z-index: -1;
        }

        .theme-light-admin .card:hover {
            transform: translateY(-5px) scale(1.02);
            background: rgba(255, 255, 255, 0.35);
            backdrop-filter: blur(30px);
            -webkit-backdrop-filter: blur(30px);
            box-shadow:
                0 35px 70px rgba(0, 0, 0, 0.15),
                0 0 0 1px rgba(255, 255, 255, 0.4),
                inset 0 1px 0 rgba(255, 255, 255, 0.6),
                inset 0 -1px 0 rgba(0, 0, 0, 0.1);
            border-color: rgba(255, 255, 255, 0.5);
        }

        .theme-light-admin .card:hover::before {
            left: 100%;
        }

        .theme-light-admin .accent {
            color: #3b82f6;
        }

        .theme-light-admin .secondary {
            color: #06b6d4;
        }

        .theme-light-admin .text-muted {
            color: #64748b;
        }

        /* Enhanced Logo styling for light theme */
        .logo-glow {
            position: relative;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(6, 182, 212, 0.1));
            border: 2px solid rgba(59, 130, 246, 0.2);
        }

        .logo-glow::before {
            content: '';
            position: absolute;
            top: -3px;
            left: -3px;
            right: -3px;
            bottom: -3px;
            border-radius: 16px;
            background: linear-gradient(45deg,
                    rgba(59, 130, 246, 0.3),
                    rgba(6, 182, 212, 0.3),
                    rgba(139, 92, 246, 0.3),
                    rgba(59, 130, 246, 0.3));
            opacity: 0;
            animation: logoGlow 3s ease-in-out infinite;
            z-index: -1;
            filter: blur(8px);
        }

        .logo-glow:hover {
            transform: scale(1.05);
            box-shadow: 0 10px 30px rgba(59, 130, 246, 0.3);
            border-color: rgba(59, 130, 246, 0.4);
        }

        .logo-glow:hover::before {
            opacity: 1;
            animation: logoGlowHover 2s ease-in-out infinite;
        }

        @keyframes logoGlow {

            0%,
            100% {
                opacity: 0.2;
                transform: rotate(0deg) scale(1);
            }

            50% {
                opacity: 0.4;
                transform: rotate(180deg) scale(1.02);
            }
        }

        @keyframes logoGlowHover {

            0%,
            100% {
                opacity: 0.6;
                transform: rotate(0deg) scale(1);
                filter: blur(8px);
            }

            50% {
                opacity: 1;
                transform: rotate(360deg) scale(1.05);
                filter: blur(12px);
            }
        }

        .logo-svg {
            transition: all 0.3s ease;
        }

        .logo-bg {
            fill: #3b82f6;
            transition: fill 0.3s ease;
        }

        .logo-symbol {
            fill: #ffffff;
            transition: fill 0.3s ease;
        }

        /* Form input styling for light theme */
        .form-input {
            background: rgba(255, 255, 255, 0.9);
            border: 2px solid #e2e8f0;
            border-radius: 0.75rem;
            padding: 0.875rem 1rem;
            color: #1e293b;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .form-input:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
            background: rgba(255, 255, 255, 1);
        }

        .form-input::placeholder {
            color: #94a3b8;
        }

        /* Button styling for light theme */
        .btn-primary {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            color: white;
            border: none;
            border-radius: 0.75rem;
            padding: 0.875rem 2rem;
            font-weight: 600;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: 0 10px 20px rgba(59, 130, 246, 0.3);
        }

        .btn-primary::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.6s ease;
        }

        .btn-primary:hover::before {
            left: 100%;
        }

        /* Custom Checkbox Styling */
        .custom-checkbox {
            position: relative;
            display: inline-flex;
            align-items: center;
            cursor: pointer;
            user-select: none;
        }

        .custom-checkbox input[type="checkbox"] {
            position: absolute;
            opacity: 0;
            cursor: pointer;
            height: 0;
            width: 0;
        }

        .custom-checkbox .checkmark {
            position: relative;
            height: 18px;
            width: 18px;
            background: rgba(255, 255, 255, 0.9);
            border: 2px solid #e2e8f0;
            border-radius: 4px;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .custom-checkbox:hover .checkmark {
            border-color: #3b82f6;
            background: rgba(255, 255, 255, 1);
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .custom-checkbox input:checked~.checkmark {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
        }

        .custom-checkbox .checkmark:after {
            content: "";
            position: absolute;
            display: none;
            left: 5px;
            top: 2px;
            width: 4px;
            height: 8px;
            border: solid white;
            border-width: 0 2px 2px 0;
            transform: rotate(45deg);
        }

        .custom-checkbox input:checked~.checkmark:after {
            display: block;
        }

        .custom-checkbox .checkbox-label {
            margin-left: 8px;
            font-size: 0.875rem;
            color: #64748b;
            font-weight: 500;
        }

        /* Floating animation for the login card */
        .floating-card {
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {

            0%,
            100% {
                transform: translateY(0px);
            }

            50% {
                transform: translateY(-10px);
            }
        }

        /* Background with Image */
        .bg-pattern {
            background-image:
                /* Light overlay for better readability */
                linear-gradient(135deg, rgba(248, 250, 252, 0.85) 0%, rgba(226, 232, 240, 0.85) 100%),
                /* Background image */
                url('../assets/image.png');
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            background-attachment: fixed;
        }



        /* Custom scrollbar for light theme */
        ::-webkit-scrollbar {
            width: 6px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f5f9;
        }

        ::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, #3b82f6, #06b6d4);
            border-radius: 3px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, #1d4ed8, #0891b2);
        }
    </style>
</head>

<body class="theme-light-admin bg-pattern">
    <div class="min-h-screen flex items-center justify-center p-4">
        <div class="w-full max-w-md">
            <!-- Login Card -->
            <div class="card floating-card">
                <!-- Logo Section -->
                <div class="text-center mb-8">
                    <div class="flex justify-center mb-4">
                        <div
                            class="w-16 h-16 rounded-xl flex items-center justify-center shadow-lg logo-glow relative overflow-hidden">
                            <svg class="w-12 h-12 logo-svg" viewBox="0 0 4091.27 4091.73"
                                xmlns="http://www.w3.org/2000/svg">
                                <g>
                                    <circle cx="2045.635" cy="2045.865" r="2045.635" class="logo-bg" />
                                    <path class="logo-symbol" fill-rule="nonzero"
                                        d="M2947.77 1754.38c40.72,-272.26 -166.56,-418.61 -450,-516.24l91.95 -368.8 -224.5 -55.94 -89.51 358.99c-59.02,-14.72 -119.63,-28.59 -179.87,-42.34l90.16 -361.46 -224.36 -55.94 -91.92 368.68c-48.84,-11.12 -96.81,-22.11 -143.35,-33.69l0.26 -1.16 -309.59 -77.31 -59.72 239.78c0,0 166.56,38.18 163.05,40.53 90.91,22.69 107.35,82.87 104.62,130.57l-104.74 420.15c6.26,1.59 14.38,3.89 23.34,7.49 -7.49,-1.86 -15.46,-3.89 -23.73,-5.87l-146.81 588.57c-11.11,27.62 -39.31,69.07 -102.87,53.33 2.25,3.26 -163.17,-40.72 -163.17,-40.72l-111.46 256.98 292.15,72.83c54.35,13.63 107.61,27.89 159.58,41.27l-92.83 373.03 224.24,55.94 91.95 -368.8c61.35,16.61 120.71,31.97 178.91,46.43l-91.69 367.33 224.51,55.94 92.83 -372.68c382.82,72.45 670.67,43.24 792.04 -303.14 98.00 -279.47 -4.86 -440.75 -206.26 -546.25 146.69,-33.83 257.18,-130.31 286.64 -329.61l-0.07 -0.05zm-512.93 719.26c-69.38,278.78 -538.76,128.08 -690.94,90.29l123.28 -494.2c152.17,37.99 640.17,113.17 567.67,403.91zm69.43 -723.3c-63.29,253.58 -453.96,124.75 -580.69,93.16l111.77 -448.21c126.73,31.59 534.85,90.55 468.94,355.05l-0.02 0z" />
                                </g>
                            </svg>
                        </div>
                    </div>
                    <h1 class="text-2xl font-bold text-slate-800 mb-2">Admin Portal</h1>
                    <p class="text-muted">Sign in to access the admin dashboard</p>
                </div>

                <!-- Login Form -->
                <form id="loginForm" class="space-y-6">
                    <!-- Username Field -->
                    <div>
                        <label for="username" class="block text-sm font-semibold text-slate-700 mb-2">
                            <i class="fas fa-user mr-2 accent"></i>Username
                        </label>
                        <input type="text" id="username" name="username" required class="form-input w-full"
                            placeholder="Enter your username">
                    </div>

                    <!-- Password Field -->
                    <div>
                        <label for="password" class="block text-sm font-semibold text-slate-700 mb-2">
                            <i class="fas fa-lock mr-2 accent"></i>Password
                        </label>
                        <div class="relative">
                            <input type="password" id="password" name="password" required
                                class="form-input w-full pr-12" placeholder="Enter your password">
                            <button type="button" id="togglePassword"
                                class="absolute right-3 top-1/2 transform -translate-y-1/2 text-slate-500 hover:text-slate-700 transition-colors">
                                <i class="fas fa-eye" id="eyeIcon"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Remember Me -->
                    <div class="flex items-center justify-between">
                        <label class="custom-checkbox">
                            <input type="checkbox" id="remember" name="remember">
                            <span class="checkmark"></span>
                            <span class="checkbox-label">Remember me</span>
                        </label>
                        <a href="#" class="text-sm accent hover:underline font-medium">Forgot password?</a>
                    </div>

                    <!-- Login Button -->
                    <button type="submit" class="btn-primary w-full">
                        <i class="fas fa-sign-in-alt mr-2"></i>
                        Sign In
                    </button>
                </form>

                <!-- Footer -->
                <div class="mt-8 pt-6 border-t border-slate-200 text-center">
                    <p class="text-sm text-muted">
                        <i class="fas fa-shield-alt mr-1 accent"></i>
                        Secure Admin Access
                    </p>
                </div>
            </div>

            <!-- Copyright -->
            <div class="text-center mt-6">
                <p class="text-sm text-muted">
                    © 2024 CryptoApp. All rights reserved.
                </p>
            </div>
        </div>
    </div>

    <script>
        // Toggle password visibility
        document.getElementById('togglePassword').addEventListener('click', function () {
            const passwordField = document.getElementById('password');
            const eyeIcon = document.getElementById('eyeIcon');

            if (passwordField.type === 'password') {
                passwordField.type = 'text';
                eyeIcon.classList.remove('fa-eye');
                eyeIcon.classList.add('fa-eye-slash');
            } else {
                passwordField.type = 'password';
                eyeIcon.classList.remove('fa-eye-slash');
                eyeIcon.classList.add('fa-eye');
            }
        });

        // Handle form submission
        document.getElementById('loginForm').addEventListener('submit', function (e) {
            e.preventDefault();

            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const remember = document.getElementById('remember').checked;

            // Basic validation
            if (!username || !password) {
                alert('Please fill in all required fields.');
                return;
            }

            // Simulate login process
            const submitButton = e.target.querySelector('button[type="submit"]');
            const originalText = submitButton.innerHTML;

            // Show loading state
            submitButton.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Signing In...';
            submitButton.disabled = true;

            // Simulate API call
            setTimeout(() => {
                // Accept any username and password - redirect to admin dashboard
                window.location.href = 'index.php';
            }, 1500);
        });

        // Add subtle animations on input focus
        document.querySelectorAll('.form-input').forEach(input => {
            input.addEventListener('focus', function () {
                this.parentElement.style.transform = 'translateY(-2px)';
            });

            input.addEventListener('blur', function () {
                this.parentElement.style.transform = 'translateY(0)';
            });
        });
    </script>
</body>

</html>