<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Add Funds - Crypto Wallet</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
            min-height: 100vh;
        }

        .stats-card {
            background: rgba(30, 41, 59, 0.4);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(59, 130, 246, 0.2);
            border-radius: 0.75rem;
            padding: 1rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }

        .amount-btn {
            background: rgba(30, 41, 59, 0.4);
            border: 2px solid rgba(59, 130, 246, 0.3);
            border-radius: 0.5rem;
            padding: 0.5rem;
            color: white;
            font-weight: 600;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        @media (max-width: 640px) {
            .amount-btn {
                padding: 0.4rem;
                font-size: 0.875rem;
            }
        }

        .amount-btn:hover {
            border-color: rgba(6, 182, 212, 0.6);
            background: rgba(30, 41, 59, 0.6);
            transform: translateY(-2px);
        }

        .amount-btn.selected {
            background: linear-gradient(135deg, #3b82f6, #06b6d4);
            border-color: rgba(6, 182, 212, 0.8);
            box-shadow: 0 8px 25px rgba(6, 182, 212, 0.3);
            color: white !important;
        }

        .amount-btn.selected .text-slate-400 {
            color: rgba(255, 255, 255, 0.8) !important;
        }

        .custom-input {
            background: rgba(15, 23, 42, 0.8);
            border: 1px solid rgba(59, 130, 246, 0.3);
            border-radius: 0.75rem;
            padding: 0.875rem 1rem;
            color: white;
            font-size: 1.125rem;
            font-weight: 600;
            text-align: left;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .custom-input:focus {
            outline: none;
            border-color: rgba(6, 182, 212, 0.6);
            box-shadow: 0 0 0 3px rgba(6, 182, 212, 0.15);
            background: rgba(15, 23, 42, 0.9);
        }

        .custom-input:hover {
            border-color: rgba(6, 182, 212, 0.4);
            background: rgba(15, 23, 42, 0.85);
        }

        .custom-input::placeholder {
            color: rgba(148, 163, 184, 0.5);
        }

        .payment-method {
            background: rgba(30, 41, 59, 0.3);
            border: 2px solid rgba(59, 130, 246, 0.2);
            border-radius: 0.75rem;
            padding: 1rem;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .payment-method:hover {
            border-color: rgba(6, 182, 212, 0.4);
            background: rgba(30, 41, 59, 0.5);
        }

        .payment-method.selected {
            border-color: rgba(6, 182, 212, 0.6);
            background: rgba(6, 182, 212, 0.1);
        }

        /* Bottom Navigation Styles */
        .accent {
            color: #06b6d4 !important;
        }

        .hover\:accent:hover {
            color: #06b6d4 !important;
        }
    </style>
</head>

<body class="text-white">
    <div class="min-h-screen flex flex-col">
        <!-- Header -->
        <div class="sticky top-0 z-50 bg-slate-800/95 backdrop-blur-sm border-b border-slate-700">
            <div class="flex items-center justify-between px-6 py-4">
                <div class="flex items-center gap-4">
                    <button onclick="goBack()"
                        class="w-10 h-10 rounded-full bg-slate-700 hover:bg-slate-600 flex items-center justify-center transition-colors">
                        <i class="fas fa-arrow-left text-slate-300"></i>
                    </button>
                    <div>
                        <h1 class="text-xl font-bold text-white">Add Funds</h1>
                        <p class="text-sm text-slate-400">Top-up your wallet balance</p>
                    </div>
                </div>
                <div class="flex items-center gap-2">
                    <div class="text-right">
                        <p class="text-xs text-slate-400">Current Balance</p>
                        <p class="text-lg font-bold text-cyan-400">$150.25</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-grow overflow-y-auto px-4 sm:px-6 py-4">
            <!-- Amount Selection -->
            <div class="stats-card mb-4">
                <div class="flex items-center gap-2 mb-4">
                    <div
                        class="w-10 h-10 rounded-lg bg-gradient-to-r from-blue-600 to-cyan-600 flex items-center justify-center">
                        <i class="fas fa-dollar-sign text-white"></i>
                    </div>
                    <div>
                        <h2 class="text-lg font-bold text-white">Select Amount</h2>
                        <p class="text-xs text-slate-400">Choose amount in multiples of $10</p>
                    </div>
                </div>

                <!-- Quick Amount Buttons -->
                <div class="grid grid-cols-2 sm:grid-cols-4 gap-3 mb-4">
                    <button class="amount-btn" onclick="selectAmount(10)">
                        <div class="text-center">
                            <div class="text-base sm:text-lg font-bold">$10</div>
                            <div class="text-xs text-slate-400">Min</div>
                        </div>
                    </button>
                    <button class="amount-btn" onclick="selectAmount(20)">
                        <div class="text-center">
                            <div class="text-base sm:text-lg font-bold">$20</div>
                            <div class="text-xs text-slate-400">Quick</div>
                        </div>
                    </button>
                    <button class="amount-btn" onclick="selectAmount(50)">
                        <div class="text-center">
                            <div class="text-base sm:text-lg font-bold">$50</div>
                            <div class="text-xs text-slate-400">Popular</div>
                        </div>
                    </button>
                    <button class="amount-btn" onclick="selectAmount(100)">
                        <div class="text-center">
                            <div class="text-base sm:text-lg font-bold">$100</div>
                            <div class="text-xs text-slate-400">Standard</div>
                        </div>
                    </button>
                    <button class="amount-btn" onclick="selectAmount(250)">
                        <div class="text-center">
                            <div class="text-base sm:text-lg font-bold">$250</div>
                            <div class="text-xs text-slate-400">Large</div>
                        </div>
                    </button>
                    <button class="amount-btn" onclick="selectAmount(500)">
                        <div class="text-center">
                            <div class="text-base sm:text-lg font-bold">$500</div>
                            <div class="text-xs text-slate-400">Maximum</div>
                        </div>
                    </button>
                </div>

                <!-- Custom Amount Input -->
                <div class="mb-4">
                    <label class="block text-sm font-medium text-slate-300 mb-3">Custom Amount</label>
                    <div class="relative">
                        <span
                            class="absolute left-4 top-1/2 transform -translate-y-1/2 text-cyan-400 text-lg font-semibold">$</span>
                        <input type="number" id="customAmount" class="custom-input w-full pl-10"
                            placeholder="Enter amount" min="10" step="10" oninput="validateCustomAmount()">
                    </div>
                    <p class="text-xs text-slate-400 mt-2 flex items-center gap-2">
                        <i class="fas fa-info-circle text-cyan-400"></i>
                        <span>Minimum $10 • Maximum $10,000 • Must be multiples of $10</span>
                    </p>
                </div>

                <!-- Selected Amount Display -->
                <div
                    class="bg-gradient-to-r from-slate-800/50 to-slate-700/50 rounded-lg p-4 border border-blue-500/20">
                    <div class="flex items-center justify-between">
                        <span class="text-slate-400">Selected Amount:</span>
                        <span class="text-2xl font-bold text-cyan-400" id="selectedAmount">$0</span>
                    </div>
                </div>
            </div>

            <!-- Payment Methods -->
            <div class="stats-card mb-4">
                <div class="flex items-center gap-2 mb-4">
                    <div
                        class="w-10 h-10 rounded-lg bg-gradient-to-r from-blue-600 to-cyan-600 flex items-center justify-center">
                        <i class="fab fa-bitcoin text-white"></i>
                    </div>
                    <div>
                        <h2 class="text-lg font-bold text-white">Payment Method</h2>
                        <p class="text-xs text-slate-400">Cryptocurrency payment only</p>
                    </div>
                </div>

                <div class="payment-method selected">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center gap-3">
                            <i class="fab fa-ethereum text-cyan-400 text-xl"></i>
                            <div>
                                <p class="font-semibold text-white">BEP20 (USDT)</p>
                                <p class="text-xs text-slate-400">24 hours processing • No fees</p>
                            </div>
                        </div>
                        <i class="fas fa-check-circle text-cyan-400"></i>
                    </div>
                </div>
            </div>

            <!-- Important Notice -->
            <div class="stats-card mb-4">
                <div class="flex items-center gap-2 mb-3">
                    <div
                        class="w-8 h-8 rounded-lg bg-gradient-to-r from-blue-600 to-cyan-600 flex items-center justify-center">
                        <i class="fas fa-exclamation-triangle text-white text-sm"></i>
                    </div>
                    <div>
                        <h2 class="text-base font-bold text-white">Important Notice</h2>
                        <p class="text-xs text-slate-400">Please read before proceeding</p>
                    </div>
                </div>
                <div
                    class="bg-gradient-to-r from-slate-800/50 to-slate-700/50 rounded-lg p-3 border border-cyan-500/20">
                    <div class="space-y-2 text-sm">
                        <div class="flex items-start gap-2">
                            <i class="fas fa-clock text-cyan-400 text-xs mt-1"></i>
                            <span class="text-slate-300">Funds will reflect in your balance within 24 hours</span>
                        </div>
                        <div class="flex items-start gap-2">
                            <i class="fas fa-plus text-blue-400 text-xs mt-1"></i>
                            <span class="text-slate-300">Add <strong class="text-cyan-400">+$10</strong> extra to
                                cover Binance network fees</span>
                        </div>
                        <div class="flex items-start gap-2">
                            <i class="fas fa-info-circle text-cyan-400 text-xs mt-1"></i>
                            <span class="text-slate-300">Example: For $50 package, send $60 to receive full $50</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Proceed Button -->
            <div class="stats-card">
                <button onclick="proceedToPayment()" id="proceedBtn" disabled
                    class="w-full bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-500 hover:to-cyan-500 disabled:from-slate-600 disabled:to-slate-700 disabled:cursor-not-allowed text-white font-bold py-4 px-6 rounded-lg text-lg transition-all duration-300 shadow-lg flex items-center justify-center gap-3">
                    <i class="fas fa-lock"></i>
                    <span>Proceed to Payment</span>
                    <span class="text-sm opacity-75" id="proceedAmount">$0</span>
                </button>
                <p class="text-xs text-slate-500 text-center mt-3">🔒 Secure payment processing • SSL encrypted</p>
            </div>
        </div>

        <!-- Sticky Bottom Navigation Bar -->
        <div class="sticky bottom-0 z-40 flex-shrink-0">
            <div class="bg-slate-800/95 backdrop-blur-sm mx-4 mb-4 rounded-2xl border border-slate-700">
                <div class="flex justify-around items-center h-16">
                    <a href="1.html" class="flex flex-col items-center gap-1 text-slate-400 hover:accent">
                        <i class="fas fa-home"></i>
                        <span class="text-xs">Home</span>
                    </a>
                    <a href="wallet.html" class="flex flex-col items-center gap-1 accent">
                        <i class="fas fa-wallet"></i>
                        <span class="text-xs">Wallet</span>
                    </a>
                    <a href="team.html" class="flex flex-col items-center gap-1 text-slate-400 hover:accent">
                        <i class="fas fa-users"></i>
                        <span class="text-xs">Team</span>
                    </a>
                    <a href="profile.html" class="flex flex-col items-center gap-1 text-slate-400 hover:accent">
                        <i class="fas fa-user"></i>
                        <span class="text-xs">Profile</span>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script>
        let selectedAmountValue = 0;
        let selectedPaymentMethod = 'card';

        function goBack() {
            window.history.back();
        }

        function selectAmount(amount) {
            selectedAmountValue = amount;

            // Clear custom input
            document.getElementById('customAmount').value = '';

            // Update UI
            updateSelectedAmount();
            updateAmountButtons();
            updateProceedButton();
        }

        function validateCustomAmount() {
            const input = document.getElementById('customAmount');
            let value = parseInt(input.value) || 0;

            // Ensure it's a multiple of 10
            if (value % 10 !== 0) {
                value = Math.floor(value / 10) * 10;
                input.value = value || '';
            }

            // Set limits
            if (value > 10000) {
                value = 10000;
                input.value = value;
            }

            selectedAmountValue = value;

            // Clear amount button selections
            document.querySelectorAll('.amount-btn').forEach(btn => btn.classList.remove('selected'));

            updateSelectedAmount();
            updateProceedButton();
        }

        function updateSelectedAmount() {
            document.getElementById('selectedAmount').textContent = `$${selectedAmountValue}`;
        }

        function updateAmountButtons() {
            document.querySelectorAll('.amount-btn').forEach(btn => {
                btn.classList.remove('selected');
            });

            // Find and select the matching button
            const buttons = document.querySelectorAll('.amount-btn');
            buttons.forEach(btn => {
                const amount = parseInt(btn.textContent.replace('$', ''));
                if (amount === selectedAmountValue) {
                    btn.classList.add('selected');
                }
            });
        }

        function updateProceedButton() {
            const btn = document.getElementById('proceedBtn');
            const amountSpan = document.getElementById('proceedAmount');

            if (selectedAmountValue >= 10) {
                btn.disabled = false;
                amountSpan.textContent = `$${selectedAmountValue}`;
            } else {
                btn.disabled = true;
                amountSpan.textContent = '$0';
            }
        }

        function selectPaymentMethod(method) {
            selectedPaymentMethod = method;

            // Update UI
            document.querySelectorAll('.payment-method').forEach(pm => {
                pm.classList.remove('selected');
                const icon = pm.querySelector('.fa-check-circle, .fa-circle');
                icon.className = 'fas fa-circle text-slate-600';
            });

            // Select the clicked method
            event.currentTarget.classList.add('selected');
            const selectedIcon = event.currentTarget.querySelector('.fa-circle');
            selectedIcon.className = 'fas fa-check-circle text-cyan-400';
        }

        function proceedToPayment() {
            if (selectedAmountValue < 10) {
                alert('Please select an amount of at least $10');
                return;
            }

            // Redirect to crypto payment page with amount
            window.location.href = `crypto-payment.html?amount=${selectedAmountValue}`;
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function () {
            updateProceedButton();
        });
    </script>
</body>

</html>