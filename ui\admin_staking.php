<?php
session_start();

// Admin authentication check
if (!isset($_SESSION['user_id']) || !$_SESSION['is_admin']) {
    header("Location: login.html");
    exit();
}

// Include your existing database configuration
require_once 'dbcon.php';
require_once 'settings_helper.php';

$message = '';
$error = '';

// Get dynamic staking configuration from database
$staking_settings = getStakingSettings($conn);
$COIN_PRICE = $staking_settings['coin_price'];
$MIN_STAKE_COINS = $staking_settings['min_coins'];
$FREEZE_MONTHS = $staking_settings['freeze_months'];

// Page configuration
$page_title = "Admin Staking Management";
$additional_css = "
<style>
    /* Enhanced form styling */
    .staking-form {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.98) 100%);
        backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 1.5rem;
        padding: 2.5rem;
        margin-bottom: 2rem;
        color: #1f2937;
        box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.1);
        animation: slideInUp 0.6s ease-out;
    }

    .form-input {
        background: rgba(255, 255, 255, 0.8);
        border: 2px solid rgba(59, 130, 246, 0.2);
        color: #1f2937;
        border-radius: 0.75rem;
        padding: 0.875rem 1rem;
        width: 100%;
        transition: all 0.3s ease;
        font-size: 0.95rem;
    }

    .form-input::placeholder {
        color: rgba(107, 114, 128, 0.7);
    }

    .form-input:focus {
        outline: none;
        border-color: rgba(59, 130, 246, 0.6);
        background: rgba(255, 255, 255, 0.95);
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        transform: translateY(-1px);
    }

    .form-label {
        color: #374151;
        font-weight: 600;
        margin-bottom: 0.5rem;
        display: block;
        font-size: 0.9rem;
    }

    /* Enhanced submit button */
    .submit-btn {
        background: linear-gradient(135deg, #3b82f6 0%, #6366f1 50%, #8b5cf6 100%);
        color: white;
        border: none;
        padding: 1rem 2.5rem;
        border-radius: 1rem;
        font-weight: 700;
        font-size: 1rem;
        letter-spacing: 0.025em;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        cursor: pointer;
        position: relative;
        overflow: hidden;
        box-shadow: 0 10px 25px -5px rgba(59, 130, 246, 0.4), 0 4px 6px -2px rgba(59, 130, 246, 0.05);
        transform: perspective(1000px) rotateX(0deg);
    }

    .submit-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
        transition: left 0.6s ease;
    }

    .submit-btn:hover::before {
        left: 100%;
    }

    .submit-btn:hover {
        background: linear-gradient(135deg, #2563eb 0%, #4f46e5 50%, #7c3aed 100%);
        transform: perspective(1000px) rotateX(-5deg) translateY(-3px);
        box-shadow: 0 20px 40px -10px rgba(59, 130, 246, 0.6), 0 10px 20px -5px rgba(59, 130, 246, 0.2);
    }

    .submit-btn:active {
        transform: perspective(1000px) rotateX(0deg) translateY(-1px);
        box-shadow: 0 5px 15px -3px rgba(59, 130, 246, 0.4);
        transition: all 0.1s ease;
    }

    .submit-btn span {
        position: relative;
        z-index: 1;
    }

    /* Enhanced animations */
    @keyframes slideInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* Enhanced table styling */
    .admin-table {
        width: 100%;
        border-collapse: separate;
        border-spacing: 0;
        background: white;
        border-radius: 1rem;
        overflow: hidden;
        box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
    }

    .admin-table thead {
        background: linear-gradient(135deg, #3b82f6 0%, #6366f1 50%, #8b5cf6 100%);
    }

    .admin-table thead th {
        color: white;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.05em;
        padding: 1rem 1.5rem;
        font-size: 0.75rem;
        border: none;
        position: relative;
        text-align: left;
    }

    .admin-table tbody tr {
        transition: all 0.2s ease;
        border-bottom: 1px solid #e2e8f0;
    }

    .admin-table tbody tr:hover {
        background-color: rgba(59, 130, 246, 0.05);
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    .admin-table tbody tr:last-child {
        border-bottom: none;
    }

    .admin-table tbody td {
        padding: 1rem 1.5rem;
        vertical-align: middle;
        border: none;
        background-color: white;
    }

    .admin-table tbody tr:hover td {
        background-color: rgba(59, 130, 246, 0.05);
    }

    /* Status badges */
    .status-badge {
        display: inline-flex;
        align-items: center;
        padding: 0.25rem 0.75rem;
        border-radius: 9999px;
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.05em;
    }

    .status-active {
        background-color: #d1fae5;
        color: #065f46;
        border: 1px solid #10b981;
    }

    .status-completed {
        background-color: #ddd6fe;
        color: #5b21b6;
        border: 1px solid #8b5cf6;
    }

    /* User search styling */
    .user-search-results {
        background: rgba(255, 255, 255, 0.98);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(59, 130, 246, 0.2);
        border-radius: 0.75rem;
        max-height: 200px;
        overflow-y: auto;
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        z-index: 10;
        margin-top: 0.25rem;
        box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
    }

    .user-search-item {
        padding: 0.875rem 1rem;
        cursor: pointer;
        border-bottom: 1px solid rgba(229, 231, 235, 0.8);
        color: #374151;
        transition: all 0.2s ease;
    }

    .user-search-item:hover {
        background-color: rgba(59, 130, 246, 0.05);
        color: #1f2937;
    }

    .user-search-item:last-child {
        border-bottom: none;
    }
</style>
";

// Handle AJAX search request for users
if (isset($_GET['search']) && !empty($_GET['search'])) {
    $search = '%' . mysqli_real_escape_string($conn, $_GET['search']) . '%';
    $query = "SELECT id, user_id, name, email, phone, is_active, deposit_wallet_balance, withdrawal_wallet_balance FROM users WHERE email LIKE '$search' OR name LIKE '$search' OR user_id LIKE '$search' LIMIT 10";
    $result = mysqli_query($conn, $query);

    $users = [];
    if ($result) {
        while ($row = mysqli_fetch_assoc($result)) {
            $users[] = $row;
        }
    }

    header('Content-Type: application/json');
    echo json_encode($users);
    exit;
}

// Handle staking form submission
if ($_POST) {
    $email = filter_var($_POST['email'] ?? '', FILTER_SANITIZE_EMAIL);
    $stake_coins = intval($_POST['stake_coins'] ?? 0);
    $stake_type = $_POST['stake_type'] ?? 'deposit'; // deposit or withdrawal wallet

    // Validation
    if (empty($email) || !filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $error = 'Please enter a valid email address.';
    } elseif ($stake_coins < $MIN_STAKE_COINS) {
        $error = "Minimum staking amount is $MIN_STAKE_COINS coins.";
    } elseif ($stake_coins % $MIN_STAKE_COINS !== 0) {
        $error = "Staking amount must be in multiples of $MIN_STAKE_COINS coins.";
    } else {
        // Check if user exists
        $email_escaped = mysqli_real_escape_string($conn, $email);
        $query = "SELECT id, user_id, name, deposit_wallet_balance, withdrawal_wallet_balance FROM users WHERE email = '$email_escaped'";
        $result = mysqli_query($conn, $query);

        if ($result && mysqli_num_rows($result) > 0) {
            $user = mysqli_fetch_assoc($result);
            $stake_amount_usd = $stake_coins * $COIN_PRICE;

            // Check wallet balance
            $wallet_balance = ($stake_type === 'deposit') ? $user['deposit_wallet_balance'] : $user['withdrawal_wallet_balance'];

            if ($wallet_balance < $stake_amount_usd) {
                $error = "Insufficient balance in " . ucfirst($stake_type) . " wallet. Available: $" . number_format($wallet_balance, 2);
            } else {
                // Calculate freeze end date (6 months from now)
                $freeze_end_date = date('Y-m-d H:i:s', strtotime("+$FREEZE_MONTHS months"));

                // Start transaction
                mysqli_begin_transaction($conn);

                try {
                    // Deduct from wallet
                    $wallet_field = $stake_type . '_wallet_balance';
                    $new_balance = $wallet_balance - $stake_amount_usd;
                    $update_query = "UPDATE users SET $wallet_field = $new_balance WHERE id = " . $user['id'];

                    if (!mysqli_query($conn, $update_query)) {
                        throw new Exception('Failed to update wallet balance');
                    }

                    // Insert staking record
                    $insert_query = "INSERT INTO staking_records (user_id, coins_staked, amount_usd, stake_type, freeze_end_date, status, created_at) 
                                   VALUES (" . $user['id'] . ", $stake_coins, $stake_amount_usd, '$stake_type', '$freeze_end_date', 'active', NOW())";

                    if (!mysqli_query($conn, $insert_query)) {
                        throw new Exception('Failed to create staking record');
                    }

                    // Log transaction in wallet history
                    $description = "Staked $stake_coins coins for 6 months (Freeze until: $freeze_end_date)";
                    $log_query = "INSERT INTO wallet_history (user_id, amount, type, wallet_type, description) 
                                VALUES (" . $user['id'] . ", -$stake_amount_usd, 'staking', '$stake_type', '$description')";
                    mysqli_query($conn, $log_query);

                    // Commit transaction
                    mysqli_commit($conn);

                    $user_display = htmlspecialchars($user['name']) . " (ID: " . htmlspecialchars($user['user_id']) . ")";
                    $message = "Successfully staked $stake_coins coins ($" . number_format($stake_amount_usd, 2) . ") for $user_display. Freeze period ends on: " . date('M d, Y', strtotime($freeze_end_date));
                } catch (Exception $e) {
                    mysqli_rollback($conn);
                    $error = 'Staking failed: ' . $e->getMessage();
                }
            }
        } else {
            $error = 'No user found with this email address.';
        }
    }
}

// Start output buffering to capture the page content
ob_start();
?>

<!-- Enhanced Page Header -->
<div class="relative overflow-hidden bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 rounded-2xl p-8 mb-8 border border-blue-100">
    <div class="absolute inset-0 bg-gradient-to-br from-blue-500/5 via-indigo-500/5 to-purple-500/5"></div>

    <!-- Subtle decorative elements -->
    <div class="absolute top-0 right-0 -mt-8 -mr-8 w-32 h-32 bg-gradient-to-br from-blue-200/20 to-indigo-200/20 rounded-full blur-3xl"></div>
    <div class="absolute bottom-0 left-0 -mb-8 -ml-8 w-40 h-40 bg-gradient-to-br from-indigo-200/20 to-purple-200/20 rounded-full blur-3xl"></div>

    <div class="relative flex flex-col lg:flex-row justify-between items-start lg:items-center space-y-4 lg:space-y-0">
        <div>
            <div class="flex items-center space-x-4 mb-4">
                <div class="w-14 h-14 bg-gradient-to-br from-green-500 to-blue-600 rounded-2xl flex items-center justify-center shadow-lg">
                    <i class="fas fa-layer-group text-white text-xl"></i>
                </div>
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">Staking Manager</h1>
                    <p class="text-gray-600 mt-1">Create and manage user staking investments</p>
                </div>
            </div>
            <div class="flex items-center space-x-6 text-sm text-gray-600">
                <div class="flex items-center space-x-2">
                    <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span>Active Stakes</span>
                </div>
                <div class="flex items-center space-x-2">
                    <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
                    <span>Freeze Period</span>
                </div>
                <div class="flex items-center space-x-2">
                    <div class="w-2 h-2 bg-purple-500 rounded-full"></div>
                    <span>Admin Control</span>
                </div>
            </div>
        </div>
        <div class="flex items-center space-x-3">
            <a href="admin_coin_price.php" class="inline-flex items-center px-4 py-2 bg-white border border-gray-200 text-gray-700 rounded-lg hover:bg-gray-50 transition-all duration-200 shadow-sm">
                <i class="fas fa-coins mr-2 text-blue-600"></i>
                Price Management
            </a>
            <div class="flex items-center space-x-2 bg-blue-50 px-4 py-2 rounded-lg border border-blue-200">
                <i class="fas fa-layer-group text-blue-600"></i>
                <span class="text-sm font-medium text-blue-700">Staking Manager</span>
            </div>
        </div>
    </div>
</div>

<!-- Staking Info Cards -->
<div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
    <div class="admin-card p-6">
        <div class="flex items-center">
            <div class="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center">
                <i class="fas fa-coins text-green-600 text-xl"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">Current Coin Price</p>
                <p class="text-2xl font-bold text-gray-900">$<?php echo number_format($COIN_PRICE, 4); ?></p>
            </div>
        </div>
    </div>

    <div class="admin-card p-6">
        <div class="flex items-center">
            <div class="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">
                <i class="fas fa-layer-group text-blue-600 text-xl"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">Minimum Stake</p>
                <p class="text-2xl font-bold text-gray-900"><?php echo number_format($MIN_STAKE_COINS); ?> coins</p>
            </div>
        </div>
    </div>

    <div class="admin-card p-6">
        <div class="flex items-center">
            <div class="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center">
                <i class="fas fa-clock text-purple-600 text-xl"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">Freeze Period</p>
                <p class="text-2xl font-bold text-gray-900"><?php echo $FREEZE_MONTHS; ?> months</p>
            </div>
        </div>
    </div>
</div>

<!-- Messages -->
<?php if ($message): ?>
    <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-lg mb-6">
        <div class="flex items-center">
            <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
            </svg>
            <?php echo htmlspecialchars($message); ?>
        </div>
    </div>
<?php endif; ?>

<?php if ($error): ?>
    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg mb-6">
        <div class="flex items-center">
            <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
            </svg>
            <?php echo htmlspecialchars($error); ?>
        </div>
    </div>
<?php endif; ?>

<!-- Staking Form -->
<div class="staking-form">
    <div class="flex items-center space-x-4 mb-8">
        <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-blue-600 rounded-xl flex items-center justify-center shadow-lg">
            <i class="fas fa-layer-group text-white text-lg"></i>
        </div>
        <div>
            <h2 class="text-2xl font-bold text-gray-900">Create Staking Investment</h2>
            <p class="text-gray-600">Stake coins for users with 6-month freeze period</p>
        </div>
    </div>

    <form method="POST" class="space-y-6">
        <!-- User Email Search -->
        <div>
            <label for="email" class="form-label">
                <i class="fas fa-envelope mr-2"></i>User Email Address
            </label>
            <div class="relative">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <i class="fas fa-search text-gray-400"></i>
                </div>
                <input type="email" id="email" name="email" required
                    value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>"
                    class="form-input pl-10"
                    placeholder="Type to search users by email, name, or ID..."
                    autocomplete="off">

                <!-- Search Results Dropdown -->
                <div id="searchResults" class="user-search-results hidden">
                    <!-- Results will be populated here -->
                </div>
            </div>

            <!-- Selected User Info -->
            <div id="userInfo" class="mt-3 p-4 bg-blue-50 border border-blue-200 rounded-lg hidden">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full flex items-center justify-center">
                            <i class="fas fa-user text-white"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-semibold text-gray-900" id="selectedUserName"></p>
                        <p class="text-sm text-gray-600" id="selectedUserEmail"></p>
                        <div class="flex space-x-4 mt-1">
                            <p class="text-xs text-gray-500">Deposit: $<span id="depositBalance">0.00</span></p>
                            <p class="text-xs text-gray-500">Withdrawal: $<span id="withdrawalBalance">0.00</span></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Wallet Type Selection -->
        <div>
            <label class="form-label">
                <i class="fas fa-wallet mr-2"></i>Wallet Type
            </label>
            <div class="grid grid-cols-2 gap-4">
                <label class="flex items-center p-4 bg-white border-2 border-blue-200 rounded-lg cursor-pointer hover:bg-blue-50 transition-all duration-200">
                    <input type="radio" name="stake_type" value="deposit" checked class="mr-3 text-blue-600">
                    <div>
                        <div class="font-medium text-gray-900">Deposit Wallet</div>
                        <div class="text-sm text-gray-500">Stake from deposit wallet</div>
                    </div>
                </label>
                <label class="flex items-center p-4 bg-white border-2 border-blue-200 rounded-lg cursor-pointer hover:bg-blue-50 transition-all duration-200">
                    <input type="radio" name="stake_type" value="withdrawal" class="mr-3 text-blue-600">
                    <div>
                        <div class="font-medium text-gray-900">Withdrawal Wallet</div>
                        <div class="text-sm text-gray-500">Stake from withdrawal wallet</div>
                    </div>
                </label>
            </div>
        </div>

        <!-- Staking Amount -->
        <div>
            <label for="stake_coins" class="form-label">
                <i class="fas fa-coins mr-2"></i>Number of Coins to Stake
            </label>
            <div class="relative">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <i class="fas fa-layer-group text-gray-400"></i>
                </div>
                <input type="number" id="stake_coins" name="stake_coins" required min="<?php echo $MIN_STAKE_COINS; ?>" step="<?php echo $MIN_STAKE_COINS; ?>"
                    value="<?php echo htmlspecialchars($_POST['stake_coins'] ?? ''); ?>"
                    class="form-input pl-10"
                    placeholder="Enter coins in multiples of <?php echo $MIN_STAKE_COINS; ?>">
            </div>
            <div class="mt-2 text-sm text-gray-600">
                <div>Minimum: <?php echo $MIN_STAKE_COINS; ?> coins (Must be in multiples of <?php echo $MIN_STAKE_COINS; ?>)</div>
                <div id="stakeValue" class="font-medium text-blue-600"></div>
            </div>
        </div>

        <!-- Submit Button -->
        <div class="pt-6">
            <button type="submit" class="submit-btn w-full">
                <span>
                    <i class="fas fa-layer-group mr-3"></i>
                    Stake Coins (6 Month Freeze)
                </span>
            </button>
            <p class="text-center text-xs text-gray-500 mt-3">
                <i class="fas fa-clock mr-1"></i>
                Coins will be frozen for 6 months from investment date
            </p>
        </div>
    </form>
</div>



<script>
    const coinPrice = <?php echo $COIN_PRICE; ?>;
    const minStakeCoins = <?php echo $MIN_STAKE_COINS; ?>;

    // Calculate stake value in real-time
    document.getElementById('stake_coins').addEventListener('input', function() {
        const coins = parseInt(this.value) || 0;
        const value = coins * coinPrice;
        const stakeValueDiv = document.getElementById('stakeValue');

        if (coins > 0) {
            stakeValueDiv.textContent = `Total Value: $${value.toFixed(2)} USD`;

            if (coins % minStakeCoins !== 0) {
                stakeValueDiv.className = 'font-medium text-red-600';
                stakeValueDiv.textContent += ` (Must be multiple of ${minStakeCoins})`;
            } else {
                stakeValueDiv.className = 'font-medium text-blue-600';
            }
        } else {
            stakeValueDiv.textContent = '';
        }
    });

    // User search functionality
    let searchTimeout;
    const emailInput = document.getElementById('email');
    const searchResults = document.getElementById('searchResults');
    const userInfo = document.getElementById('userInfo');

    emailInput.addEventListener('input', function() {
        clearTimeout(searchTimeout);
        const query = this.value.trim();

        if (query.length < 2) {
            searchResults.classList.add('hidden');
            return;
        }

        searchTimeout = setTimeout(() => {
            fetch(`?search=${encodeURIComponent(query)}`)
                .then(response => response.json())
                .then(users => {
                    displaySearchResults(users);
                })
                .catch(error => {
                    console.error('Search error:', error);
                    searchResults.classList.add('hidden');
                });
        }, 300);
    });

    function displaySearchResults(users) {
        if (users.length === 0) {
            searchResults.innerHTML = '<div class="p-3 text-gray-500 text-center">No users found</div>';
            searchResults.classList.remove('hidden');
            return;
        }

        const resultsHTML = users.map(user => `
                <div class="p-3 hover:bg-gray-50 cursor-pointer border-b border-gray-100 last:border-b-0"
                     onclick="selectUser('${user.email}', '${user.name}', '${user.user_id}', ${user.deposit_wallet_balance}, ${user.withdrawal_wallet_balance})">
                    <div class="flex justify-between items-center">
                        <div>
                            <div class="font-medium text-gray-900">${user.name}</div>
                            <div class="text-sm text-gray-600">${user.email}</div>
                            <div class="text-xs text-gray-500">ID: ${user.user_id}</div>
                        </div>
                        <div class="text-right text-xs">
                            <div class="text-green-600">D: $${parseFloat(user.deposit_wallet_balance).toFixed(2)}</div>
                            <div class="text-blue-600">W: $${parseFloat(user.withdrawal_wallet_balance).toFixed(2)}</div>
                        </div>
                    </div>
                </div>
            `).join('');

        searchResults.innerHTML = resultsHTML;
        searchResults.classList.remove('hidden');
    }

    function selectUser(email, name, userId, depositBalance, withdrawalBalance) {
        emailInput.value = email;
        searchResults.classList.add('hidden');

        document.getElementById('selectedUserName').textContent = name;
        document.getElementById('selectedUserEmail').textContent = `${email} (ID: ${userId})`;
        document.getElementById('depositBalance').textContent = parseFloat(depositBalance).toFixed(2);
        document.getElementById('withdrawalBalance').textContent = parseFloat(withdrawalBalance).toFixed(2);

        userInfo.classList.remove('hidden');
    }

    // Hide search results when clicking outside
    document.addEventListener('click', function(e) {
        if (!emailInput.contains(e.target) && !searchResults.contains(e.target)) {
            searchResults.classList.add('hidden');
        }
    });

    // Form validation
    document.querySelector('form').addEventListener('submit', function(e) {
        const coins = parseInt(document.getElementById('stake_coins').value) || 0;

        if (coins < minStakeCoins) {
            e.preventDefault();
            alert(`Minimum staking amount is ${minStakeCoins} coins!`);
            return false;
        }

        if (coins % minStakeCoins !== 0) {
            e.preventDefault();
            alert(`Staking amount must be in multiples of ${minStakeCoins} coins!`);
            return false;
        }
    });

    // Auto-hide success message after 5 seconds
    const successMessage = document.querySelector('.bg-green-50');
    if (successMessage) {
        setTimeout(() => {
            successMessage.style.opacity = '0';
            setTimeout(() => successMessage.remove(), 300);
        }, 5000);
    }
</script>

<?php
// Get the page content
$page_content = ob_get_clean();

// Additional JavaScript for form functionality
$additional_js = <<<'EOD'
<script>
    // Auto-hide success message after 5 seconds
    const successMessage = document.querySelector('.bg-green-50');
    if (successMessage) {
        setTimeout(() => {
            successMessage.style.opacity = '0';
            setTimeout(() => successMessage.remove(), 300);
        }, 5000);
    }
</script>
EOD;

// Include the layout
include 'admin/includes/layout.php';
?>