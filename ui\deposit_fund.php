<?php
session_start();

if (!isset($_SESSION['user_id'])) {
    header("Location: login.php");
    exit();
}

// Configuration for deposit
$deposit_address = 'TPAe4422G4F22B6C5B2D5E7A9A3B3C3D1E'; // Replace with your actual Binance address
$qr_code_url = 'https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=' . urlencode($deposit_address);

?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Deposit Fund</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>

<body class="bg-gray-100 font-sans">
    <?php include 'user_nav.php'; ?>

    <main class="max-w-4xl mx-auto py-6 sm:px-6 lg:px-8">
        <div class="bg-white shadow-lg rounded-lg p-8">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">Deposit Fund into Your Wallet</h2>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
                <div class="bg-gray-50 p-6 rounded-lg">
                    <h3 class="text-lg font-semibold text-gray-700 mb-4">Payment Details</h3>
                    <p class="text-sm text-gray-600 mb-2">Send your payment to the following Binance (BEP20) address:</p>
                    <div class="bg-gray-200 p-3 rounded-md text-center font-mono break-words mb-4">
                        <?php echo htmlspecialchars($deposit_address); ?>
                    </div>
                    <div class="flex justify-center">
                        <img src="<?php echo $qr_code_url; ?>" alt="Deposit QR Code">
                    </div>
                </div>

                <div>
                    <h3 class="text-lg font-semibold text-gray-700 mb-4">Submit Your Deposit Request</h3>
                    <div id="feedback-message" class="hidden p-4 mb-4 text-sm rounded-lg" role="alert"></div>
                    <form id="deposit-form" class="space-y-6" enctype="multipart/form-data">
                        <div>
                            <label for="amount" class="block text-sm font-medium text-gray-700">Amount (USD)</label>
                            <div class="mt-1 relative">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <span class="text-gray-500 sm:text-sm">$</span>
                                </div>
                                <input type="number" id="amount" name="amount" step="10" min="10" class="block w-full pl-7 pr-3 py-2 bg-white border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" placeholder="Enter amount (multiple of 10)" required>
                            </div>
                            <p class="mt-1 text-sm text-gray-500">
                                <span class="font-medium">Minimum:</span> $10 |
                                <span class="font-medium">Must be multiple of:</span> $10
                                <span class="text-indigo-600">(e.g., $10, $20, $30, $100)</span>
                            </p>
                        </div>
                        <div>
                            <label for="receipt" class="block text-sm font-medium text-gray-700">Payment Receipt</label>
                            <input type="file" id="receipt" name="receipt" class="mt-1 block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-indigo-50 file:text-indigo-700 hover:file:bg-indigo-100" required>
                            <p class="mt-1 text-sm text-gray-500">Upload a screenshot of your payment confirmation.</p>
                        </div>
                        <div>
                            <button type="submit" id="submit-btn" class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                Submit Request
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </main>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        $(document).ready(function() {
            $('#deposit-form').on('submit', function(e) {
                e.preventDefault();
                const submitBtn = $('#submit-btn');
                submitBtn.prop('disabled', true).text('Submitting...');

                const amount = parseFloat($('#amount').val());
                if (amount <= 0) {
                    showFeedback('Amount must be greater than $0.', 'error');
                    submitBtn.prop('disabled', false).text('Submit Request');
                    return;
                }
                if (amount < 10) {
                    showFeedback('Minimum deposit amount is $10.', 'error');
                    submitBtn.prop('disabled', false).text('Submit Request');
                    return;
                }
                if (amount % 10 !== 0) {
                    showFeedback('Amount must be a multiple of $10 (e.g., $10, $20, $30, $100).', 'error');
                    submitBtn.prop('disabled', false).text('Submit Request');
                    return;
                }

                const formData = new FormData(this);

                $.ajax({
                    url: '/netvis/ui/api/submit_deposit_request.php',
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        if (response.success) {
                            showFeedback('Your deposit request has been submitted successfully. It will be reviewed by an admin shortly.', 'success');
                            $('#deposit-form')[0].reset();
                        } else {
                            showFeedback(response.message || 'An unknown error occurred.', 'error');
                        }
                    },
                    error: function(jqXHR) {
                        const errorMsg = jqXHR.responseJSON ? jqXHR.responseJSON.message : 'A network error occurred. Please try again.';
                        showFeedback(errorMsg, 'error');
                    },
                    complete: function() {
                        submitBtn.prop('disabled', false).text('Submit Request');
                    }
                });
            });

            // Real-time amount validation
            $('#amount').on('input', function() {
                const amount = parseFloat($(this).val());
                const amountInput = $(this);

                // Remove previous validation classes
                amountInput.removeClass('border-red-500 border-green-500');

                if ($(this).val() === '') {
                    return; // Don't validate empty input
                }

                if (isNaN(amount) || amount <= 0) {
                    amountInput.addClass('border-red-500');
                    return;
                }

                if (amount < 10) {
                    amountInput.addClass('border-red-500');
                    return;
                }

                if (amount % 10 !== 0) {
                    amountInput.addClass('border-red-500');
                    return;
                }

                // Valid amount
                amountInput.addClass('border-green-500');
            });

            function showFeedback(message, type) {
                const feedbackMessage = $('#feedback-message');
                feedbackMessage.text(message);
                feedbackMessage.attr('class', type === 'success' ? 'p-4 mb-4 text-sm text-green-800 rounded-lg bg-green-50' : 'p-4 mb-4 text-sm text-red-800 rounded-lg bg-red-50');
                feedbackMessage.removeClass('hidden');
            }
        });
    </script>
</body>

</html>