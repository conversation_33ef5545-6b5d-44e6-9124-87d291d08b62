<?php
session_start();

// Admin authentication check
if (!isset($_SESSION['user_id']) || !$_SESSION['is_admin']) {
    header("Location: login.html");
    exit();
}

include 'dbcon.php';
include 'settings_helper.php';

$message = '';
$error = '';
$admin_id = $_SESSION['user_id'];

// Ensure settings table exists
ensureSettingsTable($conn);

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';

    if ($action === 'update_coin_price') {
        $new_price = floatval($_POST['coin_price'] ?? 0);
        $reason = trim($_POST['reason'] ?? '');

        if ($new_price <= 0) {
            $error = 'Coin price must be greater than 0.';
        } elseif ($new_price > 100) {
            $error = 'Coin price seems too high. Please verify the amount.';
        } else {
            if (setSetting($conn, 'staking_coin_price', $new_price, 'number', $admin_id, $reason)) {
                $message = "Coin price updated successfully to $" . number_format($new_price, 4) . " USD";
            } else {
                $error = 'Failed to update coin price. Please try again.';
            }
        }
    } elseif ($action === 'update_staking_settings') {
        $min_coins = intval($_POST['min_coins'] ?? 10);
        $freeze_months = intval($_POST['freeze_months'] ?? 6);
        $min_usd_amount = floatval($_POST['min_usd_amount'] ?? 10);

        $updates_successful = 0;
        $total_updates = 3;

        if (setSetting($conn, 'staking_min_coins', $min_coins, 'number', $admin_id)) {
            $updates_successful++;
        }
        if (setSetting($conn, 'staking_freeze_months', $freeze_months, 'number', $admin_id)) {
            $updates_successful++;
        }
        if (setSetting($conn, 'staking_min_usd_amount', $min_usd_amount, 'number', $admin_id)) {
            $updates_successful++;
        }

        if ($updates_successful === $total_updates) {
            $message = "All staking settings updated successfully!";
        } else {
            $error = "Some settings failed to update. Please try again.";
        }
    }
}

// Get current settings
$current_settings = getStakingSettings($conn);
$coin_price = $current_settings['coin_price'];
$min_coins = $current_settings['min_coins'];
$freeze_months = $current_settings['freeze_months'];
$min_usd_amount = $current_settings['min_usd_amount'];

// Get price history using the new function
$price_history = getCoinPriceHistory($conn, 10, 'all');

// Page configuration
$page_title = "Staking Coin Price Management";
$additional_css = "
<style>
    /* Enhanced form styling */
    .price-form {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.98) 100%);
        backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 1.5rem;
        padding: 2.5rem;
        margin-bottom: 2rem;
        color: #1f2937;
        box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.1);
        animation: slideInUp 0.6s ease-out;
    }

    .form-input {
        background: rgba(255, 255, 255, 0.8);
        border: 2px solid rgba(59, 130, 246, 0.2);
        color: #1f2937;
        border-radius: 0.75rem;
        padding: 0.875rem 1rem;
        width: 100%;
        transition: all 0.3s ease;
        font-size: 0.95rem;
    }

    .form-input::placeholder {
        color: rgba(107, 114, 128, 0.7);
    }

    .form-input:focus {
        outline: none;
        border-color: rgba(59, 130, 246, 0.6);
        background: rgba(255, 255, 255, 0.95);
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        transform: translateY(-1px);
    }

    .form-label {
        color: #374151;
        font-weight: 600;
        margin-bottom: 0.5rem;
        display: block;
        font-size: 0.9rem;
    }

    /* Enhanced submit button */
    .submit-btn {
        background: linear-gradient(135deg, #3b82f6 0%, #6366f1 50%, #8b5cf6 100%);
        color: white;
        border: none;
        padding: 1rem 2.5rem;
        border-radius: 1rem;
        font-weight: 700;
        font-size: 1rem;
        letter-spacing: 0.025em;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        cursor: pointer;
        position: relative;
        overflow: hidden;
        box-shadow: 0 10px 25px -5px rgba(59, 130, 246, 0.4), 0 4px 6px -2px rgba(59, 130, 246, 0.05);
        transform: perspective(1000px) rotateX(0deg);
    }

    .submit-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
        transition: left 0.6s ease;
    }

    .submit-btn:hover::before {
        left: 100%;
    }

    .submit-btn:hover {
        background: linear-gradient(135deg, #2563eb 0%, #4f46e5 50%, #7c3aed 100%);
        transform: perspective(1000px) rotateX(-5deg) translateY(-3px);
        box-shadow: 0 20px 40px -10px rgba(59, 130, 246, 0.6), 0 10px 20px -5px rgba(59, 130, 246, 0.2);
    }

    .submit-btn:active {
        transform: perspective(1000px) rotateX(0deg) translateY(-1px);
        box-shadow: 0 5px 15px -3px rgba(59, 130, 246, 0.4);
        transition: all 0.1s ease;
    }

    .submit-btn span {
        position: relative;
        z-index: 1;
    }

    /* Enhanced animations */
    @keyframes slideInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* Enhanced table styling */
    .admin-table {
        width: 100%;
        border-collapse: separate;
        border-spacing: 0;
        background: white;
        border-radius: 1rem;
        overflow: hidden;
        box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
    }

    .admin-table thead {
        background: linear-gradient(135deg, #3b82f6 0%, #6366f1 50%, #8b5cf6 100%);
    }

    .admin-table thead th {
        color: white;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.05em;
        padding: 1rem 1.5rem;
        font-size: 0.75rem;
        border: none;
        position: relative;
        text-align: left;
    }

    .admin-table tbody tr {
        transition: all 0.2s ease;
        border-bottom: 1px solid #e2e8f0;
    }

    .admin-table tbody tr:hover {
        background-color: rgba(59, 130, 246, 0.05);
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    .admin-table tbody tr:last-child {
        border-bottom: none;
    }

    .admin-table tbody td {
        padding: 1rem 1.5rem;
        vertical-align: middle;
        border: none;
        background-color: white;
    }

    .admin-table tbody tr:hover td {
        background-color: rgba(59, 130, 246, 0.05);
    }
</style>
";

// Start output buffering to capture the page content
ob_start();
?>

<!-- Enhanced Page Header -->
<div class="relative overflow-hidden bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 rounded-2xl p-8 mb-8 border border-blue-100">
    <div class="absolute inset-0 bg-gradient-to-br from-blue-500/5 via-indigo-500/5 to-purple-500/5"></div>

    <!-- Subtle decorative elements -->
    <div class="absolute top-0 right-0 -mt-8 -mr-8 w-32 h-32 bg-gradient-to-br from-blue-200/20 to-indigo-200/20 rounded-full blur-3xl"></div>
    <div class="absolute bottom-0 left-0 -mb-8 -ml-8 w-40 h-40 bg-gradient-to-br from-indigo-200/20 to-purple-200/20 rounded-full blur-3xl"></div>

    <div class="relative flex flex-col lg:flex-row justify-between items-start lg:items-center space-y-4 lg:space-y-0">
        <div>
            <div class="flex items-center space-x-4 mb-4">
                <div class="w-14 h-14 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl flex items-center justify-center shadow-lg">
                    <i class="fas fa-coins text-white text-xl"></i>
                </div>
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">Coin Price Manager</h1>
                    <p class="text-gray-600 mt-1">Manage coin pricing and staking configuration settings</p>
                </div>
            </div>
            <div class="flex items-center space-x-6 text-sm text-gray-600">
                <div class="flex items-center space-x-2">
                    <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span>Real-time Pricing</span>
                </div>
                <div class="flex items-center space-x-2">
                    <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
                    <span>Price History</span>
                </div>
                <div class="flex items-center space-x-2">
                    <div class="w-2 h-2 bg-purple-500 rounded-full"></div>
                    <span>Admin Control</span>
                </div>
            </div>
        </div>
        <div class="flex items-center space-x-3">
            <a href="admin_staking.php" class="inline-flex items-center px-4 py-2 bg-white border border-gray-200 text-gray-700 rounded-lg hover:bg-gray-50 transition-all duration-200 shadow-sm">
                <i class="fas fa-layer-group mr-2 text-green-600"></i>
                Staking Management
            </a>
            <div class="flex items-center space-x-2 bg-blue-50 px-4 py-2 rounded-lg border border-blue-200">
                <i class="fas fa-coins text-blue-600"></i>
                <span class="text-sm font-medium text-blue-700">Price Management</span>
            </div>
        </div>
    </div>
</div>

<!-- Success/Error Messages -->
<?php if ($message): ?>
    <div class="mb-6 bg-green-50 border border-green-200 rounded-lg p-4">
        <div class="flex items-center">
            <i class="fas fa-check-circle text-green-500 mr-3"></i>
            <span class="text-green-800 font-medium"><?php echo htmlspecialchars($message); ?></span>
        </div>
    </div>
<?php endif; ?>

<?php if ($error): ?>
    <div class="mb-6 bg-red-50 border border-red-200 rounded-lg p-4">
        <div class="flex items-center">
            <i class="fas fa-exclamation-circle text-red-500 mr-3"></i>
            <span class="text-red-800 font-medium"><?php echo htmlspecialchars($error); ?></span>
        </div>
    </div>
<?php endif; ?>

<!-- Current Settings Display -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
    <div class="admin-card p-6">
        <div class="flex items-center">
            <div class="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">
                <i class="fas fa-coins text-blue-600 text-xl"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">Current Coin Price</p>
                <p class="text-2xl font-bold text-gray-900">$<?php echo number_format($coin_price, 4); ?></p>
            </div>
        </div>
    </div>

    <div class="admin-card p-6">
        <div class="flex items-center">
            <div class="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center">
                <i class="fas fa-layer-group text-green-600 text-xl"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">Min Coins</p>
                <p class="text-2xl font-bold text-gray-900"><?php echo number_format($min_coins); ?></p>
            </div>
        </div>
    </div>

    <div class="admin-card p-6">
        <div class="flex items-center">
            <div class="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center">
                <i class="fas fa-clock text-purple-600 text-xl"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">Freeze Period</p>
                <p class="text-2xl font-bold text-gray-900"><?php echo $freeze_months; ?> months</p>
            </div>
        </div>
    </div>

    <div class="admin-card p-6">
        <div class="flex items-center">
            <div class="w-12 h-12 bg-orange-100 rounded-xl flex items-center justify-center">
                <i class="fas fa-dollar-sign text-orange-600 text-xl"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">Min USD Amount</p>
                <p class="text-2xl font-bold text-gray-900">$<?php echo number_format($min_usd_amount, 0); ?></p>
            </div>
        </div>
    </div>
</div>

<!-- Update Coin Price Form -->
<div class="price-form">
    <div class="flex items-center space-x-4 mb-8">
        <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center shadow-lg">
            <i class="fas fa-coins text-white text-lg"></i>
        </div>
        <div>
            <h2 class="text-2xl font-bold text-gray-900">Update Coin Price</h2>
            <p class="text-gray-600">Set new coin price for staking calculations</p>
        </div>
    </div>

    <form method="POST" class="space-y-4">
        <input type="hidden" name="action" value="update_coin_price">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
                <label for="coin_price" class="form-label">
                    <i class="fas fa-dollar-sign mr-2"></i>New Coin Price (USD)
                </label>
                <div class="relative">
                    <span class="absolute left-3 top-1/2 transform -translate-y-1/2 text-white text-opacity-70">$</span>
                    <input type="number"
                        id="coin_price"
                        name="coin_price"
                        step="0.0001"
                        min="0.0001"
                        max="100"
                        value="<?php echo number_format($coin_price, 4, '.', ''); ?>"
                        required
                        class="form-input pl-8"
                        placeholder="0.0000">
                </div>
            </div>
            <div>
                <label for="reason" class="form-label">
                    <i class="fas fa-comment mr-2"></i>Reason for Change (Optional)
                </label>
                <input type="text"
                    id="reason"
                    name="reason"
                    placeholder="e.g., Market adjustment, Promotion, etc."
                    maxlength="255"
                    class="form-input">
            </div>
        </div>
        <div class="flex justify-between items-center pt-4">
            <div id="price-preview" class="text-sm text-gray-600"></div>
            <button type="submit" class="submit-btn">
                <span>
                    <i class="fas fa-save mr-2"></i>
                    Update Price
                </span>
            </button>
        </div>
    </form>
</div>

<!-- Update Other Staking Settings -->
<div class="price-form">
    <div class="flex items-center space-x-4 mb-8">
        <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center shadow-lg">
            <i class="fas fa-cogs text-white text-lg"></i>
        </div>
        <div>
            <h2 class="text-2xl font-bold text-gray-900">Other Staking Settings</h2>
            <p class="text-gray-600">Configure minimum values and freeze periods</p>
        </div>
    </div>

    <form method="POST" class="space-y-4">
        <input type="hidden" name="action" value="update_staking_settings">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
                <label for="min_coins" class="form-label">
                    <i class="fas fa-layer-group mr-2"></i>Minimum Coins
                </label>
                <input type="number"
                    id="min_coins"
                    name="min_coins"
                    min="1"
                    value="<?php echo $min_coins; ?>"
                    required
                    class="form-input"
                    placeholder="10">
            </div>
            <div>
                <label for="freeze_months" class="form-label">
                    <i class="fas fa-clock mr-2"></i>Freeze Period (Months)
                </label>
                <input type="number"
                    id="freeze_months"
                    name="freeze_months"
                    min="1"
                    max="60"
                    value="<?php echo $freeze_months; ?>"
                    required
                    class="form-input"
                    placeholder="6">
            </div>
            <div>
                <label for="min_usd_amount" class="form-label">
                    <i class="fas fa-dollar-sign mr-2"></i>Min USD Amount
                </label>
                <input type="number"
                    id="min_usd_amount"
                    name="min_usd_amount"
                    min="1"
                    step="1"
                    value="<?php echo $min_usd_amount; ?>"
                    required
                    class="form-input"
                    placeholder="10">
            </div>
        </div>
        <div class="flex justify-end pt-4">
            <button type="submit" class="submit-btn">
                <span>
                    <i class="fas fa-save mr-2"></i>
                    Update Settings
                </span>
            </button>
        </div>
    </form>
</div>

<!-- Price Change History -->
<div class="admin-card p-0 overflow-hidden">
    <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center space-x-3">
            <div class="w-8 h-8 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-lg flex items-center justify-center">
                <i class="fas fa-history text-white text-sm"></i>
            </div>
            <div>
                <h3 class="text-lg font-semibold text-gray-900">Recent Price Changes</h3>
                <p class="text-sm text-gray-600">Historical record of coin price updates</p>
            </div>
        </div>
    </div>

    <?php if (!empty($price_history)): ?>
        <div class="overflow-x-auto">
            <table class="admin-table">
                <thead>
                    <tr>
                        <th>Price</th>
                        <th>Previous Price</th>
                        <th>Change</th>
                        <th>Change %</th>
                        <th>Reason</th>
                        <th>Updated By</th>
                        <th>Date & Time</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($price_history as $row): ?>
                        <?php
                        $change_color = '';
                        if ($row['change_amount'] > 0) {
                            $change_color = 'text-green-600';
                        } elseif ($row['change_amount'] < 0) {
                            $change_color = 'text-red-600';
                        } else {
                            $change_color = 'text-gray-600';
                        }
                        ?>
                        <tr>
                            <td>
                                <div class="flex items-center space-x-2">
                                    <div class="w-6 h-6 bg-blue-100 rounded flex items-center justify-center">
                                        <i class="fas fa-dollar-sign text-blue-600 text-xs"></i>
                                    </div>
                                    <span class="text-lg font-bold text-blue-600">
                                        $<?php echo number_format($row['price'], 4); ?>
                                    </span>
                                </div>
                            </td>
                            <td>
                                <span class="text-sm text-gray-600">
                                    <?php echo $row['previous_price'] ? '$' . number_format($row['previous_price'], 4) : '-'; ?>
                                </span>
                            </td>
                            <td>
                                <span class="text-sm font-medium <?php echo $change_color; ?>">
                                    <?php
                                    if ($row['change_amount']) {
                                        echo ($row['change_amount'] > 0 ? '+$' : '-$') . number_format(abs($row['change_amount']), 4);
                                    } else {
                                        echo '-';
                                    }
                                    ?>
                                </span>
                            </td>
                            <td>
                                <span class="text-sm font-medium <?php echo $change_color; ?>">
                                    <?php
                                    if ($row['change_percentage']) {
                                        echo ($row['change_percentage'] > 0 ? '+' : '') . number_format($row['change_percentage'], 2) . '%';
                                    } else {
                                        echo '-';
                                    }
                                    ?>
                                </span>
                            </td>
                            <td>
                                <span class="text-sm text-gray-600 max-w-xs truncate">
                                    <?php echo htmlspecialchars($row['reason'] ?? '-'); ?>
                                </span>
                            </td>
                            <td>
                                <div class="flex items-center space-x-2">
                                    <div class="w-6 h-6 bg-gray-100 rounded-full flex items-center justify-center">
                                        <i class="fas fa-user text-gray-600 text-xs"></i>
                                    </div>
                                    <span class="text-sm text-gray-600">
                                        <?php echo htmlspecialchars($row['updated_by_name'] ?? 'System'); ?>
                                    </span>
                                </div>
                            </td>
                            <td>
                                <div class="text-sm text-gray-900">
                                    <div class="font-medium"><?php echo date('M j, Y', strtotime($row['created_at'])); ?></div>
                                    <div class="text-xs text-gray-500"><?php echo date('g:i A', strtotime($row['created_at'])); ?></div>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    <?php else: ?>
        <div class="p-8 text-center">
            <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <i class="fas fa-history text-gray-400 text-2xl"></i>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mb-2">No Price History</h3>
            <p class="text-sm text-gray-500 mb-4">No price change history available.</p>
            <a href="create_coin_price_history.php" class="text-blue-600 hover:text-blue-800 font-medium">Create history table</a>
        </div>
    <?php endif; ?>
</div>

<?php
// Capture the page content
$page_content = ob_get_clean();

// Close database connection
$conn->close();

// Include the admin layout
include 'admin/includes/layout.php';
?>

<script>
    // Real-time price preview
    document.getElementById('coin_price').addEventListener('input', function() {
        const price = parseFloat(this.value) || 0;
        const preview = document.getElementById('price-preview');

        if (price > 0) {
            const examples = [
                `${Math.floor(10 / price)} coins = $10.00`,
                `${Math.floor(50 / price)} coins = $50.00`,
                `${Math.floor(100 / price)} coins = $100.00`
            ];
            preview.innerHTML = `<strong>Examples:</strong> ${examples.join(' • ')}`;
        } else {
            preview.innerHTML = '';
        }
    });

    // Trigger initial preview
    document.getElementById('coin_price').dispatchEvent(new Event('input'));
</script>
</body>

</html>

<?php $conn->close(); ?>