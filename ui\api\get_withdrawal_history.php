<?php
// Prevent any output before JSO<PERSON>
ini_set('display_errors', 0);
error_reporting(0);

session_start();
header('Content-Type: application/json');

if (!isset($_SESSION['user_id'])) {
    http_response_code(403);
    echo json_encode(['error' => 'Unauthorized access.']);
    exit();
}

include '../dbcon.php';

// Check database connection
if ($conn->connect_error) {
    http_response_code(500);
    echo json_encode(['error' => 'Database connection failed.']);
    exit();
}

$user_id = $_SESSION['user_id'];

try {
    $sql = "SELECT amount, binance_address, status, requested_at, reviewed_at, admin_notes 
            FROM withdrawal_requests 
            WHERE user_id = ? 
            ORDER BY requested_at DESC";

    $stmt = $conn->prepare($sql);
    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    $result = $stmt->get_result();

    $withdrawals = [];
    while ($row = $result->fetch_assoc()) {
        $withdrawals[] = $row;
    }

    echo json_encode($withdrawals);
} catch (Exception $e) {
    error_log('Get Withdrawal History Error: ' . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => 'Failed to fetch withdrawal history.']);
} finally {
    if (isset($stmt)) {
        $stmt->close();
    }
    $conn->close();
}
