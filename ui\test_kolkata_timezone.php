<?php
// Test Asia/Kolkata timezone setup
include 'dbcon.php';

echo "<h2>🕐 Testing Asia/Kolkata Timezone Setup</h2>";

// Show current timezone settings
echo "<h3>⏰ Timezone Information:</h3>";
echo "<p><strong>PHP Timezone:</strong> " . date_default_timezone_get() . "</p>";
echo "<p><strong>PHP Current Time:</strong> " . date('Y-m-d H:i:s T') . "</p>";

// Show MySQL timezone
$result = $conn->query("SELECT NOW() as mysql_time, @@session.time_zone as mysql_timezone");
$row = $result->fetch_assoc();
echo "<p><strong>MySQL Timezone:</strong> " . $row['mysql_timezone'] . "</p>";
echo "<p><strong>MySQL Current Time:</strong> " . $row['mysql_time'] . "</p>";

// Clear existing tokens and create a test token
echo "<h3>🧪 Testing Token Creation with Asia/Kolkata Timezone:</h3>";

// Clear old tokens
$conn->query("DELETE FROM password_resets");
echo "<p>🗑️ Cleared existing tokens</p>";

// Create test token
$test_email = '<EMAIL>';
$reset_token = bin2hex(random_bytes(32));
$current_time = date('Y-m-d H:i:s');
$expires_at = date('Y-m-d H:i:s', strtotime('+1 hour'));

echo "<p><strong>Test Email:</strong> " . htmlspecialchars($test_email) . "</p>";
echo "<p><strong>Current Time (IST):</strong> " . $current_time . "</p>";
echo "<p><strong>Expires At (IST):</strong> " . $expires_at . "</p>";
echo "<p><strong>Token:</strong> " . substr($reset_token, 0, 20) . "...</p>";

// Insert token
$stmt = $conn->prepare('INSERT INTO password_resets (email, token, expires_at) VALUES (?, ?, ?)');
$stmt->bind_param('sss', $test_email, $reset_token, $expires_at);

if ($stmt->execute()) {
    echo "<p>✅ Token created successfully</p>";
    
    // Verify token
    $stmt = $conn->prepare('SELECT email, token, expires_at, created_at FROM password_resets WHERE token = ?');
    $stmt->bind_param('s', $reset_token);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        $row = $result->fetch_assoc();
        echo "<h3>📋 Token Verification:</h3>";
        echo "<p><strong>Email:</strong> " . htmlspecialchars($row['email']) . "</p>";
        echo "<p><strong>Created At:</strong> " . htmlspecialchars($row['created_at']) . "</p>";
        echo "<p><strong>Expires At:</strong> " . htmlspecialchars($row['expires_at']) . "</p>";
        
        // Check if token is valid
        $current_time_check = date('Y-m-d H:i:s');
        $is_valid = strtotime($row['expires_at']) > strtotime($current_time_check);
        echo "<p><strong>Current Time Check:</strong> " . $current_time_check . "</p>";
        echo "<p><strong>Is Valid:</strong> " . ($is_valid ? "✅ Yes" : "❌ No") . "</p>";
        
        if ($is_valid) {
            $time_remaining = strtotime($row['expires_at']) - strtotime($current_time_check);
            echo "<p><strong>Time Remaining:</strong> " . round($time_remaining / 60) . " minutes</p>";
        }
        
        // Test validation query
        $stmt2 = $conn->prepare('SELECT email FROM password_resets WHERE token = ? AND expires_at > ?');
        $stmt2->bind_param('ss', $reset_token, $current_time_check);
        $stmt2->execute();
        $result2 = $stmt2->get_result();
        
        echo "<p><strong>Validation Query:</strong> " . ($result2->num_rows > 0 ? "✅ Passes" : "❌ Fails") . "</p>";
        
        // Generate test URL
        $reset_url = "http://localhost/netvis/ui/reset_password.php?token=" . $reset_token;
        echo "<h3>🔗 Test Reset URL:</h3>";
        echo "<p><a href='" . htmlspecialchars($reset_url) . "' target='_blank'>" . htmlspecialchars($reset_url) . "</a></p>";
        
        echo "<h3>🎉 Test Results:</h3>";
        if ($is_valid && $result2->num_rows > 0) {
            echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px;'>";
            echo "<p><strong>✅ Asia/Kolkata timezone is working correctly!</strong></p>";
            echo "<p>🕐 PHP and MySQL are synchronized</p>";
            echo "<p>🔑 Token generation and validation working</p>";
            echo "<p>⏰ Time calculations are accurate</p>";
            echo "</div>";
        } else {
            echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 5px;'>";
            echo "<p><strong>❌ There might still be timezone issues</strong></p>";
            echo "<p>Please check the time calculations above</p>";
            echo "</div>";
        }
        
    } else {
        echo "<p>❌ Token not found after creation</p>";
    }
} else {
    echo "<p>❌ Failed to create token: " . $conn->error . "</p>";
}

// Show timezone comparison
echo "<h3>🌍 Timezone Comparison:</h3>";
echo "<table border='1' style='border-collapse: collapse;'>";
echo "<tr><th>Timezone</th><th>Current Time</th></tr>";

// UTC time
date_default_timezone_set('UTC');
echo "<tr><td>UTC</td><td>" . date('Y-m-d H:i:s T') . "</td></tr>";

// Asia/Kolkata time
date_default_timezone_set('Asia/Kolkata');
echo "<tr><td>Asia/Kolkata</td><td>" . date('Y-m-d H:i:s T') . "</td></tr>";

echo "</table>";

$conn->close();
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h2, h3 { color: #333; }
p { margin: 10px 0; }
table { margin: 10px 0; }
th, td { padding: 8px; text-align: left; }
th { background-color: #f2f2f2; }
a { color: #007cba; }
</style>
