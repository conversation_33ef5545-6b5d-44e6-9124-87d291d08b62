<?php
session_start();

// Admin authentication check
if (!isset($_SESSION['user_id']) || !$_SESSION['is_admin']) {
    header("Location: login.php");
    exit();
}

// Include your existing database configuration
require_once 'dbcon.php';

$message = '';
$error = '';

// Handle AJAX search request
if (isset($_GET['search']) && !empty($_GET['search'])) {
    $search = '%' . mysqli_real_escape_string($conn, $_GET['search']) . '%';
    $query = "SELECT id, user_id, name, email, phone, is_active, package FROM users WHERE email LIKE '$search' OR name LIKE '$search' OR user_id LIKE '$search' LIMIT 10";
    $result = mysqli_query($conn, $query);

    $users = [];
    if ($result) {
        while ($row = mysqli_fetch_assoc($result)) {
            $users[] = $row;
        }
    }

    header('Content-Type: application/json');
    echo json_encode($users);
    exit;
}

// Handle form submission
if ($_POST) {
    $email = filter_var($_POST['email'] ?? '', FILTER_SANITIZE_EMAIL);
    $new_password = $_POST['new_password'] ?? '';
    $confirm_password = $_POST['confirm_password'] ?? '';

    // Validation
    if (empty($email) || !filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $error = 'Please enter a valid email address.';
    } elseif (empty($new_password)) {
        $error = 'Please enter a new password.';
    } elseif (strlen($new_password) < 6) {
        $error = 'Password must be at least 6 characters long.';
    } elseif ($new_password !== $confirm_password) {
        $error = 'Passwords do not match.';
    } else {
        // Check if user exists
        $email_escaped = mysqli_real_escape_string($conn, $email);
        $query = "SELECT id, user_id, name, phone, is_active, package FROM users WHERE email = '$email_escaped'";
        $result = mysqli_query($conn, $query);

        if ($result && mysqli_num_rows($result) > 0) {
            $user = mysqli_fetch_assoc($result);

            // Hash the new password
            $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
            $hashed_password_escaped = mysqli_real_escape_string($conn, $hashed_password);

            // Update password
            $update_query = "UPDATE users SET password = '$hashed_password_escaped', updated_at = NOW() WHERE email = '$email_escaped'";
            if (mysqli_query($conn, $update_query)) {
                // Log the activity in wallet_history table
                $user_id = $user['id'];
                $log_query = "INSERT INTO wallet_history (user_id, amount, type, wallet_type, description) VALUES ($user_id, 0, 'admin_action', 'system', 'Password changed by admin')";
                mysqli_query($conn, $log_query);

                $user_display = htmlspecialchars($user['name']) . " (ID: " . htmlspecialchars($user['user_id']) . ", Email: " . htmlspecialchars($email) . ")";
                $message = "Password successfully updated for " . $user_display;
            } else {
                $error = 'Failed to update password. Please try again.';
            }
        } else {
            $error = 'No user found with this email address.';
        }
    }
}

// Page configuration
$page_title = "Change User Password";
$additional_css = "
<style>
    /* Enhanced form styling */
    .password-form {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.98) 100%);
        backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 1.5rem;
        padding: 2.5rem;
        margin-bottom: 2rem;
        color: #1f2937;
        box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.1);
    }

    .form-input {
        background: rgba(255, 255, 255, 0.8);
        border: 2px solid rgba(59, 130, 246, 0.2);
        color: #1f2937;
        border-radius: 0.75rem;
        padding: 0.875rem 1rem;
        width: 100%;
        transition: all 0.3s ease;
        font-size: 0.95rem;
    }

    .form-input::placeholder {
        color: rgba(107, 114, 128, 0.7);
    }

    .form-input:focus {
        outline: none;
        border-color: rgba(59, 130, 246, 0.6);
        background: rgba(255, 255, 255, 0.95);
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        transform: translateY(-1px);
    }

    .form-label {
        color: #374151;
        font-weight: 600;
        margin-bottom: 0.5rem;
        display: block;
        font-size: 0.9rem;
    }



    /* User search styling */
    .user-search-results {
        background: rgba(255, 255, 255, 0.98);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(59, 130, 246, 0.2);
        border-radius: 0.75rem;
        max-height: 200px;
        overflow-y: auto;
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        z-index: 10;
        margin-top: 0.25rem;
        box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
    }

    .user-search-item {
        padding: 0.875rem 1rem;
        cursor: pointer;
        border-bottom: 1px solid rgba(229, 231, 235, 0.8);
        color: #374151;
        transition: all 0.2s ease;
    }

    .user-search-item:hover {
        background-color: rgba(59, 130, 246, 0.05);
        color: #1f2937;
    }

    .user-search-item:last-child {
        border-bottom: none;
    }

    /* Password strength indicator */
    .password-strength {
        height: 6px;
        border-radius: 3px;
        margin-top: 0.5rem;
        transition: all 0.3s ease;
        background: rgba(255, 255, 255, 0.1);
        overflow: hidden;
        position: relative;
    }

    .password-strength::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        height: 100%;
        width: 100%;
        background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.1) 50%, transparent 100%);
        animation: shimmer 2s infinite;
    }

    @keyframes shimmer {
        0% { transform: translateX(-100%); }
        100% { transform: translateX(100%); }
    }

    .strength-weak {
        background: linear-gradient(90deg, #ef4444, #dc2626);
        box-shadow: 0 0 10px rgba(239, 68, 68, 0.3);
    }
    .strength-medium {
        background: linear-gradient(90deg, #f59e0b, #d97706);
        box-shadow: 0 0 10px rgba(245, 158, 11, 0.3);
    }
    .strength-strong {
        background: linear-gradient(90deg, #10b981, #059669);
        box-shadow: 0 0 10px rgba(16, 185, 129, 0.3);
    }

    /* Enhanced animations */
    .password-form {
        animation: slideInUp 0.6s ease-out;
    }

    @keyframes slideInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* Enhanced focus states */
    .form-input:focus {
        outline: none;
        border-color: rgba(255, 255, 255, 0.6);
        background: rgba(255, 255, 255, 0.15);
        box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.1), 0 0 20px rgba(102, 126, 234, 0.2);
        transform: translateY(-1px);
    }

    /* Enhanced submit button */
    .submit-btn {
        background: linear-gradient(135deg, #3b82f6 0%, #6366f1 50%, #8b5cf6 100%);
        color: white;
        border: none;
        padding: 1rem 2.5rem;
        border-radius: 1rem;
        font-weight: 700;
        font-size: 1rem;
        letter-spacing: 0.025em;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        cursor: pointer;
        position: relative;
        overflow: hidden;
        box-shadow: 0 10px 25px -5px rgba(59, 130, 246, 0.4), 0 4px 6px -2px rgba(59, 130, 246, 0.05);
        transform: perspective(1000px) rotateX(0deg);
    }

    .submit-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
        transition: left 0.6s ease;
    }

    .submit-btn::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 0;
        height: 0;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 50%;
        transform: translate(-50%, -50%);
        transition: width 0.6s ease, height 0.6s ease;
    }

    .submit-btn:hover::before {
        left: 100%;
    }

    .submit-btn:hover::after {
        width: 300px;
        height: 300px;
    }

    .submit-btn:hover {
        background: linear-gradient(135deg, #2563eb 0%, #4f46e5 50%, #7c3aed 100%);
        transform: perspective(1000px) rotateX(-5deg) translateY(-3px);
        box-shadow: 0 20px 40px -10px rgba(59, 130, 246, 0.6), 0 10px 20px -5px rgba(59, 130, 246, 0.2);
    }

    .submit-btn:active {
        transform: perspective(1000px) rotateX(0deg) translateY(-1px);
        box-shadow: 0 5px 15px -3px rgba(59, 130, 246, 0.4);
        transition: all 0.1s ease;
    }

    .submit-btn:disabled {
        background: linear-gradient(135deg, #9ca3af 0%, #6b7280 100%);
        cursor: not-allowed;
        transform: none;
        box-shadow: none;
    }

    .submit-btn:disabled:hover {
        transform: none;
        box-shadow: none;
    }

    /* Button text and icon styling */
    .submit-btn i {
        transition: transform 0.3s ease;
    }

    .submit-btn:hover i {
        transform: scale(1.1) rotate(5deg);
    }

    .submit-btn span {
        position: relative;
        z-index: 1;
    }
</style>
";

// Start output buffering to capture the page content
ob_start();
?>

<!-- Enhanced Page Header -->
<div class="relative overflow-hidden bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 rounded-2xl p-8 mb-8 border border-blue-100">
    <div class="absolute inset-0 bg-gradient-to-br from-blue-500/5 via-indigo-500/5 to-purple-500/5"></div>

    <!-- Subtle decorative elements -->
    <div class="absolute top-0 right-0 -mt-8 -mr-8 w-32 h-32 bg-gradient-to-br from-blue-200/20 to-indigo-200/20 rounded-full blur-3xl"></div>
    <div class="absolute bottom-0 left-0 -mb-8 -ml-8 w-40 h-40 bg-gradient-to-br from-indigo-200/20 to-purple-200/20 rounded-full blur-3xl"></div>

    <div class="relative flex flex-col lg:flex-row justify-between items-start lg:items-center space-y-4 lg:space-y-0">
        <div>
            <div class="flex items-center space-x-4 mb-4">
                <div class="w-14 h-14 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl flex items-center justify-center shadow-lg">
                    <i class="fas fa-shield-alt text-white text-xl"></i>
                </div>
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">Password Manager</h1>
                    <p class="text-gray-600 mt-1">Secure user password management system</p>
                </div>
            </div>
            <div class="flex items-center space-x-6 text-sm text-gray-600">
                <div class="flex items-center space-x-2">
                    <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span>Encrypted Storage</span>
                </div>
                <div class="flex items-center space-x-2">
                    <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
                    <span>Audit Trail</span>
                </div>
                <div class="flex items-center space-x-2">
                    <div class="w-2 h-2 bg-purple-500 rounded-full"></div>
                    <span>Admin Access</span>
                </div>
            </div>
        </div>
        <div class="flex items-center space-x-3">
            <a href="admin_staking.php" class="inline-flex items-center px-4 py-2 bg-white border border-gray-200 text-gray-700 rounded-lg hover:bg-gray-50 transition-all duration-200 shadow-sm">
                <i class="fas fa-coins mr-2 text-green-600"></i>
                Staking Management
            </a>
            <div class="flex items-center space-x-2 bg-blue-50 px-4 py-2 rounded-lg border border-blue-200">
                <i class="fas fa-key text-blue-600"></i>
                <span class="text-sm font-medium text-blue-700">Active Session</span>
            </div>
        </div>
    </div>
</div>

<!-- Messages -->
<?php if ($message): ?>
    <div class="mb-6 bg-green-50 border border-green-200 rounded-lg p-4">
        <div class="flex items-center">
            <i class="fas fa-check-circle text-green-500 mr-3"></i>
            <span class="text-green-800 font-medium"><?php echo htmlspecialchars($message); ?></span>
        </div>
    </div>
<?php endif; ?>

<?php if ($error): ?>
    <div class="mb-6 bg-red-50 border border-red-200 rounded-lg p-4">
        <div class="flex items-center">
            <i class="fas fa-exclamation-circle text-red-500 mr-3"></i>
            <span class="text-red-800 font-medium"><?php echo htmlspecialchars($error); ?></span>
        </div>
    </div>
<?php endif; ?>

<!-- Password Change Form -->
<div class="password-form">
    <div class="flex items-center space-x-4 mb-8">
        <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center shadow-lg">
            <i class="fas fa-key text-white text-lg"></i>
        </div>
        <div>
            <h2 class="text-2xl font-bold text-gray-900">Change User Password</h2>
            <p class="text-gray-600">Search for a user and update their password securely</p>
        </div>
    </div>

    <form method="POST" class="space-y-6">
        <!-- Email Input with Search -->
        <div>
            <label for="email" class="form-label">
                <i class="fas fa-envelope mr-2"></i>User Email Address
            </label>
            <div class="relative">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <i class="fas fa-search text-gray-400"></i>
                </div>
                <input type="email" id="email" name="email" required
                    value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>"
                    class="form-input pl-10"
                    placeholder="Type to search users by email or name..."
                    autocomplete="off">

                <!-- Search Results Dropdown -->
                <div id="searchResults" class="user-search-results hidden">
                    <!-- Results will be populated here -->
                </div>
            </div>

            <!-- Selected User Info -->
            <div id="userInfo" class="mt-3 p-4 bg-blue-50 border border-blue-200 rounded-lg hidden">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full flex items-center justify-center">
                            <i class="fas fa-user text-white"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-semibold text-gray-900" id="selectedUserName"></p>
                        <p class="text-sm text-gray-600" id="selectedUserEmail"></p>
                        <p class="text-xs text-gray-500" id="selectedUserPhone"></p>
                    </div>
                    <div class="ml-auto">
                        <span id="selectedUserStatus" class="inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold bg-green-100 text-green-800"></span>
                    </div>
                </div>
            </div>
        </div>

        <!-- New Password Input -->
        <div>
            <label for="new_password" class="form-label">
                <i class="fas fa-lock mr-2"></i>New Password
            </label>
            <div class="relative">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <i class="fas fa-key text-gray-400"></i>
                </div>
                <input type="password" id="new_password" name="new_password" required
                    class="form-input pl-10"
                    placeholder="Enter new password (min 6 characters)"
                    oninput="checkPasswordStrength()">
            </div>
            <!-- Password Strength Indicator -->
            <div id="passwordStrength" class="password-strength mt-2" style="width: 0%;"></div>
            <div id="passwordStrengthText" class="text-xs text-gray-600 mt-1"></div>
        </div>

        <!-- Confirm Password Input -->
        <div>
            <label for="confirm_password" class="form-label">
                <i class="fas fa-lock mr-2"></i>Confirm New Password
            </label>
            <div class="relative">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <i class="fas fa-check text-gray-400"></i>
                </div>
                <input type="password" id="confirm_password" name="confirm_password" required
                    class="form-input pl-10"
                    placeholder="Confirm new password"
                    oninput="checkPasswordMatch()">
            </div>
            <div id="passwordMatchText" class="text-xs mt-1"></div>
        </div>

        <!-- Submit Button -->
        <div class="pt-6">
            <button type="submit" class="submit-btn w-full">
                <span>
                    <i class="fas fa-shield-alt mr-3"></i>
                    Update Password Securely
                </span>
            </button>
            <p class="text-center text-xs text-gray-500 mt-3">
                <i class="fas fa-lock mr-1"></i>
                Password will be encrypted and stored securely
            </p>
        </div>
    </form>
</div>

<?php
// Get the page content
$page_content = ob_get_clean();

// Additional JavaScript for form functionality
$additional_js = <<<'EOD'
<script>
    // User search functionality
    let searchTimeout;
    const emailInput = document.getElementById('email');
    const searchResults = document.getElementById('searchResults');
    const userInfo = document.getElementById('userInfo');

    emailInput.addEventListener('input', function() {
        clearTimeout(searchTimeout);
        const query = this.value.trim();

        if (query.length < 2) {
            searchResults.classList.add('hidden');
            return;
        }

        searchTimeout = setTimeout(() => {
            fetch(`?search=${encodeURIComponent(query)}`)
                .then(response => response.json())
                .then(users => {
                    displaySearchResults(users);
                })
                .catch(error => {
                    console.error('Search error:', error);
                    searchResults.classList.add('hidden');
                });
        }, 300);
    });

    function displaySearchResults(users) {
        if (users.length === 0) {
            searchResults.innerHTML = '<div class="p-3 text-gray-500 text-center">No users found</div>';
            searchResults.classList.remove('hidden');
            return;
        }

        const resultsHTML = users.map(user => `
                <div class="p-3 hover:bg-gray-50 cursor-pointer border-b border-gray-100 last:border-b-0" 
                     onclick="selectUser('${user.email}', '${user.name}', '${user.user_id}', '${user.phone}', ${user.is_active})">
                    <div class="flex justify-between items-center">
                        <div>
                            <div class="font-medium text-gray-900">${user.name}</div>
                            <div class="text-sm text-gray-600">${user.email}</div>
                            <div class="text-xs text-gray-500">ID: ${user.user_id}</div>
                        </div>
                        <div class="text-right">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${user.is_active == 1 ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}">
                                ${user.is_active == 1 ? 'Active' : 'Inactive'}
                            </span>
                        </div>
                    </div>
                </div>
            `).join('');

        searchResults.innerHTML = resultsHTML;
        searchResults.classList.remove('hidden');
    }

    function selectUser(email, name, userId, phone, isActive) {
        emailInput.value = email;
        searchResults.classList.add('hidden');

        document.getElementById('selectedUserName').textContent = name;
        document.getElementById('selectedUserEmail').textContent = `${email} (ID: ${userId})`;
        document.getElementById('selectedUserPhone').textContent = phone ? `Phone: ${phone}` : '';

        const statusSpan = document.getElementById('selectedUserStatus');
        if (isActive == 1) {
            statusSpan.className = 'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800';
            statusSpan.textContent = 'Active';
        } else {
            statusSpan.className = 'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800';
            statusSpan.textContent = 'Inactive';
        }

        userInfo.classList.remove('hidden');
    }

    // Hide search results when clicking outside
    document.addEventListener('click', function(e) {
        if (!emailInput.contains(e.target) && !searchResults.contains(e.target)) {
            searchResults.classList.add('hidden');
        }
    });

    // Form validation
    document.querySelector('form').addEventListener('submit', function(e) {
        const password = document.getElementById('new_password').value;
        const confirmPassword = document.getElementById('confirm_password').value;

        if (password !== confirmPassword) {
            e.preventDefault();
            alert('Passwords do not match!');
            return false;
        }

        if (password.length < 6) {
            e.preventDefault();
            alert('Password must be at least 6 characters long!');
            return false;
        }
    });

    // Password strength checker
    function checkPasswordStrength() {
        const password = document.getElementById('new_password').value;
        const strengthBar = document.getElementById('passwordStrength');
        const strengthText = document.getElementById('passwordStrengthText');

        let strength = 0;
        let text = '';
        let className = '';

        if (password.length >= 6) strength += 1;
        if (password.match(/[a-z]/)) strength += 1;
        if (password.match(/[A-Z]/)) strength += 1;
        if (password.match(/[0-9]/)) strength += 1;
        if (password.match(/[^a-zA-Z0-9]/)) strength += 1;

        switch (strength) {
            case 0:
            case 1:
            case 2:
                text = 'Weak password';
                className = 'strength-weak';
                strengthBar.style.width = '33%';
                break;
            case 3:
            case 4:
                text = 'Medium password';
                className = 'strength-medium';
                strengthBar.style.width = '66%';
                break;
            case 5:
                text = 'Strong password';
                className = 'strength-strong';
                strengthBar.style.width = '100%';
                break;
        }

        strengthBar.className = 'password-strength ' + className;
        strengthText.textContent = text;
    }

    // Password match checker
    function checkPasswordMatch() {
        const password = document.getElementById('new_password').value;
        const confirmPassword = document.getElementById('confirm_password').value;
        const matchText = document.getElementById('passwordMatchText');

        if (confirmPassword === '') {
            matchText.textContent = '';
            return;
        }

        if (password === confirmPassword) {
            matchText.textContent = 'Passwords match';
            matchText.className = 'text-xs mt-1 text-green-600';
        } else {
            matchText.textContent = 'Passwords do not match';
            matchText.className = 'text-xs mt-1 text-red-600';
        }
    }

    // Auto-hide success message after 5 seconds
    const successMessage = document.querySelector('.bg-green-50');
    if (successMessage) {
        setTimeout(() => {
            successMessage.style.opacity = '0';
            setTimeout(() => successMessage.remove(), 300);
        }, 5000);
    }
</script>
EOD;

// Include the layout
include 'admin/includes/layout.php';
?>