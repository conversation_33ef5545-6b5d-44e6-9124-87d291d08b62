# NetVis Crypto Platform - Frontend Documentation

## Overview
This document covers all frontend components, user interfaces, and client-side functionality of the NetVis Crypto Platform.

## Frontend Architecture

### Technology Stack
- **HTML5** - Semantic markup and structure
- **CSS3** - Styling with Tailwind CSS framework
- **JavaScript** - Client-side interactivity and API calls
- **PHP** - Server-side rendering and templating
- **Responsive Design** - Mobile-first approach

### Design System
- **Framework**: Tailwind CSS for utility-first styling
- **Color Scheme**: Professional blue/gray palette
- **Typography**: Clean, readable fonts
- **Components**: Reusable UI components
- **Icons**: SVG icons for scalability

## User Interface Components

### 1. Navigation Systems

#### `ui/user_nav.php` - User Navigation
**Purpose**: Main navigation for regular users
**Features**:
- Dashboard link
- Investment/Package management
- Wallet operations
- Network/Team view
- Staking interface
- Account settings
- Responsive mobile menu

**Navigation Structure**:
```php
- Dashboard (account.php)
- Invest (invest.php)
- Wallet (wallet.php)
- Network (network.php)
- Team (team.php)
- Staking (staking.php)
- History (package_history.php)
- Transfer (fund_transfer.php)
- Deposit (deposit_fund.php)
- Withdraw (withdrawal.php)
```

#### `ui/admin_nav.php` - Admin Navigation
**Purpose**: Administrative navigation panel
**Features**:
- Admin dashboard
- User management
- Financial operations
- System settings
- Reports and analytics
- Staking management
- Coin price management

### 2. Authentication Pages

#### `ui/login.php` - Login Interface
**Purpose**: User authentication page
**Features**:
- Clean, professional login form
- Email/password authentication
- Remember me functionality
- Forgot password link
- Registration redirect
- Form validation

**Key Elements**:
```html
<form class="space-y-6" action="login_process.php" method="POST">
    <input type="email" name="email" required>
    <input type="password" name="password" required>
    <button type="submit">Sign In</button>
</form>
```

#### `ui/register.html` - Registration Interface
**Purpose**: New user registration
**Features**:
- Multi-step registration form
- Sponsor ID validation
- Real-time form validation
- Terms and conditions
- Responsive design

### 3. Dashboard Interfaces

#### `ui/account.php` - User Dashboard
**Purpose**: Main user dashboard and account overview
**Features**:
- Account summary cards
- Wallet balance display (4 decimal precision)
- Recent transactions
- Package status
- Network statistics
- Quick action buttons

**Dashboard Cards**:
- Current Package Information
- Wallet Balances (Deposit/Withdrawal)
- Total Earnings Received
- Network Size and Direct Referrals
- Active Staking Records
- Recent Activity Feed

#### `ui/admin.php` - Admin Dashboard
**Purpose**: Administrative control panel
**Features**:
- System overview statistics
- Pending requests summary
- User activity monitoring
- Financial metrics
- Quick admin actions
- System health indicators

### 4. Financial Interfaces

#### `ui/wallet.php` - Wallet Management
**Purpose**: User wallet operations and history
**Features**:
- Dual wallet system (Deposit/Withdrawal)
- 4 decimal precision display
- Transaction history with filtering
- Balance transfer options
- Upgrade plan functionality
- Real-time balance updates

**Wallet Display**:
```php
<div class="wallet-card">
    <h3>Deposit Wallet</h3>
    <div class="balance">$<?php echo number_format($deposit_balance, 4); ?></div>
    <div class="actions">
        <button>Deposit</button>
        <button>Transfer</button>
    </div>
</div>
```

#### `ui/invest.php` - Investment Interface
**Purpose**: Package purchase and upgrade system
**Features**:
- Package selection with detailed information
- Wallet selection (Deposit/Withdrawal)
- Real-time price calculations
- Upgrade validation
- Purchase confirmation
- Success/error messaging

#### `ui/deposit_fund.php` - Deposit Interface
**Purpose**: Deposit request submission
**Features**:
- Amount input with validation (multiples of 10)
- Receipt upload functionality
- Preview before submission
- Status tracking
- Admin approval workflow

#### `ui/withdrawal.php` - Withdrawal Interface
**Purpose**: Withdrawal request management
**Features**:
- Amount validation (minimum $10, multiples of 10)
- Binance address input
- Balance verification
- Request history
- Status tracking

#### `ui/fund_transfer.php` - Transfer Interface
**Purpose**: Fund transfer between users
**Features**:
- Recipient selection with autocomplete
- Transfer validation (sponsor/downline only)
- Amount validation (multiples of 10)
- Confirmation dialog
- Transfer history

### 5. Staking Interfaces

#### `ui/staking.php` - Staking Management
**Purpose**: Coin staking with dynamic pricing
**Features**:
- Dynamic coin price display (4 decimal precision)
- Real-time coin calculation
- Wallet selection
- Freeze period information
- Staking history
- Claim functionality

**Dynamic Pricing Display**:
```php
<div class="price-display">
    <span>Current Price: $<?php echo number_format($COIN_PRICE, 4); ?></span>
    <span>Coins for $100: <?php echo floor(100 / $COIN_PRICE); ?></span>
</div>
```

### 6. Network Interfaces

#### `ui/network.php` - Network Structure
**Purpose**: User's network visualization
**Features**:
- Hierarchical network display
- Direct referral listing
- Network statistics
- Earnings from network
- Qualification status display

#### `ui/team.php` - Team Management
**Purpose**: Team overview and management
**Features**:
- Team member listing
- Performance metrics
- Team investment totals
- Bonus eligibility status

### 7. History and Reports

#### `ui/package_history.php` - Package History
**Purpose**: User's package purchase and earnings history
**Features**:
- Complete package timeline
- Earnings progression
- Status tracking (Active/Expired)
- ROI calculations
- Export functionality

### 8. Interactive Components

#### JavaScript Functionality (`ui/js/`)
**Purpose**: Client-side interactivity and API integration

**Key Features**:
- Real-time form validation
- AJAX API calls
- Dynamic content updates
- Interactive charts and graphs
- Responsive navigation
- Modal dialogs
- Toast notifications

**Example API Integration**:
```javascript
// Real-time coin price updates
function updateCoinPrice() {
    fetch('/netvis/ui/api/get_coin_price.php')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                document.getElementById('coin-price').textContent = 
                    '$' + parseFloat(data.coin_price).toFixed(4);
            }
        });
}
```

## Responsive Design Features

### Mobile Optimization
- Mobile-first responsive design
- Touch-friendly interface elements
- Optimized navigation for small screens
- Swipe gestures for mobile interactions
- Compressed layouts for mobile viewing

### Tablet Optimization
- Adaptive grid layouts
- Touch-optimized form controls
- Sidebar navigation adaptation
- Optimized table displays

### Desktop Features
- Full-width layouts
- Advanced data tables
- Multi-column layouts
- Hover effects and animations
- Keyboard navigation support

## User Experience Features

### Real-time Updates
- Live balance updates
- Real-time coin price display
- Instant form validation
- Dynamic content loading
- Progress indicators

### Accessibility
- ARIA labels for screen readers
- Keyboard navigation support
- High contrast mode support
- Font size adjustability
- Alt text for images

### Performance Optimization
- Lazy loading for images
- Minified CSS and JavaScript
- Optimized API calls
- Caching strategies
- Progressive loading

## Form Validation

### Client-side Validation
- Real-time input validation
- Format checking (email, phone, amounts)
- Required field validation
- Custom validation rules
- Visual feedback for errors

### Server-side Validation
- Security validation
- Business rule enforcement
- Database constraint checking
- Error message handling
- Sanitization and filtering

## Error Handling

### User-friendly Error Messages
- Clear, actionable error descriptions
- Contextual help information
- Recovery suggestions
- Contact information for support

### Success Feedback
- Confirmation messages
- Progress indicators
- Success animations
- Next step guidance

## Security Features

### Frontend Security
- CSRF token implementation
- XSS prevention
- Input sanitization
- Secure form submission
- Session management

### Data Protection
- Sensitive data masking
- Secure transmission
- Local storage encryption
- Privacy controls

This frontend architecture provides a comprehensive, user-friendly interface that supports all platform functionality while maintaining security, performance, and accessibility standards.
