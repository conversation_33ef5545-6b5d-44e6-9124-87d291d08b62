<?php
session_start();

// Check if user is admin
if (!isset($_SESSION['user_id']) || !$_SESSION['is_admin']) {
    header("Location: login.php");
    exit();
}

include 'dbcon.php';

$message = '';
$error = '';

if ($_POST) {
    try {
        $conn->begin_transaction();

        // 1. Update package history with current user earnings for active packages
        $sync_earnings_sql = "UPDATE package_history ph 
                             JOIN users u ON ph.user_id = u.id 
                             SET ph.total_earnings_received = u.total_earnings_received 
                             WHERE ph.status = 'active' AND u.plan_status = 'active'";

        if ($conn->query($sync_earnings_sql)) {
            $updated_active = $conn->affected_rows;
            $message .= "✅ Updated earnings for $updated_active active packages.<br>";
        } else {
            throw new Exception("Failed to sync active package earnings: " . $conn->error);
        }

        // 2. Mark packages as expired in history if user plan is expired
        $sync_expired_sql = "UPDATE package_history ph 
                            JOIN users u ON ph.user_id = u.id 
                            SET ph.status = 'expired', 
                                ph.expired_at = COALESCE(u.plan_expired_at, NOW()),
                                ph.total_earnings_received = u.total_earnings_received,
                                ph.duration_days = DATEDIFF(COALESCE(u.plan_expired_at, NOW()), ph.purchased_at),
                                ph.expiry_reason = 'limit_reached'
                            WHERE ph.status = 'active' AND u.plan_status = 'expired'";

        if ($conn->query($sync_expired_sql)) {
            $updated_expired = $conn->affected_rows;
            $message .= "✅ Marked $updated_expired packages as expired in history.<br>";
        } else {
            throw new Exception("Failed to sync expired packages: " . $conn->error);
        }

        // 3. Create missing package history records for users who don't have any
        $create_missing_sql = "INSERT INTO package_history (user_id, package_name, package_amount, profit_percentage, wallet_type, purchased_at, total_earnings_received, earnings_limit, status, notes)
                              SELECT
                                  u.id,
                                  u.package,
                                  COALESCE(p.package_amount, 0),
                                  COALESCE(p.profit_percentage, 0),
                                  'withdrawal' as wallet_type,
                                  NOW() as purchased_at,
                                  COALESCE(u.total_earnings_received, 0),
                                  COALESCE(p.package_amount * 2, 0) as earnings_limit,
                                  CASE
                                      WHEN u.plan_status = 'expired' THEN 'expired'
                                      ELSE 'active'
                                  END as status,
                                  'Auto-created during sync' as notes
                              FROM users u
                              LEFT JOIN packages p ON u.package = p.package_name
                              WHERE u.package IS NOT NULL
                              AND u.package != ''
                              AND u.is_admin = 0
                              AND NOT EXISTS (
                                  SELECT 1 FROM package_history ph
                                  WHERE ph.user_id = u.id
                                  AND ph.package_name = u.package
                              )";

        if ($conn->query($create_missing_sql)) {
            $created_missing = $conn->affected_rows;
            $message .= "✅ Created $created_missing missing package history records.<br>";
        } else {
            throw new Exception("Failed to create missing records: " . $conn->error);
        }

        $conn->commit();

        if (empty($error)) {
            $message .= "<br>🎯 <strong>Package History Sync Complete!</strong><br>";
            $message .= "📊 All package history records are now synchronized with current user data.";
        }
    } catch (Exception $e) {
        $conn->rollback();
        $error = "Sync failed: " . $e->getMessage();
    }
}

// Get current sync status
$status_sql = "SELECT 
                COUNT(DISTINCT u.id) as total_users_with_packages,
                COUNT(DISTINCT ph.user_id) as users_with_history,
                COUNT(*) as total_history_records,
                SUM(CASE WHEN ph.status = 'active' THEN 1 ELSE 0 END) as active_records,
                SUM(CASE WHEN ph.status = 'expired' THEN 1 ELSE 0 END) as expired_records,
                SUM(CASE WHEN ph.status = 'upgraded' THEN 1 ELSE 0 END) as upgraded_records
               FROM users u 
               LEFT JOIN package_history ph ON u.id = ph.user_id
               WHERE u.package IS NOT NULL AND u.package != '' AND u.is_admin = 0";

$status_result = $conn->query($status_sql);
$sync_status = $status_result->fetch_assoc();

// Get users missing package history
$missing_sql = "SELECT u.id, u.name, u.user_id, u.package, u.total_earnings_received, u.plan_status
                FROM users u
                WHERE u.package IS NOT NULL
                AND u.package != ''
                AND u.is_admin = 0
                AND NOT EXISTS (
                    SELECT 1 FROM package_history ph
                    WHERE ph.user_id = u.id
                )
                ORDER BY u.id DESC
                LIMIT 10";

$missing_result = $conn->query($missing_sql);
$missing_users = $missing_result->fetch_all(MYSQLI_ASSOC);

$conn->close();
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sync Package History - NetVis</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>

<body class="bg-gray-100">
    <?php include 'admin_nav.php'; ?>

    <main class="max-w-6xl mx-auto py-6 sm:px-6 lg:px-8">
        <div class="bg-white shadow-lg rounded-lg p-8">
            <div class="mb-6">
                <h2 class="text-3xl font-bold text-gray-800">Sync Package History</h2>
                <p class="text-gray-600 mt-2">Synchronize package history records with current user data and create missing records.</p>
            </div>

            <?php if ($message): ?>
                <div class="bg-green-50 border border-green-200 text-green-800 px-4 py-3 rounded-lg mb-6">
                    <?php echo $message; ?>
                </div>
            <?php endif; ?>

            <?php if ($error): ?>
                <div class="bg-red-50 border border-red-200 text-red-800 px-4 py-3 rounded-lg mb-6">
                    <?php echo $error; ?>
                </div>
            <?php endif; ?>

            <!-- Current Sync Status -->
            <div class="mb-8">
                <h3 class="text-xl font-semibold text-gray-800 mb-4">Current Sync Status</h3>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div class="bg-blue-50 p-6 rounded-lg">
                        <h4 class="text-blue-600 font-medium mb-2">Users with Packages</h4>
                        <p class="text-3xl font-bold text-blue-800"><?php echo number_format($sync_status['total_users_with_packages']); ?></p>
                    </div>
                    <div class="bg-green-50 p-6 rounded-lg">
                        <h4 class="text-green-600 font-medium mb-2">Users with History</h4>
                        <p class="text-3xl font-bold text-green-800"><?php echo number_format($sync_status['users_with_history']); ?></p>
                    </div>
                    <div class="bg-purple-50 p-6 rounded-lg">
                        <h4 class="text-purple-600 font-medium mb-2">Total History Records</h4>
                        <p class="text-3xl font-bold text-purple-800"><?php echo number_format($sync_status['total_history_records']); ?></p>
                    </div>
                </div>

                <div class="mt-6 grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div class="bg-yellow-50 p-4 rounded-lg text-center">
                        <h4 class="text-yellow-600 font-medium mb-1">Active Records</h4>
                        <p class="text-2xl font-bold text-yellow-800"><?php echo number_format($sync_status['active_records']); ?></p>
                    </div>
                    <div class="bg-red-50 p-4 rounded-lg text-center">
                        <h4 class="text-red-600 font-medium mb-1">Expired Records</h4>
                        <p class="text-2xl font-bold text-red-800"><?php echo number_format($sync_status['expired_records']); ?></p>
                    </div>
                    <div class="bg-indigo-50 p-4 rounded-lg text-center">
                        <h4 class="text-indigo-600 font-medium mb-1">Upgraded Records</h4>
                        <p class="text-2xl font-bold text-indigo-800"><?php echo number_format($sync_status['upgraded_records']); ?></p>
                    </div>
                </div>
            </div>

            <!-- Sync Actions -->
            <div class="mb-8">
                <h3 class="text-xl font-semibold text-gray-800 mb-4">Sync Actions</h3>

                <div class="bg-blue-50 p-6 rounded-lg mb-4">
                    <h4 class="text-blue-800 font-semibold mb-3">🔄 What This Sync Will Do:</h4>
                    <div class="text-blue-700 text-sm space-y-2">
                        <p><strong>1. Update Active Package Earnings:</strong> Sync current user earnings with package history records</p>
                        <p><strong>2. Mark Expired Packages:</strong> Update package history status for users with expired plans</p>
                        <p><strong>3. Create Missing Records:</strong> Generate package history for users who don't have any records</p>
                        <p><strong>4. Maintain Data Integrity:</strong> Ensure all package data is consistent across the system</p>
                    </div>
                </div>

                <form method="POST">
                    <button type="submit" class="w-full bg-indigo-600 text-white py-3 px-4 rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 font-medium">
                        Run Package History Sync
                    </button>
                </form>
            </div>

            <!-- Users Missing Package History -->
            <?php if (!empty($missing_users)): ?>
                <div class="mb-6">
                    <h3 class="text-xl font-semibold text-gray-800 mb-4">Users Missing Package History (Sample)</h3>

                    <div class="overflow-x-auto border border-gray-200 rounded-lg">
                        <table class="min-w-full bg-white">
                            <thead class="bg-gray-100">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-600 uppercase">User</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-600 uppercase">Package</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-600 uppercase">Earnings</th>
                                    <th class="px-6 py-3 text-center text-xs font-medium text-gray-600 uppercase">Plan Status</th>
                                </tr>
                            </thead>
                            <tbody class="divide-y divide-gray-200">
                                <?php foreach ($missing_users as $user): ?>
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div>
                                                <div class="text-sm font-medium text-gray-900"><?php echo htmlspecialchars($user['name']); ?></div>
                                                <div class="text-sm text-gray-500">ID: <?php echo htmlspecialchars($user['user_id']); ?></div>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            <?php echo htmlspecialchars($user['package']); ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right">
                                            $<?php echo number_format($user['total_earnings_received'], 2); ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-center">
                                            <?php
                                            $status_class = $user['plan_status'] === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800';
                                            ?>
                                            <span class="px-2 py-1 text-xs font-medium rounded-full <?php echo $status_class; ?>">
                                                <?php echo ucfirst($user['plan_status']); ?>
                                            </span>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Quick Links -->
            <div class="flex space-x-4">
                <a href="admin_package_history.php" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
                    View Package History
                </a>
                <a href="test_plan_expiration.php" class="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700">
                    Test Plan Expiration
                </a>
                <a href="admin.php" class="bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700">
                    Admin Dashboard
                </a>
            </div>
        </div>
    </main>
</body>

</html>