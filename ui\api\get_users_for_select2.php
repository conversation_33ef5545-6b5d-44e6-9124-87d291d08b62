<?php
session_start();
header('Content-Type: application/json');

// Admin check
if (!isset($_SESSION['user_id']) || !isset($_SESSION['is_admin']) || !$_SESSION['is_admin']) {
    http_response_code(403);
    echo json_encode(['error' => 'Access Denied']);
    exit();
}

include '../dbcon.php';

$searchTerm = $_GET['q'] ?? '';
$sender_id = $_GET['sender_id'] ?? null;

$results = ['results' => []];
$search_param = '%' . $conn->real_escape_string($searchTerm) . '%';

if ($sender_id) {
    // Fetch sender's sponsor ID
    $stmt_sender = $conn->prepare('SELECT sponser_id FROM users WHERE id = ?');
    $stmt_sender->bind_param('i', $sender_id);
    $stmt_sender->execute();
    $sender_sponsor_id = $stmt_sender->get_result()->fetch_assoc()['sponser_id'] ?? null;
    $stmt_sender->close();

    // Use a recursive CTE to find all downline members, and UNION with the direct sponsor
    $sql = '
        WITH RECURSIVE downline (id, name) AS (
            SELECT id, name FROM users WHERE sponser_id = ?
            UNION ALL
            SELECT u.id, u.name FROM users u JOIN downline d ON u.sponser_id = d.id
        )
        SELECT id, name FROM downline
        WHERE name LIKE ? OR id LIKE ?
        UNION
        SELECT id, name FROM users
        WHERE id = ? AND (name LIKE ? OR id LIKE ?)
    ';

    $stmt = $conn->prepare($sql);
    $stmt->bind_param('ississ', $sender_id, $search_param, $search_param, $sender_sponsor_id, $search_param, $search_param);
} else {
    // Original query to fetch any user (for the sender dropdown)
    $sql = 'SELECT id, name FROM users WHERE name LIKE ? OR id LIKE ? ORDER BY name ASC';
    $stmt = $conn->prepare($sql);
    $stmt->bind_param('ss', $search_param, $search_param);
}

$stmt->execute();
$result = $stmt->get_result();

while ($row = $result->fetch_assoc()) {
    $results['results'][] = [
        'id' => $row['id'],
        'text' => $row['name'] . ' (ID: ' . $row['id'] . ')'
    ];
}

$stmt->close();
$conn->close();

echo json_encode($results);
?>
