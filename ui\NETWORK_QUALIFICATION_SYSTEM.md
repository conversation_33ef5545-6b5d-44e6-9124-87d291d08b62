# Network Income Qualification System

## Overview
The network income qualification system ensures that users must have a minimum number of direct referrals to receive network commissions at higher levels. This encourages active recruitment and network building.

## Qualification Requirements

| Level | Commission % | Required Direct Referrals | Description |
|-------|-------------|---------------------------|-------------|
| Level 1 | 10% | 0 (No requirement) | Always qualify for 1st level income |
| Level 2 | 5% | 3 direct referrals | Need at least 3 direct referrals to receive 2nd level income |
| Level 3 | 3% | 5 direct referrals | Need at least 5 direct referrals to receive 3rd level income |

## How It Works

### 1. Daily Earnings Calculation Process
1. **User receives daily profit** (Level 0 - the earning user)
2. **Level 1 Commission**: Direct sponsor always receives 10% commission (no qualification needed)
3. **Level 2 Commission**: 2nd level sponsor receives 5% commission ONLY if they have 3+ direct referrals
4. **Level 3 Commission**: 3rd level sponsor receives 3% commission ONLY if they have 5+ direct referrals

### 2. Qualification Check Process
For each sponsor at Level 2 and above:
1. **Count Direct Referrals**: Count active users with packages who are directly sponsored by this user
2. **Check Qualification**: Compare direct referral count with requirement for that level
3. **Distribute Commission**: Only if qualified, otherwise skip and log the failure

### 3. Direct Referral Criteria
A direct referral counts toward qualification if:
- ✅ User is active (`is_active = 1`)
- ✅ User is not deleted (`deleted_at IS NULL`)
- ✅ User has a package (`package IS NOT NULL AND package != ''`)
- ✅ User is directly sponsored by the sponsor (`sponser_id = sponsor_id`)

## Implementation Details

### Database Changes
- **New Prepared Statement**: `$count_direct_referrals_stmt` to count qualifying direct referrals
- **Qualification Logic**: Added before commission distribution in `calculate_earnings.php`
- **Failure Logging**: Records qualification failures in `wallet_history` table

### Code Logic
```php
// Define qualification requirements
$qualification_requirements = [
    1 => 0,  // Level 1: No qualification needed
    2 => 3,  // Level 2: Need at least 3 direct referrals
    3 => 5,  // Level 3: Need at least 5 direct referrals
];

// Check qualification
$required_referrals = $qualification_requirements[$level] ?? 0;
$is_qualified = $direct_referrals_count >= $required_referrals;

// Only distribute commission if qualified
if ($commissionPercentage > 0 && $is_qualified) {
    // Distribute commission
} else {
    // Log qualification failure
}
```

## Benefits

### 1. Encourages Active Recruitment
- Users must build their direct network to earn higher level commissions
- Promotes active participation in network building
- Rewards users who bring in more direct referrals

### 2. Fair Distribution
- Prevents passive users from earning high-level commissions
- Ensures commissions go to users who actively contribute to network growth
- Creates incentive structure for network expansion

### 3. Transparency
- All qualification failures are logged in wallet_history
- Users can see exactly why they didn't receive certain level commissions
- Admin can track qualification patterns and network growth

## Testing and Monitoring

### 1. Test Script: `test_network_qualification.php`
- Shows qualification requirements
- Displays current user qualification status
- Tests qualification logic for random users
- Analyzes individual user networks
- Shows recent qualification failures

### 2. Monitoring Tools
- **Wallet History**: Track qualification failures with type `qualification_failed`
- **User Analysis**: See direct referral counts and qualification status
- **Network Structure**: Analyze individual user networks and their qualification levels

## Example Scenarios

### Scenario 1: New User
- **Direct Referrals**: 0
- **Qualifications**: Level 1 only
- **Earnings**: Can receive Level 1 commissions (10%) but not Level 2 or 3

### Scenario 2: Growing Network
- **Direct Referrals**: 3
- **Qualifications**: Level 1 and Level 2
- **Earnings**: Can receive Level 1 (10%) and Level 2 (5%) commissions, but not Level 3

### Scenario 3: Established Leader
- **Direct Referrals**: 8
- **Qualifications**: All levels (1, 2, and 3)
- **Earnings**: Can receive all commission levels (10%, 5%, and 3%)

## Configuration

The qualification requirements can be easily modified in `calculate_earnings.php`:

```php
$qualification_requirements = [
    1 => 0,  // Level 1: Always qualify
    2 => 3,  // Level 2: Change this number to adjust requirement
    3 => 5,  // Level 3: Change this number to adjust requirement
    4 => 7,  // Level 4: Add more levels if needed
    5 => 10  // Level 5: Add more levels if needed
];
```

## Files Modified

1. **`ui/calculate_earnings.php`** - Added qualification logic to commission distribution
2. **`ui/test_network_qualification.php`** - NEW: Testing and monitoring tool
3. **`ui/NETWORK_QUALIFICATION_SYSTEM.md`** - NEW: Documentation

The qualification system is now fully integrated with the earnings calculation and will automatically enforce the direct referral requirements for network income distribution.
