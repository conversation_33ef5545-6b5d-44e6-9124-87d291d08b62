-- ============================================
-- NETVIS CRYPTO PLATFORM - FINAL SCHEMA
-- Complete Database Structure with All Features
-- ============================================

-- Set SQL mode and character set
SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
SET AUTOCOMMIT = 0;
START TRANSACTION;
SET time_zone = "+00:00";

-- Database configuration
/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

-- ============================================
-- DROP TABLES (in reverse dependency order)
-- ============================================
DROP TABLE IF EXISTS `coin_price_history`;
DROP TABLE IF EXISTS `system_settings`;
DROP TABLE IF EXISTS `package_history`;
DROP TABLE IF EXISTS `staking_records`;
DROP TABLE IF EXISTS `withdrawal_requests`;
DROP TABLE IF EXISTS `deposit_requests`;
DROP TABLE IF EXISTS `wallet_history`;
DROP TABLE IF EXISTS `bonuses`;
DROP TABLE IF EXISTS `levels`;
DROP TABLE IF EXISTS `packages`;
DROP TABLE IF EXISTS `users`;

-- ============================================
-- TABLE: users (Core user management)
-- ============================================
CREATE TABLE `users` (
  `id` int(6) UNSIGNED NOT NULL AUTO_INCREMENT,
  `package` varchar(30) DEFAULT NULL,
  `sponser_id` varchar(30) DEFAULT NULL COMMENT 'Stores the numeric ID of the sponsor user',
  `user_id` varchar(20) DEFAULT NULL COMMENT 'Public-facing user ID (e.g., FL123456)',
  `binanace_address` varchar(255) DEFAULT NULL,
  `name` varchar(50) DEFAULT NULL,
  `email` varchar(50) DEFAULT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `password` varchar(255) DEFAULT NULL,
  `deposit_wallet_balance` decimal(20,4) NOT NULL DEFAULT 0.0000,
  `withdrawal_wallet_balance` decimal(20,4) NOT NULL DEFAULT 0.0000,
  `total_earnings_received` decimal(20,4) DEFAULT 0.0000,
  `is_admin` tinyint(1) NOT NULL DEFAULT 0,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `plan_status` enum('active','expired') NOT NULL DEFAULT 'active',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_id_unique` (`user_id`),
  UNIQUE KEY `email_unique` (`email`),
  KEY `idx_sponser_id` (`sponser_id`),
  KEY `idx_is_active` (`is_active`),
  KEY `idx_plan_status` (`plan_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ============================================
-- TABLE: packages (Investment packages)
-- ============================================
CREATE TABLE `packages` (
  `id` int(6) UNSIGNED NOT NULL AUTO_INCREMENT,
  `package_name` varchar(255) NOT NULL,
  `package_amount` decimal(10,4) NOT NULL,
  `profit_percentage` decimal(5,4) NOT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `created_by` int(6) UNSIGNED NOT NULL,
  `updated_by` int(6) UNSIGNED DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `fk_packages_created_by` (`created_by`),
  KEY `fk_packages_updated_by` (`updated_by`),
  KEY `idx_is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;



-- --------------------------------------------
-- Add foreign key constraints
-- --------------------------------------------

-- Foreign key for users.updated_by → users.id
ALTER TABLE `users`
  ADD CONSTRAINT `fk_users_updated_by`
  FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`)
  ON DELETE SET NULL ON UPDATE CASCADE;

-- Foreign keys for packages
ALTER TABLE `packages`
  ADD CONSTRAINT `fk_packages_created_by`
  FOREIGN KEY (`created_by`) REFERENCES `users` (`id`)
  ON DELETE CASCADE ON UPDATE CASCADE,
  ADD CONSTRAINT `fk_packages_updated_by`
  FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`)
  ON DELETE SET NULL ON UPDATE CASCADE;



ALTER TABLE `users`
  ADD CONSTRAINT `users_ibfk_1` FOREIGN KEY (`sponsor_id`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- Foreign keys for staking_records
ALTER TABLE `staking_records`
  ADD CONSTRAINT `fk_staking_user_id`
  FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
  ON DELETE CASCADE ON UPDATE CASCADE,
  ADD CONSTRAINT `fk_staking_admin`
  FOREIGN KEY (`created_by_admin`) REFERENCES `users` (`id`)
  ON DELETE SET NULL ON UPDATE CASCADE;

-- ============================================
-- TABLE: bonuses (Bonus management)
-- ============================================
CREATE TABLE `bonuses` (
  `id` int(6) UNSIGNED NOT NULL AUTO_INCREMENT,
  `bonus_name` varchar(255) NOT NULL,
  `bonus_fund` decimal(10,4) NOT NULL,
  `team_investment` decimal(10,4) NOT NULL,
  `bonus_type` enum('team_bonus','leadership_bonus','performance_bonus') NOT NULL DEFAULT 'team_bonus',
  `eligibility_criteria` text DEFAULT NULL COMMENT 'JSON or text describing eligibility criteria',
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_bonus_type` (`bonus_type`),
  KEY `idx_is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------
-- Create table: bonus_claims
-- --------------------------------------------
CREATE TABLE IF NOT EXISTS `bonus_claims` (
  `id` int(6) UNSIGNED NOT NULL AUTO_INCREMENT,
  `user_id` int(6) UNSIGNED NOT NULL,
  `bonus_id` int(6) UNSIGNED NOT NULL,
  `status` enum('pending','approved','rejected') NOT NULL DEFAULT 'pending',
  `claimed_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `reviewed_by` int(6) UNSIGNED DEFAULT NULL,
  `reviewed_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_bonus_unique` (`user_id`, `bonus_id`),
  KEY `fk_claims_user_id` (`user_id`),
  KEY `fk_claims_bonus_id` (`bonus_id`),
  KEY `fk_claims_reviewed_by` (`reviewed_by`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Foreign keys for bonus_claims
ALTER TABLE `bonus_claims`
  ADD CONSTRAINT `fk_claims_user_id`
  FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
  ON DELETE CASCADE ON UPDATE CASCADE,
  ADD CONSTRAINT `fk_claims_bonus_id`
  FOREIGN KEY (`bonus_id`) REFERENCES `bonuses` (`id`)
  ON DELETE CASCADE ON UPDATE CASCADE,
  ADD CONSTRAINT `fk_claims_reviewed_by`
  FOREIGN KEY (`reviewed_by`) REFERENCES `users` (`id`)
  ON DELETE SET NULL ON UPDATE CASCADE;

-- ============================================
-- TABLE: levels (Network level management)
-- ============================================
CREATE TABLE `levels` (
  `id` int(6) UNSIGNED NOT NULL AUTO_INCREMENT,
  `level_number` int(2) UNSIGNED NOT NULL,
  `distribution_percentage` decimal(5,2) NOT NULL,
  `min_direct_referrals` int(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT 'Minimum direct referrals required for this level',
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `level_number_unique` (`level_number`),
  KEY `idx_is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;


-- ============================================
-- TABLE: wallet_history (Transaction history)
-- ============================================
CREATE TABLE `wallet_history` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `amount` decimal(20,4) NOT NULL,
  `type` varchar(50) NOT NULL COMMENT 'daily_profit, network_commission, deposit, withdrawal, etc.',
  `wallet_type` varchar(20) NOT NULL DEFAULT 'withdrawal' COMMENT 'deposit or withdrawal wallet',
  `description` varchar(255) DEFAULT NULL,
  `related_user_id` int(11) DEFAULT NULL COMMENT 'For network commissions, ID of user who generated the profit',
  `transaction_hash` varchar(255) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_type` (`type`),
  KEY `idx_wallet_type` (`wallet_type`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ============================================
-- TABLE: deposit_requests (Deposit management)
-- ============================================
CREATE TABLE `deposit_requests` (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
  `user_id` int(11) UNSIGNED NOT NULL,
  `amount` decimal(20,4) NOT NULL,
  `receipt_path` varchar(255) NOT NULL,
  `status` enum('pending','approved','rejected') NOT NULL DEFAULT 'pending',
  `requested_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `reviewed_by` int(11) UNSIGNED DEFAULT NULL,
  `reviewed_at` timestamp NULL DEFAULT NULL,
  `admin_notes` text DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `fk_deposit_requests_user_id` (`user_id`),
  KEY `fk_deposit_requests_reviewed_by` (`reviewed_by`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ============================================
-- TABLE: staking_records (Staking management)
-- ============================================
CREATE TABLE `staking_records` (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
  `user_id` int(11) UNSIGNED NOT NULL,
  `coins_staked` int(11) NOT NULL,
  `amount_usd` decimal(20,4) NOT NULL,
  `stake_type` enum('deposit','withdrawal') NOT NULL DEFAULT 'deposit',
  `freeze_end_date` timestamp NOT NULL,
  `status` enum('active','completed','cancelled','claim_pending') NOT NULL DEFAULT 'active',
  `profit_earned` decimal(20,4) DEFAULT 0.0000,
  `claim_requested_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `completed_at` timestamp NULL DEFAULT NULL,
  `created_by_admin` int(11) UNSIGNED DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `fk_staking_user_id` (`user_id`),
  KEY `fk_staking_admin` (`created_by_admin`),
  KEY `idx_freeze_end_date` (`freeze_end_date`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;


-- ============================================
-- TABLE: withdrawal_requests (Withdrawal management)
-- ============================================
CREATE TABLE `withdrawal_requests` (
  `id` int(11) AUTO_INCREMENT PRIMARY KEY,
  `user_id` int(11) NOT NULL,
  `amount` decimal(20,4) NOT NULL,
  `binance_address` varchar(255) NOT NULL,
  `status` enum('pending','approved','rejected') DEFAULT 'pending',
  `requested_at` timestamp DEFAULT current_timestamp(),
  `reviewed_at` timestamp NULL,
  `reviewed_by` int(11) NULL,
  `admin_notes` text NULL,
  `transaction_hash` varchar(255) NULL,
  `created_at` timestamp DEFAULT current_timestamp(),
  `updated_at` timestamp DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ============================================
-- TABLE: package_history (Package purchase history)
-- ============================================
CREATE TABLE `package_history` (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
  `user_id` int(11) UNSIGNED NOT NULL,
  `package_name` varchar(255) NOT NULL,
  `package_amount` decimal(20,4) NOT NULL,
  `profit_percentage` decimal(5,4) NOT NULL,
  `wallet_type` enum('deposit','withdrawal') NOT NULL,
  `purchased_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `expired_at` timestamp NULL DEFAULT NULL,
  `total_earnings_received` decimal(20,4) DEFAULT 0.0000,
  `earnings_limit` decimal(20,4) NOT NULL COMMENT '2x package amount',
  `status` enum('active','expired','upgraded') NOT NULL DEFAULT 'active',
  `duration_days` int(11) NULL COMMENT 'Days the package was active',
  `expiry_reason` varchar(100) DEFAULT NULL COMMENT 'limit_reached, upgraded, etc.',
  PRIMARY KEY (`id`),
  KEY `fk_package_history_user_id` (`user_id`),
  KEY `idx_status` (`status`),
  KEY `idx_purchased_at` (`purchased_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ============================================
-- TABLE: system_settings (Dynamic system configuration)
-- ============================================
CREATE TABLE `system_settings` (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
  `setting_key` varchar(100) NOT NULL UNIQUE,
  `setting_value` text NOT NULL,
  `setting_type` enum('string','number','boolean','json') NOT NULL DEFAULT 'string',
  `description` text DEFAULT NULL,
  `updated_by` int(11) UNSIGNED DEFAULT NULL,
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_setting_key` (`setting_key`),
  KEY `fk_settings_updated_by` (`updated_by`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ============================================
-- TABLE: coin_price_history (Coin price tracking for graphs)
-- ============================================
CREATE TABLE `coin_price_history` (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
  `price` decimal(10,4) NOT NULL,
  `previous_price` decimal(10,4) DEFAULT NULL,
  `change_amount` decimal(10,4) DEFAULT NULL,
  `change_percentage` decimal(8,4) DEFAULT NULL,
  `updated_by` int(11) UNSIGNED NOT NULL,
  `reason` varchar(255) DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `fk_coin_price_updated_by` (`updated_by`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;





-- ============================================
-- TABLE: password_resets (Password reset tokens)
-- ============================================
CREATE TABLE "CREATE TABLE IF NOT EXISTS password_resets (
    id INT AUTO_INCREMENT PRIMARY KEY,
    email VARCHAR(255) NOT NULL,
    token VARCHAR(255) NOT NULL UNIQUE,
    expires_at DATETIME NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_email (email),
    INDEX idx_token (token),
    INDEX idx_expires_at (expires_at)
)";

-- ============================================
-- TABLE: user_sessions (Remember me functionality)
-- ============================================

CREATE TABLE IF NOT EXISTS user_sessions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    session_token VARCHAR(255) NOT NULL UNIQUE,
    expires_at DATETIME NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_session_token (session_token),
    INDEX idx_expires_at (expires_at)
)

-- ============================================
-- INITIAL DATA INSERTS
-- ============================================

-- Insert default admin user (password: admin123)
INSERT INTO `users` (`id`, `user_id`, `name`, `email`, `password`, `is_admin`, `is_active`) VALUES
(1, 'ADMIN001', 'System Administrator', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 1, 1);

-- Insert default packages
INSERT INTO `packages` (`package_name`, `package_amount`, `profit_percentage`, `is_active`, `created_by`) VALUES
('Starter Package', 100.0000, 1.0000, 1, 1),
('Basic Package', 500.0000, 1.2000, 1, 1),
('Premium Package', 1000.0000, 1.5000, 1, 1),
('VIP Package', 5000.0000, 2.0000, 1, 1),
('Elite Package', 10000.0000, 2.5000, 1, 1);

-- Insert default levels with qualification requirements
INSERT INTO `levels` (`level_number`, `distribution_percentage`, `min_direct_referrals`, `is_active`) VALUES
(1, 10.00, 0, 1),  -- Level 1: 10% commission, no qualification needed
(2, 5.00, 3, 1),   -- Level 2: 5% commission, need 3 direct referrals
(3, 3.00, 5, 1);   -- Level 3: 3% commission, need 5 direct referrals

-- Insert default bonuses
INSERT INTO `bonuses` (`bonus_name`, `bonus_fund`, `team_investment`, `bonus_type`, `eligibility_criteria`, `is_active`) VALUES
('Team Building Bonus', 1000.0000, 10000.0000, 'team_bonus', 'Minimum team investment of $10,000', 1),
('Leadership Bonus', 2500.0000, 25000.0000, 'leadership_bonus', 'Minimum team investment of $25,000 and 10 direct referrals', 1),
('Performance Bonus', 5000.0000, 50000.0000, 'performance_bonus', 'Minimum team investment of $50,000 and 20 direct referrals', 1);

-- Insert default system settings
INSERT INTO `system_settings` (`setting_key`, `setting_value`, `setting_type`, `description`, `updated_by`) VALUES
('staking_coin_price', '0.4500', 'number', 'Price per staking coin in USD', 1),
('staking_min_coins', '10', 'number', 'Minimum coins required for staking', 1),
('staking_freeze_months', '6', 'number', 'Number of months for staking freeze period', 1),
('staking_min_usd_amount', '10', 'number', 'Minimum USD amount for staking (must be multiple of this)', 1),
('platform_name', 'NetVis Crypto Platform', 'string', 'Platform name displayed in UI', 1),
('daily_profit_enabled', '1', 'boolean', 'Enable/disable daily profit distribution', 1),
('network_commission_enabled', '1', 'boolean', 'Enable/disable network commission distribution', 1);

-- Insert initial coin price history
INSERT INTO `coin_price_history` (`price`, `updated_by`, `reason`) VALUES
(0.4500, 1, 'Initial system setup');

-- ============================================
-- AUTO_INCREMENT values for tables
-- ============================================
ALTER TABLE `users` MODIFY `id` int(6) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;
ALTER TABLE `packages` MODIFY `id` int(6) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;
ALTER TABLE `bonuses` MODIFY `id` int(6) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;
ALTER TABLE `levels` MODIFY `id` int(6) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;
ALTER TABLE `wallet_history` MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;
ALTER TABLE `deposit_requests` MODIFY `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT;
ALTER TABLE `withdrawal_requests` MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;
ALTER TABLE `staking_records` MODIFY `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT;
ALTER TABLE `package_history` MODIFY `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT;
ALTER TABLE `system_settings` MODIFY `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=8;
ALTER TABLE `coin_price_history` MODIFY `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

-- ============================================
-- FOREIGN KEY CONSTRAINTS
-- ============================================
ALTER TABLE `packages`
  ADD CONSTRAINT `fk_packages_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE,
  ADD CONSTRAINT `fk_packages_updated_by` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE `deposit_requests`
  ADD CONSTRAINT `fk_deposit_requests_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  ADD CONSTRAINT `fk_deposit_requests_reviewed_by` FOREIGN KEY (`reviewed_by`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE `staking_records`
  ADD CONSTRAINT `fk_staking_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  ADD CONSTRAINT `fk_staking_admin` FOREIGN KEY (`created_by_admin`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE `package_history`
  ADD CONSTRAINT `fk_package_history_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE `system_settings`
  ADD CONSTRAINT `fk_settings_updated_by` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE `coin_price_history`
  ADD CONSTRAINT `fk_coin_price_updated_by` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

COMMIT;
