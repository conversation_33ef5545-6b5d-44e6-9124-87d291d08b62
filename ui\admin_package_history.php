<?php
session_start();

if (!isset($_SESSION['user_id']) || !$_SESSION['is_admin']) {
    header("Location: login.html");
    exit();
}

include 'dbcon.php';

// Initialize parameters
$search_user = $_GET['search'] ?? '';
$status_filter = $_GET['status'] ?? 'all';
$limit = 50;
$page = max(1, intval($_GET['page'] ?? 1));
$offset = ($page - 1) * $limit;

// Build query conditions
$conditions = [];
$params = [];
$types = '';

if ($search_user) {
    $conditions[] = "(u.name LIKE ? OR u.user_id LIKE ?)";
    $search_term = "%$search_user%";
    $params = array_merge($params, [$search_term, $search_term]);
    $types .= 'ss';
}

if ($status_filter !== 'all') {
    $conditions[] = "ph.status = ?";
    $params[] = $status_filter;
    $types .= 's';
}

$where_clause = $conditions ? 'WHERE ' . implode(' AND ', $conditions) : '';

// Helper function to execute prepared statements
function executeQuery($conn, $sql, $params, $types)
{
    $stmt = $conn->prepare($sql);
    if ($params) {
        $stmt->bind_param($types, ...$params);
    }
    $stmt->execute();
    $result = $stmt->get_result();
    $stmt->close();
    return $result;
}

// Main query for package history
$history_sql = "SELECT ph.*, u.name as user_name, u.user_id as user_identifier,
                CASE WHEN ph.status = 'active' THEN DATEDIFF(NOW(), ph.purchased_at)
                     WHEN ph.duration_days IS NOT NULL THEN ph.duration_days
                     ELSE DATEDIFF(COALESCE(ph.expired_at, NOW()), ph.purchased_at) END as calculated_duration,
                CASE WHEN ph.status = 'active' THEN (ph.total_earnings_received / ph.earnings_limit) * 100
                     ELSE 100 END as progress_percentage
                FROM package_history ph
                JOIN users u ON ph.user_id = u.id
                $where_clause
                ORDER BY ph.purchased_at DESC
                LIMIT ? OFFSET ?";

$history_params = array_merge($params, [$limit, $offset]);
$history_types = $types . 'ii';
$package_history = executeQuery($conn, $history_sql, $history_params, $history_types)->fetch_all(MYSQLI_ASSOC);

// Count query for pagination
$count_sql = "SELECT COUNT(*) as total FROM package_history ph JOIN users u ON ph.user_id = u.id $where_clause";
$total_records = executeQuery($conn, $count_sql, $params, $types)->fetch_assoc()['total'];
$total_pages = ceil($total_records / $limit);

// Statistics query
$stats_sql = "SELECT COUNT(*) as total_packages,
                     SUM(ph.package_amount) as total_invested,
                     SUM(ph.total_earnings_received) as total_earned,
                     SUM(CASE WHEN ph.status = 'active' THEN 1 ELSE 0 END) as active_packages,
                     SUM(CASE WHEN ph.status = 'expired' THEN 1 ELSE 0 END) as expired_packages,
                     SUM(CASE WHEN ph.status = 'upgraded' THEN 1 ELSE 0 END) as upgraded_packages
              FROM package_history ph JOIN users u ON ph.user_id = u.id $where_clause";
$stats = executeQuery($conn, $stats_sql, $params, $types)->fetch_assoc();

// Helper functions for display
function formatDate($date, $format = 'M j, Y')
{
    return date($format, strtotime($date));
}

function getStatusBadge($status, $reason = null)
{
    $badge = "<span class='status-badge status-$status'>" . ucfirst($status) . "</span>";
    if ($reason) {
        $reasons = ['limit_reached' => 'Limit reached', 'upgraded' => 'Upgraded', 'manual' => 'Manual'];
        $badge .= "<div class='text-xs text-gray-400 mt-1'>" . ($reasons[$reason] ?? $reason) . "</div>";
    }
    return $badge;
}

// Page configuration
$page_title = "Package History Management";
$additional_css = '
<style>
    /* Enhanced table styling */
    .admin-table {
        width: 100%;
        border-collapse: separate;
        border-spacing: 0;
        background: white;
    }

    .admin-table thead {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    .admin-table thead th {
        color: white;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.05em;
        padding: 1rem 1.5rem;
        font-size: 0.75rem;
        border: none;
        position: relative;
        text-align: left;
    }

    .admin-table thead th:last-child {
        text-align: center;
    }

    .admin-table tbody tr {
        transition: all 0.2s ease;
        border-bottom: 1px solid #e2e8f0;
    }

    .admin-table tbody tr:hover {
        background-color: #f8fafc;
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    .admin-table tbody tr:last-child {
        border-bottom: none;
    }

    .admin-table tbody td {
        padding: 1rem 1.5rem;
        vertical-align: middle;
        border: none;
        background-color: white;
    }

    .admin-table tbody tr:hover td {
        background-color: #f8fafc;
    }

    /* Status badges */
    .status-badge {
        display: inline-flex;
        align-items: center;
        padding: 0.25rem 0.75rem;
        border-radius: 9999px;
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.05em;
    }

    .status-active {
        background-color: #d1fae5;
        color: #065f46;
        border: 1px solid #10b981;
    }

    .status-expired {
        background-color: #fee2e2;
        color: #991b1b;
        border: 1px solid #ef4444;
    }

    .status-upgraded {
        background-color: #ddd6fe;
        color: #5b21b6;
        border: 1px solid #8b5cf6;
    }

    /* Progress bars */
    .progress-bar {
        width: 100%;
        height: 0.5rem;
        background-color: #e5e7eb;
        border-radius: 9999px;
        overflow: hidden;
    }

    .progress-fill {
        height: 100%;
        background: linear-gradient(90deg, #10b981, #059669);
        border-radius: 9999px;
        transition: width 0.3s ease;
    }

    /* Compact Filter section */
    .filter-container {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 0.75rem;
        padding: 1rem 1.5rem;
        margin-bottom: 1.5rem;
    }

    .filter-input {
        background: rgba(255, 255, 255, 0.1);
        border: 2px solid rgba(255, 255, 255, 0.2);
        color: white;
        border-radius: 0.5rem;
        transition: all 0.3s ease;
        font-size: 0.875rem;
    }

    .filter-input::placeholder {
        color: rgba(255, 255, 255, 0.7);
    }

    .filter-input:focus {
        outline: none;
        border-color: rgba(255, 255, 255, 0.5);
        background: rgba(255, 255, 255, 0.15);
        box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.1);
    }

    .filter-select {
        background: rgba(255, 255, 255, 0.1);
        border: 2px solid rgba(255, 255, 255, 0.2);
        color: white;
        border-radius: 0.5rem;
        transition: all 0.3s ease;
        font-size: 0.875rem;
        appearance: none;
        background-image: linear-gradient(45deg, transparent 50%, rgba(255,255,255,0.7) 50%), linear-gradient(135deg, rgba(255,255,255,0.7) 50%, transparent 50%);
        background-position: calc(100% - 12px) calc(1em + 2px), calc(100% - 8px) calc(1em + 2px);
        background-size: 4px 4px, 4px 4px;
        background-repeat: no-repeat;
    }

    .filter-select:focus {
        outline: none;
        border-color: rgba(255, 255, 255, 0.5);
        background: rgba(255, 255, 255, 0.15);
    }

    .filter-select option {
        background: #4c51bf;
        color: white;
    }

    .filter-btn-compact {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 2.5rem;
        height: 2.5rem;
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-radius: 0.5rem;
        color: white;
        text-decoration: none;
        transition: all 0.3s ease;
        backdrop-filter: blur(10px);
    }

    .filter-btn-compact:hover {
        border-color: rgba(255, 255, 255, 0.5);
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    }

    /* Pagination */
    .pagination-btn {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        padding: 0.5rem 1rem;
        border-radius: 0.375rem;
        font-weight: 500;
        font-size: 0.875rem;
        transition: all 0.2s ease;
        border: 1px solid #e2e8f0;
        background: white;
        color: #374151;
        text-decoration: none;
    }

    .pagination-btn:hover {
        background: #f8fafc;
        border-color: #d1d5db;
        transform: translateY(-1px);
    }

    .pagination-btn.active {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-color: #667eea;
    }

    .pagination-btn:disabled {
        opacity: 0.5;
        cursor: not-allowed;
    }
</style>
';

// Start output buffering to capture the page content
ob_start();

$conn->close();
?>

<!-- Page Header -->
<div class="flex flex-col lg:flex-row justify-between items-start lg:items-center space-y-4 lg:space-y-0 mb-6">
    <div>
        <h1 class="text-3xl font-bold text-gray-900">Package History Management</h1>
        <p class="text-gray-600 mt-1">Complete history of all user package purchases and performance</p>
    </div>
    <div class="flex items-center space-x-2 bg-blue-50 px-4 py-2 rounded-lg border border-blue-200">
        <i class="fas fa-history text-blue-600"></i>
        <span class="text-sm font-medium text-blue-700">Package History</span>
    </div>
</div>

<!-- Summary Statistics -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4 mb-6">
    <?php
    $stat_cards = [
        ['icon' => 'box', 'color' => 'blue', 'label' => 'Total Packages', 'value' => number_format($stats['total_packages'])],
        ['icon' => 'dollar-sign', 'color' => 'green', 'label' => 'Total Invested', 'value' => '$' . number_format($stats['total_invested'], 0)],
        ['icon' => 'chart-line', 'color' => 'purple', 'label' => 'Total Earned', 'value' => '$' . number_format($stats['total_earned'], 0)],
        ['icon' => 'play', 'color' => 'yellow', 'label' => 'Active', 'value' => number_format($stats['active_packages'])],
        ['icon' => 'times-circle', 'color' => 'red', 'label' => 'Expired', 'value' => number_format($stats['expired_packages'])],
        ['icon' => 'arrow-up', 'color' => 'indigo', 'label' => 'Upgraded', 'value' => number_format($stats['upgraded_packages'])]
    ];

    foreach ($stat_cards as $card): ?>
        <div class="admin-card p-4">
            <div class="flex items-center">
                <div class="w-10 h-10 bg-<?php echo $card['color']; ?>-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-<?php echo $card['icon']; ?> text-<?php echo $card['color']; ?>-600"></i>
                </div>
                <div class="ml-3">
                    <p class="text-xs font-medium text-gray-600"><?php echo $card['label']; ?></p>
                    <p class="text-lg font-bold text-gray-900"><?php echo $card['value']; ?></p>
                </div>
            </div>
        </div>
    <?php endforeach; ?>
</div>

<!-- Compact Filter Section -->
<div class="filter-container">
    <form method="GET" class="flex items-center gap-3">
        <!-- Search Input with Icon -->
        <div class="flex-1 relative">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <i class="fas fa-search text-white text-opacity-70"></i>
            </div>
            <input type="text" id="search" name="search" value="<?php echo htmlspecialchars($search_user); ?>"
                placeholder="Search by name or user ID..."
                class="filter-input pl-10 pr-4 py-2.5 w-full">
        </div>

        <!-- Status Filter with Icon -->
        <div class="relative">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <i class="fas fa-filter text-white text-opacity-70"></i>
            </div>
            <select id="status" name="status" class="filter-select pl-10 pr-8 py-2.5 min-w-32">
                <?php
                $status_options = ['all' => 'All', 'active' => 'Active', 'expired' => 'Expired', 'upgraded' => 'Upgraded'];
                foreach ($status_options as $value => $label): ?>
                    <option value="<?= $value ?>" <?= $status_filter === $value ? 'selected' : '' ?>><?= $label ?></option>
                <?php endforeach; ?>
            </select>
        </div>

        <!-- Inline Action Buttons -->
        <div class="flex items-center space-x-2">
            <button type="submit" class="filter-btn-compact bg-white bg-opacity-20 hover:bg-opacity-30">
                <i class="fas fa-search"></i>
            </button>
            <a href="admin_package_history.php" class="filter-btn-compact bg-white bg-opacity-10 hover:bg-opacity-20">
                <i class="fas fa-undo"></i>
            </a>
        </div>
    </form>
</div>

<!-- Package History Table -->
<div class="admin-card p-0 overflow-hidden">
    <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center space-x-3">
            <div class="w-8 h-8 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-lg flex items-center justify-center">
                <i class="fas fa-history text-white text-sm"></i>
            </div>
            <div>
                <h3 class="text-lg font-semibold text-gray-900">Package History Records</h3>
                <p class="text-sm text-gray-600">Complete history of user package investments and performance</p>
            </div>
        </div>
    </div>

    <div class="overflow-x-auto">
        <table class="admin-table">
            <thead>
                <tr>
                    <th>User</th>
                    <th>Package</th>
                    <th>Amount</th>
                    <th>Purchased</th>
                    <th>Duration</th>
                    <th>Earnings</th>
                    <th>Progress</th>
                    <th>Status</th>
                </tr>
            </thead>
            <tbody>
                <?php if (empty($package_history)): ?>
                    <tr>
                        <td colspan="8" class="text-center py-12">
                            <div class="flex flex-col items-center">
                                <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                                    <i class="fas fa-history text-gray-400 text-2xl"></i>
                                </div>
                                <h3 class="text-lg font-medium text-gray-900 mb-2">No Package History Found</h3>
                                <p class="text-sm text-gray-500">No package history records match your search criteria.</p>
                            </div>
                        </td>
                    </tr>
                <?php else: ?>
                    <?php foreach ($package_history as $r):
                        $roi = $r['package_amount'] > 0 ? ($r['total_earnings_received'] / $r['package_amount']) * 100 : 0;
                        $progress = min($r['progress_percentage'], 100);
                    ?>
                        <tr>
                            <td>
                                <div class="flex items-center space-x-3">
                                    <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                        <i class="fas fa-user text-blue-600 text-xs"></i>
                                    </div>
                                    <div>
                                        <div class="text-sm font-medium text-gray-900"><?= htmlspecialchars($r['user_name']) ?></div>
                                        <div class="text-xs text-gray-500">ID: <?= htmlspecialchars($r['user_identifier']) ?></div>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div class="flex items-center space-x-2">
                                    <div class="w-6 h-6 bg-purple-100 rounded flex items-center justify-center">
                                        <i class="fas fa-box text-purple-600 text-xs"></i>
                                    </div>
                                    <div>
                                        <div class="text-sm font-medium text-gray-900"><?= htmlspecialchars($r['package_name']) ?></div>
                                        <div class="text-xs text-gray-500"><?= $r['profit_percentage'] ?>% daily</div>
                                    </div>
                                </div>
                            </td>
                            <td class="text-center">
                                <div class="text-lg font-bold text-green-600">$<?= number_format($r['package_amount'], 2) ?></div>
                                <div class="text-xs text-gray-500">Limit: $<?= number_format($r['earnings_limit'], 2) ?></div>
                            </td>
                            <td class="text-center">
                                <div class="text-sm font-medium text-gray-900"><?= formatDate($r['purchased_at']) ?></div>
                                <div class="text-xs text-gray-500"><?= formatDate($r['purchased_at'], 'g:i A') ?></div>
                            </td>
                            <td class="text-center">
                                <div class="text-sm font-medium text-gray-900"><?= $r['calculated_duration'] ?> days</div>
                                <?php if ($r['expired_at']): ?>
                                    <div class="text-xs text-gray-500">Ended: <?= formatDate($r['expired_at'], 'M j') ?></div>
                                <?php endif; ?>
                            </td>
                            <td class="text-center">
                                <div class="text-lg font-bold text-purple-600">$<?= number_format($r['total_earnings_received'], 2) ?></div>
                                <div class="text-xs text-gray-500"><?= number_format($roi, 1) ?>% ROI</div>
                            </td>
                            <td class="text-center">
                                <div class="progress-bar mb-2">
                                    <div class="progress-fill" style="width: <?= $progress ?>%"></div>
                                </div>
                                <div class="text-xs font-medium text-gray-600"><?= number_format($progress, 1) ?>%</div>
                            </td>
                            <td class="text-center">
                                <?= getStatusBadge($r['status'], $r['expiry_reason']) ?>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                <?php endif; ?>
            </tbody>
        </table>
    </div>

    <!-- Enhanced Pagination -->
    <?php if ($total_pages > 1): ?>
        <div class="px-6 py-4 border-t border-gray-200 bg-gray-50">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-2">
                    <i class="fas fa-info-circle text-blue-600"></i>
                    <span class="text-sm text-gray-700">
                        Showing <?php echo $offset + 1; ?> to <?php echo min($offset + $limit, $total_records); ?> of <?php echo number_format($total_records); ?> results
                    </span>
                </div>
                <div class="flex items-center space-x-1">
                    <?php if ($page > 1): ?>
                        <a href="?page=<?php echo $page - 1; ?>&search=<?php echo urlencode($search_user); ?>&status=<?php echo urlencode($status_filter); ?>"
                            class="pagination-btn">
                            <i class="fas fa-chevron-left mr-1"></i>
                            Previous
                        </a>
                    <?php endif; ?>

                    <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                        <a href="?page=<?php echo $i; ?>&search=<?php echo urlencode($search_user); ?>&status=<?php echo urlencode($status_filter); ?>"
                            class="pagination-btn <?php echo $i === $page ? 'active' : ''; ?>">
                            <?php echo $i; ?>
                        </a>
                    <?php endfor; ?>

                    <?php if ($page < $total_pages): ?>
                        <a href="?page=<?php echo $page + 1; ?>&search=<?php echo urlencode($search_user); ?>&status=<?php echo urlencode($status_filter); ?>"
                            class="pagination-btn">
                            Next
                            <i class="fas fa-chevron-right ml-1"></i>
                        </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>

<?php
// Capture the page content
$page_content = ob_get_clean();

// Include the admin layout
include 'admin/includes/layout.php';
?>