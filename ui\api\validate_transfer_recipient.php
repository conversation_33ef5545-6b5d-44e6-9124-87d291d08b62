<?php
ini_set('display_errors', 0);
error_reporting(0);

session_start();
header('Content-Type: application/json');

if (!isset($_SESSION['user_id'])) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Authentication required.']);
    exit();
}

include '../dbcon.php';

// Check database connection
if ($conn->connect_error) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Database connection failed.']);
    exit();
}

$data = json_decode(file_get_contents('php://input'), true);
$recipient_user_id = $data['recipient_user_id'] ?? '';
$sender_id = $_SESSION['user_id'];

if (empty($recipient_user_id)) {
    echo json_encode(['success' => false, 'message' => 'User ID is required.']);
    exit();
}

try {
    // 1. Get sender's details
    $sender_sql = "SELECT id, sponser_id, name FROM users WHERE id = ?";
    $stmt = $conn->prepare($sender_sql);
    $stmt->bind_param("i", $sender_id);
    $stmt->execute();
    $sender_result = $stmt->get_result();
    
    if ($sender_result->num_rows === 0) {
        throw new Exception('Sender not found.');
    }
    
    $sender = $sender_result->fetch_assoc();
    $stmt->close();

    // 2. Get recipient's details
    $recipient_sql = "SELECT id, sponser_id, name FROM users WHERE user_id = ? AND is_active = 1 AND deleted_at IS NULL";
    $stmt = $conn->prepare($recipient_sql);
    $stmt->bind_param("s", $recipient_user_id);
    $stmt->execute();
    $recipient_result = $stmt->get_result();
    
    if ($recipient_result->num_rows === 0) {
        echo json_encode(['success' => false, 'message' => 'User not found or inactive.']);
        exit();
    }
    
    $recipient = $recipient_result->fetch_assoc();
    $stmt->close();

    // 3. Check if sender is trying to transfer to themselves
    if ($sender['id'] == $recipient['id']) {
        echo json_encode(['success' => false, 'message' => 'You cannot transfer funds to yourself.']);
        exit();
    }

    // 4. Check relationship
    $relationship = '';
    $is_valid_recipient = false;

    // Case 1: Recipient is the sender's direct sponsor
    if ($sender['sponser_id'] == $recipient['id']) {
        $is_valid_recipient = true;
        $relationship = 'sponsor';
    } else {
        // Case 2: Recipient is in the sender's downline (any level)
        $sql_downline_check = '
            WITH RECURSIVE downline_check (id) AS (
                SELECT id FROM users WHERE sponser_id = ?
                UNION ALL
                SELECT u.id FROM users u JOIN downline_check d ON u.sponser_id = d.id
            )
            SELECT COUNT(*) as count FROM downline_check WHERE id = ?
        ';
        $stmt_check = $conn->prepare($sql_downline_check);
        $stmt_check->bind_param('ii', $sender['id'], $recipient['id']);
        $stmt_check->execute();
        $is_downline = $stmt_check->get_result()->fetch_assoc()['count'] > 0;
        $stmt_check->close();

        if ($is_downline) {
            $is_valid_recipient = true;
            $relationship = 'downline';
        }
    }

    if (!$is_valid_recipient) {
        echo json_encode([
            'success' => false, 
            'message' => 'Invalid recipient. You can only transfer to your direct sponsor or downline members.'
        ]);
        exit();
    }

    // Valid recipient
    echo json_encode([
        'success' => true,
        'user' => [
            'id' => $recipient['id'],
            'name' => $recipient['name']
        ],
        'relationship' => $relationship
    ]);

} catch (Exception $e) {
    error_log('Recipient Validation Error: ' . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'An error occurred during validation.']);
} finally {
    if (isset($stmt)) {
        $stmt->close();
    }
    $conn->close();
}
?>
