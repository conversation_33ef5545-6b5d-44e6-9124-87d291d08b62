<?php
session_start();
$_SESSION['admin_logged_in'] = true;
$_SESSION['admin_id'] = 1;

include 'dbcon.php';

echo "<h2>🔧 Dashboard Column Fix Tool</h2>";

// Function to test query and suggest fixes
function testAndFixQuery($conn, $query, $description) {
    echo "<h3>🧪 Testing: $description</h3>";
    echo "<p><strong>Query:</strong> <code>$query</code></p>";
    
    $result = $conn->query($query);
    if ($result) {
        echo "<p style='color: green;'>✅ Query successful! Rows: " . $result->num_rows . "</p>";
        return $result;
    } else {
        echo "<p style='color: red;'>❌ Query failed: " . $conn->error . "</p>";
        
        // Try to suggest fixes for common column name issues
        if (strpos($conn->error, 'full_name') !== false) {
            echo "<p style='color: orange;'>🔧 Trying alternative: replacing 'full_name' with 'name'</p>";
            $fixed_query = str_replace('full_name', 'name', $query);
            echo "<p><strong>Fixed Query:</strong> <code>$fixed_query</code></p>";
            
            $fixed_result = $conn->query($fixed_query);
            if ($fixed_result) {
                echo "<p style='color: green;'>✅ Fixed query successful! Rows: " . $fixed_result->num_rows . "</p>";
                return $fixed_result;
            } else {
                echo "<p style='color: red;'>❌ Fixed query also failed: " . $conn->error . "</p>";
                
                // Try username
                echo "<p style='color: orange;'>🔧 Trying alternative: replacing 'name' with 'username'</p>";
                $fixed_query2 = str_replace('name', 'username', $fixed_query);
                echo "<p><strong>Fixed Query 2:</strong> <code>$fixed_query2</code></p>";
                
                $fixed_result2 = $conn->query($fixed_query2);
                if ($fixed_result2) {
                    echo "<p style='color: green;'>✅ Second fix successful! Rows: " . $fixed_result2->num_rows . "</p>";
                    return $fixed_result2;
                } else {
                    echo "<p style='color: red;'>❌ All attempts failed</p>";
                }
            }
        }
        return false;
    }
}

// Test all dashboard queries
echo "<div style='background: #f0f9ff; border: 2px solid #0ea5e9; border-radius: 12px; padding: 20px; margin: 20px 0;'>";
echo "<h3 style='color: #0c4a6e; margin-top: 0;'>🎯 Dashboard Query Tests</h3>";

// Test 1: Users query
$users_query = "SELECT user_id, full_name, created_at FROM users WHERE is_admin = 0 ORDER BY created_at DESC LIMIT 5";
$users_result = testAndFixQuery($conn, $users_query, "Recent Users");

// Test 2: Deposits query  
$deposits_query = "SELECT dr.*, u.full_name FROM deposit_requests dr JOIN users u ON dr.user_id = u.user_id ORDER BY dr.created_at DESC LIMIT 5";
$deposits_result = testAndFixQuery($conn, $deposits_query, "Recent Deposits");

// Test 3: Withdrawals query
$withdrawals_query = "SELECT wr.*, u.full_name FROM withdrawal_requests wr JOIN users u ON wr.user_id = u.user_id ORDER BY wr.created_at DESC LIMIT 5";
$withdrawals_result = testAndFixQuery($conn, $withdrawals_query, "Recent Withdrawals");

echo "</div>";

// Show working queries
echo "<div style='background: #dcfce7; border: 2px solid #16a34a; border-radius: 12px; padding: 20px; margin: 20px 0;'>";
echo "<h3 style='color: #15803d; margin-top: 0;'>✅ Working Queries for Dashboard</h3>";

// Generate corrected queries based on what worked
echo "<h4>📝 Copy these corrected queries to your dashboard:</h4>";

// Check users table structure to determine correct column
$desc_result = $conn->query("DESCRIBE users");
$user_columns = [];
if ($desc_result) {
    while ($row = $desc_result->fetch_assoc()) {
        $user_columns[] = $row['Field'];
    }
}

$name_column = 'user_id'; // fallback
if (in_array('name', $user_columns)) {
    $name_column = 'name';
} elseif (in_array('username', $user_columns)) {
    $name_column = 'username';
} elseif (in_array('full_name', $user_columns)) {
    $name_column = 'full_name';
}

echo "<div style='background: #f0fdf4; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
echo "<h5 style='color: #166534; margin-top: 0;'>1. Recent Users Query:</h5>";
echo "<code style='display: block; background: white; padding: 10px; border-radius: 4px; color: #166534;'>";
echo "SELECT user_id, $name_column, created_at FROM users WHERE is_admin = 0 ORDER BY created_at DESC LIMIT 5";
echo "</code>";
echo "</div>";

echo "<div style='background: #f0fdf4; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
echo "<h5 style='color: #166534; margin-top: 0;'>2. Recent Deposits Query:</h5>";
echo "<code style='display: block; background: white; padding: 10px; border-radius: 4px; color: #166534;'>";
echo "SELECT dr.*, u.$name_column FROM deposit_requests dr JOIN users u ON dr.user_id = u.user_id ORDER BY dr.created_at DESC LIMIT 5";
echo "</code>";
echo "</div>";

echo "<div style='background: #f0fdf4; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
echo "<h5 style='color: #166534; margin-top: 0;'>3. Recent Withdrawals Query:</h5>";
echo "<code style='display: block; background: white; padding: 10px; border-radius: 4px; color: #166534;'>";
echo "SELECT wr.*, u.$name_column FROM withdrawal_requests wr JOIN users u ON wr.user_id = u.user_id ORDER BY wr.created_at DESC LIMIT 5";
echo "</code>";
echo "</div>";

echo "<div style='background: #f0fdf4; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
echo "<h5 style='color: #166534; margin-top: 0;'>4. Display Code Fix:</h5>";
echo "<code style='display: block; background: white; padding: 10px; border-radius: 4px; color: #166534;'>";
echo "// In the display sections, use:<br>";
echo "htmlspecialchars(\$user['$name_column'])<br>";
echo "htmlspecialchars(\$deposit['$name_column'])<br>";
echo "htmlspecialchars(\$withdrawal['$name_column'])";
echo "</code>";
echo "</div>";

echo "</div>";

// Test basic statistics
echo "<h3>📊 Basic Statistics Test:</h3>";
$stats_queries = [
    'Total Users' => "SELECT COUNT(*) as count FROM users WHERE is_admin = 0",
    'Total Deposits' => "SELECT COUNT(*) as count, COALESCE(SUM(amount), 0) as total FROM deposit_requests",
    'Total Withdrawals' => "SELECT COUNT(*) as count, COALESCE(SUM(amount), 0) as total FROM withdrawal_requests"
];

foreach ($stats_queries as $label => $query) {
    $result = $conn->query($query);
    if ($result) {
        $data = $result->fetch_assoc();
        echo "<p style='color: green;'>✅ <strong>$label:</strong> " . $data['count'] . " records";
        if (isset($data['total'])) {
            echo " (Total: $" . number_format($data['total'], 2) . ")";
        }
        echo "</p>";
    } else {
        echo "<p style='color: red;'>❌ <strong>$label:</strong> Query failed - " . $conn->error . "</p>";
    }
}

echo "<div style='background: #fef3c7; border: 2px solid #f59e0b; border-radius: 12px; padding: 20px; margin: 20px 0;'>";
echo "<h3 style='color: #92400e; margin-top: 0;'>🚀 Next Steps:</h3>";
echo "<ol style='color: #b45309;'>";
echo "<li>The dashboard has been updated with fallback column names</li>";
echo "<li>Try accessing the dashboard now: <a href='admin_dashboard.php' style='color: #3b82f6;'>admin_dashboard.php</a></li>";
echo "<li>If you still see errors, use the working queries shown above</li>";
echo "<li>The dashboard will now show 'Unknown User' if no name column is found</li>";
echo "</ol>";
echo "</div>";

$conn->close();
?>

<style>
body { 
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; 
    margin: 20px; 
    background: #f8fafc;
    line-height: 1.6;
}
h2, h3, h4, h5 { color: #1f2937; }
code { 
    background: #f1f5f9; 
    padding: 2px 6px; 
    border-radius: 4px; 
    font-family: monospace;
    font-size: 0.9em;
}
</style>
