<?php
session_start();

// Simple admin check - bypass for testing
$_SESSION['admin_logged_in'] = true;
$_SESSION['admin_id'] = 1;

// Include database connection
include 'dbcon.php';

// Set page title
$page_title = 'Admin Dashboard';

// Debug: Check if we should show debug info
$show_debug = isset($_GET['debug']) && $_GET['debug'] === '1';

// Fetch dashboard statistics
try {
    // Users Statistics
    $total_users_query = "SELECT COUNT(*) as total FROM users WHERE is_admin = 0";
    $total_users_result = $conn->query($total_users_query);
    $total_users = $total_users_result->fetch_assoc()['total'];

    $active_users_query = "SELECT COUNT(*) as active FROM users WHERE is_admin = 0 AND plan_status = 'active'";
    $active_users_result = $conn->query($active_users_query);
    $active_users = $active_users_result->fetch_assoc()['active'];

    $expired_users_query = "SELECT COUNT(*) as expired FROM users WHERE is_admin = 0 AND plan_status = 'expired'";
    $expired_users_result = $conn->query($expired_users_query);
    $expired_users = $expired_users_result->fetch_assoc()['expired'];

    $new_users_today_query = "SELECT COUNT(*) as new_today FROM users WHERE is_admin = 0 AND DATE(reg_date) = CURDATE()";
    $new_users_today_result = $conn->query($new_users_today_query);
    $new_users_today = $new_users_today_result->fetch_assoc()['new_today'];

    // Financial Statistics
    $total_deposits_query = "SELECT COALESCE(SUM(amount), 0) as total FROM deposit_requests WHERE status = 'approved'";
    $total_deposits_result = $conn->query($total_deposits_query);
    $total_deposits = $total_deposits_result->fetch_assoc()['total'];

    $total_withdrawals_query = "SELECT COALESCE(SUM(amount), 0) as total FROM withdrawal_requests WHERE status = 'approved'";
    $total_withdrawals_result = $conn->query($total_withdrawals_query);
    $total_withdrawals = $total_withdrawals_result->fetch_assoc()['total'];

    $pending_deposits_query = "SELECT COUNT(*) as pending FROM deposit_requests WHERE status = 'pending'";
    $pending_deposits_result = $conn->query($pending_deposits_query);
    $pending_deposits = $pending_deposits_result->fetch_assoc()['pending'];

    $pending_withdrawals_query = "SELECT COUNT(*) as pending FROM withdrawal_requests WHERE status = 'pending'";
    $pending_withdrawals_result = $conn->query($pending_withdrawals_query);
    $pending_withdrawals = $pending_withdrawals_result->fetch_assoc()['pending'];

    // Package Statistics
    $total_packages_query = "SELECT COUNT(*) as total FROM packages";
    $total_packages_result = $conn->query($total_packages_query);
    $total_packages = $total_packages_result->fetch_assoc()['total'];

    $total_investments_query = "SELECT COALESCE(SUM(package_amount), 0) as total FROM package_history";
    $total_investments_result = $conn->query($total_investments_query);
    $total_investments = $total_investments_result->fetch_assoc()['total'];

    // Staking Statistics
    $active_staking_query = "SELECT COUNT(*) as active, COALESCE(SUM(amount_usd), 0) as total_amount FROM staking_records WHERE status = 'active'";
    $active_staking_result = $conn->query($active_staking_query);
    $staking_data = $active_staking_result->fetch_assoc();
    $active_staking_count = $staking_data['active'];
    $total_staking_amount = $staking_data['total_amount'];

    // Wallet Balances
    $total_deposit_balance_query = "SELECT COALESCE(SUM(deposit_wallet_balance), 0) as total FROM users WHERE is_admin = 0";
    $total_deposit_balance_result = $conn->query($total_deposit_balance_query);
    $total_deposit_balance = $total_deposit_balance_result->fetch_assoc()['total'];

    $total_withdrawal_balance_query = "SELECT COALESCE(SUM(withdrawal_wallet_balance), 0) as total FROM users WHERE is_admin = 0";
    $total_withdrawal_balance_result = $conn->query($total_withdrawal_balance_query);
    $total_withdrawal_balance = $total_withdrawal_balance_result->fetch_assoc()['total'];

    // Recent Activities - using actual database column names (not schema.sql)
    $recent_users_query = "SELECT id, name, user_id, package, reg_date FROM users WHERE is_admin = 0 ORDER BY reg_date DESC LIMIT 5";
    $recent_users_result = $conn->query($recent_users_query);
    if (!$recent_users_result) {
        error_log("Recent users query failed: " . $conn->error);
        // Try alternative if reg_date doesn't exist
        $recent_users_query = "SELECT id, name, user_id, package, updated_at FROM users WHERE is_admin = 0 ORDER BY updated_at DESC LIMIT 5";
        $recent_users_result = $conn->query($recent_users_query);
    }

    // Deposits using schema.sql column names (requested_at)
    $recent_deposits_query = "SELECT dr.id, dr.user_id, dr.amount, dr.status, dr.requested_at, u.name, u.user_id as public_user_id FROM deposit_requests dr JOIN users u ON dr.user_id = u.id ORDER BY dr.requested_at DESC LIMIT 5";
    $recent_deposits_result = $conn->query($recent_deposits_query);
    if (!$recent_deposits_result) {
        error_log("Recent deposits query failed: " . $conn->error);
    }

    // Withdrawals using schema.sql column names (requested_at)
    $recent_withdrawals_query = "SELECT wr.id, wr.user_id, wr.amount, wr.status, wr.requested_at, u.name, u.user_id as public_user_id FROM withdrawal_requests wr JOIN users u ON wr.user_id = u.id ORDER BY wr.requested_at DESC LIMIT 5";
    $recent_withdrawals_result = $conn->query($recent_withdrawals_query);
    if (!$recent_withdrawals_result) {
        error_log("Recent withdrawals query failed: " . $conn->error);
    }

    // Monthly Growth Data for Charts
    $monthly_users_query = "SELECT DATE_FORMAT(reg_date, '%Y-%m') as month, COUNT(*) as count FROM users WHERE is_admin = 0 AND reg_date >= DATE_SUB(NOW(), INTERVAL 6 MONTH) GROUP BY DATE_FORMAT(reg_date, '%Y-%m') ORDER BY month";
    $monthly_users_result = $conn->query($monthly_users_query);
    $monthly_users_data = [];
    while ($row = $monthly_users_result->fetch_assoc()) {
        $monthly_users_data[] = $row;
    }

    $monthly_deposits_query = "SELECT DATE_FORMAT(requested_at, '%Y-%m') as month, COALESCE(SUM(amount), 0) as total FROM deposit_requests WHERE status = 'approved' AND requested_at >= DATE_SUB(NOW(), INTERVAL 6 MONTH) GROUP BY DATE_FORMAT(requested_at, '%Y-%m') ORDER BY month";
    $monthly_deposits_result = $conn->query($monthly_deposits_query);
    $monthly_deposits_data = [];
    while ($row = $monthly_deposits_result->fetch_assoc()) {
        $monthly_deposits_data[] = $row;
    }

    // Current coin price
    $coin_price_query = "SELECT price FROM coin_price_history ORDER BY created_at DESC LIMIT 1";
    $coin_price_result = $conn->query($coin_price_query);
    $current_coin_price = $coin_price_result->num_rows > 0 ? $coin_price_result->fetch_assoc()['price'] : 0.45;
} catch (Exception $e) {
    error_log("Dashboard Error: " . $e->getMessage());
    // Set default values if queries fail
    $total_users = $active_users = $new_users_today = 0;
    $expired_users = 0; // Add missing variable
    $total_deposits = $total_withdrawals = 0;
    $pending_deposits = $pending_withdrawals = 0;
    $total_packages = $total_investments = 0;
    $active_staking_count = $total_staking_amount = 0;
    $total_deposit_balance = $total_withdrawal_balance = 0;
    $monthly_users_data = $monthly_deposits_data = [];
    $current_coin_price = 0.45;

    // Initialize result variables to prevent undefined variable warnings
    $recent_users_result = null;
    $recent_deposits_result = null;
    $recent_withdrawals_result = null;
}

// Debug: Check if queries executed successfully and show any errors
if (!isset($recent_users_result) || $recent_users_result === false) {
    error_log("Recent users query failed: " . $conn->error);
    $recent_users_result = null;
}

if (!isset($recent_deposits_result) || $recent_deposits_result === false) {
    error_log("Recent deposits query failed: " . $conn->error);
    $recent_deposits_result = null;
}

if (!isset($recent_withdrawals_result) || $recent_withdrawals_result === false) {
    error_log("Recent withdrawals query failed: " . $conn->error);
    $recent_withdrawals_result = null;
}

// Debug output if requested
if ($show_debug) {
    echo "<div style='background: #fef3c7; border: 2px solid #f59e0b; padding: 20px; margin: 20px; border-radius: 12px;'>";
    echo "<h3 style='color: #92400e;'>🔍 Dashboard Debug Info</h3>";
    echo "<p><strong>Total Users:</strong> $total_users</p>";
    echo "<p><strong>Active Users:</strong> $active_users</p>";
    echo "<p><strong>Expired Users:</strong> $expired_users</p>";
    echo "<p><strong>Total Deposits:</strong> $total_deposits</p>";
    echo "<p><strong>Total Withdrawals:</strong> $total_withdrawals</p>";
    echo "<p><strong>Active Staking:</strong> $active_staking_count ($total_staking_amount)</p>";
    echo "<p><strong>Recent Users Result:</strong> " . ($recent_users_result ? $recent_users_result->num_rows . ' rows' : 'NULL') . "</p>";
    echo "<p><strong>Recent Deposits Result:</strong> " . ($recent_deposits_result ? $recent_deposits_result->num_rows . ' rows' : 'NULL') . "</p>";
    echo "<p><strong>Recent Withdrawals Result:</strong> " . ($recent_withdrawals_result ? $recent_withdrawals_result->num_rows . ' rows' : 'NULL') . "</p>";
    echo "</div>";
}
// Additional CSS for dashboard-specific styles
$additional_css = '
<style>
    /* Scrollable Activity Cards */
    .scrollbar-thin {
        scrollbar-width: thin;
        scrollbar-color: #cbd5e1 #f1f5f9;
    }

    .scrollbar-thumb-gray-300::-webkit-scrollbar-thumb {
        background-color: #cbd5e1;
        border-radius: 3px;
    }

    .scrollbar-track-gray-100::-webkit-scrollbar-track {
        background-color: #f3f4f6;
    }

    /* Smooth scrolling for activity cards */
    .max-h-80 {
        max-height: 20rem;
        scroll-behavior: smooth;
    }

    /* Enhanced hover effects for activity items */
    .admin-card .space-y-3>div:hover {
        transform: translateX(2px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    /* Chart Container */
    .chart-container {
        position: relative;
        height: 300px;
        width: 100%;
    }
</style>
';

// Dashboard content
$page_content = '
    <!-- Dashboard Content -->
    <div class="space-y-6 fade-in">';

// Start output buffering to capture the dashboard content
ob_start();
?>
<!-- Dashboard Content -->
<div class="space-y-6 fade-in">
    <!-- Page Header -->
    <div class="flex flex-col lg:flex-row justify-between items-start lg:items-center space-y-4 lg:space-y-0">
        <div>
            <h1 class="text-3xl font-bold text-gray-900">Admin Dashboard</h1>
            <p class="text-gray-600 mt-1">Welcome back! Here's what's happening with your platform.</p>
        </div>
        <div class="flex items-center space-x-3">
            <div class="flex items-center space-x-2 bg-green-50 px-4 py-2 rounded-lg border border-green-200">
                <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                <span class="text-sm font-medium text-green-700">System Online</span>
            </div>
            <div class="text-sm text-gray-500">
                Last updated: <?php echo date('M d, Y H:i'); ?>
            </div>
        </div>
    </div>

    <!-- Key Metrics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <!-- Total Users -->
        <div class="admin-card p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600">Total Users</p>
                    <p class="text-3xl font-bold text-gray-900"><?php echo number_format($total_users); ?></p>
                    <p class="text-sm text-green-600 mt-1">
                        <i class="fas fa-arrow-up mr-1"></i>
                        <?php echo $new_users_today; ?> new today
                    </p>
                </div>
                <div class="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">
                    <i class="fas fa-users text-blue-600 text-xl"></i>
                </div>
            </div>
        </div>

        <!-- Active Users -->
        <div class="admin-card p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600">Active Users</p>
                    <p class="text-3xl font-bold text-gray-900"><?php echo number_format($active_users); ?></p>
                    <p class="text-sm text-gray-500 mt-1">
                        <?php echo $total_users > 0 ? round(($active_users / $total_users) * 100, 1) : 0; ?>% of total
                    </p>
                </div>
                <div class="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center">
                    <i class="fas fa-user-check text-green-600 text-xl"></i>
                </div>
            </div>
        </div>

        <!-- Total Deposits -->
        <div class="admin-card p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600">Total Deposits</p>
                    <p class="text-3xl font-bold text-gray-900">$<?php echo number_format($total_deposits, 2); ?></p>
                    <p class="text-sm text-orange-600 mt-1">
                        <i class="fas fa-clock mr-1"></i>
                        <?php echo $pending_deposits; ?> pending
                    </p>
                </div>
                <div class="w-12 h-12 bg-emerald-100 rounded-xl flex items-center justify-center">
                    <i class="fas fa-arrow-down text-emerald-600 text-xl"></i>
                </div>
            </div>
        </div>

        <!-- Total Withdrawals -->
        <div class="admin-card p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600">Total Withdrawals</p>
                    <p class="text-3xl font-bold text-gray-900">$<?php echo number_format($total_withdrawals, 2); ?></p>
                    <p class="text-sm text-red-600 mt-1">
                        <i class="fas fa-clock mr-1"></i>
                        <?php echo $pending_withdrawals; ?> pending
                    </p>
                </div>
                <div class="w-12 h-12 bg-red-100 rounded-xl flex items-center justify-center">
                    <i class="fas fa-arrow-up text-red-600 text-xl"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Additional Metrics -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <!-- Expired Users -->
        <div class="admin-card p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600">Expired Accounts</p>
                    <p class="text-3xl font-bold text-gray-900"><?php echo number_format($expired_users); ?></p>
                    <p class="text-sm text-gray-500 mt-1">
                        <?php echo $total_users > 0 ? round(($expired_users / $total_users) * 100, 1) : 0; ?>% of total
                    </p>
                </div>
                <div class="w-12 h-12 bg-red-100 rounded-xl flex items-center justify-center">
                    <i class="fas fa-user-times text-red-600 text-xl"></i>
                </div>
            </div>
        </div>

        <!-- Active Staking -->
        <div class="admin-card p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600">Active Staking</p>
                    <p class="text-3xl font-bold text-gray-900">$<?php echo number_format($total_staking_amount, 2); ?></p>
                    <p class="text-sm text-gray-500 mt-1"><?php echo number_format($active_staking_count); ?> stakes</p>
                </div>
                <div class="w-12 h-12 bg-yellow-100 rounded-xl flex items-center justify-center">
                    <i class="fas fa-coins text-yellow-600 text-xl"></i>
                </div>
            </div>
        </div>

        <!-- Total Investments -->
        <div class="admin-card p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600">Total Investments</p>
                    <p class="text-3xl font-bold text-gray-900">$<?php echo number_format($total_investments, 2); ?></p>
                    <p class="text-sm text-gray-500 mt-1"><?php echo number_format($total_packages); ?> packages</p>
                </div>
                <div class="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center">
                    <i class="fas fa-chart-pie text-purple-600 text-xl"></i>
                </div>
            </div>
        </div>

        <!-- Platform Health -->
        <div class="admin-card p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600">Platform Health</p>
                    <p class="text-3xl font-bold text-gray-900"><?php echo round((($active_users / max($total_users, 1)) * 100), 1); ?>%</p>
                    <p class="text-sm text-gray-500 mt-1">Active rate</p>
                </div>
                <div class="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center">
                    <i class="fas fa-heartbeat text-green-600 text-xl"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Section -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- User Growth Chart -->
        <div class="admin-card p-6">
            <div class="flex items-center justify-between mb-6">
                <h3 class="text-lg font-semibold text-gray-900">User Growth (Last 6 Months)</h3>
                <i class="fas fa-chart-line text-blue-600"></i>
            </div>
            <div class="chart-container">
                <canvas id="userGrowthChart"></canvas>
            </div>
        </div>

        <!-- Deposits Chart -->
        <div class="admin-card p-6">
            <div class="flex items-center justify-between mb-6">
                <h3 class="text-lg font-semibold text-gray-900">Monthly Deposits (Last 6 Months)</h3>
                <i class="fas fa-chart-bar text-green-600"></i>
            </div>
            <div class="chart-container">
                <canvas id="depositsChart"></canvas>
            </div>
        </div>
    </div>

    <!-- Recent Activities Section -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Recent Users -->
        <div class="admin-card p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-900">Recent Users</h3>
                <div class="flex items-center space-x-2">
                    <span class="text-xs text-gray-500">Latest 5</span>
                    <i class="fas fa-user-plus text-blue-600"></i>
                </div>
            </div>
            <div class="space-y-3 max-h-80 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
                <?php if ($recent_users_result && $recent_users_result->num_rows > 0): ?>
                    <?php $count = 0; ?>
                    <?php while (($user = $recent_users_result->fetch_assoc()) && $count < 5): ?>
                        <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                            <div>
                                <p class="text-sm font-medium text-gray-900"><?php echo htmlspecialchars($user['name']); ?></p>
                                <p class="text-xs text-gray-500">ID: <?php echo $user['user_id']; ?> • Package: <?php echo htmlspecialchars($user['package']); ?></p>
                            </div>
                            <div class="text-xs text-gray-400">
                                <?php echo date('M d', strtotime($user['reg_date'] ?? $user['updated_at'] ?? 'now')); ?>
                            </div>
                        </div>
                        <?php $count++; ?>
                    <?php endwhile; ?>
                <?php else: ?>
                    <p class="text-sm text-gray-500 text-center py-4">No recent users</p>
                <?php endif; ?>
            </div>
        </div>

        <!-- Recent Deposits -->
        <div class="admin-card p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-900">Recent Deposits</h3>
                <div class="flex items-center space-x-2">
                    <span class="text-xs text-gray-500">Latest 5</span>
                    <i class="fas fa-arrow-down text-green-600"></i>
                </div>
            </div>
            <div class="space-y-3 max-h-80 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
                <?php if ($recent_deposits_result && $recent_deposits_result->num_rows > 0): ?>
                    <?php $count = 0; ?>
                    <?php while (($deposit = $recent_deposits_result->fetch_assoc()) && $count < 5): ?>
                        <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                            <div>
                                <p class="text-sm font-medium text-gray-900"><?php echo htmlspecialchars($deposit['name']); ?></p>
                                <p class="text-xs text-gray-500">$<?php echo number_format($deposit['amount'], 2); ?> • ID: <?php echo $deposit['public_user_id'] ?? $deposit['user_id']; ?></p>
                            </div>
                            <div class="text-right">
                                <span class="badge badge-<?php echo $deposit['status'] == 'approved' ? 'success' : ($deposit['status'] == 'pending' ? 'warning' : 'danger'); ?>">
                                    <?php echo ucfirst($deposit['status']); ?>
                                </span>
                                <p class="text-xs text-gray-400 mt-1">
                                    <?php echo date('M d', strtotime($deposit['requested_at'] ?? $deposit['id'])); ?>
                                </p>
                            </div>
                        </div>
                        <?php $count++; ?>
                    <?php endwhile; ?>
                <?php else: ?>
                    <p class="text-sm text-gray-500 text-center py-4">No recent deposits</p>
                <?php endif; ?>
            </div>
        </div>

        <!-- Recent Withdrawals -->
        <div class="admin-card p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-900">Recent Withdrawals</h3>
                <div class="flex items-center space-x-2">
                    <span class="text-xs text-gray-500">Latest 5</span>
                    <i class="fas fa-arrow-up text-red-600"></i>
                </div>
            </div>
            <div class="space-y-3 max-h-80 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
                <?php if ($recent_withdrawals_result && $recent_withdrawals_result->num_rows > 0): ?>
                    <?php $count = 0; ?>
                    <?php while (($withdrawal = $recent_withdrawals_result->fetch_assoc()) && $count < 5): ?>
                        <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                            <div>
                                <p class="text-sm font-medium text-gray-900"><?php echo htmlspecialchars($withdrawal['name']); ?></p>
                                <p class="text-xs text-gray-500">$<?php echo number_format($withdrawal['amount'], 2); ?> • ID: <?php echo $withdrawal['public_user_id'] ?? $withdrawal['user_id']; ?></p>
                            </div>
                            <div class="text-right">
                                <span class="badge badge-<?php echo $withdrawal['status'] == 'approved' ? 'success' : ($withdrawal['status'] == 'pending' ? 'warning' : 'danger'); ?>">
                                    <?php echo ucfirst($withdrawal['status']); ?>
                                </span>
                                <p class="text-xs text-gray-400 mt-1">
                                    <?php echo date('M d', strtotime($withdrawal['requested_at'] ?? $withdrawal['id'])); ?>
                                </p>
                            </div>
                        </div>
                        <?php $count++; ?>
                    <?php endwhile; ?>
                <?php else: ?>
                    <p class="text-sm text-gray-500 text-center py-4">No recent withdrawals</p>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Quick Stats Summary -->
    <div class="admin-card p-6">
        <div class="flex items-center justify-between mb-6">
            <h3 class="text-xl font-semibold text-gray-900">Platform Summary</h3>
            <div class="flex items-center space-x-2">
                <div class="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                <span class="text-sm text-gray-600">Live Data</span>
            </div>
        </div>
        <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
            <div class="text-center p-4 bg-blue-50 rounded-lg">
                <p class="text-2xl font-bold text-blue-600"><?php echo number_format($total_users); ?></p>
                <p class="text-sm text-gray-600">Total Users</p>
            </div>
            <div class="text-center p-4 bg-green-50 rounded-lg">
                <p class="text-2xl font-bold text-green-600">$<?php echo number_format($total_deposits, 0); ?></p>
                <p class="text-sm text-gray-600">Deposits</p>
            </div>
            <div class="text-center p-4 bg-red-50 rounded-lg">
                <p class="text-2xl font-bold text-red-600">$<?php echo number_format($total_withdrawals, 0); ?></p>
                <p class="text-sm text-gray-600">Withdrawals</p>
            </div>
            <div class="text-center p-4 bg-purple-50 rounded-lg">
                <p class="text-2xl font-bold text-purple-600">$<?php echo number_format($total_investments, 0); ?></p>
                <p class="text-sm text-gray-600">Investments</p>
            </div>
            <div class="text-center p-4 bg-yellow-50 rounded-lg">
                <p class="text-2xl font-bold text-yellow-600">$<?php echo number_format($total_staking_amount, 0); ?></p>
                <p class="text-sm text-gray-600">Staking</p>
            </div>
            <div class="text-center p-4 bg-indigo-50 rounded-lg">
                <p class="text-2xl font-bold text-indigo-600">$<?php echo number_format($current_coin_price, 4); ?></p>
                <p class="text-sm text-gray-600">Coin Price</p>
            </div>
        </div>
    </div>
    <?php
    // Get the dashboard content
    $dashboard_content = ob_get_clean();
    $page_content .= $dashboard_content . '</div>';

    // Additional JavaScript for dashboard
    $additional_js = '
    <script>
        // User Growth Chart
        const userGrowthCtx = document.getElementById(\'userGrowthChart\');
        if (userGrowthCtx) {
            const userGrowthData = ' . json_encode($monthly_users_data) . ';

            new Chart(userGrowthCtx, {
                type: \"line\",
                data: {
                    labels: userGrowthData.map(item => {
                        const date = new Date(item.month + \"-01\");
                        return date.toLocaleDateString(\"en-US\", {
                            month: \"short\",
                            year: \"2-digit\"
                        });
                    }),
                    datasets: [{
                        label: \"New Users\",
                        data: userGrowthData.map(item => item.count),
                        borderColor: \"rgb(59, 130, 246)\",
                        backgroundColor: \"rgba(59, 130, 246, 0.1)\",
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                color: \"rgba(0, 0, 0, 0.05)\"
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            });
        }

        // Deposits Chart
        const depositsCtx = document.getElementById(\"depositsChart\");
        if (depositsCtx) {
            const depositsData = ' . json_encode($monthly_deposits_data) . ';

            new Chart(depositsCtx, {
                type: \"bar\",
                data: {
                    labels: depositsData.map(item => {
                        const date = new Date(item.month + \"-01\");
                        return date.toLocaleDateString(\"en-US\", {
                            month: \"short\",
                            year: \"2-digit\"
                        });
                    }),
                    datasets: [{
                        label: \"Deposits ($)\",
                        data: depositsData.map(item => item.total),
                        backgroundColor: \"rgba(34, 197, 94, 0.8)\",
                        borderColor: \"rgb(34, 197, 94)\",
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                color: \"rgba(0, 0, 0, 0.05)\"
                            },
                            ticks: {
                                callback: function(value) {
                                    return \"$\" + value.toLocaleString();
                                }
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            });
        }

        // Auto-refresh dashboard every 5 minutes
        setTimeout(function() {
            location.reload();
        }, 300000);

        // Real-time clock update
        function updateClock() {
            const now = new Date();
            const timeString = now.toLocaleTimeString(\"en-US\", {
                hour12: false,
                timeZone: \"Asia/Kolkata\"
            });

            // Update any time displays
            const timeElements = document.querySelectorAll(\".server-time\");
            timeElements.forEach(element => {
                element.textContent = timeString;
            });
        }

        // Update clock every second
        setInterval(updateClock, 1000);
        updateClock(); // Initial call
    </script>
    ';

    // Include the layout
    include 'admin/includes/layout.php';
    ?>