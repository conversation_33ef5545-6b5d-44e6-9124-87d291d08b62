<?php
session_start();

if (!isset($_SESSION['user_id'])) {
    header("Location: login.php");
    exit();
}

include 'dbcon.php';

$user_id = $_SESSION['user_id'];

// Fetch user's wallet balances and plan information
$sql = "SELECT u.binanace_address, u.deposit_wallet_balance, u.withdrawal_wallet_balance, u.package, u.total_earnings_received, u.plan_status, u.plan_expired_at, p.package_amount
        FROM users u
        LEFT JOIN packages p ON u.package = p.package_name
        WHERE u.id = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param("i", $user_id);
$stmt->execute();
$result = $stmt->get_result();
$user = $result->fetch_assoc();

$binanace_address = $user['binanace_address'] ?? 'Not set';
$deposit_wallet_balance = $user['deposit_wallet_balance'] ?? 0;
$withdrawal_wallet_balance = $user['withdrawal_wallet_balance'] ?? 0;
$package_name = $user['package'] ?? 'No package';
$package_amount = $user['package_amount'] ?? 0;
$total_earnings = $user['total_earnings_received'] ?? 0;
$plan_status = $user['plan_status'] ?? 'active';
$plan_expired_at = $user['plan_expired_at'];
$earnings_limit = $package_amount * 2;
$earnings_progress = $package_amount > 0 ? ($total_earnings / $earnings_limit) * 100 : 0;

$stmt->close();
$conn->close();
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Wallet</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>

<body class="bg-gray-100 font-sans">
    <?php include 'user_nav.php'; ?>

    <main class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div class="bg-white shadow-lg rounded-lg p-8">
            <h2 class="text-3xl font-bold text-gray-800 mb-4">Wallet</h2>
            <p class="text-gray-600 mb-6">Your Binance Address: <strong class="font-mono bg-gray-100 p-1 rounded"><?php echo htmlspecialchars($binanace_address); ?></strong></p>

            <div class="mb-8 bg-gray-50 p-6 rounded-lg">
                <h3 class="text-lg font-semibold text-gray-700">Total Balance</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                    <div class="bg-blue-100 p-6 rounded-lg text-center">
                        <h3 class="text-lg font-medium text-gray-600 mb-2">Deposit Wallet</h3>
                        <p class="text-4xl font-bold text-gray-800">$<?php echo number_format($deposit_wallet_balance, 4); ?></p>
                    </div>
                    <div class="bg-green-100 p-6 rounded-lg text-center">
                        <h3 class="text-lg font-medium text-gray-600 mb-2">Withdrawal Wallet</h3>
                        <p class="text-4xl font-bold text-gray-800">$<?php echo number_format($withdrawal_wallet_balance, 4); ?></p>
                    </div>
                </div>
            </div>

            <!-- Plan Status Section -->
            <div class="mb-8 bg-gray-50 p-6 rounded-lg">
                <h3 class="text-lg font-semibold text-gray-700 mb-4">Plan Status</h3>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Current Plan Info -->
                    <div class="bg-white p-4 rounded-lg border border-gray-200">
                        <h4 class="text-sm font-medium text-gray-600 mb-2">Current Plan</h4>
                        <p class="text-xl font-bold text-gray-800"><?php echo htmlspecialchars($package_name); ?></p>
                        <p class="text-sm text-gray-500">Package Amount: $<?php echo number_format($package_amount, 2); ?></p>
                    </div>

                    <!-- Plan Status -->
                    <div class="bg-white p-4 rounded-lg border border-gray-200">
                        <h4 class="text-sm font-medium text-gray-600 mb-2">Status</h4>
                        <?php if ($plan_status === 'active'): ?>
                            <div class="flex items-center">
                                <span class="px-3 py-1 bg-green-100 text-green-800 text-sm font-medium rounded-full">Active</span>
                                <span class="ml-2 text-sm text-gray-500">Earning profits</span>
                            </div>
                        <?php else: ?>
                            <div class="flex items-center">
                                <span class="px-3 py-1 bg-red-100 text-red-800 text-sm font-medium rounded-full">Expired</span>
                                <span class="ml-2 text-sm text-gray-500">Plan limit reached</span>
                            </div>
                            <?php if ($plan_expired_at): ?>
                                <p class="text-xs text-gray-400 mt-1">Expired: <?php echo date('M j, Y', strtotime($plan_expired_at)); ?></p>
                            <?php endif; ?>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Earnings Progress -->
                <div class="mt-6">
                    <div class="flex justify-between items-center mb-2">
                        <h4 class="text-sm font-medium text-gray-600">Earnings Progress</h4>
                        <span class="text-sm text-gray-500">
                            $<?php echo number_format($total_earnings, 2); ?> / $<?php echo number_format($earnings_limit, 2); ?>
                        </span>
                    </div>

                    <div class="w-full bg-gray-200 rounded-full h-3">
                        <div class="<?php echo $plan_status === 'expired' ? 'bg-red-500' : 'bg-green-500'; ?> h-3 rounded-full transition-all duration-300"
                            style="width: <?php echo min($earnings_progress, 100); ?>%"></div>
                    </div>

                    <div class="flex justify-between text-xs text-gray-500 mt-1">
                        <span>0%</span>
                        <span><?php echo number_format($earnings_progress, 1); ?>%</span>
                        <span>100% (2x Plan Amount)</span>
                    </div>

                    <?php if ($plan_status === 'expired'): ?>
                        <div class="mt-4 p-4 bg-red-50 border border-red-200 rounded-lg">
                            <div class="flex items-center">
                                <svg class="w-5 h-5 text-red-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                                </svg>
                                <div>
                                    <h5 class="text-red-800 font-medium">Plan Expired</h5>
                                    <p class="text-red-700 text-sm">You've reached the maximum earnings (2x your package amount). Upgrade your plan to continue earning profits and commissions.</p>
                                </div>
                            </div>
                            <div class="mt-3">
                                <a href="invest.php" class="inline-flex items-center px-4 py-2 bg-red-600 text-white text-sm font-medium rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2">
                                    Upgrade Plan
                                </a>
                            </div>
                        </div>
                    <?php elseif ($earnings_progress > 80): ?>
                        <div class="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                            <div class="flex items-center">
                                <svg class="w-5 h-5 text-yellow-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                                </svg>
                                <div>
                                    <h5 class="text-yellow-800 font-medium">Plan Nearing Expiration</h5>
                                    <p class="text-yellow-700 text-sm">You're close to reaching the maximum earnings. Consider upgrading your plan soon.</p>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <div>
                <h3 class="text-xl font-semibold text-gray-700 mb-4">Transaction History</h3>
                <div class="mb-4 border-b border-gray-200">
                    <nav class="-mb-px flex space-x-8" aria-label="Tabs">
                        <a href="#" data-wallet="deposit" class="wallet-tab border-indigo-500 text-indigo-600 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">Deposit Wallet</a>
                        <a href="#" data-wallet="withdrawal" class="wallet-tab border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">Withdrawal Wallet</a>
                    </nav>
                </div>

                <div id="withdrawal-sub-filters" class="hidden mb-4 border-b border-gray-200">
                    <nav class="-mb-px flex space-x-6" aria-label="TransactionType">
                        <a href="#" data-type="all" class="type-filter-tab border-indigo-500 text-indigo-600 whitespace-nowrap pb-2 px-1 border-b-2 font-medium text-sm">All</a>
                        <a href="#" data-type="daily_profit" class="type-filter-tab border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-200 whitespace-nowrap pb-2 px-1 border-b-2 font-medium text-sm">Daily Profit</a>
                        <a href="#" data-type="network_commission" class="type-filter-tab border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-200 whitespace-nowrap pb-2 px-1 border-b-2 font-medium text-sm">Network Commission</a>
                        <a href="#" data-type="bonus" class="type-filter-tab border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-200 whitespace-nowrap pb-2 px-1 border-b-2 font-medium text-sm">Bonus</a>
                    </nav>
                </div>

                <div class="flex flex-col sm:flex-row justify-between items-center mb-4 gap-4">
                    <div class="flex items-center space-x-2">
                        <input type="date" id="start-date" class="form-input block w-full px-3 py-2 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                        <span class="text-gray-500">to</span>
                        <input type="date" id="end-date" class="form-input block w-full px-3 py-2 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                        <button id="filter-btn" class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">Filter</button>
                        <button id="reset-btn" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 focus:outline-none">Reset</button>
                    </div>
                    <div id="summary-section" class="text-right">
                        <h4 class="text-lg font-semibold text-gray-700">Filtered Total</h4>
                        <p id="filtered-total" class="text-2xl font-bold text-indigo-600">$0.0000</p>
                    </div>
                </div>
                <div class="overflow-x-auto border border-gray-200 rounded-lg">
                    <table class="min-w-full bg-white">
                        <thead class="bg-gray-100">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">Date</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">Type</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">Description</th>
                                <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-600 uppercase tracking-wider">Amount</th>
                            </tr>
                        </thead>
                        <tbody id="wallet-history-body" class="bg-white divide-y divide-gray-200">
                            <!-- JS will populate this section -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </main>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        $(document).ready(function() {
            let activeWalletType = 'deposit';
            let activeTransactionType = 'all';

            function toggleSubFilters() {
                if (activeWalletType === 'withdrawal') {
                    $('#withdrawal-sub-filters').removeClass('hidden');
                } else {
                    $('#withdrawal-sub-filters').addClass('hidden');
                }
            }

            function fetchHistory(walletType, transactionType = 'all', startDate = null, endDate = null) {
                const historyBody = $('#wallet-history-body');
                historyBody.html('<tr><td colspan="4" class="text-center p-8 text-gray-500">Loading history...</td></tr>');

                let url = `/netvis/ui/api/get_wallet_history.php?wallet_type=${walletType}`;
                if (transactionType && transactionType !== 'all') {
                    url += `&type=${transactionType}`;
                }
                if (startDate) url += `&start_date=${startDate}`;
                if (endDate) url += `&end_date=${endDate}`;

                $.ajax({
                    url: url,
                    type: 'GET',
                    dataType: 'json',
                    success: function(transactions) {
                        historyBody.empty();
                        let filteredTotal = 0;
                        if (transactions.length > 0) {
                            transactions.forEach(tx => {
                                const isCredit = ['deposit', 'admin_fund_add', 'daily_profit', 'network_commission', 'bonus', 'transfer_in'].includes(tx.type);
                                const amountClass = isCredit ? 'text-green-600' : 'text-red-600';
                                const sign = isCredit ? '+' : '-';

                                const row = `
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">${new Date(tx.created_at).toLocaleString()}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-800">${tx.type.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}</td>
                                    <td class="px-6 py-4 text-sm text-gray-600">${tx.description}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-right font-semibold ${amountClass}">${sign}$${parseFloat(tx.amount).toFixed(4)}</td>
                                </tr>`;
                                historyBody.append(row);

                                if (isCredit) {
                                    filteredTotal += parseFloat(tx.amount);
                                } else {
                                    filteredTotal -= parseFloat(tx.amount);
                                }
                            });
                        } else {
                            historyBody.html('<tr><td colspan="4" class="text-center p-8 text-gray-500">No transactions found for this period.</td></tr>');
                        }
                        $('#filtered-total').text(`$${filteredTotal.toFixed(4)}`);
                    },
                    error: function() {
                        historyBody.html('<tr><td colspan="4" class="text-center p-8 text-red-500">Failed to load transaction history.</td></tr>');
                    }
                });
            }

            $('.wallet-tab').on('click', function(e) {
                e.preventDefault();
                $('.wallet-tab').removeClass('border-indigo-500 text-indigo-600').addClass('border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300');
                $(this).removeClass('border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300').addClass('border-indigo-500 text-indigo-600');

                activeWalletType = $(this).data('wallet');
                activeTransactionType = 'all';

                $('.type-filter-tab').removeClass('border-indigo-500 text-indigo-600').addClass('border-transparent text-gray-500');
                $('.type-filter-tab[data-type="all"]').addClass('border-indigo-500 text-indigo-600').removeClass('border-transparent text-gray-500');

                toggleSubFilters();

                $('#start-date').val('');
                $('#end-date').val('');
                fetchHistory(activeWalletType, activeTransactionType);
            });

            $('.type-filter-tab').on('click', function(e) {
                e.preventDefault();
                $('.type-filter-tab').removeClass('border-indigo-500 text-indigo-600').addClass('border-transparent text-gray-500');
                $(this).addClass('border-indigo-500 text-indigo-600').removeClass('border-transparent text-gray-500');

                activeTransactionType = $(this).data('type');
                fetchHistory(activeWalletType, activeTransactionType, $('#start-date').val(), $('#end-date').val());
            });

            $('#filter-btn').on('click', function() {
                const startDate = $('#start-date').val();
                const endDate = $('#end-date').val();
                fetchHistory(activeWalletType, activeTransactionType, startDate, endDate);
            });

            $('#reset-btn').on('click', function() {
                $('#start-date').val('');
                $('#end-date').val('');
                activeTransactionType = 'all';
                $('.type-filter-tab').removeClass('border-indigo-500 text-indigo-600').addClass('border-transparent text-gray-500');
                $('.type-filter-tab[data-type="all"]').addClass('border-indigo-500 text-indigo-600').removeClass('border-transparent text-gray-500');

                fetchHistory(activeWalletType, activeTransactionType);
            });

            toggleSubFilters();
            fetchHistory(activeWalletType, activeTransactionType);
        });
    </script>
</body>

</html>