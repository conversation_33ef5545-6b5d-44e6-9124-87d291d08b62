<?php
session_start();

if (!isset($_SESSION['user_id'])) {
    header("Location: login.html");
    exit();
}

include 'dbcon.php';

echo "<h2>Purchase API Test</h2>";
echo "<p>This page tests the package purchase API to identify any issues.</p>";

// Get user details
$user_id = $_SESSION['user_id'];
$user_sql = "SELECT u.*, p.package_amount as current_package_amount FROM users u LEFT JOIN packages p ON u.package = p.package_name WHERE u.id = ?";
$stmt = $conn->prepare($user_sql);
$stmt->bind_param("i", $user_id);
$stmt->execute();
$user_result = $stmt->get_result();
$user = $user_result->fetch_assoc();
$stmt->close();

echo "<h3>1. Current User Status</h3>";
echo "<table border='1' style='border-collapse: collapse;'>";
echo "<tr><th>Field</th><th>Value</th></tr>";
echo "<tr><td>User ID</td><td>{$user['id']}</td></tr>";
echo "<tr><td>Name</td><td>{$user['name']}</td></tr>";
echo "<tr><td>Current Package</td><td>" . ($user['package'] ?? 'None') . "</td></tr>";
echo "<tr><td>Current Package Amount</td><td>$" . number_format($user['current_package_amount'] ?? 0, 2) . "</td></tr>";
echo "<tr><td>Deposit Wallet</td><td>$" . number_format($user['deposit_wallet_balance'], 2) . "</td></tr>";
echo "<tr><td>Withdrawal Wallet</td><td>$" . number_format($user['withdrawal_wallet_balance'], 2) . "</td></tr>";
echo "<tr><td>Plan Status</td><td>{$user['plan_status']}</td></tr>";
echo "</table>";

// Get available packages
echo "<h3>2. Available Packages</h3>";
$packages_sql = "SELECT * FROM packages WHERE is_active = 1 ORDER BY package_amount ASC";
$packages_result = $conn->query($packages_sql);

if ($packages_result->num_rows > 0) {
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>ID</th><th>Package Name</th><th>Amount</th><th>Profit %</th><th>Can Upgrade?</th></tr>";
    while ($package = $packages_result->fetch_assoc()) {
        $can_upgrade = $package['package_amount'] > ($user['current_package_amount'] ?? 0);
        $upgrade_status = $can_upgrade ? "✅ Yes" : "❌ No (not an upgrade)";
        echo "<tr>";
        echo "<td>{$package['id']}</td>";
        echo "<td>{$package['package_name']}</td>";
        echo "<td>$" . number_format($package['package_amount'], 2) . "</td>";
        echo "<td>{$package['profit_percentage']}%</td>";
        echo "<td>$upgrade_status</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>No active packages found.</p>";
}

// Test API endpoint
echo "<h3>3. API Endpoint Test</h3>";
if (isset($_POST['test_api'])) {
    $test_package_id = intval($_POST['package_id']);
    $test_wallet_type = $_POST['wallet_type'];
    
    echo "<div style='background: #f0f8ff; padding: 15px; border: 1px solid #4169E1; margin: 10px 0;'>";
    echo "<h4>Testing API Call...</h4>";
    echo "<p><strong>Package ID:</strong> $test_package_id</p>";
    echo "<p><strong>Wallet Type:</strong> $test_wallet_type</p>";
    
    // Simulate the API call
    $api_url = "http://localhost/netvis/ui/api/purchase_package.php";
    $post_data = json_encode([
        'package_id' => $test_package_id,
        'wallet_type' => $test_wallet_type
    ]);
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $api_url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $post_data);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Cookie: ' . $_SERVER['HTTP_COOKIE'] // Pass session cookie
    ]);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $curl_error = curl_error($ch);
    curl_close($ch);
    
    echo "<h5>API Response:</h5>";
    echo "<p><strong>HTTP Code:</strong> $http_code</p>";
    
    if ($curl_error) {
        echo "<p style='color: red;'><strong>cURL Error:</strong> $curl_error</p>";
    } else {
        echo "<p><strong>Response:</strong></p>";
        echo "<pre style='background: #f5f5f5; padding: 10px; border: 1px solid #ddd;'>$response</pre>";
        
        $json_response = json_decode($response, true);
        if ($json_response) {
            if (isset($json_response['success']) && $json_response['success']) {
                echo "<p style='color: green;'>✅ <strong>Success!</strong> Package purchase completed.</p>";
            } elseif (isset($json_response['error'])) {
                echo "<p style='color: red;'>❌ <strong>Error:</strong> {$json_response['error']}</p>";
            }
        } else {
            echo "<p style='color: orange;'>⚠️ <strong>Warning:</strong> Response is not valid JSON. This might indicate a PHP error.</p>";
        }
    }
    echo "</div>";
}

// Test form
echo "<h3>4. Test API Call</h3>";
echo "<form method='POST'>";
echo "<div style='margin: 10px 0;'>";
echo "<label>Package to Test:</label><br>";
echo "<select name='package_id' required>";
echo "<option value=''>-- Select Package --</option>";

// Reset packages result
$packages_result = $conn->query($packages_sql);
while ($package = $packages_result->fetch_assoc()) {
    echo "<option value='{$package['id']}'>{$package['package_name']} - $" . number_format($package['package_amount'], 2) . "</option>";
}
echo "</select>";
echo "</div>";

echo "<div style='margin: 10px 0;'>";
echo "<label>Wallet Type:</label><br>";
echo "<input type='radio' name='wallet_type' value='deposit' checked> Deposit Wallet<br>";
echo "<input type='radio' name='wallet_type' value='withdrawal'> Withdrawal Wallet";
echo "</div>";

echo "<button type='submit' name='test_api' style='background: #4CAF50; color: white; padding: 10px 20px; border: none; cursor: pointer; border-radius: 5px;'>Test API Call</button>";
echo "</form>";

// Check for common issues
echo "<h3>5. Common Issues Check</h3>";
echo "<div style='background: #fff3cd; padding: 15px; border: 1px solid #ffc107; margin: 10px 0;'>";
echo "<h4>🔍 Potential Issues:</h4>";
echo "<ul>";

// Check if purchase_package.php exists
if (file_exists('api/purchase_package.php')) {
    echo "<li>✅ API file exists: api/purchase_package.php</li>";
} else {
    echo "<li>❌ API file missing: api/purchase_package.php</li>";
}

// Check if packages table has data
$packages_count = $conn->query("SELECT COUNT(*) as count FROM packages WHERE is_active = 1")->fetch_assoc()['count'];
if ($packages_count > 0) {
    echo "<li>✅ Active packages available: $packages_count</li>";
} else {
    echo "<li>❌ No active packages found</li>";
}

// Check if package_history table exists
$tables = $conn->query("SHOW TABLES LIKE 'package_history'");
if ($tables->num_rows > 0) {
    echo "<li>✅ package_history table exists</li>";
} else {
    echo "<li>❌ package_history table missing</li>";
}

// Check user wallet balances
$has_balance = ($user['deposit_wallet_balance'] > 0 || $user['withdrawal_wallet_balance'] > 0);
if ($has_balance) {
    echo "<li>✅ User has wallet balance</li>";
} else {
    echo "<li>⚠️ User has no wallet balance (may cause insufficient funds error)</li>";
}

echo "</ul>";
echo "</div>";

echo "<h3>6. Manual API Test</h3>";
echo "<p>You can also test the API directly by opening the browser developer tools (F12) and running this JavaScript in the console:</p>";
echo "<pre style='background: #f5f5f5; padding: 10px; border: 1px solid #ddd;'>";
echo "fetch('/netvis/ui/api/purchase_package.php', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ package_id: 1, wallet_type: 'deposit' })
})
.then(response => response.json())
.then(data => console.log(data))
.catch(error => console.error('Error:', error));";
echo "</pre>";

$conn->close();
?>
