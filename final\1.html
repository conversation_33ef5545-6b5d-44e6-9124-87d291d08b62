<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Finance Dashboard</title>

    <!-- Tailwind CSS for styling -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Google Fonts: Inter -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">

    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css"
        xintegrity="sha512-SnH5WK+bZxgPHs44uWIX+LLJAJ9/2PkPKZ5QiAj6Ta86w+fsb2TkcmfRyVX3pBnMFcV7oQPJkl9QevSCWr3W6A=="
        crossorigin="anonymous" referrerpolicy="no-referrer" />


    <style>
        /* Define the custom theme and other base styles */
        body {
            font-family: 'Inter', sans-serif;
        }

        .theme-ocean {
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
            color: #e2e8f0;
            /* Using a softer white for better readability */
        }

        .theme-ocean .card {
            background: rgba(30, 41, 59, 0.8);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            /* For Safari */
            border: 1px solid rgba(59, 130, 246, 0.2);
            border-radius: 1rem;
            /* Consistent rounded corners */
            padding: 1rem;
            transition: all 0.3s ease;
        }

        .theme-ocean .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        }

        .theme-ocean .accent {
            color: #3b82f6;
        }

        .theme-ocean .secondary {
            color: #06b6d4;
        }

        /* Marquee animation for scrolling text */
        .marquee {
            white-space: nowrap;
            overflow: hidden;
            box-sizing: border-box;
        }

        .marquee-content {
            display: inline-block;
            padding-left: 100%;
            animation: marquee 15s linear infinite;
        }

        @keyframes marquee {
            0% {
                transform: translateX(0);
            }

            100% {
                transform: translateX(-100%);
            }
        }

        /* Custom scrollbar for better aesthetics */
        ::-webkit-scrollbar {
            width: 5px;
        }

        ::-webkit-scrollbar-track {
            background: #1e293b;
        }

        ::-webkit-scrollbar-thumb {
            background: #3b82f6;
            border-radius: 10px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #2563eb;
        }

        /* Action Button Styles - Matching Reference Image */
        .action-btn {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 0.75rem;
            padding: 1rem 0.5rem;
            background: transparent;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .action-btn:hover {
            transform: translateY(-2px);
        }

        .action-btn:active {
            transform: translateY(0);
        }

        .action-icon-circle {
            width: 3.5rem;
            height: 3.5rem;
            border-radius: 50%;
            background: rgba(30, 41, 59, 0.8);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(59, 130, 246, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .action-icon-circle::before {
            content: '';
            position: absolute;
            inset: 0;
            border-radius: 50%;
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(6, 182, 212, 0.1));
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .action-btn:hover .action-icon-circle {
            transform: scale(1.1);
            border-color: rgba(59, 130, 246, 0.4);
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.2);
        }

        .action-btn:hover .action-icon-circle::before {
            opacity: 1;
        }

        .action-icon-circle i {
            font-size: 1.25rem;
            z-index: 1;
            transition: all 0.3s ease;
        }

        .action-btn:hover .action-icon-circle i {
            transform: scale(1.1);
        }

        .action-label {
            font-size: 0.875rem;
            font-weight: 600;
            color: #ffffff;
            text-align: center;
            transition: all 0.3s ease;
        }

        .action-btn:hover .action-label {
            color: #3b82f6;
        }

        /* Enhanced Stats Cards */
        .stats-card {
            background: rgba(30, 41, 59, 0.8);
            backdrop-filter: blur(15px);
            border: 1px solid rgba(59, 130, 246, 0.2);
            border-radius: 1.25rem;
            padding: 1.25rem;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .stats-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, #3b82f6, #06b6d4);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .stats-card:hover::before {
            opacity: 1;
        }

        .stats-card:hover {
            transform: translateY(-5px);
            border-color: rgba(59, 130, 246, 0.4);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.4);
        }

        /* Enhanced Stats Card Styles */
        .stats-card-interactive {
            background: rgba(30, 41, 59, 0.9);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(59, 130, 246, 0.2);
            border-radius: 1.5rem;
            padding: 1.5rem;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            cursor: pointer;
            min-height: 140px;
        }

        .stats-card-interactive::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, transparent, var(--card-accent), transparent);
            opacity: 0;
            transition: opacity 0.4s ease;
        }

        .stats-card-interactive:hover::before {
            opacity: 1;
        }

        .stats-card-interactive::after {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, var(--card-accent-alpha) 0%, transparent 70%);
            opacity: 0;
            transition: opacity 0.4s ease;
            pointer-events: none;
        }

        .stats-card-interactive:hover::after {
            opacity: 0.1;
        }

        .stats-card-interactive:hover {
            transform: translateY(-8px) scale(1.02);
            border-color: var(--card-accent);
            box-shadow: 0 20px 50px rgba(0, 0, 0, 0.5), 0 0 30px var(--card-accent-alpha);
        }

        .stats-card-interactive:active {
            transform: translateY(-4px) scale(1.01);
        }

        /* Card Icon Styles */
        .card-icon-container {
            width: 3rem;
            height: 3rem;
            border-radius: 1rem;
            display: flex;
            align-items: center;
            justify-content: center;
            background: var(--card-accent-alpha);
            border: 1px solid var(--card-accent);
            transition: all 0.4s ease;
            margin: 0 auto 1rem;
        }

        .stats-card-interactive:hover .card-icon-container {
            transform: scale(1.1) rotate(5deg);
            box-shadow: 0 8px 20px var(--card-accent-alpha);
        }

        .card-icon {
            font-size: 1.25rem;
            color: var(--card-accent);
            transition: all 0.4s ease;
        }

        .stats-card-interactive:hover .card-icon {
            transform: scale(1.1);
            filter: brightness(1.2);
        }

        /* Card Value Animation */
        .card-value {
            font-size: 1.75rem;
            font-weight: 700;
            color: #ffffff;
            transition: all 0.4s ease;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .stats-card-interactive:hover .card-value {
            transform: scale(1.05);
            color: var(--card-accent);
            text-shadow: 0 0 10px var(--card-accent-alpha);
        }

        /* Progress Bar Enhancement */
        .enhanced-progress {
            width: 100%;
            height: 4px;
            background: rgba(30, 41, 59, 0.8);
            border-radius: 2px;
            overflow: hidden;
            margin-top: 0.75rem;
            position: relative;
        }

        .enhanced-progress::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            height: 100%;
            background: linear-gradient(90deg, var(--card-accent), var(--card-accent-light));
            border-radius: 2px;
            transition: width 1s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 0 10px var(--card-accent-alpha);
        }

        /* Card Specific Colors */
        .card-blue {
            --card-accent: #3b82f6;
            --card-accent-light: #60a5fa;
            --card-accent-alpha: rgba(59, 130, 246, 0.3);
        }

        .card-green {
            --card-accent: #10b981;
            --card-accent-light: #34d399;
            --card-accent-alpha: rgba(16, 185, 129, 0.3);
        }

        .card-yellow {
            --card-accent: #f59e0b;
            --card-accent-light: #fbbf24;
            --card-accent-alpha: rgba(245, 158, 11, 0.3);
        }

        .card-purple {
            --card-accent: #8b5cf6;
            --card-accent-light: #a78bfa;
            --card-accent-alpha: rgba(139, 92, 246, 0.3);
        }

        /* Pulse Animation for Live Data */
        .live-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            background: #10b981;
            border-radius: 50%;
            animation: pulse 2s infinite;
            margin-right: 0.5rem;
        }

        @keyframes pulse {
            0% {
                opacity: 1;
                transform: scale(1);
            }

            50% {
                opacity: 0.5;
                transform: scale(1.2);
            }

            100% {
                opacity: 1;
                transform: scale(1);
            }
        }

        /* Card Subtitle Enhancement */
        .card-subtitle {
            font-size: 0.75rem;
            color: #94a3b8;
            font-weight: 500;
            transition: color 0.4s ease;
        }

        .stats-card-interactive:hover .card-subtitle {
            color: #e2e8f0;
        }

        /* Scrollable Transaction Container */
        .transaction-scroll-container {
            max-height: 240px;
            overflow-y: auto;
            overflow-x: hidden;
            padding-right: 4px;
            margin-right: -4px;
        }

        .transaction-scroll-container::-webkit-scrollbar {
            width: 4px;
        }

        .transaction-scroll-container::-webkit-scrollbar-track {
            background: rgba(30, 41, 59, 0.5);
            border-radius: 2px;
        }

        .transaction-scroll-container::-webkit-scrollbar-thumb {
            background: rgba(59, 130, 246, 0.6);
            border-radius: 2px;
            transition: background 0.3s ease;
        }

        .transaction-scroll-container::-webkit-scrollbar-thumb:hover {
            background: rgba(59, 130, 246, 0.8);
        }

        /* Transaction Item Styles */
        .transaction-item {
            padding: 0.75rem;
            border-radius: 0.75rem;
            background: rgba(30, 41, 59, 0.3);
            border: 1px solid rgba(59, 130, 246, 0.1);
            transition: all 0.3s ease;
            margin-bottom: 0.75rem;
        }

        .transaction-item:hover {
            background: rgba(30, 41, 59, 0.5);
            border-color: rgba(59, 130, 246, 0.2);
            transform: translateX(4px);
        }

        .transaction-item:last-child {
            margin-bottom: 0;
        }

        /* Fade effect for scrollable area */
        .transaction-scroll-container::before {
            content: '';
            position: sticky;
            top: 0;
            display: block;
            height: 8px;
            background: linear-gradient(to bottom, rgba(30, 41, 59, 0.8), transparent);
            z-index: 1;
            margin-bottom: -8px;
        }

        .transaction-scroll-container::after {
            content: '';
            position: sticky;
            bottom: 0;
            display: block;
            height: 8px;
            background: linear-gradient(to top, rgba(30, 41, 59, 0.8), transparent);
            z-index: 1;
            margin-top: -8px;
        }

        /* Crypto Ticker Styles */
        .ticker-container {
            white-space: nowrap;
            width: max-content;
        }

        .animate-scroll {
            animation: scroll 30s linear infinite;
        }

        @keyframes scroll {
            0% {
                transform: translateX(0);
            }

            100% {
                transform: translateX(-50%);
            }
        }

        .ticker-item {
            flex-shrink: 0;
            min-width: max-content;
        }
    </style>
</head>

<body class="theme-ocean min-h-screen bg-slate-900">

    <!-- Main Dashboard Container -->
    <div class="container mx-auto max-w-md min-h-screen bg-slate-900 flex flex-col">

        <!-- Sleek Top Navigation Bar -->
        <header
            class="sticky top-0 z-50 flex-shrink-0 flex items-center justify-between px-4 py-3 border-b border-slate-700/50 bg-slate-900/95 backdrop-blur-sm">
            <!-- Company Logo -->
            <div class="flex items-center gap-2">
                <i class="fab fa-bitcoin fa-lg secondary"></i>
                <span class="font-bold text-white text-lg">CryptoApp</span>
            </div>
            <!-- Header Icons -->
            <div class="flex items-center gap-3">
                <!-- Forex Trading Icon -->
                <button onclick="openForexTrading()"
                    class="w-8 h-8 rounded-full bg-cyan-500/20 flex items-center justify-center border border-cyan-400 hover:bg-cyan-500/30 transition-colors">
                    <i class="fas fa-chart-line text-sm text-cyan-400"></i>
                </button>
                <!-- User Avatar -->
                <div
                    class="w-8 h-8 rounded-full bg-blue-500/20 flex items-center justify-center border border-blue-400">
                    <i class="fas fa-user text-sm accent"></i>
                </div>
            </div>
        </header>

        <!-- Main content with scrolling -->
        <div class="flex-grow overflow-y-auto px-6 py-6">

            <!-- Premium Offer Message -->
            <div
                class="relative mb-6 overflow-hidden rounded-2xl bg-gradient-to-br from-slate-900/80 via-slate-800/60 to-slate-900/80 border border-blue-400/30 backdrop-blur-xl shadow-2xl">
                <!-- Glassmorphism Background -->
                <div
                    class="absolute inset-0 bg-gradient-to-r from-transparent via-blue-400/10 to-transparent animate-pulse">
                </div>

                <!-- Premium Border Glow -->
                <div
                    class="absolute inset-0 rounded-2xl bg-gradient-to-r from-blue-500/20 via-cyan-500/20 to-blue-500/20 blur-sm">
                </div>

                <!-- Offer Content -->
                <div class="relative px-6 py-5">
                    <div class="flex items-center gap-4">
                        <!-- Premium Icon -->
                        <div
                            class="flex-shrink-0 w-12 h-12 rounded-xl bg-gradient-to-br from-blue-500/30 to-cyan-600/30 backdrop-blur-sm border border-blue-400/40 flex items-center justify-center shadow-lg">
                            <i class="fas fa-crown text-blue-300 text-lg"></i>
                        </div>

                        <!-- Offer Content -->
                        <div class="flex-grow">
                            <div class="flex items-center gap-2 mb-1">
                                <div class="w-2 h-2 rounded-full bg-blue-400 animate-pulse"></div>
                                <span class="text-blue-300 font-semibold text-xs uppercase tracking-wider">Exclusive
                                    Offer</span>
                            </div>
                            <h3 class="text-white font-bold text-lg leading-tight mb-1">
                                Premium <span
                                    class="text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-cyan-400">20%
                                    Bonus</span>
                            </h3>
                            <p class="text-slate-300 text-sm">
                                First deposit bonus • Minimum $100 • 24h validity
                            </p>
                        </div>

                        <!-- CTA Button -->
                        <div class="flex-shrink-0">
                            <button
                                class="group relative overflow-hidden bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-500 hover:to-cyan-500 text-white font-semibold px-5 py-2.5 rounded-xl text-sm transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-blue-500/25">
                                <span class="relative z-10">Activate</span>
                                <div
                                    class="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                                </div>
                            </button>
                        </div>
                    </div>

                    <!-- Elegant Progress Indicator -->
                    <div class="mt-4 pt-4 border-t border-blue-500/20">
                        <div class="flex items-center justify-between text-xs mb-2">
                            <span class="text-slate-400 font-medium">Time remaining</span>
                            <span id="offer-timer" class="text-blue-300 font-semibold tabular-nums">23:45:12</span>
                        </div>
                        <div class="relative w-full h-2 bg-slate-700/50 rounded-full overflow-hidden">
                            <div class="absolute inset-0 bg-gradient-to-r from-blue-600/30 to-cyan-600/30 rounded-full">
                            </div>
                            <div class="relative h-full bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full offer-progress transition-all duration-1000 ease-out"
                                style="width: 75%">
                                <div
                                    class="absolute inset-0 bg-gradient-to-r from-white/30 to-transparent animate-shimmer">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Subtle Corner Accents -->
                <div
                    class="absolute top-0 right-0 w-16 h-16 bg-gradient-to-br from-blue-400/10 to-transparent rounded-full -translate-y-8 translate-x-8">
                </div>
                <div
                    class="absolute bottom-0 left-0 w-12 h-12 bg-gradient-to-tr from-cyan-400/10 to-transparent rounded-full translate-y-6 -translate-x-6">
                </div>
            </div>

            <!-- Enhanced Interactive Stats Grid -->
            <div class="grid grid-cols-2 gap-4 mb-6">
                <!-- Standard Account Card -->
                <div class="stats-card-interactive card-blue text-center" data-card="account"
                    onclick="showCardDetails('account')">
                    <div class="card-icon-container">
                        <i class="fas fa-wallet card-icon"></i>
                    </div>
                    <p class="card-subtitle mb-2">
                        <span class="live-indicator"></span>Standard Account
                    </p>
                    <p class="card-value mb-2" data-value="100.00">$100.00</p>
                    <!-- <div class="enhanced-progress" data-progress="75"></div>
                    <p class="text-xs text-slate-400 mt-2">75% Active</p> -->
                </div>

                <!-- Today's Profit Card -->
                <div class="stats-card-interactive card-green text-center" data-card="today"
                    onclick="showCardDetails('today')">
                    <div class="card-icon-container">
                        <i class="fas fa-chart-line card-icon"></i>
                    </div>
                    <p class="card-subtitle mb-2">
                        <span class="live-indicator"></span>Today's Profit
                    </p>
                    <p class="card-value mb-1" data-value="12.75">$12.75</p>
                    <!-- <div class="flex items-center justify-center gap-2">
                        <span class="text-xs text-green-300">+2.55% ↗</span>
                        <span class="text-xs text-slate-400">vs yesterday</span>
                    </div> -->
                </div>

                <!-- Total Profit Card -->
                <div class="stats-card-interactive card-yellow text-center" data-card="total"
                    onclick="showCardDetails('total')">
                    <div class="card-icon-container">
                        <i class="fas fa-trophy card-icon"></i>
                    </div>
                    <p class="card-subtitle mb-2">
                        <span class="live-indicator"></span>Total Profit
                    </p>
                    <p class="card-value mb-1" data-value="245.50">$245.50</p>
                    <!-- <div class="flex items-center justify-center gap-2">
                        <span class="text-xs text-yellow-300">+24.55% ↗</span>
                        <span class="text-xs text-slate-400">all time</span>
                    </div> -->
                </div>

                <!-- Wallet Balance Card -->
                <div class="stats-card-interactive card-purple text-center" data-card="wallet"
                    onclick="showCardDetails('wallet')">
                    <div class="card-icon-container">
                        <i class="fas fa-coins card-icon"></i>
                    </div>
                    <p class="card-subtitle mb-2">
                        <span class="live-indicator"></span>Wallet Balance
                    </p>
                    <p class="card-value mb-1" data-value="85.30">$85.30</p>
                    <!-- <div class="flex items-center justify-center gap-2">
                        <span class="text-xs text-purple-300">Available</span>
                        <span class="text-xs text-slate-400">• Ready to use</span>
                    </div> -->
                </div>
            </div>

            <!-- Enhanced Stacking Info -->
            <!-- Premium Active Staking Section -->
            <div
                class="relative mb-6 overflow-hidden rounded-2xl bg-gradient-to-br from-slate-900/90 via-slate-800/70 to-slate-900/90 border border-cyan-400/20 backdrop-blur-sm shadow-xl">
                <!-- Background Glow Effect -->
                <div class="absolute inset-0 bg-gradient-to-r from-transparent via-cyan-400/5 to-transparent"></div>

                <!-- Header Section -->
                <div class="relative px-6 py-5 border-b border-slate-700/50">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center gap-3">
                            <div
                                class="w-10 h-10 rounded-xl bg-gradient-to-br from-cyan-500/20 to-blue-600/20 backdrop-blur-sm border border-cyan-400/30 flex items-center justify-center">
                                <i class="fas fa-layer-group text-cyan-400 text-lg"></i>
                            </div>
                            <div>
                                <h3 class="font-bold text-white text-lg">Active Staking</h3>
                                <p class="text-xs text-slate-400">Bitcoin Pool • High Yield</p>
                            </div>
                        </div>
                        <div
                            class="flex items-center gap-2 bg-gradient-to-r from-cyan-500/10 to-blue-500/10 px-3 py-1.5 rounded-full border border-cyan-400/20">
                            <div class="w-2 h-2 bg-cyan-400 rounded-full animate-pulse shadow-lg shadow-cyan-400/50">
                            </div>
                            <span class="text-xs text-cyan-300 font-semibold">LIVE</span>
                        </div>
                    </div>
                </div>

                <!-- Stats Grid -->
                <div class="relative px-6 py-5">
                    <div class="grid grid-cols-3 gap-4 mb-6">
                        <!-- Staked Amount -->
                        <div class="text-center p-3 rounded-xl bg-slate-800/30 border border-slate-700/50">
                            <p class="text-xs text-slate-400 mb-2 font-medium">Staked Amount</p>
                            <p class="text-xl font-bold text-white mb-1">$50.00</p>
                            <div class="w-8 h-0.5 bg-gradient-to-r from-blue-500 to-cyan-500 mx-auto rounded-full">
                            </div>
                        </div>

                        <!-- Crypto Asset -->
                        <div class="text-center p-3 rounded-xl bg-slate-800/30 border border-slate-700/50">
                            <div class="relative mx-auto w-14 h-14 mb-2">
                                <div
                                    class="w-full h-full rounded-full bg-gradient-to-br from-orange-400/20 to-orange-600/20 border border-orange-400/30 flex items-center justify-center backdrop-blur-sm">
                                    <i class="fab fa-bitcoin text-orange-400 text-xl"></i>
                                </div>
                                <div
                                    class="absolute -top-1 -right-1 w-5 h-5 bg-gradient-to-br from-cyan-500 to-blue-600 rounded-full flex items-center justify-center border-2 border-slate-900">
                                    <i class="fas fa-fire text-white text-xs"></i>
                                </div>
                            </div>
                            <p class="text-xs text-slate-400 font-medium">BTC Pool</p>
                        </div>

                        <!-- Current Value -->
                        <div class="text-center p-3 rounded-xl bg-slate-800/30 border border-slate-700/50">
                            <p class="text-xs text-slate-400 mb-2 font-medium">Current Value</p>
                            <p class="text-xl font-bold text-cyan-400 mb-1">$52.00</p>
                            <div class="flex items-center justify-center gap-1">
                                <span class="text-xs text-cyan-300 font-semibold">+4.0%</span>
                                <i class="fas fa-arrow-up text-cyan-400 text-xs"></i>
                            </div>
                        </div>
                    </div>

                    <!-- Enhanced Progress Section -->
                    <div
                        class="bg-gradient-to-r from-slate-800/50 to-slate-700/30 rounded-xl p-4 mb-4 border border-slate-600/30">
                        <div class="flex justify-between items-center mb-3">
                            <div class="flex items-center gap-2">
                                <i class="fas fa-chart-line text-cyan-400 text-sm"></i>
                                <span class="text-sm text-slate-300 font-medium">Staking Progress</span>
                            </div>
                            <div class="flex items-center gap-2">
                                <span class="text-sm text-cyan-400 font-bold">65%</span>
                                <div class="text-xs text-slate-400 bg-slate-700/50 px-2 py-1 rounded-full">
                                    4 months remaining
                                </div>
                            </div>
                        </div>
                        <div class="relative w-full h-3 bg-slate-700/50 rounded-full overflow-hidden">
                            <div
                                class="absolute inset-0 bg-gradient-to-r from-slate-600/30 to-slate-500/30 rounded-full">
                            </div>
                            <div class="relative h-full bg-gradient-to-r from-cyan-500 via-blue-500 to-cyan-400 rounded-full transition-all duration-2000 ease-out shadow-lg shadow-cyan-500/30"
                                style="width: 65%">
                                <div
                                    class="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent animate-shimmer rounded-full">
                                </div>
                            </div>
                        </div>
                        <div class="flex justify-between text-xs text-slate-400 mt-2">
                            <span>Started: Jan 1, 2024</span>
                            <span>Est. completion: 6 months</span>
                        </div>
                    </div>

                    <!-- Timeline Information -->
                    <div class="grid grid-cols-2 gap-4">
                        <div class="bg-slate-800/30 rounded-lg p-3 border border-slate-700/40">
                            <div class="flex items-center gap-2 mb-2">
                                <i class="fas fa-calendar-plus text-blue-400 text-sm"></i>
                                <span class="text-xs text-slate-400 font-medium">Registration</span>
                            </div>
                            <p class="text-sm text-white font-semibold">01/01/24</p>
                            <p class="text-xs text-slate-400">10:30:00 UTC</p>
                        </div>
                        <div class="bg-slate-800/30 rounded-lg p-3 border border-slate-700/40">
                            <div class="flex items-center gap-2 mb-2">
                                <i class="fas fa-arrow-up text-cyan-400 text-sm"></i>
                                <span class="text-xs text-slate-400 font-medium">Last Top-up</span>
                            </div>
                            <p class="text-sm text-white font-semibold">15/06/24</p>
                            <p class="text-xs text-slate-400">18:45:15 UTC</p>
                        </div>
                    </div>
                </div>

                <!-- Subtle Corner Accent -->
                <div
                    class="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-cyan-400/5 to-transparent rounded-full -translate-y-10 translate-x-10">
                </div>
            </div>

            <!-- Action Buttons - Circular Design -->
            <div class="grid grid-cols-4 gap-4 mb-6">
                <button class="action-btn" data-action="deposit">
                    <div class="action-icon-circle">
                        <i class="fas fa-download text-blue-400"></i>
                    </div>
                    <span class="action-label">Deposit</span>
                </button>
                <button class="action-btn" data-action="topup">
                    <div class="action-icon-circle">
                        <i class="fas fa-plus text-blue-400"></i>
                    </div>
                    <span class="action-label">Topup</span>
                </button>
                <button class="action-btn" data-action="transfer">
                    <div class="action-icon-circle">
                        <i class="fas fa-exchange-alt text-blue-400"></i>
                    </div>
                    <span class="action-label">Transfer</span>
                </button>
                <button class="action-btn" data-action="stack">
                    <div class="action-icon-circle">
                        <i class="fas fa-layer-group text-blue-400"></i>
                    </div>
                    <span class="action-label">Stack</span>
                </button>
            </div>

            <!-- Recent Transactions - Scrollable -->
            <div class="stats-card">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="font-bold text-white">Recent Transactions</h3>
                    <div class="text-xs text-slate-400 bg-slate-700/50 px-2 py-1 rounded-full">
                        Scroll for more
                    </div>
                </div>

                <!-- Scrollable Transaction List -->
                <div class="transaction-scroll-container">
                    <ul class="space-y-3">
                        <!-- Network Profit Transaction -->
                        <li class="transaction-item flex justify-between items-center">
                            <div class="flex items-center gap-3">
                                <div
                                    class="w-10 h-10 rounded-full bg-green-500/20 flex items-center justify-center border border-green-500/30">
                                    <i class="fas fa-network-wired text-green-400"></i>
                                </div>
                                <div>
                                    <p class="font-semibold text-white">Network Profit</p>
                                    <p class="text-xs text-slate-400">July 04, 2025 - 14:30</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <p class="font-semibold text-green-400">+$18.50</p>
                                <p class="text-xs text-green-300">+3.7%</p>
                            </div>
                        </li>

                        <!-- Withdrawal Transaction -->
                        <li class="transaction-item flex justify-between items-center">
                            <div class="flex items-center gap-3">
                                <div
                                    class="w-10 h-10 rounded-full bg-red-500/20 flex items-center justify-center border border-red-500/30">
                                    <i class="fas fa-arrow-up text-red-400"></i>
                                </div>
                                <div>
                                    <p class="font-semibold text-white">Withdrawal to Bank</p>
                                    <p class="text-xs text-slate-400">July 03, 2025 - 16:45</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <p class="font-semibold text-red-400">-$25.00</p>
                                <p class="text-xs text-slate-400">Completed</p>
                            </div>
                        </li>

                        <!-- Daily Profit Transaction -->
                        <li class="transaction-item flex justify-between items-center">
                            <div class="flex items-center gap-3">
                                <div
                                    class="w-10 h-10 rounded-full bg-green-500/20 flex items-center justify-center border border-green-500/30">
                                    <i class="fas fa-calendar-day text-green-400"></i>
                                </div>
                                <div>
                                    <p class="font-semibold text-white">Daily Profit</p>
                                    <p class="text-xs text-slate-400">July 02, 2025 - 09:15</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <p class="font-semibold text-green-400">+$12.25</p>
                                <p class="text-xs text-green-300">+2.5%</p>
                            </div>
                        </li>

                        <!-- Additional Scrollable Transactions -->
                        <li class="transaction-item flex justify-between items-center">
                            <div class="flex items-center gap-3">
                                <div
                                    class="w-10 h-10 rounded-full bg-red-500/20 flex items-center justify-center border border-red-500/30">
                                    <i class="fas fa-credit-card text-red-400"></i>
                                </div>
                                <div>
                                    <p class="font-semibold text-white">Withdrawal to Wallet</p>
                                    <p class="text-xs text-slate-400">July 01, 2025 - 11:20</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <p class="font-semibold text-red-400">-$15.30</p>
                                <p class="text-xs text-slate-400">Pending</p>
                            </div>
                        </li>

                        <li class="transaction-item flex justify-between items-center">
                            <div class="flex items-center gap-3">
                                <div
                                    class="w-10 h-10 rounded-full bg-green-500/20 flex items-center justify-center border border-green-500/30">
                                    <i class="fas fa-trophy text-green-400"></i>
                                </div>
                                <div>
                                    <p class="font-semibold text-white">Bonus Profit</p>
                                    <p class="text-xs text-slate-400">June 30, 2025 - 18:00</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <p class="font-semibold text-green-400">+$20.00</p>
                                <p class="text-xs text-green-300">+4.0%</p>
                            </div>
                        </li>

                        <li class="transaction-item flex justify-between items-center">
                            <div class="flex items-center gap-3">
                                <div
                                    class="w-10 h-10 rounded-full bg-red-500/20 flex items-center justify-center border border-red-500/30">
                                    <i class="fas fa-university text-red-400"></i>
                                </div>
                                <div>
                                    <p class="font-semibold text-white">Bank Withdrawal</p>
                                    <p class="text-xs text-slate-400">June 29, 2025 - 14:30</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <p class="font-semibold text-red-400">-$50.00</p>
                                <p class="text-xs text-slate-400">Completed</p>
                            </div>
                        </li>

                        <li class="transaction-item flex justify-between items-center">
                            <div class="flex items-center gap-3">
                                <div
                                    class="w-10 h-10 rounded-full bg-green-500/20 flex items-center justify-center border border-green-500/30">
                                    <i class="fas fa-sun text-green-400"></i>
                                </div>
                                <div>
                                    <p class="font-semibold text-white">Daily Profit</p>
                                    <p class="text-xs text-slate-400">June 28, 2025 - 10:45</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <p class="font-semibold text-green-400">+$15.75</p>
                                <p class="text-xs text-green-300">+3.2%</p>
                            </div>
                        </li>

                        <li class="transaction-item flex justify-between items-center">
                            <div class="flex items-center gap-3">
                                <div
                                    class="w-10 h-10 rounded-full bg-green-500/20 flex items-center justify-center border border-green-500/30">
                                    <i class="fas fa-globe text-green-400"></i>
                                </div>
                                <div>
                                    <p class="font-semibold text-white">Network Profit</p>
                                    <p class="text-xs text-slate-400">June 27, 2025 - 16:20</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <p class="font-semibold text-green-400">+$22.40</p>
                                <p class="text-xs text-green-300">+4.5%</p>
                            </div>
                        </li>
                    </ul>
                </div>
            </div>

            <!-- Premium Referral Section -->
            <div
                class="relative mt-8 mb-6 overflow-hidden rounded-2xl bg-gradient-to-br from-slate-900/90 via-slate-800/70 to-slate-900/90 border border-blue-400/20 backdrop-blur-sm shadow-xl">
                <!-- Background Glow Effect -->
                <div class="absolute inset-0 bg-gradient-to-r from-transparent via-blue-400/5 to-transparent"></div>

                <!-- Header Section -->
                <div class="relative px-6 py-5 border-b border-slate-700/50">
                    <div class="flex items-center gap-3">
                        <div
                            class="w-10 h-10 rounded-xl bg-gradient-to-br from-blue-500/20 to-cyan-600/20 backdrop-blur-sm border border-blue-400/30 flex items-center justify-center">
                            <i class="fas fa-share-alt text-blue-400 text-lg"></i>
                        </div>
                        <div>
                            <h3 class="font-bold text-white text-lg">Share & Earn</h3>
                            <p class="text-xs text-slate-400">Invite friends and earn rewards</p>
                        </div>
                    </div>
                </div>

                <!-- Referral Content -->
                <div class="relative px-6 py-5">
                    <!-- Referral Stats -->
                    <div class="grid grid-cols-3 gap-4 mb-6">
                        <div class="text-center p-3 rounded-xl bg-slate-800/30 border border-slate-700/50">
                            <p class="text-xs text-slate-400 mb-1 font-medium">Total Referrals</p>
                            <p class="text-xl font-bold text-blue-400">12</p>
                        </div>
                        <div class="text-center p-3 rounded-xl bg-slate-800/30 border border-slate-700/50">
                            <p class="text-xs text-slate-400 mb-1 font-medium">This Month</p>
                            <p class="text-xl font-bold text-cyan-400">3</p>
                        </div>
                        <div class="text-center p-3 rounded-xl bg-slate-800/30 border border-slate-700/50">
                            <p class="text-xs text-slate-400 mb-1 font-medium">Earned</p>
                            <p class="text-xl font-bold text-white">$24.50</p>
                        </div>
                    </div>

                    <!-- Professional Referral Link Section -->
                    <div class="space-y-4 mb-6">
                        <!-- Header -->
                        <div class="flex items-center justify-between">
                            <div class="flex items-center gap-2">
                                <div class="w-6 h-6 rounded-md bg-blue-500/20 flex items-center justify-center">
                                    <i class="fas fa-link text-blue-400 text-xs"></i>
                                </div>
                                <span class="text-sm text-slate-300 font-medium">Referral Link</span>
                            </div>
                            <span
                                class="text-xs text-blue-400 font-semibold bg-blue-500/10 px-2 py-1 rounded-md border border-blue-500/20">
                                Up to 20%
                            </span>
                        </div>

                        <!-- Clean Link Container -->
                        <div class="relative">
                            <div
                                class="flex items-center bg-slate-800/60 border border-slate-600/40 rounded-xl overflow-hidden backdrop-blur-sm">
                                <div class="flex-1 px-4 py-3">
                                    <input type="text" value="https://cryptoapp.com/ref/user123" readonly
                                        class="w-full bg-transparent text-white text-sm font-mono outline-none select-all"
                                        id="referralLink">
                                </div>
                                <div class="border-l border-slate-600/40">
                                    <button onclick="copyReferralLink()"
                                        class="px-6 py-3 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-500 hover:to-blue-600 text-white font-medium text-sm transition-all duration-200 flex items-center gap-2 group">
                                        <i class="fas fa-copy text-xs group-hover:scale-110 transition-transform"></i>
                                        <span>Copy</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Share Options -->
                    <div class="grid grid-cols-4 gap-3">
                        <button onclick="shareVia('whatsapp')"
                            class="flex flex-col items-center gap-2 p-3 rounded-xl bg-slate-800/30 border border-slate-700/50 hover:border-green-500/30 transition-all duration-300 group">
                            <div
                                class="w-8 h-8 rounded-full bg-green-500/20 flex items-center justify-center group-hover:bg-green-500/30 transition-colors">
                                <i class="fab fa-whatsapp text-green-400 text-sm"></i>
                            </div>
                            <span class="text-xs text-slate-400 group-hover:text-green-400">WhatsApp</span>
                        </button>

                        <button onclick="shareVia('telegram')"
                            class="flex flex-col items-center gap-2 p-3 rounded-xl bg-slate-800/30 border border-slate-700/50 hover:border-blue-500/30 transition-all duration-300 group">
                            <div
                                class="w-8 h-8 rounded-full bg-blue-500/20 flex items-center justify-center group-hover:bg-blue-500/30 transition-colors">
                                <i class="fab fa-telegram text-blue-400 text-sm"></i>
                            </div>
                            <span class="text-xs text-slate-400 group-hover:text-blue-400">Telegram</span>
                        </button>

                        <button onclick="shareVia('twitter')"
                            class="flex flex-col items-center gap-2 p-3 rounded-xl bg-slate-800/30 border border-slate-700/50 hover:border-cyan-500/30 transition-all duration-300 group">
                            <div
                                class="w-8 h-8 rounded-full bg-cyan-500/20 flex items-center justify-center group-hover:bg-cyan-500/30 transition-colors">
                                <i class="fab fa-twitter text-cyan-400 text-sm"></i>
                            </div>
                            <span class="text-xs text-slate-400 group-hover:text-cyan-400">Twitter</span>
                        </button>

                        <button onclick="shareVia('more')"
                            class="flex flex-col items-center gap-2 p-3 rounded-xl bg-slate-800/30 border border-slate-700/50 hover:border-purple-500/30 transition-all duration-300 group">
                            <div
                                class="w-8 h-8 rounded-full bg-purple-500/20 flex items-center justify-center group-hover:bg-purple-500/30 transition-colors">
                                <i class="fas fa-ellipsis-h text-purple-400 text-sm"></i>
                            </div>
                            <span class="text-xs text-slate-400 group-hover:text-purple-400">More</span>
                        </button>
                    </div>
                </div>

                <!-- Subtle Corner Accent -->
                <div
                    class="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-blue-400/5 to-transparent rounded-full -translate-y-10 translate-x-10">
                </div>
            </div>
        </div>

        <!-- Sticky Bottom Navigation Bar -->
        <div class="sticky bottom-0 z-40 flex-shrink-0">
            <div class="bg-slate-800/95 backdrop-blur-sm mx-4 mb-4 rounded-2xl border border-slate-700">
                <div class="flex justify-around items-center h-16">
                    <a href="#" class="flex flex-col items-center gap-1 accent">
                        <i class="fas fa-home"></i>
                        <span class="text-xs">Home</span>
                    </a>
                    <a href="wallet.html" class="flex flex-col items-center gap-1 text-slate-400 hover:accent">
                        <i class="fas fa-wallet"></i>
                        <span class="text-xs">Wallet</span>
                    </a>
                    <a href="team.html" class="flex flex-col items-center gap-1 text-slate-400 hover:accent">
                        <i class="fas fa-users"></i>
                        <span class="text-xs">Team</span>
                    </a>
                    <a href="profile.html" class="flex flex-col items-center gap-1 text-slate-400 hover:accent">
                        <i class="fas fa-user"></i>
                        <span class="text-xs">Profile</span>
                    </a>
                </div>
            </div>
        </div>

        <!-- JavaScript for Enhanced Interactions -->
        <script>
            // Action button click handlers
            document.querySelectorAll('.action-btn').forEach(button => {
                button.addEventListener('click', function () {
                    const action = this.dataset.action;

                    // Add click animation
                    this.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        this.style.transform = '';
                    }, 150);

                    // Handle different actions - redirect to specific pages
                    switch (action) {
                        case 'deposit':
                            window.location.href = 'wallet.html?tab=deposit';
                            break;
                        case 'topup':
                            window.location.href = 'upgrade.html';
                            break;
                        case 'transfer':
                            window.location.href = 'wallet.html?tab=transfer';
                            break;
                        case 'stack':
                            window.location.href = 'staking.html';
                            break;
                    }
                });
            });

            // Modal functionality
            function showActionModal(title, description, icon, color) {
                // Create modal overlay
                const overlay = document.createElement('div');
                overlay.className = 'fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4';

                // Create modal content
                overlay.innerHTML = `
                <div class="bg-slate-800 rounded-2xl p-6 w-full max-w-sm border border-slate-700 transform scale-95 opacity-0 transition-all duration-300">
                    <div class="text-center mb-6">
                        <div class="w-16 h-16 mx-auto mb-4 rounded-full flex items-center justify-center" style="background: ${color}20; border: 2px solid ${color}30;">
                            <i class="${icon} text-2xl" style="color: ${color};"></i>
                        </div>
                        <h3 class="text-xl font-bold text-white mb-2">${title}</h3>
                        <p class="text-slate-400 text-sm">${description}</p>
                    </div>

                    <div class="space-y-4 mb-6">
                        <div>
                            <label class="block text-sm font-medium text-slate-300 mb-2">Amount</label>
                            <input type="number" placeholder="0.00" class="w-full bg-slate-700 border border-slate-600 rounded-lg px-4 py-3 text-white placeholder-slate-400 focus:border-blue-400 focus:outline-none">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-slate-300 mb-2">Currency</label>
                            <select class="w-full bg-slate-700 border border-slate-600 rounded-lg px-4 py-3 text-white focus:border-blue-400 focus:outline-none">
                                <option>USD</option>
                                <option>BTC</option>
                                <option>ETH</option>
                            </select>
                        </div>
                    </div>

                    <div class="flex gap-3">
                        <button class="flex-1 bg-slate-700 text-white py-3 rounded-lg font-medium hover:bg-slate-600 transition-colors" onclick="closeModal()">
                            Cancel
                        </button>
                        <button class="flex-1 text-white py-3 rounded-lg font-medium transition-colors" style="background: ${color};" onclick="processAction('${action}')">
                            Confirm
                        </button>
                    </div>
                </div>
            `;

                document.body.appendChild(overlay);

                // Animate modal in
                setTimeout(() => {
                    const modal = overlay.querySelector('div');
                    modal.style.transform = 'scale(1)';
                    modal.style.opacity = '1';
                }, 10);

                // Close on overlay click
                overlay.addEventListener('click', function (e) {
                    if (e.target === overlay) {
                        closeModal();
                    }
                });
            }

            function closeModal() {
                const overlay = document.querySelector('.fixed.inset-0');
                if (overlay) {
                    const modal = overlay.querySelector('div');
                    modal.style.transform = 'scale(0.95)';
                    modal.style.opacity = '0';
                    setTimeout(() => {
                        overlay.remove();
                    }, 300);
                }
            }

            function processAction(action) {
                // Simulate processing
                const button = document.querySelector(`[data-action="${action}"]`);
                const originalContent = button.innerHTML;

                button.innerHTML = `
                <div class="action-icon-container bg-gray-500/20 border-gray-400/30">
                    <i class="fas fa-spinner fa-spin text-gray-400"></i>
                </div>
                <span class="action-label">Processing...</span>
            `;

                setTimeout(() => {
                    button.innerHTML = originalContent;
                    closeModal();

                    // Show success message
                    showToast(`${action.charAt(0).toUpperCase() + action.slice(1)} completed successfully!`, 'success');
                }, 2000);
            }

            function showToast(message, type = 'info') {
                const toast = document.createElement('div');
                toast.className = `fixed top-4 right-4 z-50 px-4 py-3 rounded-lg text-white font-medium transform translate-x-full transition-transform duration-300 ${type === 'success' ? 'bg-green-500' : 'bg-blue-500'
                    }`;
                toast.textContent = message;

                document.body.appendChild(toast);

                setTimeout(() => {
                    toast.style.transform = 'translateX(0)';
                }, 10);

                setTimeout(() => {
                    toast.style.transform = 'translateX(full)';
                    setTimeout(() => toast.remove(), 300);
                }, 3000);
            }

            // Animate stats on load
            window.addEventListener('load', function () {
                const progressBars = document.querySelectorAll('[style*="width:"]');
                progressBars.forEach(bar => {
                    const width = bar.style.width;
                    bar.style.width = '0%';
                    setTimeout(() => {
                        bar.style.width = width;
                    }, 500);
                });

                // Initialize enhanced card features
                initializeEnhancedCards();
            });

            // Initialize enhanced card features
            function initializeEnhancedCards() {
                // Initialize enhanced progress bars
                initializeProgressBars();

                // Initialize card animations
                initializeCardAnimations();
            }

            // Initialize enhanced progress bars
            function initializeProgressBars() {
                const progressBars = document.querySelectorAll('.enhanced-progress');
                progressBars.forEach(bar => {
                    const progress = bar.dataset.progress || 0;

                    // Create progress element if it doesn't exist
                    if (!bar.querySelector('.progress-fill')) {
                        const progressFill = document.createElement('div');
                        progressFill.className = 'progress-fill';
                        progressFill.style.cssText = `
                        position: absolute;
                        top: 0;
                        left: 0;
                        height: 100%;
                        background: linear-gradient(90deg, var(--card-accent), var(--card-accent-light));
                        border-radius: 2px;
                        transition: width 1s cubic-bezier(0.4, 0, 0.2, 1);
                        box-shadow: 0 0 10px var(--card-accent-alpha);
                        width: 0%;
                    `;
                        bar.appendChild(progressFill);
                    }

                    // Animate progress bar
                    setTimeout(() => {
                        const progressFill = bar.querySelector('.progress-fill');
                        if (progressFill) {
                            progressFill.style.width = progress + '%';
                        }
                    }, 1200);
                });
            }

            // Initialize card animations and interactions
            function initializeCardAnimations() {
                const cards = document.querySelectorAll('.stats-card-interactive');

                cards.forEach((card, index) => {
                    // Stagger card entrance animations
                    card.style.opacity = '0';
                    card.style.transform = 'translateY(20px)';

                    setTimeout(() => {
                        card.style.transition = 'all 0.6s cubic-bezier(0.4, 0, 0.2, 1)';
                        card.style.opacity = '1';
                        card.style.transform = 'translateY(0)';
                    }, index * 150);

                    // Add value counting animation
                    const valueElement = card.querySelector('.card-value');
                    if (valueElement) {
                        const targetValue = parseFloat(valueElement.dataset.value);
                        setTimeout(() => {
                            animateValue(valueElement, 0, targetValue, 1500);
                        }, index * 150 + 300);
                    }
                });
            }

            // Animate number counting
            function animateValue(element, start, end, duration) {
                const startTime = performance.now();
                const prefix = element.textContent.includes('$') ? '$' : '';

                function updateValue(currentTime) {
                    const elapsed = currentTime - startTime;
                    const progress = Math.min(elapsed / duration, 1);

                    // Easing function for smooth animation
                    const easeOutCubic = 1 - Math.pow(1 - progress, 3);
                    const currentValue = start + (end - start) * easeOutCubic;

                    element.textContent = prefix + currentValue.toFixed(2);

                    if (progress < 1) {
                        requestAnimationFrame(updateValue);
                    }
                }

                requestAnimationFrame(updateValue);
            }

            // Show detailed card information
            function showCardDetails(cardType) {
                const cardData = {
                    account: {
                        title: 'Standard Account Details',
                        content: `
                        <div class="space-y-4">
                            <div class="flex justify-between items-center">
                                <span class="text-slate-400">Account Type:</span>
                                <span class="text-white font-semibold">Standard</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-slate-400">Balance:</span>
                                <span class="text-blue-400 font-semibold">$100.00</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-slate-400">Activity Level:</span>
                                <span class="text-green-400 font-semibold">75% Active</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-slate-400">Last Activity:</span>
                                <span class="text-white">2 hours ago</span>
                            </div>
                            <div class="mt-4 p-3 bg-blue-500/10 rounded-lg border border-blue-500/20">
                                <p class="text-blue-400 text-sm">💡 Upgrade to Premium for higher limits and exclusive features!</p>
                            </div>
                        </div>
                    `,
                        icon: 'fas fa-wallet',
                        color: '#3b82f6'
                    },
                    today: {
                        title: "Today's Profit Breakdown",
                        content: `
                        <div class="space-y-4">
                            <div class="flex justify-between items-center">
                                <span class="text-slate-400">Network Profit:</span>
                                <span class="text-green-400 font-semibold">+$8.50</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-slate-400">Daily Profit:</span>
                                <span class="text-green-400 font-semibold">+$2.75</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-slate-400">Bonus Profit:</span>
                                <span class="text-green-400 font-semibold">+$1.50</span>
                            </div>
                            <div class="border-t border-slate-600 pt-3">
                                <div class="flex justify-between items-center">
                                    <span class="text-white font-semibold">Total Today:</span>
                                    <span class="text-green-400 font-bold text-lg">+$12.75</span>
                                </div>
                            </div>
                            <div class="mt-4 p-3 bg-green-500/10 rounded-lg border border-green-500/20">
                                <p class="text-green-400 text-sm">🚀 Great performance! You're up 2.55% from yesterday.</p>
                            </div>
                        </div>
                    `,
                        icon: 'fas fa-chart-line',
                        color: '#10b981'
                    }
                };

                const data = cardData[cardType];
                if (data) {
                    showActionModal(data.title, data.content, data.icon, data.color);
                }
            }

            // Referral Functions
            function copyReferralLink() {
                const linkInput = document.getElementById('referralLink');
                linkInput.select();
                linkInput.setSelectionRange(0, 99999); // For mobile devices

                navigator.clipboard.writeText(linkInput.value).then(function () {
                    showToast('Referral link copied to clipboard!', 'success');

                    // Update button temporarily
                    const button = event.target.closest('button');
                    const originalHTML = button.innerHTML;
                    button.innerHTML = '<i class="fas fa-check mr-1"></i>Copied!';
                    button.classList.add('bg-green-600');

                    setTimeout(() => {
                        button.innerHTML = originalHTML;
                        button.classList.remove('bg-green-600');
                    }, 2000);
                }).catch(function (err) {
                    // Fallback for older browsers
                    document.execCommand('copy');
                    showToast('Referral link copied!', 'success');
                });
            }

            function shareVia(platform) {
                const referralLink = document.getElementById('referralLink').value;
                const message = "🚀 Join me on CryptoApp and start earning! Get 20% bonus on your first deposit. ";

                switch (platform) {
                    case 'whatsapp':
                        window.open(`https://wa.me/?text=${encodeURIComponent(message + referralLink)}`, '_blank');
                        break;
                    case 'telegram':
                        window.open(`https://t.me/share/url?url=${encodeURIComponent(referralLink)}&text=${encodeURIComponent(message)}`, '_blank');
                        break;
                    case 'twitter':
                        window.open(`https://twitter.com/intent/tweet?text=${encodeURIComponent(message)}&url=${encodeURIComponent(referralLink)}`, '_blank');
                        break;
                    case 'more':
                        if (navigator.share) {
                            navigator.share({
                                title: 'Join CryptoApp',
                                text: message,
                                url: referralLink
                            }).catch(console.error);
                        } else {
                            // Fallback - copy to clipboard
                            copyReferralLink();
                        }
                        break;
                }

                showToast(`Shared via ${platform.charAt(0).toUpperCase() + platform.slice(1)}!`, 'success');
            }

            // Open Forex Trading Page
            function openForexTrading() {
                window.location.href = 'forex.html';
            }
        </script>

</body>

</html>