<?php
// Fix timezone issues in password reset system
include 'dbcon.php';

echo "<h2>🕐 Fixing Timezone Issues</h2>";

// Set PHP timezone to UTC
date_default_timezone_set('UTC');
echo "<p>✅ PHP timezone set to UTC</p>";

// Set MySQL timezone to UTC for this session
$conn->query("SET time_zone = '+00:00'");
echo "<p>✅ MySQL timezone set to UTC</p>";

// Show current times
echo "<h3>⏰ Current Time Information:</h3>";
echo "<p><strong>PHP time (UTC):</strong> " . date('Y-m-d H:i:s') . "</p>";

$result = $conn->query("SELECT NOW() as mysql_time, UTC_TIMESTAMP() as mysql_utc");
$row = $result->fetch_assoc();
echo "<p><strong>MySQL NOW():</strong> " . $row['mysql_time'] . "</p>";
echo "<p><strong>MySQL UTC_TIMESTAMP():</strong> " . $row['mysql_utc'] . "</p>";

// Clear all existing tokens to start fresh
$conn->query("DELETE FROM password_resets");
echo "<p>🗑️ Cleared all existing password reset tokens</p>";

// Test creating a new token with proper timezone
$test_email = '<EMAIL>';
$reset_token = bin2hex(random_bytes(32));
$expires_at = date('Y-m-d H:i:s', strtotime('+1 hour'));

echo "<h3>🧪 Testing Token Creation:</h3>";
echo "<p><strong>Test Email:</strong> " . htmlspecialchars($test_email) . "</p>";
echo "<p><strong>Token:</strong> " . substr($reset_token, 0, 20) . "...</p>";
echo "<p><strong>Current Time:</strong> " . date('Y-m-d H:i:s') . "</p>";
echo "<p><strong>Expires At:</strong> " . $expires_at . "</p>";

// Insert test token
$stmt = $conn->prepare('INSERT INTO password_resets (email, token, expires_at) VALUES (?, ?, ?)');
$stmt->bind_param('sss', $test_email, $reset_token, $expires_at);

if ($stmt->execute()) {
    echo "<p>✅ Test token created successfully</p>";
    
    // Verify the token
    $stmt = $conn->prepare('SELECT email, token, expires_at, created_at FROM password_resets WHERE token = ?');
    $stmt->bind_param('s', $reset_token);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        $row = $result->fetch_assoc();
        echo "<h3>📋 Token Verification:</h3>";
        echo "<p><strong>Email:</strong> " . htmlspecialchars($row['email']) . "</p>";
        echo "<p><strong>Created At:</strong> " . htmlspecialchars($row['created_at']) . "</p>";
        echo "<p><strong>Expires At:</strong> " . htmlspecialchars($row['expires_at']) . "</p>";
        
        $current_time = date('Y-m-d H:i:s');
        $is_valid = strtotime($row['expires_at']) > strtotime($current_time);
        echo "<p><strong>Is Valid:</strong> " . ($is_valid ? "✅ Yes" : "❌ No") . "</p>";
        
        if ($is_valid) {
            $time_remaining = strtotime($row['expires_at']) - strtotime($current_time);
            echo "<p><strong>Time Remaining:</strong> " . round($time_remaining / 60) . " minutes</p>";
        }
        
        // Test the validation query used in reset_password.php
        $stmt2 = $conn->prepare('SELECT email FROM password_resets WHERE token = ? AND expires_at > ?');
        $stmt2->bind_param('ss', $reset_token, $current_time);
        $stmt2->execute();
        $result2 = $stmt2->get_result();
        
        echo "<p><strong>Validation Query Result:</strong> " . ($result2->num_rows > 0 ? "✅ Passes" : "❌ Fails") . "</p>";
        
        // Generate test URL
        $reset_url = "http://localhost/netvis/ui/reset_password.php?token=" . $reset_token;
        echo "<h3>🔗 Test Reset URL:</h3>";
        echo "<p><a href='" . htmlspecialchars($reset_url) . "' target='_blank'>" . htmlspecialchars($reset_url) . "</a></p>";
        
    } else {
        echo "<p>❌ Token not found after creation</p>";
    }
} else {
    echo "<p>❌ Failed to create test token: " . $conn->error . "</p>";
}

$conn->close();
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h2, h3 { color: #333; }
p { margin: 10px 0; }
a { color: #007cba; }
</style>
