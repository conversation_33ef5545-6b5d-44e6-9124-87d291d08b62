<?php
session_start();
header('Content-Type: application/json');

if (!isset($_SESSION['user_id'])) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Unauthorized access.']);
    exit();
}

$user_id = $_SESSION['user_id'];
$amount = $_POST['amount'] ?? null;

// --- Validation ---
if (empty($amount)) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Amount is required.']);
    exit();
}
if (!is_numeric($amount)) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Amount must be a valid number.']);
    exit();
}
if ($amount <= 0) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Amount must be a positive number.']);
    exit();
}
if (fmod((float)$amount, 10) != 0) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Amount must be a multiple of 10.']);
    exit();
}

if (!isset($_FILES['receipt']) || !is_uploaded_file($_FILES['receipt']['tmp_name'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Payment receipt is required. Please upload a file.']);
    exit();
}

if ($_FILES['receipt']['error'] !== UPLOAD_ERR_OK) {
    $upload_errors = [
        UPLOAD_ERR_INI_SIZE   => 'File is too large (server configuration).',
        UPLOAD_ERR_FORM_SIZE  => 'File is too large (form configuration).',
        UPLOAD_ERR_PARTIAL    => 'File was only partially uploaded.',
        UPLOAD_ERR_NO_FILE    => 'No file was uploaded.',
        UPLOAD_ERR_NO_TMP_DIR => 'Server is missing a temporary folder.',
        UPLOAD_ERR_CANT_WRITE => 'Server failed to write file to disk.',
        UPLOAD_ERR_EXTENSION  => 'A PHP extension stopped the file upload.',
    ];
    $error_message = $upload_errors[$_FILES['receipt']['error']] ?? 'Unknown upload error.';
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => $error_message]);
    exit();
}

// --- File Handling ---
$receipt = $_FILES['receipt'];
$upload_dir = __DIR__ . '/../../uploads/receipts/';
if (!is_dir($upload_dir)) {
    mkdir($upload_dir, 0777, true);
}

$file_ext = strtolower(pathinfo($receipt['name'], PATHINFO_EXTENSION));
$allowed_exts = ['jpg', 'jpeg', 'png', 'gif'];
if (!in_array($file_ext, $allowed_exts)) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Invalid file type. Only JPG, JPEG, PNG, GIF are allowed.']);
    exit();
}

$new_filename = uniqid('receipt_', true) . '.' . $file_ext;
$upload_path = $upload_dir . $new_filename;

if (!move_uploaded_file($receipt['tmp_name'], $upload_path)) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Failed to save uploaded file.']);
    exit();
}

$db_path = 'uploads/receipts/' . $new_filename;

// --- Database Interaction ---
$servername = "localhost";
$username = "root";
$password = "";
$dbname = "crypto";

$conn = new mysqli($servername, $username, $password, $dbname);
if ($conn->connect_error) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Database connection failed.']);
    exit();
}

try {
    $sql = "INSERT INTO deposit_requests (user_id, amount, receipt_path, status) VALUES (?, ?, ?, 'pending')";
    $stmt = $conn->prepare($sql);
    if ($stmt === false) throw new Exception('Failed to prepare statement.');
    $stmt->bind_param("ids", $user_id, $amount, $db_path);
    if (!$stmt->execute()) throw new Exception('Failed to create deposit request.');

    echo json_encode(['success' => true]);

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => $e->getMessage()]);
} finally {
    if (isset($stmt)) $stmt->close();
    $conn->close();
}
?>
