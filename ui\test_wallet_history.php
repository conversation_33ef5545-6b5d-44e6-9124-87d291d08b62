<?php
// Test script to check wallet_history table structure
include 'dbcon.php';

echo "<h2>Testing wallet_history table structure</h2>";

// Check if wallet_history table exists
$check_table = "SHOW TABLES LIKE 'wallet_history'";
$result = $conn->query($check_table);

if ($result->num_rows == 0) {
    echo "❌ wallet_history table does not exist!<br>";
} else {
    echo "✅ wallet_history table exists!<br><br>";
    
    // Show table structure
    echo "<h3>Table Structure:</h3>";
    $describe = "DESCRIBE wallet_history";
    $result = $conn->query($describe);
    
    if ($result) {
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
        
        while ($row = $result->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . $row['Field'] . "</td>";
            echo "<td>" . $row['Type'] . "</td>";
            echo "<td>" . $row['Null'] . "</td>";
            echo "<td>" . $row['Key'] . "</td>";
            echo "<td>" . $row['Default'] . "</td>";
            echo "<td>" . $row['Extra'] . "</td>";
            echo "</tr>";
        }
        echo "</table><br>";
    }
    
    // Test insert
    echo "<h3>Testing Insert:</h3>";
    try {
        $test_sql = "INSERT INTO wallet_history (user_id, amount, type, wallet_type, description) VALUES (1, -10.00, 'withdrawal', 'withdrawal', 'Test withdrawal')";
        if ($conn->query($test_sql)) {
            echo "✅ Test insert successful!<br>";
            
            // Clean up test record
            $cleanup = "DELETE FROM wallet_history WHERE description = 'Test withdrawal' AND user_id = 1";
            $conn->query($cleanup);
            echo "✅ Test record cleaned up!<br>";
        } else {
            echo "❌ Test insert failed: " . $conn->error . "<br>";
        }
    } catch (Exception $e) {
        echo "❌ Test insert exception: " . $e->getMessage() . "<br>";
    }
}

$conn->close();
?>
