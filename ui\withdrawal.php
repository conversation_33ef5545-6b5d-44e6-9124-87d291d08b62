<?php
session_start();

if (!isset($_SESSION['user_id'])) {
    header("Location: login.php");
    exit();
}

include 'dbcon.php';

$user_id = $_SESSION['user_id'];

// Fetch user's withdrawal wallet balance
$balance_sql = "SELECT withdrawal_wallet_balance, binanace_address FROM users WHERE id = ?";
$stmt = $conn->prepare($balance_sql);
$stmt->bind_param("i", $user_id);
$stmt->execute();
$stmt->store_result();
$stmt->bind_result($withdrawal_balance, $user_binance_address);
$stmt->fetch();
$withdrawal_balance = $withdrawal_balance ?? 0;
$user_binance_address = $user_binance_address ?? '';

$stmt->close();
$conn->close();
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Withdrawal</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>

<body class="bg-gray-100 font-sans">
    <?php include 'user_nav.php'; ?>

    <main class="max-w-4xl mx-auto py-6 sm:px-6 lg:px-8">
        <div class="bg-white shadow-lg rounded-lg p-8">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">Withdrawal Request</h2>

            <!-- Balance Display -->
            <div class="mb-8 bg-gray-50 p-6 rounded-lg">
                <h3 class="text-lg font-semibold text-gray-700 mb-4">Your Withdrawal Wallet Balance</h3>
                <div class="bg-white p-4 rounded-lg border border-gray-200">
                    <div class="flex justify-between items-center">
                        <div>
                            <h4 class="text-sm font-medium text-gray-600">Available for Withdrawal</h4>
                            <p class="text-3xl font-bold text-blue-600">$<?php echo number_format($withdrawal_balance, 4); ?></p>
                        </div>
                        <div class="text-right">
                            <div class="text-xs text-gray-500">Withdrawal Wallet</div>
                            <div class="text-sm text-gray-700">Ready to withdraw</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Withdrawal Form -->
            <div id="feedback-message" class="hidden p-4 mb-4 text-sm rounded-lg" role="alert"></div>

            <form id="withdrawal-form" class="space-y-6">
                <div>
                    <label for="amount" class="block text-sm font-medium text-gray-700">Withdrawal Amount (USD)</label>
                    <div class="mt-1 relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <span class="text-gray-500 sm:text-sm">$</span>
                        </div>
                        <input type="number" name="amount" id="amount" step="10" min="10" class="block w-full pl-7 pr-3 py-2 bg-white border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" placeholder="Enter amount (multiple of 10)" required>
                    </div>
                    <p class="mt-1 text-sm text-gray-500">
                        <span class="font-medium">Minimum:</span> $10 |
                        <span class="font-medium">Must be multiple of:</span> $10
                        <span class="text-indigo-600">(e.g., $10, $20, $30, $100)</span>
                    </p>
                </div>

                <div>
                    <label for="binance_address" class="block text-sm font-medium text-gray-700">Binance (BEP20) Address</label>
                    <input type="text" name="binance_address" id="binance_address" value="<?php echo htmlspecialchars($user_binance_address); ?>" class="mt-1 block w-full px-3 py-2 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" placeholder="Enter your Binance BEP20 address" required>
                    <p class="mt-1 text-sm text-gray-500">Enter the Binance address where you want to receive your withdrawal.</p>
                </div>

                <div>
                    <label for="password" class="block text-sm font-medium text-gray-700">Your Password (for verification)</label>
                    <input type="password" name="password" id="password" class="mt-1 block w-full px-3 py-2 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" required autocomplete="current-password">
                </div>

                <div class="bg-yellow-50 border border-yellow-200 rounded-md p-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                            </svg>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-yellow-800">Important Notice</h3>
                            <div class="mt-2 text-sm text-yellow-700">
                                <ul class="list-disc pl-5 space-y-1">
                                    <li>Withdrawal requests are processed manually by administrators</li>
                                    <li>Processing time: 24-48 hours during business days</li>
                                    <li>Minimum withdrawal amount is $10</li>
                                    <li>Amount must be a multiple of $10</li>
                                    <li>Ensure your Binance address is correct - transactions cannot be reversed</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <div>
                    <button type="submit" id="submit-btn" class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        Submit Withdrawal Request
                    </button>
                </div>
            </form>
        </div>

        <!-- Withdrawal History -->
        <div class="mt-8 bg-white shadow-lg rounded-lg p-8">
            <h3 class="text-2xl font-bold text-gray-800 mb-4">Withdrawal History</h3>
            <div class="overflow-x-auto border border-gray-200 rounded-lg">
                <table class="min-w-full bg-white">
                    <thead class="bg-gray-100">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">Date</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-600 uppercase tracking-wider">Amount</th>
                            <th class="px-6 py-3 text-center text-xs font-medium text-gray-600 uppercase tracking-wider">Status</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">Address</th>
                        </tr>
                    </thead>
                    <tbody id="history-body">
                        <!-- History will be loaded here -->
                    </tbody>
                </table>
            </div>
        </div>
    </main>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        $(document).ready(function() {
            const withdrawalBalance = <?php echo $withdrawal_balance; ?>;

            $('#withdrawal-form').on('submit', function(e) {
                e.preventDefault();
                const submitBtn = $('#submit-btn');
                submitBtn.prop('disabled', true).text('Submitting...');

                const amount = parseFloat($('#amount').val());
                const binanceAddress = $('#binance_address').val().trim();
                const password = $('#password').val();

                // Validation
                if (!amount || !binanceAddress || !password) {
                    showFeedback('Please fill in all fields.', 'error');
                    submitBtn.prop('disabled', false).text('Submit Withdrawal Request');
                    return;
                }

                if (amount <= 0) {
                    showFeedback('Amount must be greater than $0.', 'error');
                    submitBtn.prop('disabled', false).text('Submit Withdrawal Request');
                    return;
                }

                if (amount < 10) {
                    showFeedback('Minimum withdrawal amount is $10.', 'error');
                    submitBtn.prop('disabled', false).text('Submit Withdrawal Request');
                    return;
                }

                if (amount % 10 !== 0) {
                    showFeedback('Amount must be a multiple of $10 (e.g., $10, $20, $30, $100).', 'error');
                    submitBtn.prop('disabled', false).text('Submit Withdrawal Request');
                    return;
                }

                if (amount > withdrawalBalance) {
                    showFeedback(`Insufficient balance. Available: $${withdrawalBalance.toFixed(4)}`, 'error');
                    submitBtn.prop('disabled', false).text('Submit Withdrawal Request');
                    return;
                }

                const formData = new FormData(this);

                $.ajax({
                    url: '/netvis/ui/api/submit_withdrawal_request.php',
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        console.log('Withdrawal response:', response);

                        if (response.success) {
                            console.log('Withdrawal successful, showing success message');
                            showFeedback('Your withdrawal request has been submitted successfully. Page will reload shortly...', 'success');

                            // Reload page after 3 seconds to show updated balance and history
                            setTimeout(function() {
                                console.log('Reloading page...');
                                window.location.reload();
                            }, 3000);
                        } else {
                            console.log('Withdrawal failed:', response.message);
                            showFeedback(response.message || 'An unknown error occurred.', 'error');
                        }
                    },
                    error: function(jqXHR) {
                        console.log('Error details:', jqXHR);
                        console.log('Status:', jqXHR.status);
                        console.log('Response text:', jqXHR.responseText);

                        let errorMsg = 'A network error occurred. Please try again.';

                        if (jqXHR.responseJSON && jqXHR.responseJSON.message) {
                            errorMsg = jqXHR.responseJSON.message;
                        } else if (jqXHR.responseText) {
                            // Check if the response text contains "successfully" - might be a success response treated as error
                            if (jqXHR.responseText.includes('successfully') || jqXHR.responseText.includes('success')) {
                                console.log('Detected success in error response, treating as success');
                                showFeedback('Your withdrawal request has been submitted successfully. Page will reload shortly...', 'success');
                                setTimeout(function() {
                                    window.location.reload();
                                }, 3000);
                                return;
                            }
                            errorMsg = 'Server error occurred. Please try again.';
                        }

                        showFeedback(errorMsg, 'error');
                    },
                    complete: function() {
                        submitBtn.prop('disabled', false).text('Submit Withdrawal Request');
                    }
                });
            });

            // Real-time amount validation
            $('#amount').on('input', function() {
                const amount = parseFloat($(this).val());
                const amountInput = $(this);

                // Remove previous validation classes
                amountInput.removeClass('border-red-500 border-green-500');

                if ($(this).val() === '') {
                    return; // Don't validate empty input
                }

                if (isNaN(amount) || amount <= 0) {
                    amountInput.addClass('border-red-500');
                    return;
                }

                if (amount < 10) {
                    amountInput.addClass('border-red-500');
                    return;
                }

                if (amount % 10 !== 0) {
                    amountInput.addClass('border-red-500');
                    return;
                }

                if (amount > withdrawalBalance) {
                    amountInput.addClass('border-red-500');
                    return;
                }

                // Valid amount
                amountInput.addClass('border-green-500');
            });



            function loadWithdrawalHistory() {
                $.ajax({
                    url: '/netvis/ui/api/get_withdrawal_history.php',
                    type: 'GET',
                    success: function(data) {
                        const tbody = $('#history-body');
                        tbody.empty();

                        if (data.length === 0) {
                            tbody.append('<tr><td colspan="4" class="px-6 py-4 text-center text-gray-500">No withdrawal history found.</td></tr>');
                            return;
                        }

                        data.forEach(function(withdrawal) {
                            const statusClass = withdrawal.status === 'approved' ? 'text-green-600' :
                                withdrawal.status === 'rejected' ? 'text-red-600' : 'text-yellow-600';
                            const statusText = withdrawal.status.charAt(0).toUpperCase() + withdrawal.status.slice(1);

                            tbody.append(`
                                <tr class="border-t border-gray-200">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${new Date(withdrawal.requested_at).toLocaleDateString()}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right">$${parseFloat(withdrawal.amount).toFixed(2)}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm ${statusClass} text-center font-medium">${statusText}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 font-mono">${withdrawal.binance_address.substring(0, 20)}...</td>
                                </tr>
                            `);
                        });
                    },
                    error: function() {
                        $('#history-body').html('<tr><td colspan="4" class="px-6 py-4 text-center text-red-500">Error loading withdrawal history.</td></tr>');
                    }
                });
            }

            // Load withdrawal history on page load
            loadWithdrawalHistory();
        });

        // Global functions (outside document ready)
        function showFeedback(message, type) {
            const feedbackMessage = $('#feedback-message');
            feedbackMessage.text(message);

            // Clear all previous classes and set new ones
            feedbackMessage.removeClass('hidden bg-red-50 bg-green-50 text-red-800 text-green-800');

            if (type === 'success') {
                feedbackMessage.addClass('p-4 mb-4 text-sm text-green-800 rounded-lg bg-green-50 border border-green-200');
            } else {
                feedbackMessage.addClass('p-4 mb-4 text-sm text-red-800 rounded-lg bg-red-50 border border-red-200');
            }

            // Auto-hide after 5 seconds (but not for success messages that trigger reload)
            if (type !== 'success') {
                setTimeout(function() {
                    feedbackMessage.addClass('hidden');
                }, 5000);
            }
        }
    </script>
</body>

</html>