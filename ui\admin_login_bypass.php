<?php
session_start();

// Set admin session for testing
$_SESSION['admin_logged_in'] = true;
$_SESSION['admin_id'] = 1;
$_SESSION['user_id'] = 1;

echo "<!DOCTYPE html>";
echo "<html lang='en'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>Admin Access Granted - CryptoApp</title>";
echo "<style>";
echo "body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 0; padding: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; display: flex; align-items: center; justify-content: center; }";
echo ".container { background: white; padding: 40px; border-radius: 20px; box-shadow: 0 20px 50px rgba(0,0,0,0.3); text-align: center; max-width: 500px; }";
echo ".success-icon { font-size: 4rem; color: #10b981; margin-bottom: 20px; }";
echo ".title { color: #1f2937; font-size: 2rem; font-weight: bold; margin-bottom: 10px; }";
echo ".subtitle { color: #6b7280; margin-bottom: 30px; }";
echo ".dashboard-btn { background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%); color: white; padding: 15px 30px; text-decoration: none; border-radius: 12px; font-weight: 600; display: inline-block; transition: all 0.3s ease; box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3); }";
echo ".dashboard-btn:hover { transform: translateY(-2px); box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4); }";
echo ".info-box { background: #f0f9ff; border: 2px solid #0ea5e9; border-radius: 12px; padding: 20px; margin: 20px 0; text-align: left; }";
echo ".info-title { color: #0c4a6e; font-weight: bold; margin-bottom: 10px; }";
echo ".info-text { color: #0369a1; line-height: 1.6; }";
echo ".session-info { background: #f0fdf4; border: 2px solid #22c55e; border-radius: 12px; padding: 15px; margin: 20px 0; }";
echo ".session-title { color: #15803d; font-weight: bold; margin-bottom: 8px; }";
echo ".session-item { color: #166534; font-size: 0.9rem; margin: 5px 0; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<div class='container'>";
echo "<div class='success-icon'>✅</div>";
echo "<h1 class='title'>Admin Access Granted!</h1>";
echo "<p class='subtitle'>You are now logged in as an administrator</p>";

echo "<div class='session-info'>";
echo "<div class='session-title'>🔐 Session Information:</div>";
echo "<div class='session-item'>✅ Admin Status: <strong>Active</strong></div>";
echo "<div class='session-item'>✅ Admin ID: <strong>" . $_SESSION['admin_id'] . "</strong></div>";
echo "<div class='session-item'>✅ User ID: <strong>" . $_SESSION['user_id'] . "</strong></div>";
echo "<div class='session-item'>✅ Login Time: <strong>" . date('Y-m-d H:i:s') . "</strong></div>";
echo "</div>";

echo "<div class='info-box'>";
echo "<div class='info-title'>🎯 Dashboard Features Available:</div>";
echo "<div class='info-text'>";
echo "• Live user statistics and growth charts<br>";
echo "• Real-time financial data (deposits/withdrawals)<br>";
echo "• Investment and staking analytics<br>";
echo "• Recent activity feeds<br>";
echo "• Quick action buttons for pending requests<br>";
echo "• System health monitoring<br>";
echo "• Responsive admin panel design";
echo "</div>";
echo "</div>";

echo "<a href='admin_dashboard.php' class='dashboard-btn'>🚀 Open Admin Dashboard</a>";

echo "<div style='margin-top: 30px; padding-top: 20px; border-top: 1px solid #e5e7eb;'>";
echo "<p style='color: #6b7280; font-size: 0.9rem;'>Session will remain active until browser is closed</p>";
echo "<p style='color: #6b7280; font-size: 0.9rem;'>For production use, implement proper authentication</p>";
echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";
?>
