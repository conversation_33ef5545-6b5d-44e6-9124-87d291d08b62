<?php
header('Content-Type: application/json');
include 'dbcon.php';

$response = ['success' => false, 'message' => ''];

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $password = $_POST['password'] ?? '';
    $token = $_POST['token'] ?? '';
    $email = $_POST['email'] ?? '';

    // Basic validation
    if (empty($password) || empty($token) || empty($email)) {
        $response['message'] = 'All fields are required.';
        echo json_encode($response);
        exit;
    }

    // Validate password length
    if (strlen($password) < 6) {
        $response['message'] = 'Password must be at least 6 characters long.';
        echo json_encode($response);
        exit;
    }

    // Verify token and check if it's not expired
    $current_time = date('Y-m-d H:i:s');
    $stmt = $conn->prepare('SELECT email, expires_at FROM password_resets WHERE token = ? AND email = ?');
    $stmt->bind_param('ss', $token, $email);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows > 0) {
        $row = $result->fetch_assoc();

        // Check if token is still valid
        if (strtotime($row['expires_at']) > strtotime($current_time)) {
            // Token is valid, update password
            $password_hash = password_hash($password, PASSWORD_DEFAULT);

            $stmt = $conn->prepare('UPDATE users SET password = ? WHERE email = ? AND is_active = 1');
            $stmt->bind_param('ss', $password_hash, $email);

            if ($stmt->execute()) {
                // Delete the used reset token
                $stmt = $conn->prepare('DELETE FROM password_resets WHERE token = ? AND email = ?');
                $stmt->bind_param('ss', $token, $email);
                $stmt->execute();

                $response['success'] = true;
                $response['message'] = 'Password updated successfully! You can now login with your new password.';
            } else {
                $response['message'] = 'Failed to update password. Please try again.';
            }
        } else {
            $response['message'] = 'Reset token has expired. Please request a new password reset.';
        }
        $stmt->close();
    } else {
        $response['message'] = 'Invalid reset token.';
    }
} else {
    $response['message'] = 'Invalid request method.';
}

$conn->close();
echo json_encode($response);
