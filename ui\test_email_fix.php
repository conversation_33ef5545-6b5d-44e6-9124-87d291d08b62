<?php
// Test the fixed email functionality
include 'email_helper.php';
include 'dbcon.php';

echo "<h2>🧪 Testing Fixed Email Functionality</h2>";

$test_email = '<EMAIL>';

echo "<h3>Step 1: Generate Real Reset Token</h3>";
$reset_token = bin2hex(random_bytes(32));
$expires_at = date('Y-m-d H:i:s', strtotime('+1 hour'));

echo "<p>🔑 <strong>Token:</strong> " . substr($reset_token, 0, 20) . "...</p>";
echo "<p>⏰ <strong>Expires:</strong> " . $expires_at . " (IST)</p>";

echo "<h3>Step 2: Store Token in Database</h3>";
$stmt = $conn->prepare('INSERT INTO password_resets (email, token, expires_at) VALUES (?, ?, ?) ON DUPLICATE KEY UPDATE token = VALUES(token), expires_at = VALUES(expires_at), created_at = NOW()');
$stmt->bind_param('sss', $test_email, $reset_token, $expires_at);

if ($stmt->execute()) {
    echo "<p>✅ Token stored successfully in database</p>";
    
    echo "<h3>Step 3: Generate Reset URL</h3>";
    $reset_url = "http://localhost/netvis/ui/reset_password.php?token=" . $reset_token;
    echo "<p>🔗 <strong>Reset URL:</strong> <a href='" . htmlspecialchars($reset_url) . "' target='_blank'>" . htmlspecialchars($reset_url) . "</a></p>";
    
    echo "<h3>Step 4: Verify Token Works</h3>";
    $current_time = date('Y-m-d H:i:s');
    $stmt = $conn->prepare('SELECT email, expires_at FROM password_resets WHERE token = ?');
    $stmt->bind_param('s', $reset_token);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        $row = $result->fetch_assoc();
        $is_valid = strtotime($row['expires_at']) > strtotime($current_time);
        
        echo "<p>✅ Token found in database</p>";
        echo "<p>🕐 <strong>Current Time:</strong> " . $current_time . "</p>";
        echo "<p>🕐 <strong>Expires At:</strong> " . $row['expires_at'] . "</p>";
        echo "<p>✅ <strong>Token Status:</strong> " . ($is_valid ? "VALID ✅" : "EXPIRED ❌") . "</p>";
        
        if ($is_valid) {
            $time_remaining = strtotime($row['expires_at']) - strtotime($current_time);
            echo "<p>⏱️ <strong>Time Remaining:</strong> " . round($time_remaining / 60) . " minutes</p>";
        }
    } else {
        echo "<p>❌ Token not found in database</p>";
    }
    
    echo "<h3>Step 5: Create Email Content</h3>";
    $subject = 'Password Reset Link - CryptoApp (Test)';
    $html_message = createPasswordResetLinkEmail('Test User', $reset_url);
    echo "<p>✅ Email content created successfully</p>";
    echo "<p>📧 <strong>Subject:</strong> " . htmlspecialchars($subject) . "</p>";
    
    echo "<h3>🎉 Test Results</h3>";
    if ($is_valid) {
        echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<p><strong>✅ EMAIL FIX IS WORKING CORRECTLY!</strong></p>";
        echo "<p>🔐 Real token generated and stored</p>";
        echo "<p>🕐 Timezone handling correct (Asia/Kolkata)</p>";
        echo "<p>🔗 Reset URL contains valid token</p>";
        echo "<p>✉️ Email template ready to send</p>";
        echo "<p><strong>🎯 The test email will now send working reset links!</strong></p>";
        echo "</div>";
        
        echo "<h3>📋 What Changed:</h3>";
        echo "<ul>";
        echo "<li><strong>Before:</strong> Test email sent fake token 'test123' ❌</li>";
        echo "<li><strong>After:</strong> Test email creates real database token ✅</li>";
        echo "<li><strong>Before:</strong> Reset link didn't work ❌</li>";
        echo "<li><strong>After:</strong> Reset link works immediately ✅</li>";
        echo "</ul>";
        
    } else {
        echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 5px;'>";
        echo "<p><strong>❌ There might be an issue with token validation</strong></p>";
        echo "</div>";
    }
    
} else {
    echo "<p>❌ Failed to store token: " . $conn->error . "</p>";
}

$stmt->close();
$conn->close();
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h2, h3 { color: #333; }
p { margin: 10px 0; }
a { color: #007cba; }
ul { margin-left: 20px; }
li { margin: 5px 0; }
</style>
