<?php
// Prevent any output before JSON
ini_set('display_errors', 0);
error_reporting(0);

session_start();
header('Content-Type: application/json');

// Admin authentication check
if (!isset($_SESSION['user_id']) || !$_SESSION['is_admin']) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Unauthorized access.']);
    exit();
}

include '../dbcon.php';

// Check database connection
if ($conn->connect_error) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Database connection failed.']);
    exit();
}

$admin_id = $_SESSION['user_id'];
$data = json_decode(file_get_contents('php://input'), true);

$claim_id = $data['claim_id'] ?? null;
$action = $data['action'] ?? null; // 'approve' or 'reject'
$reason = $data['reason'] ?? '';

if (!$claim_id || !$action || !in_array($action, ['approve', 'reject'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Invalid request data.']);
    exit();
}

// For rejection, reason is required
if ($action === 'reject' && empty(trim($reason))) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Rejection reason is required.']);
    exit();
}

try {
    // Start transaction
    $conn->begin_transaction();

    // 1. Get staking claim details
    $sql_get_claim = "SELECT sr.*, u.name as user_name 
                      FROM staking_records sr 
                      JOIN users u ON sr.user_id = u.id 
                      WHERE sr.id = ? AND sr.status = 'claim_pending'";
    $stmt_get_claim = $conn->prepare($sql_get_claim);
    
    if (!$stmt_get_claim) {
        throw new Exception('Failed to prepare statement: ' . $conn->error);
    }
    
    $stmt_get_claim->bind_param("i", $claim_id);
    
    if (!$stmt_get_claim->execute()) {
        throw new Exception('Failed to execute query: ' . $stmt_get_claim->error);
    }
    
    $claim_result = $stmt_get_claim->get_result();
    
    if ($claim_result->num_rows === 0) {
        throw new Exception('Staking claim not found or already processed.');
    }
    
    $staking_claim = $claim_result->fetch_assoc();
    $stmt_get_claim->close();

    if ($action === 'approve') {
        // 2a. For approval - add amount to user's withdrawal wallet
        $sql_update_wallet = "UPDATE users SET withdrawal_wallet_balance = withdrawal_wallet_balance + ? WHERE id = ?";
        $stmt_update_wallet = $conn->prepare($sql_update_wallet);
        
        if (!$stmt_update_wallet) {
            throw new Exception('Failed to prepare wallet update: ' . $conn->error);
        }
        
        $stmt_update_wallet->bind_param("di", $staking_claim['amount_usd'], $staking_claim['user_id']);
        
        if (!$stmt_update_wallet->execute()) {
            throw new Exception('Failed to update wallet balance: ' . $stmt_update_wallet->error);
        }
        
        $stmt_update_wallet->close();
        
        // Log the approval in wallet history
        $description = "Staking claim approved - \${$staking_claim['amount_usd']} ({$staking_claim['coins_staked']} coins) added to withdrawal wallet";
        $log_sql = "INSERT INTO wallet_history (user_id, amount, type, wallet_type, description) VALUES (?, ?, 'staking_claim_approved', 'withdrawal', ?)";
        $stmt_log = $conn->prepare($log_sql);
        
        if (!$stmt_log) {
            throw new Exception('Failed to prepare log statement: ' . $conn->error);
        }
        
        $stmt_log->bind_param("ids", $staking_claim['user_id'], $staking_claim['amount_usd'], $description);
        
        if (!$stmt_log->execute()) {
            throw new Exception('Failed to log approval: ' . $stmt_log->error);
        }
        
        $stmt_log->close();
        
        $new_status = 'completed';
        
    } else { // reject
        // 2b. For rejection - revert staking status back to active
        $new_status = 'active';
        
        // Log the rejection in wallet history
        $description = "Staking claim rejected - \${$staking_claim['amount_usd']} ({$staking_claim['coins_staked']} coins). Reason: " . $reason;
        $log_sql = "INSERT INTO wallet_history (user_id, amount, type, wallet_type, description) VALUES (?, 0, 'staking_claim_rejected', 'withdrawal', ?)";
        $stmt_log = $conn->prepare($log_sql);
        
        if (!$stmt_log) {
            throw new Exception('Failed to prepare rejection log: ' . $conn->error);
        }
        
        $stmt_log->bind_param("is", $staking_claim['user_id'], $description);
        
        if (!$stmt_log->execute()) {
            throw new Exception('Failed to log rejection: ' . $stmt_log->error);
        }
        
        $stmt_log->close();
    }

    // 3. Update the staking record status
    $sql_update_claim = "UPDATE staking_records 
                        SET status = ?, completed_at = ?, created_by_admin = ? 
                        WHERE id = ?";
    $stmt_update_claim = $conn->prepare($sql_update_claim);
    
    if (!$stmt_update_claim) {
        throw new Exception('Failed to prepare claim update: ' . $conn->error);
    }
    
    $completed_at = $action === 'approve' ? date('Y-m-d H:i:s') : null;
    $stmt_update_claim->bind_param("ssii", $new_status, $completed_at, $admin_id, $claim_id);
    
    if (!$stmt_update_claim->execute()) {
        throw new Exception('Failed to update staking claim status: ' . $stmt_update_claim->error);
    }
    
    $stmt_update_claim->close();

    // Commit transaction
    $conn->commit();

    echo json_encode([
        'success' => true, 
        'message' => "Staking claim {$action}ed successfully."
    ]);

} catch (Exception $e) {
    $conn->rollback();
    error_log('Process Staking Claim Error: ' . $e->getMessage());
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => $e->getMessage()]);
} finally {
    // Close statements safely
    if (isset($stmt_get_claim) && $stmt_get_claim !== false) {
        $stmt_get_claim->close();
    }
    if (isset($stmt_update_wallet) && $stmt_update_wallet !== false) {
        $stmt_update_wallet->close();
    }
    if (isset($stmt_log) && $stmt_log !== false) {
        $stmt_log->close();
    }
    if (isset($stmt_update_claim) && $stmt_update_claim !== false) {
        $stmt_update_claim->close();
    }
    if (isset($conn) && $conn !== false) {
        $conn->close();
    }
}
?>
