<?php
session_start();
$_SESSION['admin_logged_in'] = true;
$_SESSION['admin_id'] = 1;

include 'dbcon.php';

echo "<!DOCTYPE html>";
echo "<html lang='en'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>Scrollable Cards Test - CryptoApp Admin</title>";
echo "<script src='https://cdn.tailwindcss.com'></script>";
echo "<link rel='stylesheet' href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css' />";
echo "<style>";
echo "body { font-family: 'Inter', sans-serif; background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%); }";
echo ".admin-card { background: rgba(255, 255, 255, 0.95); backdrop-filter: blur(10px); border: 1px solid rgba(59, 130, 246, 0.1); border-radius: 1rem; box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05); }";
echo ".badge { display: inline-flex; align-items: center; padding: 0.25rem 0.75rem; border-radius: 9999px; font-size: 0.75rem; font-weight: 600; }";
echo ".badge-success { background: rgba(34, 197, 94, 0.1); color: #16a34a; border: 1px solid rgba(34, 197, 94, 0.2); }";
echo ".badge-warning { background: rgba(245, 158, 11, 0.1); color: #d97706; border: 1px solid rgba(245, 158, 11, 0.2); }";
echo ".badge-danger { background: rgba(239, 68, 68, 0.1); color: #dc2626; border: 1px solid rgba(239, 68, 68, 0.2); }";
echo "::-webkit-scrollbar { width: 6px; }";
echo "::-webkit-scrollbar-track { background: #f1f5f9; }";
echo "::-webkit-scrollbar-thumb { background: linear-gradient(135deg, #3b82f6, #06b6d4); border-radius: 3px; }";
echo ".scrollbar-thin { scrollbar-width: thin; scrollbar-color: #cbd5e1 #f1f5f9; }";
echo "</style>";
echo "</head>";
echo "<body class='p-6'>";

echo "<h2 class='text-3xl font-bold text-gray-900 mb-6'>🎯 Scrollable Activity Cards Test</h2>";

echo "<div class='grid grid-cols-1 lg:grid-cols-3 gap-6'>";

// Test Recent Users with more data
echo "<div class='admin-card p-6'>";
echo "<div class='flex items-center justify-between mb-4'>";
echo "<h3 class='text-lg font-semibold text-gray-900'>Recent Users</h3>";
echo "<div class='flex items-center space-x-2'>";
echo "<span class='text-xs text-gray-500'>Latest 5</span>";
echo "<i class='fas fa-user-plus text-blue-600'></i>";
echo "</div>";
echo "</div>";
echo "<div class='space-y-3 max-h-80 overflow-y-auto scrollbar-thin'>";

// Get recent users
$users_query = "SELECT id, name, user_id, package, reg_date FROM users WHERE is_admin = 0 ORDER BY reg_date DESC LIMIT 10";
$users_result = $conn->query($users_query);

if ($users_result && $users_result->num_rows > 0) {
    $count = 0;
    while ($user = $users_result->fetch_assoc() && $count < 5) {
        echo "<div class='flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors'>";
        echo "<div>";
        echo "<p class='text-sm font-medium text-gray-900'>" . htmlspecialchars($user['name']) . "</p>";
        echo "<p class='text-xs text-gray-500'>ID: " . $user['user_id'] . " • Package: " . htmlspecialchars($user['package']) . "</p>";
        echo "</div>";
        echo "<div class='text-xs text-gray-400'>";
        echo date('M d', strtotime($user['reg_date']));
        echo "</div>";
        echo "</div>";
        $count++;
    }
} else {
    echo "<p class='text-sm text-gray-500 text-center py-4'>No recent users</p>";
}

echo "</div>";
echo "</div>";

// Test Recent Deposits
echo "<div class='admin-card p-6'>";
echo "<div class='flex items-center justify-between mb-4'>";
echo "<h3 class='text-lg font-semibold text-gray-900'>Recent Deposits</h3>";
echo "<div class='flex items-center space-x-2'>";
echo "<span class='text-xs text-gray-500'>Latest 5</span>";
echo "<i class='fas fa-arrow-down text-green-600'></i>";
echo "</div>";
echo "</div>";
echo "<div class='space-y-3 max-h-80 overflow-y-auto scrollbar-thin'>";

// Get recent deposits
$deposits_query = "SELECT dr.id, dr.user_id, dr.amount, dr.status, u.name, u.user_id as public_user_id FROM deposit_requests dr JOIN users u ON dr.user_id = u.id ORDER BY dr.id DESC LIMIT 10";
$deposits_result = $conn->query($deposits_query);

if ($deposits_result && $deposits_result->num_rows > 0) {
    $count = 0;
    while ($deposit = $deposits_result->fetch_assoc() && $count < 5) {
        echo "<div class='flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors'>";
        echo "<div>";
        echo "<p class='text-sm font-medium text-gray-900'>" . htmlspecialchars($deposit['name']) . "</p>";
        echo "<p class='text-xs text-gray-500'>$" . number_format($deposit['amount'], 2) . " • ID: " . $deposit['public_user_id'] . "</p>";
        echo "</div>";
        echo "<div class='text-right'>";
        $badge_class = $deposit['status'] == 'approved' ? 'success' : ($deposit['status'] == 'pending' ? 'warning' : 'danger');
        echo "<span class='badge badge-$badge_class'>" . ucfirst($deposit['status']) . "</span>";
        echo "<p class='text-xs text-gray-400 mt-1'>ID: " . $deposit['id'] . "</p>";
        echo "</div>";
        echo "</div>";
        $count++;
    }
} else {
    echo "<p class='text-sm text-gray-500 text-center py-4'>No recent deposits</p>";
}

echo "</div>";
echo "</div>";

// Test Recent Withdrawals
echo "<div class='admin-card p-6'>";
echo "<div class='flex items-center justify-between mb-4'>";
echo "<h3 class='text-lg font-semibold text-gray-900'>Recent Withdrawals</h3>";
echo "<div class='flex items-center space-x-2'>";
echo "<span class='text-xs text-gray-500'>Latest 5</span>";
echo "<i class='fas fa-arrow-up text-red-600'></i>";
echo "</div>";
echo "</div>";
echo "<div class='space-y-3 max-h-80 overflow-y-auto scrollbar-thin'>";

// Get recent withdrawals
$withdrawals_query = "SELECT wr.id, wr.user_id, wr.amount, wr.status, u.name, u.user_id as public_user_id FROM withdrawal_requests wr JOIN users u ON wr.user_id = u.id ORDER BY wr.id DESC LIMIT 10";
$withdrawals_result = $conn->query($withdrawals_query);

if ($withdrawals_result && $withdrawals_result->num_rows > 0) {
    $count = 0;
    while ($withdrawal = $withdrawals_result->fetch_assoc() && $count < 5) {
        echo "<div class='flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors'>";
        echo "<div>";
        echo "<p class='text-sm font-medium text-gray-900'>" . htmlspecialchars($withdrawal['name']) . "</p>";
        echo "<p class='text-xs text-gray-500'>$" . number_format($withdrawal['amount'], 2) . " • ID: " . $withdrawal['public_user_id'] . "</p>";
        echo "</div>";
        echo "<div class='text-right'>";
        $badge_class = $withdrawal['status'] == 'approved' ? 'success' : ($withdrawal['status'] == 'pending' ? 'warning' : 'danger');
        echo "<span class='badge badge-$badge_class'>" . ucfirst($withdrawal['status']) . "</span>";
        echo "<p class='text-xs text-gray-400 mt-1'>ID: " . $withdrawal['id'] . "</p>";
        echo "</div>";
        echo "</div>";
        $count++;
    }
} else {
    echo "<p class='text-sm text-gray-500 text-center py-4'>No recent withdrawals</p>";
}

echo "</div>";
echo "</div>";

echo "</div>";

echo "<div class='mt-8 p-6 bg-white rounded-xl border border-blue-200'>";
echo "<h3 class='text-lg font-semibold text-gray-900 mb-4'>✅ Scrollable Cards Features:</h3>";
echo "<ul class='space-y-2 text-sm text-gray-600'>";
echo "<li>✅ <strong>Limited to 5 items</strong> per card for clean display</li>";
echo "<li>✅ <strong>Scrollable containers</strong> with custom scrollbars</li>";
echo "<li>✅ <strong>Hover effects</strong> on individual items</li>";
echo "<li>✅ <strong>Enhanced information</strong> showing user IDs and packages</li>";
echo "<li>✅ <strong>Status badges</strong> with color coding</li>";
echo "<li>✅ <strong>Responsive design</strong> that works on all screen sizes</li>";
echo "</ul>";

echo "<div class='mt-6 text-center'>";
echo "<a href='admin_dashboard.php' class='inline-flex items-center px-6 py-3 bg-blue-600 text-white font-semibold rounded-lg hover:bg-blue-700 transition-colors'>";
echo "<i class='fas fa-tachometer-alt mr-2'></i>";
echo "Open Main Dashboard";
echo "</a>";
echo "</div>";

echo "</div>";

echo "</body>";
echo "</html>";

$conn->close();
?>
