<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Upgrade Package - Crypto Wallet</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <style>
        body {
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
            min-height: 100vh;
        }

        .stats-card {
            background: rgba(30, 41, 59, 0.4);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(59, 130, 246, 0.2);
            border-radius: 0.5rem;
            padding: 0.75rem;
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
        }

        .package-card {
            background: rgba(15, 23, 42, 0.8);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(59, 130, 246, 0.3);
            border-radius: 0.75rem;
            padding: 1rem;
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .package-card:hover {
            border-color: rgba(6, 182, 212, 0.6);
            background: rgba(30, 41, 59, 0.6);
            transform: translateY(-2px);
            box-shadow: 0 15px 40px rgba(6, 182, 212, 0.2);
        }

        .package-card.selected {
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(6, 182, 212, 0.1));
            border-color: rgba(6, 182, 212, 0.8);
            box-shadow: 0 15px 40px rgba(6, 182, 212, 0.3);
        }

        .package-card.current {
            border-color: rgba(34, 197, 94, 0.6);
            background: rgba(34, 197, 94, 0.1);
        }

        .package-card.current::before {
            content: "CURRENT";
            position: absolute;
            top: 0.5rem;
            right: 0.5rem;
            background: linear-gradient(135deg, #22c55e, #16a34a);
            color: white;
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            font-size: 0.625rem;
            font-weight: 700;
        }

        .upgrade-btn {
            background: linear-gradient(135deg, #3b82f6, #06b6d4);
            border: none;
            border-radius: 0.5rem;
            padding: 0.75rem 1.5rem;
            color: white;
            font-weight: 600;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .upgrade-btn:hover {
            background: linear-gradient(135deg, #2563eb, #0891b2);
            transform: translateY(-1px);
            box-shadow: 0 8px 25px rgba(6, 182, 212, 0.3);
        }

        .upgrade-btn:disabled {
            background: linear-gradient(135deg, #64748b, #475569);
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        /* Bottom Navigation Styles */
        .accent {
            color: #06b6d4 !important;
        }

        .hover\:accent:hover {
            color: #06b6d4 !important;
        }
    </style>
</head>

<body class="text-white">
    <div class="min-h-screen flex flex-col">
        <!-- Header -->
        <div class="sticky top-0 z-50 bg-slate-800/95 backdrop-blur-sm border-b border-slate-700">
            <div class="flex items-center justify-between px-6 py-4">
                <div class="flex items-center gap-4">
                    <button onclick="goBack()"
                        class="w-10 h-10 rounded-full bg-slate-700 hover:bg-slate-600 flex items-center justify-center transition-colors">
                        <i class="fas fa-arrow-left text-slate-300"></i>
                    </button>
                    <div>
                        <h1 class="text-xl font-bold text-white">Upgrade Package</h1>
                        <p class="text-sm text-slate-400">Choose your investment plan</p>
                    </div>
                </div>
                <div class="flex items-center gap-2">
                    <div class="text-right">
                        <p class="text-xs text-slate-400">Top-up Balance</p>
                        <p class="text-lg font-bold text-cyan-400">$150.25</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-grow overflow-y-auto px-4 sm:px-6 py-3">
            <!-- Current Package -->
            <div class="stats-card mb-3">
                <div class="flex items-center gap-2 mb-3">
                    <div
                        class="w-8 h-8 rounded-lg bg-gradient-to-r from-blue-600 to-cyan-600 flex items-center justify-center">
                        <i class="fas fa-crown text-white text-sm"></i>
                    </div>
                    <div>
                        <h2 class="text-base font-bold text-white">Current Package</h2>
                        <p class="text-xs text-slate-400">Your active investment plan</p>
                    </div>
                </div>
                <div
                    class="bg-gradient-to-r from-slate-800/50 to-slate-700/50 rounded-lg p-3 border border-cyan-500/20">
                    <div class="flex items-center justify-between">
                        <div>
                            <h3 class="text-base font-bold text-cyan-400">Trial Package</h3>
                            <p class="text-xs text-slate-400">$10 Investment • 0.5% Daily Profit</p>
                        </div>
                        <div class="text-right">
                            <p class="text-xl font-bold text-cyan-400">$10</p>
                            <p class="text-xs text-slate-500">Active</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Available Packages -->
            <div class="stats-card mb-3">
                <div class="flex items-center gap-2 mb-3">
                    <div
                        class="w-8 h-8 rounded-lg bg-gradient-to-r from-blue-600 to-cyan-600 flex items-center justify-center">
                        <i class="fas fa-rocket text-white text-sm"></i>
                    </div>
                    <div>
                        <h2 class="text-base font-bold text-white">Available Packages</h2>
                        <p class="text-xs text-slate-400">Choose your next investment level</p>
                    </div>
                </div>

                <div class="grid grid-cols-1 sm:grid-cols-2 gap-3">
                    <!-- Basic Package -->
                    <div class="package-card" onclick="selectPackage('basic', 50, 0.8)">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center gap-3">
                                <div
                                    class="w-10 h-10 rounded-lg bg-gradient-to-r from-blue-600 to-cyan-600 flex items-center justify-center">
                                    <i class="fas fa-star text-white text-sm"></i>
                                </div>
                                <div>
                                    <h3 class="text-base font-bold text-white">Basic</h3>
                                    <p class="text-xs text-slate-400">Investment Package</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <p class="text-xl font-bold text-blue-400">$50</p>
                                <p class="text-xs text-cyan-400 font-medium">0.8% Daily</p>
                            </div>
                        </div>
                    </div>

                    <!-- Standard Package -->
                    <div class="package-card" onclick="selectPackage('standard', 100, 1.0)">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center gap-3">
                                <div
                                    class="w-10 h-10 rounded-lg bg-gradient-to-r from-cyan-600 to-blue-600 flex items-center justify-center">
                                    <i class="fas fa-gem text-white text-sm"></i>
                                </div>
                                <div>
                                    <h3 class="text-base font-bold text-white">Standard</h3>
                                    <p class="text-xs text-slate-400">Investment Package</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <p class="text-xl font-bold text-blue-400">$100</p>
                                <p class="text-xs text-cyan-400 font-medium">1.0% Daily</p>
                            </div>
                        </div>
                    </div>

                    <!-- Premium Package -->
                    <div class="package-card" onclick="selectPackage('premium', 500, 1.2)">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center gap-3">
                                <div
                                    class="w-10 h-10 rounded-lg bg-gradient-to-r from-cyan-600 to-blue-600 flex items-center justify-center">
                                    <i class="fas fa-crown text-white text-sm"></i>
                                </div>
                                <div>
                                    <h3 class="text-base font-bold text-white">Premium</h3>
                                    <p class="text-xs text-slate-400">Investment Package</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <p class="text-xl font-bold text-blue-400">$500</p>
                                <p class="text-xs text-cyan-400 font-medium">1.2% Daily</p>
                            </div>
                        </div>
                    </div>

                    <!-- VIP Package -->
                    <div class="package-card" onclick="selectPackage('vip', 1000, 1.3)">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center gap-3">
                                <div
                                    class="w-10 h-10 rounded-lg bg-gradient-to-r from-blue-600 to-cyan-600 flex items-center justify-center">
                                    <i class="fas fa-trophy text-white text-sm"></i>
                                </div>
                                <div>
                                    <h3 class="text-base font-bold text-white">VIP</h3>
                                    <p class="text-xs text-slate-400">Investment Package</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <p class="text-xl font-bold text-blue-400">$1000</p>
                                <p class="text-xs text-cyan-400 font-medium">1.3% Daily</p>
                            </div>
                        </div>
                    </div>

                    <!-- Elite Package -->
                    <div class="package-card" onclick="selectPackage('elite', 2500, 1.5)">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center gap-3">
                                <div
                                    class="w-10 h-10 rounded-lg bg-gradient-to-r from-cyan-600 to-blue-600 flex items-center justify-center">
                                    <i class="fas fa-diamond text-white text-sm"></i>
                                </div>
                                <div>
                                    <h3 class="text-base font-bold text-white">Elite Club</h3>
                                    <p class="text-xs text-slate-400">Investment Package</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <p class="text-xl font-bold text-blue-400">$2500</p>
                                <p class="text-xs text-cyan-400 font-medium">1.5% Daily</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Selected Package Details -->
            <div class="stats-card mb-4" id="selectedPackageDetails" style="display: none;">
                <div class="flex items-center gap-3 mb-4">
                    <div
                        class="w-10 h-10 rounded-lg bg-gradient-to-r from-blue-600 to-cyan-600 flex items-center justify-center">
                        <i class="fas fa-star text-white"></i>
                    </div>
                    <div>
                        <h2 class="text-lg font-bold text-white">Upgrade Details</h2>
                        <p class="text-xs text-slate-400">Review your package upgrade</p>
                    </div>
                </div>

                <div
                    class="bg-gradient-to-r from-slate-800/50 to-slate-700/50 rounded-lg p-4 border border-blue-500/20">
                    <div class="grid grid-cols-2 gap-4 text-sm">
                        <div>
                            <p class="text-slate-400">Package:</p>
                            <p class="font-bold text-blue-400" id="selectedPackageName">-</p>
                        </div>
                        <div>
                            <p class="text-slate-400">Investment:</p>
                            <p class="font-bold text-white" id="selectedPackageAmount">$0</p>
                        </div>
                        <div>
                            <p class="text-slate-400">Daily Profit:</p>
                            <p class="font-bold text-cyan-400" id="selectedPackageProfit">0%</p>
                        </div>
                        <div>
                            <p class="text-slate-400">Upgrade Cost:</p>
                            <p class="font-bold text-cyan-400" id="upgradeCost">$0</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Upgrade Button -->
            <div class="stats-card">
                <button onclick="processUpgrade()" id="upgradeBtn" disabled
                    class="upgrade-btn w-full text-lg flex items-center justify-center gap-3">
                    <i class="fas fa-arrow-up"></i>
                    <span>Upgrade Package</span>
                    <span class="text-sm opacity-75" id="upgradeAmount">$0</span>
                </button>
                <p class="text-xs text-slate-500 text-center mt-3">🔒 Secure upgrade • Funds deducted from top-up wallet
                </p>
            </div>
        </div>

        <!-- Sticky Bottom Navigation Bar -->
        <div class="sticky bottom-0 z-40 flex-shrink-0">
            <div class="bg-slate-800/95 backdrop-blur-sm mx-4 mb-4 rounded-2xl border border-slate-700">
                <div class="flex justify-around items-center h-16">
                    <a href="1.html" class="flex flex-col items-center gap-1 text-slate-400 hover:accent">
                        <i class="fas fa-home"></i>
                        <span class="text-xs">Home</span>
                    </a>
                    <a href="wallet.html" class="flex flex-col items-center gap-1 accent">
                        <i class="fas fa-wallet"></i>
                        <span class="text-xs">Wallet</span>
                    </a>
                    <a href="team.html" class="flex flex-col items-center gap-1 text-slate-400 hover:accent">
                        <i class="fas fa-users"></i>
                        <span class="text-xs">Team</span>
                    </a>
                    <a href="profile.html" class="flex flex-col items-center gap-1 text-slate-400 hover:accent">
                        <i class="fas fa-user"></i>
                        <span class="text-xs">Profile</span>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script>
        let selectedPackage = null;
        let currentPackageAmount = 10; // Current Trial package
        let topupBalance = 150.25;

        function goBack() {
            window.history.back();
        }

        function selectPackage(name, amount, dailyProfit) {
            selectedPackage = { name, amount, dailyProfit };

            // Clear all selections
            document.querySelectorAll('.package-card').forEach(card => {
                card.classList.remove('selected');
            });

            // Select clicked package
            event.currentTarget.classList.add('selected');

            // Calculate upgrade cost (difference between new and current package)
            const upgradeCost = amount - currentPackageAmount;

            // Update details
            document.getElementById('selectedPackageName').textContent = name.charAt(0).toUpperCase() + name.slice(1);
            document.getElementById('selectedPackageAmount').textContent = `$${amount}`;
            document.getElementById('selectedPackageProfit').textContent = `${dailyProfit}%`;
            document.getElementById('upgradeCost').textContent = `$${upgradeCost}`;
            document.getElementById('upgradeAmount').textContent = `$${upgradeCost}`;

            // Show details section
            document.getElementById('selectedPackageDetails').style.display = 'block';

            // Enable/disable upgrade button
            const upgradeBtn = document.getElementById('upgradeBtn');
            if (upgradeCost <= topupBalance && upgradeCost > 0) {
                upgradeBtn.disabled = false;
            } else {
                upgradeBtn.disabled = true;
            }
        }

        function processUpgrade() {
            if (!selectedPackage) {
                Swal.fire({
                    icon: 'info',
                    title: 'No Package Selected',
                    text: 'Please select a package to upgrade to',
                    background: '#1e293b',
                    color: '#ffffff',
                    confirmButtonColor: '#06b6d4',
                    iconColor: '#06b6d4'
                });
                return;
            }

            const upgradeCost = selectedPackage.amount - currentPackageAmount;

            if (upgradeCost <= 0) {
                Swal.fire({
                    icon: 'info',
                    title: 'Invalid Upgrade',
                    text: 'You cannot downgrade your package',
                    background: '#1e293b',
                    color: '#ffffff',
                    confirmButtonColor: '#3b82f6',
                    iconColor: '#3b82f6'
                });
                return;
            }

            if (upgradeCost > topupBalance) {
                Swal.fire({
                    icon: 'info',
                    title: 'Insufficient Balance',
                    text: 'Please add funds to your top-up wallet.',
                    background: '#1e293b',
                    color: '#ffffff',
                    confirmButtonColor: '#3b82f6',
                    iconColor: '#3b82f6'
                });
                return;
            }

            // Show confirmation dialog
            Swal.fire({
                title: 'Confirm Package Upgrade',
                html: `
                    <div class="text-left space-y-3">
                        <div class="flex justify-between">
                            <span class="text-slate-400">New Package:</span>
                            <span class="font-bold text-blue-400">${selectedPackage.name.charAt(0).toUpperCase() + selectedPackage.name.slice(1)}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-slate-400">Investment Amount:</span>
                            <span class="font-bold">$${selectedPackage.amount}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-slate-400">Daily Profit Rate:</span>
                            <span class="font-bold text-cyan-400">${selectedPackage.dailyProfit}%</span>
                        </div>
                        <div class="flex justify-between border-t border-slate-600 pt-2">
                            <span class="text-slate-400">Upgrade Cost:</span>
                            <span class="font-bold text-cyan-400">$${upgradeCost}</span>
                        </div>
                    </div>
                `,
                icon: 'question',
                background: '#1e293b',
                color: '#ffffff',
                showCancelButton: true,
                confirmButtonColor: '#06b6d4',
                cancelButtonColor: '#475569',
                confirmButtonText: 'Confirm Upgrade',
                cancelButtonText: 'Cancel',
                iconColor: '#06b6d4'
            }).then((result) => {
                if (result.isConfirmed) {
                    // Show success message
                    Swal.fire({
                        icon: 'success',
                        title: 'Upgrade Successful!',
                        html: `
                            <div class="text-center space-y-2">
                                <p>You are now on the <strong class="text-blue-400">${selectedPackage.name.charAt(0).toUpperCase() + selectedPackage.name.slice(1)}</strong> package.</p>
                                <p>Daily profit rate: <strong class="text-cyan-400">${selectedPackage.dailyProfit}%</strong></p>
                                <p class="text-sm text-slate-400">$${upgradeCost} has been deducted from your top-up wallet.</p>
                            </div>
                        `,
                        background: '#1e293b',
                        color: '#ffffff',
                        confirmButtonColor: '#06b6d4',
                        confirmButtonText: 'Continue',
                        iconColor: '#06b6d4'
                    }).then(() => {
                        // In a real app, this would redirect back to wallet or dashboard
                        window.location.href = 'wallet.html';
                    });
                }
            });
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function () {
            // Mark current package as current (Trial - $10)
            // This would be dynamic based on user's actual current package
        });
    </script>
</body>

</html>