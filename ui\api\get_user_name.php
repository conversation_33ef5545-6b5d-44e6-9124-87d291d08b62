<?php
ini_set('display_errors', 0);
ini_set('log_errors', 1);
ini_set('error_log', 'c:/xampp/htdocs/netvis/php_errors.log');
error_reporting(E_ALL);
header('Content-Type: application/json');

include '../dbcon.php';

$recipient_user_id = $_GET['user_id'] ?? '';

if (empty($recipient_user_id)) {
    echo json_encode(['success' => false, 'message' => 'User ID is required.']);
    exit();
}

$stmt = $conn->prepare('SELECT name FROM users WHERE user_id = ?');
if ($stmt === false) {
    echo json_encode(['success' => false, 'message' => 'Database error: ' . $conn->error]);
    exit();
}
$stmt->bind_param('s', $recipient_user_id);
if (!$stmt->execute()) {
    echo json_encode(['success' => false, 'message' => 'Database error: ' . $stmt->error]);
    exit();
}
$stmt->store_result();
$stmt->bind_result($name);

if ($stmt->num_rows > 0) {
    $stmt->fetch();
    echo json_encode(['success' => true, 'name' => $name]);
} else {
    echo json_encode(['success' => false, 'message' => 'User not found.']);
}

$stmt->close();
$conn->close();
?>
