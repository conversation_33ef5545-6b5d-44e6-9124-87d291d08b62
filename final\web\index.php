<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CryptoApp - Advanced Crypto Investment Platform</title>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">

    <!-- AOS Animation Library -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">

    <style>
        body {
            font-family: 'Inter', sans-serif;
        }

        /* Custom Theme Colors - Ocean Theme */
        .theme-ocean {
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
            color: #e2e8f0;
        }

        .glass-card {
            background: rgba(30, 41, 59, 0.8);
            backdrop-filter: blur(15px);
            -webkit-backdrop-filter: blur(15px);
            border: 1px solid rgba(59, 130, 246, 0.2);
            border-radius: 1rem;
            transition: all 0.3s ease;
        }

        .glass-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            border-color: rgba(59, 130, 246, 0.4);
        }

        .accent-blue {
            color: #3b82f6;
        }

        .accent-cyan {
            color: #06b6d4;
        }

        .gradient-text {
            background: linear-gradient(135deg, #3b82f6, #06b6d4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .btn-primary {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            border: none;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #1d4ed8, #1e40af);
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(59, 130, 246, 0.4);
        }

        .btn-secondary {
            background: linear-gradient(135deg, #06b6d4, #0891b2);
            border: none;
            transition: all 0.3s ease;
        }

        .btn-secondary:hover {
            background: linear-gradient(135deg, #0891b2, #0e7490);
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(6, 182, 212, 0.4);
        }

        .hero-bg {
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #0f172a 100%);
            position: relative;
            overflow: hidden;
        }

        .hero-bg::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at 30% 20%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 70% 80%, rgba(6, 182, 212, 0.1) 0%, transparent 50%);
            pointer-events: none;
        }

        .floating-animation {
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {

            0%,
            100% {
                transform: translateY(0px);
            }

            50% {
                transform: translateY(-20px);
            }
        }

        .pulse-glow {
            animation: pulse-glow 2s ease-in-out infinite alternate;
        }

        @keyframes pulse-glow {
            from {
                box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
            }

            to {
                box-shadow: 0 0 30px rgba(59, 130, 246, 0.6);
            }
        }

        /* Smooth scrolling */
        html {
            scroll-behavior: smooth;
        }

        /* Custom scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: #1e293b;
        }

        ::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, #3b82f6, #06b6d4);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, #1d4ed8, #0891b2);
        }

        /* Navigation styles */
        .nav-link {
            position: relative;
            transition: all 0.3s ease;
        }

        .nav-link::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 0;
            width: 0;
            height: 2px;
            background: linear-gradient(90deg, #3b82f6, #06b6d4);
            transition: width 0.3s ease;
        }

        .nav-link:hover::after {
            width: 100%;
        }

        /* Scrolled Nav */
        nav.nav-scrolled {
            background: rgba(15, 23, 42, 0.6);
            backdrop-filter: blur(12px);
            -webkit-backdrop-filter: blur(12px);
            border-bottom-color: rgba(59, 130, 246, 0.1);
            box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.2);
        }

        /* Stats counter animation */
        .counter {
            font-weight: 700;
            font-size: 2.5rem;
        }

        /* FAQ accordion styles */
        .faq-item {
            border-bottom: 1px solid rgba(59, 130, 246, 0.2);
        }

        .faq-content {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;
        }

        .faq-content.active {
            max-height: 200px;
        }

        /* Review card styles */
        .review-card {
            background: rgba(30, 41, 59, 0.6);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(59, 130, 246, 0.1);
            transition: all 0.3s ease;
        }

        .review-card:hover {
            border-color: rgba(59, 130, 246, 0.3);
            transform: translateY(-5px);
        }

        /* Mobile menu styles */
        .mobile-menu {
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            width: 320px !important;
            max-width: 85vw !important;
            height: 100vh !important;
            height: 100dvh !important;
            /* Dynamic viewport height for mobile */
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%) !important;
            border-right: 2px solid rgba(59, 130, 246, 0.3) !important;
            transform: translateX(-100%) !important;
            transition: transform 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;
            z-index: 9999 !important;
            box-shadow: 0 0 50px rgba(0, 0, 0, 0.5) !important;
            overflow-y: auto !important;
            overflow-x: hidden !important;
            -webkit-overflow-scrolling: touch !important;
        }

        .mobile-menu.active {
            transform: translateX(0) !important;
            will-change: transform !important;
        }

        .mobile-menu-overlay {
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            width: 100vw !important;
            height: 100vh !important;
            height: 100dvh !important;
            /* Dynamic viewport height for mobile */
            background: rgba(0, 0, 0, 0.6) !important;
            opacity: 0 !important;
            visibility: hidden !important;
            transition: all 0.4s ease !important;
            z-index: 9998 !important;
            -webkit-tap-highlight-color: transparent !important;
        }

        .mobile-menu-overlay.active {
            opacity: 1 !important;
            visibility: visible !important;
        }

        /* Force hardware acceleration for smooth animations */
        .mobile-menu,
        .mobile-menu-overlay {
            -webkit-transform: translateZ(0) !important;
            transform: translateZ(0) !important;
            -webkit-backface-visibility: hidden !important;
            backface-visibility: hidden !important;
        }

        /* Mobile responsive improvements */
        @media (max-width: 768px) {

            /* Mobile Navigation */
            nav {
                backdrop-filter: blur(25px) !important;
                -webkit-backdrop-filter: blur(25px) !important;
                background: rgba(15, 23, 42, 0.98) !important;
                border-bottom: 1px solid rgba(59, 130, 246, 0.4) !important;
                padding: 0.5rem 0 !important;
                width: 100vw !important;
                max-width: 100vw !important;
                overflow-x: hidden !important;
            }

            nav .container {
                padding-left: 0.75rem !important;
                padding-right: 0.75rem !important;
                padding-top: 0.5rem !important;
                padding-bottom: 0.5rem !important;
                max-width: 100% !important;
                width: 100% !important;
                margin: 0 auto !important;
            }

            /* Mobile Logo Improvements */
            nav .gradient-text {
                font-size: 1.125rem !important;
                font-weight: 800 !important;
            }

            nav .w-8 {
                width: 1.75rem !important;
                height: 1.75rem !important;
            }

            /* Mobile Menu Button */
            #mobileMenuBtn {
                padding: 0.5rem !important;
                border-radius: 0.5rem !important;
            }

            /* Mobile Hero Section */
            .hero-bg {
                min-height: 100vh;
                padding-top: 4.5rem !important;
                padding-bottom: 2rem;
                padding-left: 0.75rem !important;
                padding-right: 0.75rem !important;
            }

            .hero-bg::before {
                background: radial-gradient(circle at 50% 20%, rgba(59, 130, 246, 0.2) 0%, transparent 60%),
                    radial-gradient(circle at 50% 80%, rgba(6, 182, 212, 0.15) 0%, transparent 60%);
            }

            /* Mobile Glass Cards */
            .glass-card {
                background: rgba(30, 41, 59, 0.95) !important;
                backdrop-filter: blur(25px) !important;
                -webkit-backdrop-filter: blur(25px) !important;
                border: 1px solid rgba(59, 130, 246, 0.4) !important;
                border-radius: 1rem !important;
                padding: 1rem !important;
                margin: 0.25rem !important;
            }

            /* Mobile Typography */
            .gradient-text {
                background: linear-gradient(135deg, #60a5fa, #22d3ee) !important;
                -webkit-background-clip: text !important;
                -webkit-text-fill-color: transparent !important;
                background-clip: text !important;
            }

            /* Mobile Counters */
            .counter {
                font-size: 1.75rem !important;
                font-weight: 800 !important;
                line-height: 1.2 !important;
            }

            /* Mobile Hero Title */
            .hero-bg h1 {
                font-size: 2rem !important;
                line-height: 1.1 !important;
                margin-bottom: 1rem !important;
            }

            /* Mobile Hero Description */
            .hero-bg p {
                font-size: 1rem !important;
                line-height: 1.5 !important;
                margin-bottom: 2rem !important;
            }
        }

        @media (max-width: 640px) {

            /* Extra small mobile adjustments */
            nav {
                padding-top: 0.5rem !important;
                padding-bottom: 0.5rem !important;
                width: 100vw !important;
                max-width: 100vw !important;
            }

            nav .container {
                padding-left: 0.5rem !important;
                padding-right: 0.5rem !important;
                padding-top: 0.375rem !important;
                padding-bottom: 0.375rem !important;
                max-width: 100% !important;
                width: 100% !important;
            }

            /* Mobile Logo - Extra Small */
            nav .gradient-text {
                font-size: 1rem !important;
            }

            nav .w-8 {
                width: 1.5rem !important;
                height: 1.5rem !important;
            }

            .hero-bg {
                padding-top: 4rem !important;
                padding-left: 0.5rem !important;
                padding-right: 0.5rem !important;
            }

            .glass-card {
                padding: 0.875rem !important;
                margin: 0.125rem !important;
                border-radius: 0.875rem !important;
            }

            .counter {
                font-size: 1.5rem !important;
                font-weight: 700 !important;
            }

            /* Improve button spacing on small screens */
            .btn-primary,
            .border {
                padding-top: 0.875rem !important;
                padding-bottom: 0.875rem !important;
                font-size: 1rem !important;
                font-weight: 600 !important;
            }

            /* Mobile Hero Title - Extra Small */
            .hero-bg h1 {
                font-size: 1.75rem !important;
                line-height: 1.1 !important;
            }

            /* Mobile Hero Description - Extra Small */
            .hero-bg p {
                font-size: 0.9rem !important;
                line-height: 1.4 !important;
            }


        }

        @media (max-width: 480px) {

            /* Very small screens */
            nav .container {
                padding-left: 0.75rem;
                padding-right: 0.75rem;
            }

            .hero-bg {
                padding-top: 4rem;
                padding-left: 0.25rem;
                padding-right: 0.25rem;
            }

            .glass-card {
                padding: 0.75rem;
                border-radius: 0.75rem;
            }

            .counter {
                font-size: 1.25rem;
            }

            /* Smaller text on very small screens */
            h1 {
                line-height: 1.1;
            }
        }

        /* Improve mobile navigation */
        @media (max-width: 1024px) {
            nav {
                backdrop-filter: blur(20px);
                -webkit-backdrop-filter: blur(20px);
            }
        }

        /* Mobile Menu Drawer Specific Styles */
        @media (max-width: 768px) {
            .mobile-menu {
                width: 300px !important;
                max-width: 85vw !important;
                padding-bottom: env(safe-area-inset-bottom, 0px) !important;
                min-height: 100vh !important;
                min-height: 100dvh !important;
            }

            .mobile-menu .gradient-text {
                font-size: 1.25rem !important;
                font-weight: 800 !important;
            }

            .mobile-menu nav a {
                font-size: 1rem !important;
                font-weight: 500 !important;
                min-height: 48px !important;
                /* Better touch targets */
            }

            .mobile-menu nav a:hover,
            .mobile-menu nav a:active {
                background: rgba(59, 130, 246, 0.1) !important;
            }

            .mobile-menu .btn-primary {
                background: linear-gradient(135deg, #3b82f6, #06b6d4) !important;
                font-size: 1rem !important;
                min-height: 48px !important;
            }

            /* iOS Safe Area Support */
            .mobile-menu>div:last-child {
                padding-bottom: calc(2rem + env(safe-area-inset-bottom, 0px)) !important;
                margin-bottom: env(safe-area-inset-bottom, 0px) !important;
            }
        }

        @media (max-width: 480px) {
            .mobile-menu {
                width: 280px !important;
                max-width: 90vw !important;
            }

            /* Extra padding for smaller devices */
            .mobile-menu>div:last-child {
                padding-bottom: calc(2.5rem + env(safe-area-inset-bottom, 0px)) !important;
            }
        }

        /* Android specific fixes */
        @media screen and (-webkit-min-device-pixel-ratio: 0) {
            .mobile-menu {
                -webkit-transform: translate3d(-100%, 0, 0) !important;
                transform: translate3d(-100%, 0, 0) !important;
            }

            .mobile-menu.active {
                -webkit-transform: translate3d(0, 0, 0) !important;
                transform: translate3d(0, 0, 0) !important;
            }
        }

        /* Force GPU acceleration on Android */
        @supports (-webkit-appearance: none) {
            .mobile-menu {
                -webkit-transform: translateX(-100%) translateZ(0) !important;
                transform: translateX(-100%) translateZ(0) !important;
            }

            .mobile-menu.active {
                -webkit-transform: translateX(0) translateZ(0) !important;
                transform: translateX(0) translateZ(0) !important;
            }
        }

        /* iOS specific adjustments */
        @supports (padding: max(0px)) {
            .mobile-menu {
                padding-bottom: max(1rem, env(safe-area-inset-bottom));
            }
        }

        /* Prevent horizontal scrolling on mobile */
        @media (max-width: 768px) {

            html,
            body {
                overflow-x: hidden !important;
                max-width: 100vw !important;
            }

            * {
                max-width: 100% !important;
            }
        }

        /* Scroll to Top Button with Progress Bar */
        .scroll-to-top {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            width: 56px;
            /* Slightly smaller */
            height: 56px;
            background: rgba(30, 41, 59, 0.8);
            /* Glassmorphism effect */
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #e2e8f0;
            font-size: 1.25rem;
            cursor: pointer;
            opacity: 0;
            visibility: hidden;
            transform: translateY(100px);
            transition: all 0.5s cubic-bezier(0.19, 1, 0.22, 1);
            z-index: 1000;
            border: 1px solid rgba(59, 130, 246, 0.3);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }

        .scroll-to-top.visible {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        .scroll-to-top:hover {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 0 25px rgba(59, 130, 246, 0.5), 0 15px 35px rgba(0, 0, 0, 0.3);
            border-color: rgba(59, 130, 246, 0.5);
        }

        .scroll-to-top .fa-arrow-up {
            transition: transform 0.3s ease;
        }

        .scroll-to-top:hover .fa-arrow-up {
            transform: scale(1.1);
        }

        .scroll-progress {
            position: absolute;
            top: -2px;
            /* Adjusted for new border */
            left: -2px;
            width: 60px;
            /* Adjusted for new size */
            height: 60px;
            border-radius: 50%;
            background: conic-gradient(#3b82f6 0deg, transparent 0deg);
            transition: background 0.2s cubic-bezier(0.19, 1, 0.22, 1);
            z-index: -1;
            /* Place it behind the button content */
        }

        @media (max-width: 768px) {
            .scroll-to-top {
                bottom: 1.5rem;
                right: 1.5rem;
                width: 50px;
                height: 50px;
                font-size: 1.25rem;
            }

            .scroll-progress {
                width: 56px;
                height: 56px;
            }
        }
    </style>
</head>

<body class="theme-ocean">
    <!-- Navigation -->
    <nav id="mainNav" class="fixed top-0 left-0 right-0 z-50 transition-all duration-300 border-0 border-b border-transparent">
        <div class="container mx-auto px-4 sm:px-6 py-3 sm:py-4">
            <div class="flex items-center justify-between">
                <!-- Logo -->
                <div class="flex items-center space-x-2 sm:space-x-3">
                    <div class="w-8 h-8 sm:w-10 sm:h-10 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-xl flex items-center justify-center">
                        <i class="fas fa-chart-line text-white text-sm sm:text-lg"></i>
                    </div>
                    <span class="text-xl sm:text-2xl font-bold gradient-text">CryptoApp</span>
                </div>

                <!-- Desktop Navigation -->
                <div class="hidden lg:flex items-center space-x-6 xl:space-x-8">
                    <a href="#home" class="nav-link text-gray-300 hover:text-white text-sm xl:text-base">Home</a>
                    <a href="#about" class="nav-link text-gray-300 hover:text-white text-sm xl:text-base">About</a>
                    <a href="#ai-bot" class="nav-link text-gray-300 hover:text-white text-sm xl:text-base">AI Bot</a>
                    <a href="#faq" class="nav-link text-gray-300 hover:text-white text-sm xl:text-base">FAQ</a>
                    <a href="#reviews" class="nav-link text-gray-300 hover:text-white text-sm xl:text-base">Reviews</a>
                    <a href="#contact" class="nav-link text-gray-300 hover:text-white text-sm xl:text-base">Contact</a>
                </div>

                <!-- Auth Buttons -->
                <div class="hidden lg:flex items-center space-x-3 xl:space-x-4">
                    <a href="../1.html" class="px-4 xl:px-6 py-2 text-white border border-blue-500 rounded-lg hover:bg-blue-500/10 transition-all text-sm xl:text-base">
                        Login
                    </a>
                    <a href="../1.html" class="px-4 xl:px-6 py-2 text-white btn-primary rounded-lg text-sm xl:text-base">
                        Register
                    </a>
                </div>

                <!-- Mobile Menu Button -->
                <button id="mobileMenuBtn" class="lg:hidden text-white p-2 hover:bg-white/10 rounded-lg transition-colors">
                    <i class="fas fa-bars text-lg sm:text-xl"></i>
                </button>
            </div>
        </div>
    </nav>

    <!-- Mobile Menu Overlay -->
    <div id="mobileMenuOverlay" class="mobile-menu-overlay"></div>

    <!-- Mobile Menu Drawer -->
    <div id="mobileMenu" class="mobile-menu lg:hidden">
        <div class="flex flex-col h-full">
            <!-- Mobile Menu Header -->
            <div class="flex items-center justify-between p-6 border-b border-gray-700">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-xl flex items-center justify-center">
                        <i class="fas fa-chart-line text-white text-lg"></i>
                    </div>
                    <span class="text-xl font-bold gradient-text">CryptoApp</span>
                </div>
                <button id="mobileMenuClose" class="text-white p-2 rounded-lg hover:bg-white/10 transition-colors">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>

            <!-- Mobile Navigation Links -->
            <div class="flex-1 py-6">
                <nav class="space-y-2 px-6">
                    <a href="#home" class="flex items-center space-x-3 text-white hover:text-blue-400 hover:bg-white/5 transition-all py-3 px-4 rounded-lg">
                        <i class="fas fa-home w-5"></i>
                        <span class="font-medium">Home</span>
                    </a>
                    <a href="#about" class="flex items-center space-x-3 text-white hover:text-blue-400 hover:bg-white/5 transition-all py-3 px-4 rounded-lg">
                        <i class="fas fa-info-circle w-5"></i>
                        <span class="font-medium">About</span>
                    </a>
                    <a href="#ai-bot" class="flex items-center space-x-3 text-white hover:text-blue-400 hover:bg-white/5 transition-all py-3 px-4 rounded-lg">
                        <i class="fas fa-robot w-5"></i>
                        <span class="font-medium">AI Bot</span>
                    </a>
                    <a href="#faq" class="flex items-center space-x-3 text-white hover:text-blue-400 hover:bg-white/5 transition-all py-3 px-4 rounded-lg">
                        <i class="fas fa-question-circle w-5"></i>
                        <span class="font-medium">FAQ</span>
                    </a>
                    <a href="#reviews" class="flex items-center space-x-3 text-white hover:text-blue-400 hover:bg-white/5 transition-all py-3 px-4 rounded-lg">
                        <i class="fas fa-star w-5"></i>
                        <span class="font-medium">Reviews</span>
                    </a>
                    <a href="#contact" class="flex items-center space-x-3 text-white hover:text-blue-400 hover:bg-white/5 transition-all py-3 px-4 rounded-lg">
                        <i class="fas fa-envelope w-5"></i>
                        <span class="font-medium">Contact</span>
                    </a>
                </nav>
            </div>

            <!-- Mobile Auth Buttons -->
            <div class="p-6 pb-8 border-t border-gray-700 space-y-3" style="padding-bottom: max(2rem, env(safe-area-inset-bottom, 2rem));">
                <a href="../1.html" class="block w-full px-6 py-3 text-white border border-blue-500 rounded-lg text-center font-medium hover:bg-blue-500/10 transition-all">
                    Login
                </a>
                <a href="../1.html" class="block w-full px-6 py-3 text-white btn-primary rounded-lg text-center font-medium">
                    Register
                </a>
            </div>
        </div>
    </div>

    <!-- Hero Section -->
    <section id="home" class="hero-bg min-h-screen flex items-center justify-center pt-16 sm:pt-20 pb-8">
        <div class="container mx-auto px-4 sm:px-6 text-center">
            <div class="max-w-4xl mx-auto" data-aos="fade-up">
                <h1 class="text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-bold mb-4 sm:mb-6 leading-tight">
                    The Future of <span class="gradient-text">Crypto Investment</span>
                </h1>
                <p class="text-base sm:text-lg md:text-xl lg:text-2xl text-gray-300 mb-6 sm:mb-8 leading-relaxed px-2 sm:px-0">
                    Join thousands of investors earning passive income through our advanced AI-powered crypto trading platform.
                    Start with as little as $10 and watch your portfolio grow.
                </p>
                <div class="flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center items-center mb-8 sm:mb-12 px-4 sm:px-0">
                    <a href="../1.html" class="w-full sm:w-auto px-6 sm:px-8 py-3 sm:py-4 btn-primary text-white rounded-lg text-base sm:text-lg font-semibold text-center">
                        <i class="fas fa-rocket mr-2"></i>
                        Start Investing Now
                    </a>
                    <a href="#about" class="w-full sm:w-auto px-6 sm:px-8 py-3 sm:py-4 border border-cyan-500 text-cyan-400 rounded-lg text-base sm:text-lg font-semibold hover:bg-cyan-500/10 transition-all text-center">
                        <i class="fas fa-play mr-2"></i>
                        Learn More
                    </a>
                </div>

                <!-- Hero Stats -->
                <div class="grid grid-cols-2 md:grid-cols-4 gap-3 sm:gap-6 lg:gap-8 mt-8 sm:mt-12 lg:mt-16 px-2 sm:px-0" data-aos="fade-up" data-aos-delay="200">
                    <div class="glass-card p-3 sm:p-4 lg:p-6 text-center">
                        <div class="counter gradient-text text-xl sm:text-2xl lg:text-3xl" data-target="50000">0</div>
                        <p class="text-gray-400 mt-1 sm:mt-2 text-xs sm:text-sm lg:text-base">Active Users</p>
                    </div>
                    <div class="glass-card p-3 sm:p-4 lg:p-6 text-center">
                        <div class="counter gradient-text text-xl sm:text-2xl lg:text-3xl" data-target="25">0</div>
                        <p class="text-gray-400 mt-1 sm:mt-2 text-xs sm:text-sm lg:text-base">Countries</p>
                    </div>
                    <div class="glass-card p-3 sm:p-4 lg:p-6 text-center">
                        <div class="counter gradient-text text-xl sm:text-2xl lg:text-3xl" data-target="1000000">0</div>
                        <p class="text-gray-400 mt-1 sm:mt-2 text-xs sm:text-sm lg:text-base">Total Invested ($)</p>
                    </div>
                    <div class="glass-card p-3 sm:p-4 lg:p-6 text-center">
                        <div class="counter gradient-text text-xl sm:text-2xl lg:text-3xl" data-target="99">0</div>
                        <p class="text-gray-400 mt-1 sm:mt-2 text-xs sm:text-sm lg:text-base">Success Rate (%)</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- About Section -->
    <section id="about" class="py-20">
        <div class="container mx-auto px-6">
            <div class="text-center mb-16" data-aos="fade-up">
                <h2 class="text-4xl md:text-5xl font-bold mb-6">
                    About <span class="gradient-text">CryptoApp</span>
                </h2>
                <p class="text-xl text-gray-300 max-w-3xl mx-auto">
                    We're revolutionizing crypto investment with cutting-edge technology, transparent operations,
                    and a commitment to helping our users achieve financial freedom.
                </p>
            </div>

            <div class="grid md:grid-cols-2 gap-12 items-center">
                <div data-aos="fade-right">
                    <div class="glass-card p-8">
                        <h3 class="text-2xl font-bold mb-6 accent-blue">Our Mission</h3>
                        <p class="text-gray-300 mb-6 leading-relaxed">
                            To democratize cryptocurrency investment by providing a secure, user-friendly platform
                            that leverages artificial intelligence to maximize returns while minimizing risks.
                        </p>
                        <div class="space-y-4">
                            <div class="flex items-center space-x-3">
                                <i class="fas fa-shield-alt text-blue-500"></i>
                                <span>Bank-level security protocols</span>
                            </div>
                            <div class="flex items-center space-x-3">
                                <i class="fas fa-chart-line text-cyan-500"></i>
                                <span>AI-powered trading algorithms</span>
                            </div>
                            <div class="flex items-center space-x-3">
                                <i class="fas fa-users text-blue-500"></i>
                                <span>24/7 customer support</span>
                            </div>
                            <div class="flex items-center space-x-3">
                                <i class="fas fa-globe text-cyan-500"></i>
                                <span>Global accessibility</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div data-aos="fade-left">
                    <div class="grid grid-cols-2 gap-6">
                        <div class="glass-card p-6 text-center floating-animation">
                            <i class="fas fa-trophy text-4xl text-yellow-500 mb-4"></i>
                            <h4 class="text-lg font-semibold mb-2">Award Winning</h4>
                            <p class="text-gray-400 text-sm">Best Crypto Platform 2024</p>
                        </div>
                        <div class="glass-card p-6 text-center floating-animation" style="animation-delay: 1s;">
                            <i class="fas fa-lock text-4xl text-green-500 mb-4"></i>
                            <h4 class="text-lg font-semibold mb-2">Secure</h4>
                            <p class="text-gray-400 text-sm">Military-grade encryption</p>
                        </div>
                        <div class="glass-card p-6 text-center floating-animation" style="animation-delay: 2s;">
                            <i class="fas fa-rocket text-4xl text-blue-500 mb-4"></i>
                            <h4 class="text-lg font-semibold mb-2">Fast</h4>
                            <p class="text-gray-400 text-sm">Instant transactions</p>
                        </div>
                        <div class="glass-card p-6 text-center floating-animation" style="animation-delay: 3s;">
                            <i class="fas fa-heart text-4xl text-red-500 mb-4"></i>
                            <h4 class="text-lg font-semibold mb-2">Trusted</h4>
                            <p class="text-gray-400 text-sm">50K+ happy users</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- AI Bot Section -->
    <section id="ai-bot" class="py-20 bg-gradient-to-r from-slate-900/50 to-slate-800/50">
        <div class="container mx-auto px-6">
            <div class="text-center mb-16" data-aos="fade-up">
                <h2 class="text-4xl md:text-5xl font-bold mb-6">
                    Meet Our <span class="gradient-text">AI Trading Bot</span>
                </h2>
                <p class="text-xl text-gray-300 max-w-3xl mx-auto">
                    Our advanced AI bot analyzes market trends 24/7, executing trades with precision and maximizing your returns
                    while you sleep. Experience the future of automated crypto trading.
                </p>
            </div>

            <div class="grid lg:grid-cols-2 gap-12 items-center">
                <div data-aos="fade-right">
                    <div class="glass-card p-8 pulse-glow">
                        <div class="flex items-center mb-6">
                            <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-2xl flex items-center justify-center mr-4">
                                <i class="fas fa-robot text-white text-2xl"></i>
                            </div>
                            <div>
                                <h3 class="text-2xl font-bold">CryptoBot AI</h3>
                                <p class="text-green-400">● Online & Trading</p>
                            </div>
                        </div>

                        <div class="space-y-6">
                            <div class="bg-slate-800/50 rounded-lg p-4">
                                <h4 class="font-semibold mb-2 accent-blue">Key Features:</h4>
                                <ul class="space-y-2 text-gray-300">
                                    <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i> Real-time market analysis</li>
                                    <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i> Risk management protocols</li>
                                    <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i> Portfolio diversification</li>
                                    <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i> Automated profit taking</li>
                                </ul>
                            </div>

                            <div class="bg-slate-800/50 rounded-lg p-4">
                                <h4 class="font-semibold mb-2 accent-cyan">Performance Stats:</h4>
                                <div class="grid grid-cols-2 gap-4">
                                    <div class="text-center">
                                        <div class="text-2xl font-bold gradient-text">98.7%</div>
                                        <div class="text-sm text-gray-400">Accuracy Rate</div>
                                    </div>
                                    <div class="text-center">
                                        <div class="text-2xl font-bold gradient-text">24/7</div>
                                        <div class="text-sm text-gray-400">Active Trading</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div data-aos="fade-left">
                    <div class="space-y-6">
                        <div class="glass-card p-6">
                            <div class="flex items-center mb-4">
                                <i class="fas fa-brain text-blue-500 text-2xl mr-3"></i>
                                <h4 class="text-xl font-semibold">Machine Learning</h4>
                            </div>
                            <p class="text-gray-300">
                                Our AI continuously learns from market patterns, improving its trading strategies
                                and adapting to changing market conditions.
                            </p>
                        </div>

                        <div class="glass-card p-6">
                            <div class="flex items-center mb-4">
                                <i class="fas fa-shield-alt text-cyan-500 text-2xl mr-3"></i>
                                <h4 class="text-xl font-semibold">Risk Management</h4>
                            </div>
                            <p class="text-gray-300">
                                Advanced risk assessment algorithms protect your investments with stop-loss mechanisms
                                and portfolio balancing strategies.
                            </p>
                        </div>

                        <div class="glass-card p-6">
                            <div class="flex items-center mb-4">
                                <i class="fas fa-chart-bar text-green-500 text-2xl mr-3"></i>
                                <h4 class="text-xl font-semibold">Real-time Analytics</h4>
                            </div>
                            <p class="text-gray-300">
                                Get instant insights into your portfolio performance with detailed analytics
                                and transparent reporting on all trading activities.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- FAQ Section -->
    <section id="faq" class="py-20">
        <div class="container mx-auto px-6">
            <div class="text-center mb-16" data-aos="fade-up">
                <h2 class="text-4xl md:text-5xl font-bold mb-6">
                    Frequently Asked <span class="gradient-text">Questions</span>
                </h2>
                <p class="text-xl text-gray-300 max-w-3xl mx-auto">
                    Get answers to the most common questions about our platform, security, and investment process.
                </p>
            </div>

            <div class="max-w-4xl mx-auto" data-aos="fade-up" data-aos-delay="200">
                <div class="space-y-4">
                    <div class="faq-item glass-card">
                        <button class="faq-question w-full text-left p-6 flex justify-between items-center" onclick="toggleFAQ(this)">
                            <span class="text-lg font-semibold">How does the AI trading bot work?</span>
                            <i class="fas fa-chevron-down transition-transform"></i>
                        </button>
                        <div class="faq-content px-6 pb-6">
                            <p class="text-gray-300">
                                Our AI bot uses advanced machine learning algorithms to analyze market trends, news sentiment,
                                and technical indicators. It executes trades automatically based on predefined strategies,
                                operating 24/7 to maximize your investment returns while managing risk.
                            </p>
                        </div>
                    </div>

                    <div class="faq-item glass-card">
                        <button class="faq-question w-full text-left p-6 flex justify-between items-center" onclick="toggleFAQ(this)">
                            <span class="text-lg font-semibold">What is the minimum investment amount?</span>
                            <i class="fas fa-chevron-down transition-transform"></i>
                        </button>
                        <div class="faq-content px-6 pb-6">
                            <p class="text-gray-300">
                                You can start investing with as little as $10. We offer various investment packages ranging
                                from Trial ($10) to Elite Club ($2500), each with different daily profit rates and features.
                            </p>
                        </div>
                    </div>

                    <div class="faq-item glass-card">
                        <button class="faq-question w-full text-left p-6 flex justify-between items-center" onclick="toggleFAQ(this)">
                            <span class="text-lg font-semibold">How secure is my investment?</span>
                            <i class="fas fa-chevron-down transition-transform"></i>
                        </button>
                        <div class="faq-content px-6 pb-6">
                            <p class="text-gray-300">
                                We employ bank-level security measures including military-grade encryption, cold storage
                                for crypto assets, two-factor authentication, and regular security audits. Your funds are
                                protected by advanced risk management protocols.
                            </p>
                        </div>
                    </div>

                    <div class="faq-item glass-card">
                        <button class="faq-question w-full text-left p-6 flex justify-between items-center" onclick="toggleFAQ(this)">
                            <span class="text-lg font-semibold">How do I withdraw my profits?</span>
                            <i class="fas fa-chevron-down transition-transform"></i>
                        </button>
                        <div class="faq-content px-6 pb-6">
                            <p class="text-gray-300">
                                Withdrawals are processed through BEP20 addresses with a 10% GAS fee. Processing time is
                                typically 24 hours. You can withdraw your profits anytime from your withdrawal wallet
                                through our secure platform.
                            </p>
                        </div>
                    </div>

                    <div class="faq-item glass-card">
                        <button class="faq-question w-full text-left p-6 flex justify-between items-center" onclick="toggleFAQ(this)">
                            <span class="text-lg font-semibold">What are the daily profit rates?</span>
                            <i class="fas fa-chevron-down transition-transform"></i>
                        </button>
                        <div class="faq-content px-6 pb-6">
                            <p class="text-gray-300">
                                Daily profit rates vary by package: Trial (0.5%), Basic (0.7%), Standard (0.9%),
                                Premium (1.1%), VIP (1.3%), and Elite Club (1.5%). Profits are automatically credited
                                to your withdrawal wallet daily.
                            </p>
                        </div>
                    </div>

                    <div class="faq-item glass-card">
                        <button class="faq-question w-full text-left p-6 flex justify-between items-center" onclick="toggleFAQ(this)">
                            <span class="text-lg font-semibold">Is there a referral program?</span>
                            <i class="fas fa-chevron-down transition-transform"></i>
                        </button>
                        <div class="faq-content px-6 pb-6">
                            <p class="text-gray-300">
                                Yes! Our 3-level referral program offers network income and bonus rewards. You earn
                                commissions when your referrals invest, with bonuses available for achieving specific
                                investment milestones in your network.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- User Reviews Section -->
    <section id="reviews" class="py-20 bg-gradient-to-r from-slate-900/30 to-slate-800/30">
        <div class="container mx-auto px-6">
            <div class="text-center mb-16" data-aos="fade-up">
                <h2 class="text-4xl md:text-5xl font-bold mb-6">
                    What Our <span class="gradient-text">Users Say</span>
                </h2>
                <p class="text-xl text-gray-300 max-w-3xl mx-auto">
                    Join thousands of satisfied investors who have transformed their financial future with CryptoApp.
                </p>
            </div>

            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8" data-aos="fade-up" data-aos-delay="200">
                <div class="review-card rounded-xl p-6">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-full flex items-center justify-center mr-4">
                            <span class="text-white font-bold">JD</span>
                        </div>
                        <div>
                            <h4 class="font-semibold">John Davis</h4>
                            <div class="flex text-yellow-400">
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                            </div>
                        </div>
                    </div>
                    <p class="text-gray-300 mb-4">
                        "CryptoApp has completely changed my investment strategy. The AI bot is incredibly accurate,
                        and I've seen consistent profits every month. Highly recommended!"
                    </p>
                    <div class="text-sm text-gray-400">
                        <i class="fas fa-calendar mr-1"></i>
                        Member since 2023
                    </div>
                </div>

                <div class="review-card rounded-xl p-6">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full flex items-center justify-center mr-4">
                            <span class="text-white font-bold">SM</span>
                        </div>
                        <div>
                            <h4 class="font-semibold">Sarah Miller</h4>
                            <div class="flex text-yellow-400">
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                            </div>
                        </div>
                    </div>
                    <p class="text-gray-300 mb-4">
                        "As a beginner in crypto, CryptoApp made everything so easy. The platform is user-friendly,
                        and the customer support is excellent. I'm earning passive income daily!"
                    </p>
                    <div class="text-sm text-gray-400">
                        <i class="fas fa-calendar mr-1"></i>
                        Member since 2024
                    </div>
                </div>

                <div class="review-card rounded-xl p-6">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-teal-500 rounded-full flex items-center justify-center mr-4">
                            <span class="text-white font-bold">MJ</span>
                        </div>
                        <div>
                            <h4 class="font-semibold">Michael Johnson</h4>
                            <div class="flex text-yellow-400">
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                            </div>
                        </div>
                    </div>
                    <p class="text-gray-300 mb-4">
                        "The security features give me peace of mind. I've been investing for 8 months now,
                        and the returns have exceeded my expectations. The AI really works!"
                    </p>
                    <div class="text-sm text-gray-400">
                        <i class="fas fa-calendar mr-1"></i>
                        Member since 2023
                    </div>
                </div>

                <div class="review-card rounded-xl p-6">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-gradient-to-br from-orange-500 to-red-500 rounded-full flex items-center justify-center mr-4">
                            <span class="text-white font-bold">LW</span>
                        </div>
                        <div>
                            <h4 class="font-semibold">Lisa Wang</h4>
                            <div class="flex text-yellow-400">
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                            </div>
                        </div>
                    </div>
                    <p class="text-gray-300 mb-4">
                        "I love the transparency of this platform. Every transaction is clear, withdrawals are fast,
                        and the referral program has helped me earn extra income. Amazing!"
                    </p>
                    <div class="text-sm text-gray-400">
                        <i class="fas fa-calendar mr-1"></i>
                        Member since 2024
                    </div>
                </div>

                <div class="review-card rounded-xl p-6">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-gradient-to-br from-indigo-500 to-purple-500 rounded-full flex items-center justify-center mr-4">
                            <span class="text-white font-bold">RB</span>
                        </div>
                        <div>
                            <h4 class="font-semibold">Robert Brown</h4>
                            <div class="flex text-yellow-400">
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                            </div>
                        </div>
                    </div>
                    <p class="text-gray-300 mb-4">
                        "Started with the trial package and gradually upgraded to VIP. The consistent daily profits
                        and professional service make this the best crypto platform I've used."
                    </p>
                    <div class="text-sm text-gray-400">
                        <i class="fas fa-calendar mr-1"></i>
                        Member since 2023
                    </div>
                </div>

                <div class="review-card rounded-xl p-6">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-gradient-to-br from-cyan-500 to-blue-500 rounded-full flex items-center justify-center mr-4">
                            <span class="text-white font-bold">EG</span>
                        </div>
                        <div>
                            <h4 class="font-semibold">Emma Garcia</h4>
                            <div class="flex text-yellow-400">
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                            </div>
                        </div>
                    </div>
                    <p class="text-gray-300 mb-4">
                        "The mobile app is fantastic! I can monitor my investments anywhere. The AI bot handles
                        everything while I focus on my daily life. Truly passive income!"
                    </p>
                    <div class="text-sm text-gray-400">
                        <i class="fas fa-calendar mr-1"></i>
                        Member since 2024
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Community Section -->
    <section id="community" class="py-20">
        <div class="container mx-auto px-6">
            <div class="text-center mb-16" data-aos="fade-up">
                <h2 class="text-4xl md:text-5xl font-bold mb-6">
                    Join Our <span class="gradient-text">Global Community</span>
                </h2>
                <p class="text-xl text-gray-300 max-w-3xl mx-auto">
                    Connect with thousands of investors worldwide and be part of the crypto revolution.
                </p>
            </div>

            <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-8" data-aos="fade-up" data-aos-delay="200">
                <div class="glass-card p-8 text-center">
                    <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-2xl flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-users text-white text-2xl"></i>
                    </div>
                    <div class="counter gradient-text text-3xl font-bold" data-target="50000">0</div>
                    <p class="text-gray-400 mt-2">Active Members</p>
                </div>

                <div class="glass-card p-8 text-center">
                    <div class="w-16 h-16 bg-gradient-to-br from-green-500 to-teal-500 rounded-2xl flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-globe text-white text-2xl"></i>
                    </div>
                    <div class="counter gradient-text text-3xl font-bold" data-target="25">0</div>
                    <p class="text-gray-400 mt-2">Countries</p>
                </div>

                <div class="glass-card p-8 text-center">
                    <div class="w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-500 rounded-2xl flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-chart-line text-white text-2xl"></i>
                    </div>
                    <div class="counter gradient-text text-3xl font-bold" data-target="1000000">0</div>
                    <p class="text-gray-400 mt-2">Total Invested ($)</p>
                </div>

                <div class="glass-card p-8 text-center">
                    <div class="w-16 h-16 bg-gradient-to-br from-orange-500 to-red-500 rounded-2xl flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-trophy text-white text-2xl"></i>
                    </div>
                    <div class="counter gradient-text text-3xl font-bold" data-target="99">0</div>
                    <p class="text-gray-400 mt-2">Success Rate (%)</p>
                </div>
            </div>

            <div class="text-center mt-12" data-aos="fade-up" data-aos-delay="400">
                <a href="../1.html" class="px-8 py-4 btn-primary text-white rounded-lg text-lg font-semibold inline-block">
                    <i class="fas fa-rocket mr-2"></i>
                    Join Our Community Today
                </a>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="py-20 bg-gradient-to-r from-slate-900/50 to-slate-800/50">
        <div class="container mx-auto px-6">
            <div class="text-center mb-16" data-aos="fade-up">
                <h2 class="text-4xl md:text-5xl font-bold mb-6">
                    Get In <span class="gradient-text">Touch</span>
                </h2>
                <p class="text-xl text-gray-300 max-w-3xl mx-auto">
                    Have questions? Need support? Our team is here to help you 24/7.
                </p>
            </div>

            <div class="grid lg:grid-cols-2 gap-12">
                <!-- Contact Form -->
                <div data-aos="fade-right">
                    <div class="glass-card p-8">
                        <h3 class="text-2xl font-bold mb-6 accent-blue">Send us a Message</h3>
                        <form id="contactForm" class="space-y-6">
                            <div class="grid md:grid-cols-2 gap-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-300 mb-2">First Name</label>
                                    <input type="text" name="firstName" required
                                        class="w-full px-4 py-3 bg-slate-800/50 border border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-white">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-300 mb-2">Last Name</label>
                                    <input type="text" name="lastName" required
                                        class="w-full px-4 py-3 bg-slate-800/50 border border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-white">
                                </div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-300 mb-2">Email Address</label>
                                <input type="email" name="email" required
                                    class="w-full px-4 py-3 bg-slate-800/50 border border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-white">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-300 mb-2">Subject</label>
                                <select name="subject" required
                                    class="w-full px-4 py-3 bg-slate-800/50 border border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-white">
                                    <option value="">Select a subject</option>
                                    <option value="general">General Inquiry</option>
                                    <option value="support">Technical Support</option>
                                    <option value="investment">Investment Questions</option>
                                    <option value="partnership">Partnership</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-300 mb-2">Message</label>
                                <textarea name="message" rows="5" required
                                    class="w-full px-4 py-3 bg-slate-800/50 border border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-white"
                                    placeholder="Tell us how we can help you..."></textarea>
                            </div>
                            <button type="submit" class="w-full btn-primary text-white py-3 rounded-lg font-semibold">
                                <i class="fas fa-paper-plane mr-2"></i>
                                Send Message
                            </button>
                        </form>
                    </div>
                </div>

                <!-- Contact Info -->
                <div data-aos="fade-left">
                    <div class="space-y-8">
                        <div class="glass-card p-6">
                            <div class="flex items-center mb-4">
                                <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-lg flex items-center justify-center mr-4">
                                    <i class="fas fa-envelope text-white"></i>
                                </div>
                                <div>
                                    <h4 class="font-semibold">Email Support</h4>
                                    <p class="text-gray-400"><EMAIL></p>
                                </div>
                            </div>
                            <p class="text-gray-300">
                                Get help with your account, technical issues, or general questions.
                            </p>
                        </div>

                        <div class="glass-card p-6">
                            <div class="flex items-center mb-4">
                                <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-teal-500 rounded-lg flex items-center justify-center mr-4">
                                    <i class="fas fa-comments text-white"></i>
                                </div>
                                <div>
                                    <h4 class="font-semibold">Live Chat</h4>
                                    <p class="text-gray-400">Available 24/7</p>
                                </div>
                            </div>
                            <p class="text-gray-300">
                                Chat with our support team for instant assistance and quick solutions.
                            </p>
                        </div>

                        <div class="glass-card p-6">
                            <div class="flex items-center mb-4">
                                <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-500 rounded-lg flex items-center justify-center mr-4">
                                    <i class="fas fa-map-marker-alt text-white"></i>
                                </div>
                                <div>
                                    <h4 class="font-semibold">Office Location</h4>
                                    <p class="text-gray-400">Global Headquarters</p>
                                </div>
                            </div>
                            <p class="text-gray-300">
                                123 Crypto Street, Digital City<br>
                                Blockchain District, BC 12345<br>
                                United States
                            </p>
                        </div>

                        <div class="glass-card p-6">
                            <h4 class="font-semibold mb-4">Follow Us</h4>
                            <div class="flex space-x-4">
                                <a href="#" class="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center hover:bg-blue-700 transition-colors">
                                    <i class="fab fa-twitter text-white"></i>
                                </a>
                                <a href="#" class="w-10 h-10 bg-blue-800 rounded-lg flex items-center justify-center hover:bg-blue-900 transition-colors">
                                    <i class="fab fa-facebook text-white"></i>
                                </a>
                                <a href="#" class="w-10 h-10 bg-pink-600 rounded-lg flex items-center justify-center hover:bg-pink-700 transition-colors">
                                    <i class="fab fa-instagram text-white"></i>
                                </a>
                                <a href="#" class="w-10 h-10 bg-blue-700 rounded-lg flex items-center justify-center hover:bg-blue-800 transition-colors">
                                    <i class="fab fa-linkedin text-white"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-slate-900/80 backdrop-blur-lg border-t border-blue-500/20 py-12">
        <div class="container mx-auto px-6">
            <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
                <!-- Company Info -->
                <div>
                    <div class="flex items-center space-x-3 mb-6">
                        <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-xl flex items-center justify-center">
                            <i class="fas fa-chart-line text-white text-lg"></i>
                        </div>
                        <span class="text-2xl font-bold gradient-text">CryptoApp</span>
                    </div>
                    <p class="text-gray-400 mb-6">
                        The future of crypto investment. Join thousands of investors earning passive income
                        through our AI-powered trading platform.
                    </p>
                    <div class="flex space-x-4">
                        <a href="#" class="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center hover:bg-blue-700 transition-colors">
                            <i class="fab fa-twitter text-white"></i>
                        </a>
                        <a href="#" class="w-10 h-10 bg-blue-800 rounded-lg flex items-center justify-center hover:bg-blue-900 transition-colors">
                            <i class="fab fa-facebook text-white"></i>
                        </a>
                        <a href="#" class="w-10 h-10 bg-pink-600 rounded-lg flex items-center justify-center hover:bg-pink-700 transition-colors">
                            <i class="fab fa-instagram text-white"></i>
                        </a>
                        <a href="#" class="w-10 h-10 bg-blue-700 rounded-lg flex items-center justify-center hover:bg-blue-800 transition-colors">
                            <i class="fab fa-linkedin text-white"></i>
                        </a>
                    </div>
                </div>

                <!-- Quick Links -->
                <div>
                    <h4 class="text-lg font-semibold mb-6 accent-blue">Quick Links</h4>
                    <ul class="space-y-3">
                        <li><a href="#home" class="text-gray-400 hover:text-white transition-colors">Home</a></li>
                        <li><a href="#about" class="text-gray-400 hover:text-white transition-colors">About</a></li>
                        <li><a href="#ai-bot" class="text-gray-400 hover:text-white transition-colors">AI Bot</a></li>
                        <li><a href="#faq" class="text-gray-400 hover:text-white transition-colors">FAQ</a></li>
                        <li><a href="#reviews" class="text-gray-400 hover:text-white transition-colors">Reviews</a></li>
                        <li><a href="#contact" class="text-gray-400 hover:text-white transition-colors">Contact</a></li>
                    </ul>
                </div>

                <!-- Services -->
                <div>
                    <h4 class="text-lg font-semibold mb-6 accent-cyan">Services</h4>
                    <ul class="space-y-3">
                        <li><a href="../1.html" class="text-gray-400 hover:text-white transition-colors">Crypto Trading</a></li>
                        <li><a href="../1.html" class="text-gray-400 hover:text-white transition-colors">AI Bot Trading</a></li>
                        <li><a href="../1.html" class="text-gray-400 hover:text-white transition-colors">Portfolio Management</a></li>
                        <li><a href="../1.html" class="text-gray-400 hover:text-white transition-colors">Staking Rewards</a></li>
                        <li><a href="../1.html" class="text-gray-400 hover:text-white transition-colors">Referral Program</a></li>
                        <li><a href="../1.html" class="text-gray-400 hover:text-white transition-colors">24/7 Support</a></li>
                    </ul>
                </div>

                <!-- Contact Info -->
                <div>
                    <h4 class="text-lg font-semibold mb-6 gradient-text">Contact Info</h4>
                    <div class="space-y-4">
                        <div class="flex items-center space-x-3">
                            <i class="fas fa-envelope text-blue-500"></i>
                            <span class="text-gray-400"><EMAIL></span>
                        </div>
                        <div class="flex items-center space-x-3">
                            <i class="fas fa-phone text-cyan-500"></i>
                            <span class="text-gray-400">+****************</span>
                        </div>
                        <div class="flex items-start space-x-3">
                            <i class="fas fa-map-marker-alt text-blue-500 mt-1"></i>
                            <span class="text-gray-400">
                                123 Crypto Street, Digital City<br>
                                Blockchain District, BC 12345<br>
                                United States
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="border-t border-gray-700 mt-12 pt-8 text-center">
                <p class="text-gray-400">
                    2024 CryptoApp. All rights reserved. |
                    <a href="#" class="hover:text-white transition-colors">Privacy Policy</a> |
                    <a href="#" class="hover:text-white transition-colors">Terms of Service</a>
                </p>
            </div>
        </div>
    </footer>

    <!-- Scroll to Top Button with Progress Bar -->
    <div id="scrollToTop" class="scroll-to-top">
        <div class="scroll-progress"></div>
        <i class="fas fa-arrow-up"></i>
    </div>

    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <script>
        // Navbar scroll effect
        const nav = document.getElementById('mainNav');
        window.addEventListener('scroll', () => {
            if (window.scrollY > 50) {
                nav.classList.add('nav-scrolled');
            } else {
                nav.classList.remove('nav-scrolled');
            }
        });

        // Initialize AOS
        AOS.init({
            duration: 1000,
            once: true
        });

        // Mobile menu toggle
        const mobileMenuBtn = document.getElementById('mobileMenuBtn');
        const mobileMenu = document.getElementById('mobileMenu');
        const mobileMenuOverlay = document.getElementById('mobileMenuOverlay');
        const mobileMenuClose = document.getElementById('mobileMenuClose');

        // Open mobile menu
        function openMobileMenu() {
            mobileMenu.classList.add('active');
            mobileMenuOverlay.classList.add('active');
            document.body.style.overflow = 'hidden';
        }

        // Close mobile menu
        function closeMobileMenu() {
            mobileMenu.classList.remove('active');
            mobileMenuOverlay.classList.remove('active');
            document.body.style.overflow = 'auto';
        }

        // Event listeners
        mobileMenuBtn.addEventListener('click', (e) => {
            e.preventDefault();
            openMobileMenu();
        });

        mobileMenuClose.addEventListener('click', (e) => {
            e.preventDefault();
            closeMobileMenu();
        });

        // Close mobile menu when clicking overlay
        mobileMenuOverlay.addEventListener('click', closeMobileMenu);

        // Close mobile menu when clicking on navigation links
        document.querySelectorAll('#mobileMenu a[href^="#"]').forEach(link => {
            link.addEventListener('click', closeMobileMenu);
        });

        // Handle escape key to close mobile menu
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && mobileMenu.classList.contains('active')) {
                closeMobileMenu();
            }
        });

        // Scroll to Top Button with Progress Bar
        const scrollToTopBtn = document.getElementById('scrollToTop');
        const scrollProgress = document.querySelector('.scroll-progress');

        // Show/hide scroll to top button and update progress
        function updateScrollProgress() {
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
            const scrollHeight = document.documentElement.scrollHeight - window.innerHeight;
            const scrollPercent = (scrollTop / scrollHeight) * 100;

            // Update progress bar
            const degrees = (scrollPercent / 100) * 360;
            scrollProgress.style.background = `conic-gradient(#22d3ee ${degrees}deg, transparent ${degrees}deg)`;

            // Show/hide button
            if (scrollTop > 300) {
                scrollToTopBtn.classList.add('visible');
            } else {
                scrollToTopBtn.classList.remove('visible');
            }
        }

        // Smooth scroll to top
        function scrollToTop() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        }

        // Event listeners for scroll to top
        window.addEventListener('scroll', updateScrollProgress);
        scrollToTopBtn.addEventListener('click', scrollToTop);

        // Initial call
        updateScrollProgress();

        // Counter animation
        function animateCounters() {
            const counters = document.querySelectorAll('.counter');

            counters.forEach(counter => {
                const target = parseInt(counter.getAttribute('data-target'));
                const increment = target / 100;
                let current = 0;

                const updateCounter = () => {
                    if (current < target) {
                        current += increment;
                        if (target >= 1000000) {
                            counter.textContent = (current / 1000000).toFixed(1) + 'M';
                        } else if (target >= 1000) {
                            counter.textContent = (current / 1000).toFixed(0) + 'K';
                        } else {
                            counter.textContent = Math.ceil(current);
                        }
                        requestAnimationFrame(updateCounter);
                    } else {
                        if (target >= 1000000) {
                            counter.textContent = (target / 1000000).toFixed(1) + 'M';
                        } else if (target >= 1000) {
                            counter.textContent = (target / 1000).toFixed(0) + 'K';
                        } else {
                            counter.textContent = target;
                        }
                    }
                };

                updateCounter();
            });
        }

        // Trigger counter animation when hero section is in view
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    animateCounters();
                    observer.unobserve(entry.target);
                }
            });
        });

        observer.observe(document.querySelector('#home'));

        // FAQ toggle functionality
        function toggleFAQ(button) {
            const content = button.nextElementSibling;
            const icon = button.querySelector('i');

            // Close all other FAQ items
            document.querySelectorAll('.faq-content').forEach(item => {
                if (item !== content) {
                    item.classList.remove('active');
                    item.previousElementSibling.querySelector('i').style.transform = 'rotate(0deg)';
                }
            });

            // Toggle current FAQ item
            content.classList.toggle('active');
            if (content.classList.contains('active')) {
                icon.style.transform = 'rotate(180deg)';
            } else {
                icon.style.transform = 'rotate(0deg)';
            }
        }

        // Contact form submission
        document.getElementById('contactForm').addEventListener('submit', function(e) {
            e.preventDefault();

            // Get form data
            const formData = new FormData(this);
            const data = Object.fromEntries(formData);

            // Simulate form submission
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;

            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Sending...';
            submitBtn.disabled = true;

            setTimeout(() => {
                alert('Thank you for your message! We will get back to you within 24 hours.');
                this.reset();
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            }, 2000);
        });

        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function(e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Add scroll effect to navigation
        window.addEventListener('scroll', () => {
            const nav = document.querySelector('nav');
            if (window.scrollY > 100) {
                nav.style.background = 'rgba(30, 41, 59, 0.95)';
            } else {
                nav.style.background = 'rgba(30, 41, 59, 0.8)';
            }
        });
    </script>
</body>

</html>