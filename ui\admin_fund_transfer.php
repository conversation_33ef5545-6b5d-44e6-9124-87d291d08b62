<?php
session_start();

if (!isset($_SESSION['user_id']) || !isset($_SESSION['is_admin']) || !$_SESSION['is_admin']) {
    header("Location: login.php");
    exit();
}

include 'dbcon.php';

// Page configuration
$page_title = "Fund Transfer Log";
$additional_css = '
<style>
    /* Enhanced table styling */
    .admin-table {
        background: white;
        border-radius: 0.75rem;
        overflow: hidden;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        width: 100%;
        border-collapse: separate;
        border-spacing: 0;
    }

    .admin-table thead {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    .admin-table thead th {
    text-align: center;
        color: white;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.05em;
        padding: 1rem 1.5rem;
        font-size: 0.75rem;
    }

    .admin-table tbody tr {
        transition: all 0.2s ease;
    }

    .admin-table tbody tr:hover {
        background-color: #f8fafc;
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    .admin-table tbody td {
    text-align: center;
        padding: 1rem 1.5rem;
        border-bottom: 1px solid #e2e8f0;
        background-color: white;
        vertical-align: middle;
    }

    /* Ensure action column has proper background */
    .admin-table tbody td:last-child {
        background-color: white;
        border-right: none;
    }

    /* Status badges */
    .status-badge {
        display: inline-flex;
        align-items: center;
        padding: 0.25rem 0.75rem;
        border-radius: 9999px;
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.05em;
    }

    .status-admin {
        background-color: #fef3c7;
        color: #92400e;
        border: 1px solid #fbbf24;
    }

    .status-user {
        background-color: #dbeafe;
        color: #1e40af;
        border: 1px solid #3b82f6;
    }

    /* Filter styling */
    .filter-container {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 0.75rem;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
    }

    .filter-select {
        background: white;
        border: 2px solid #e2e8f0;
        border-radius: 0.5rem;
        padding: 0.75rem 1rem;
        font-weight: 500;
        transition: all 0.2s ease;
    }

    .filter-select:focus {
        outline: none;
        border-color: #3b82f6;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }

    /* Amount styling */
    .amount-debit {
        color: #dc2626;
        font-weight: 700;
        font-family: "Monaco", "Menlo", monospace;
    }

    .amount-credit {
        color: #059669;
        font-weight: 700;
        font-family: "Monaco", "Menlo", monospace;
    }

    /* Wallet type styling */
    .wallet-deposit {
        background-color: #ecfdf5;
        color: #065f46;
        padding: 0.25rem 0.5rem;
        border-radius: 0.375rem;
        font-size: 0.75rem;
        font-weight: 600;
    }

    .wallet-withdrawal {
        background-color: #fef2f2;
        color: #991b1b;
        padding: 0.25rem 0.5rem;
        border-radius: 0.375rem;
        font-size: 0.75rem;
        font-weight: 600;
    }

    /* Action button styling */
    .action-btn {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 2rem;
        height: 2rem;
        border-radius: 0.375rem;
        transition: all 0.2s ease;
        border: 1px solid transparent;
    }

    .action-btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    .action-btn-view {
        background-color: #dbeafe;
        color: #1e40af;
        border-color: #3b82f6;
    }

    .action-btn-view:hover {
        background-color: #3b82f6;
        color: white;
    }
</style>
';

// Get filter values, default to 'all'
$filter = $_GET['filter'] ?? 'all';
$wallet_filter = $_GET['wallet_filter'] ?? 'all';

// Fetch all fund transfer records with enhanced information
$sql = "SELECT
            wh.id,
            wh.amount,
            wh.created_at,
            sender.name AS sender_name,
            sender.id AS sender_id,
            recipient.name AS recipient_name,
            recipient.id AS recipient_id,
            wh.wallet_type AS wallet_type,
            wh.description,
            CASE
                WHEN wh.description LIKE 'Admin-initiated%' THEN 'Admin'
                ELSE 'User'
            END AS transfer_by
        FROM
            wallet_history wh
        JOIN
            users sender ON wh.user_id = sender.id
        LEFT JOIN
            users recipient ON wh.related_user_id = recipient.id
        WHERE
            wh.type = 'fund_transfer_debit'";

// Add filter conditions
if ($filter == 'admin') {
    $sql .= " AND wh.description LIKE 'Admin-initiated%'";
} elseif ($filter == 'user') {
    $sql .= " AND (wh.description NOT LIKE 'Admin-initiated%' OR wh.description IS NULL)";
}

// Add wallet filter condition
if ($wallet_filter == 'deposit') {
    $sql .= " AND wh.wallet_type = 'deposit'";
} elseif ($wallet_filter == 'withdrawal') {
    $sql .= " AND wh.wallet_type = 'withdrawal'";
}

$sql .= " ORDER BY wh.created_at DESC LIMIT 100";

$result = $conn->query($sql);

// Start output buffering to capture the page content
ob_start();

?>
<!-- Page Header -->
<div class="flex flex-col lg:flex-row justify-between items-start lg:items-center space-y-4 lg:space-y-0 mb-6">
    <div>
        <h1 class="text-3xl font-bold text-gray-900">Fund Transfer Log</h1>
        <p class="text-gray-600 mt-1">Monitor all internal fund transfers between users</p>
    </div>
    <div class="flex items-center space-x-2 bg-blue-50 px-4 py-2 rounded-lg border border-blue-200">
        <i class="fas fa-exchange-alt text-blue-600"></i>
        <span class="text-sm font-medium text-blue-700">Transfer History</span>
    </div>
</div>

<!-- Filter Section -->
<div class="filter-container">
    <div class="flex items-center justify-between">
        <div class="flex items-center space-x-3">
            <div class="w-10 h-10 bg-white bg-opacity-20 rounded-xl flex items-center justify-center">
                <i class="fas fa-filter text-white text-lg"></i>
            </div>
            <div>
                <h3 class="text-lg font-semibold text-white">Filter Transfers</h3>
                <p class="text-blue-100 text-sm">Filter by transfer type and initiator</p>
            </div>
        </div>
        <div class="flex items-center space-x-4">
            <form method="GET" action="admin_fund_transfer.php" class="flex items-center space-x-4">
                <div class="flex items-center space-x-2">
                    <label for="filter" class="text-white font-medium">Type:</label>
                    <select name="filter" id="filter" class="filter-select" onchange="this.form.submit()">
                        <option value="all" <?php if ($filter == 'all') echo 'selected'; ?>>All Types</option>
                        <option value="admin" <?php if ($filter == 'admin') echo 'selected'; ?>>Admin-Initiated</option>
                        <option value="user" <?php if ($filter == 'user') echo 'selected'; ?>>User-Initiated</option>
                    </select>
                </div>

                <div class="flex items-center space-x-2">
                    <label for="wallet_filter" class="text-white font-medium">Wallet:</label>
                    <select name="wallet_filter" id="wallet_filter" class="filter-select" onchange="this.form.submit()">
                        <option value="all" <?php if ($wallet_filter == 'all') echo 'selected'; ?>>All Wallets</option>
                        <option value="deposit" <?php if ($wallet_filter == 'deposit') echo 'selected'; ?>>Deposit Wallet</option>
                        <option value="withdrawal" <?php if ($wallet_filter == 'withdrawal') echo 'selected'; ?>>Withdrawal Wallet</option>
                    </select>
                </div>

                <noscript><button type="submit" class="btn-primary">Filter</button></noscript>
            </form>
        </div>
    </div>
</div>

<!-- Transfer Statistics -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
    <?php
    // Get statistics with current filters applied
    $stats_sql = "SELECT
        COUNT(*) as total_transfers,
        SUM(CASE WHEN description LIKE 'Admin-initiated%' THEN 1 ELSE 0 END) as admin_transfers,
        SUM(CASE WHEN description NOT LIKE 'Admin-initiated%' OR description IS NULL THEN 1 ELSE 0 END) as user_transfers,
        SUM(CASE WHEN wallet_type = 'deposit' THEN 1 ELSE 0 END) as deposit_transfers,
        SUM(CASE WHEN wallet_type = 'withdrawal' THEN 1 ELSE 0 END) as withdrawal_transfers,
        SUM(ABS(amount)) as total_amount
        FROM wallet_history
        WHERE type = 'fund_transfer_debit'";

    // Apply same filters to statistics
    if ($filter == 'admin') {
        $stats_sql .= " AND description LIKE 'Admin-initiated%'";
    } elseif ($filter == 'user') {
        $stats_sql .= " AND (description NOT LIKE 'Admin-initiated%' OR description IS NULL)";
    }

    if ($wallet_filter == 'deposit') {
        $stats_sql .= " AND wallet_type = 'deposit'";
    } elseif ($wallet_filter == 'withdrawal') {
        $stats_sql .= " AND wallet_type = 'withdrawal'";
    }
    $stats_result = $conn->query($stats_sql);
    $stats = $stats_result->fetch_assoc();
    ?>

    <div class="admin-card p-6">
        <div class="flex items-center">
            <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center shadow-lg">
                <i class="fas fa-exchange-alt text-white text-xl"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">Total Transfers</p>
                <p class="text-2xl font-bold text-gray-900"><?php echo number_format($stats['total_transfers']); ?></p>
            </div>
        </div>
    </div>

    <div class="admin-card p-6">
        <div class="flex items-center">
            <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl flex items-center justify-center shadow-lg">
                <i class="fas fa-wallet text-white text-xl"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">Deposit Wallet</p>
                <p class="text-2xl font-bold text-gray-900"><?php echo number_format($stats['deposit_transfers']); ?></p>
            </div>
        </div>
    </div>

    <div class="admin-card p-6">
        <div class="flex items-center">
            <div class="w-12 h-12 bg-gradient-to-br from-red-500 to-pink-600 rounded-xl flex items-center justify-center shadow-lg">
                <i class="fas fa-credit-card text-white text-xl"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">Withdrawal Wallet</p>
                <p class="text-2xl font-bold text-gray-900"><?php echo number_format($stats['withdrawal_transfers']); ?></p>
            </div>
        </div>
    </div>

    <div class="admin-card p-6">
        <div class="flex items-center">
            <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-xl flex items-center justify-center shadow-lg">
                <i class="fas fa-dollar-sign text-white text-xl"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">Total Amount</p>
                <p class="text-2xl font-bold text-gray-900">$<?php echo number_format($stats['total_amount'], 2); ?></p>
            </div>
        </div>
    </div>
</div>

<!-- Transfer Table -->
<div class="admin-card p-0 overflow-hidden">
    <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center space-x-3">
            <div class="w-8 h-8 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-lg flex items-center justify-center">
                <i class="fas fa-list text-white text-sm"></i>
            </div>
            <div>
                <h3 class="text-lg font-semibold text-gray-900">Transfer Records</h3>
                <p class="text-sm text-gray-600">Recent fund transfer activities</p>
            </div>
        </div>
    </div>

    <div class="overflow-x-auto">
        <table class="admin-table w-full">
            <thead>
                <tr>
                    <th class="py-3 px-6 text-xs font-medium text-gray-500 uppercase tracking-wider" style="text-align: left !important;">Date</th>
                    <th class="py-3 px-6 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Sender</th>
                    <th class="py-3 px-6 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Recipient</th>
                    <th class="py-3 px-6 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                    <th class="py-3 px-6 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Wallet</th>
                    <th class="py-3 px-6 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Initiated By</th>
                    <th class="py-3 px-6 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
            </thead>
            <tbody>
                <?php if ($result && $result->num_rows > 0): ?>
                    <?php while ($row = $result->fetch_assoc()): ?>
                        <tr>
                            <td style="text-align: left !important;">
                                <div class="flex flex-col">
                                    <span class="text-sm font-medium text-gray-900">
                                        <?php echo date('M j, Y', strtotime($row['created_at'])); ?>
                                    </span>
                                    <span class="text-xs text-gray-500">
                                        <?php echo date('g:i A', strtotime($row['created_at'])); ?>
                                    </span>
                                </div>
                            </td>
                            <td>
                                <div class="flex items-center space-x-3">
                                    <div class="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                                        <i class="fas fa-user-minus text-red-600 text-xs"></i>
                                    </div>
                                    <div>
                                        <div class="text-sm font-medium text-gray-900">
                                            <?php echo htmlspecialchars($row['sender_name']); ?>
                                        </div>
                                        <div class="text-xs text-gray-500">
                                            ID: <?php echo $row['sender_id']; ?>
                                        </div>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div class="flex items-center space-x-3">
                                    <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                                        <i class="fas fa-user-plus text-green-600 text-xs"></i>
                                    </div>
                                    <div>
                                        <div class="text-sm font-medium text-gray-900">
                                            <?php echo htmlspecialchars($row['recipient_name'] ?? 'N/A'); ?>
                                        </div>
                                        <div class="text-xs text-gray-500">
                                            ID: <?php echo $row['recipient_id'] ?? 'N/A'; ?>
                                        </div>
                                    </div>
                                </div>
                            </td>
                            <td class="text-right">
                                <span class="amount-debit">
                                    -$<?php echo number_format(abs($row['amount']), 2); ?>
                                </span>
                            </td>
                            <td class="text-center">
                                <span class="wallet-<?php echo $row['wallet_type']; ?>">
                                    <?php echo ucfirst($row['wallet_type']); ?>
                                </span>
                            </td>
                            <td class="text-center">
                                <span class="status-badge status-<?php echo strtolower($row['transfer_by']); ?>">
                                    <i class="fas fa-<?php echo $row['transfer_by'] == 'Admin' ? 'user-shield' : 'user'; ?> mr-1"></i>
                                    <?php echo $row['transfer_by']; ?>
                                </span>
                            </td>
                            <td class="text-center">
                                <div class="flex items-center justify-center space-x-2">
                                    <button class="action-btn action-btn-view"
                                        onclick="viewTransferDetails(<?php echo $row['id']; ?>)"
                                        title="View Transfer Details">
                                        <i class="fas fa-eye text-sm"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    <?php endwhile; ?>
                <?php else: ?>
                    <tr>
                        <td colspan="7" class="text-center py-12">
                            <div class="flex flex-col items-center">
                                <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                                    <i class="fas fa-exchange-alt text-gray-400 text-2xl"></i>
                                </div>
                                <h3 class="text-lg font-medium text-gray-900 mb-2">No Transfers Found</h3>
                                <p class="text-sm text-gray-500 mb-4">No fund transfers found for the selected filter.</p>
                                <a href="admin_manual_transfer.php" class="btn-primary text-sm">
                                    <i class="fas fa-plus mr-2"></i>
                                    Create Transfer
                                </a>
                            </div>
                        </td>
                    </tr>
                <?php endif; ?>
            </tbody>
        </table>
    </div>
</div>
<?php
// Get the page content
$page_content = ob_get_clean();

// Additional JavaScript for enhanced functionality
$additional_js = '
<script>
    function viewTransferDetails(transferId) {
        // You can implement a modal or redirect to details page
        alert("Transfer ID: " + transferId + " - Details functionality can be implemented here");
    }

    // Add some interactive features
    document.addEventListener("DOMContentLoaded", function() {
        // Add fade-in animation to cards
        const cards = document.querySelectorAll(".admin-card");
        cards.forEach((card, index) => {
            card.style.opacity = "0";
            card.style.transform = "translateY(20px)";
            setTimeout(() => {
                card.style.transition = "all 0.5s ease";
                card.style.opacity = "1";
                card.style.transform = "translateY(0)";
            }, index * 100);
        });
    });
</script>
';

// Include the layout
include 'admin/includes/layout.php';
?>