<?php
include 'dbcon.php';

$token = $_GET['token'] ?? '';
$valid_token = false;
$email = '';

if (!empty($token)) {
    // Verify token
    $current_time = date('Y-m-d H:i:s');
    $stmt = $conn->prepare('SELECT email, expires_at FROM password_resets WHERE token = ?');
    $stmt->bind_param('s', $token);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows > 0) {
        $row = $result->fetch_assoc();
        $email = $row['email'];

        // Check if token is still valid
        if (strtotime($row['expires_at']) > strtotime($current_time)) {
            $valid_token = true;
        }
    }
    $stmt->close();
}

$conn->close();
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reset Password - CryptoApp</title>

    <!-- Tailwind CSS for styling -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Google Fonts: Inter -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">

    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css"
        crossorigin="anonymous" referrerpolicy="no-referrer" />

    <style>
        /* Define the custom light theme and other base styles */
        body {
            font-family: 'Inter', sans-serif;
        }

        /* Light Theme - Professional Admin Style */
        .theme-light-admin {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            color: #1e293b;
            min-height: 100vh;
        }

        .theme-light-admin .card {
            /* Enhanced Glassmorphism Effect */
            background: rgba(255, 255, 255, 0.25);
            backdrop-filter: blur(25px);
            -webkit-backdrop-filter: blur(25px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 1.5rem;
            padding: 2.5rem;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow:
                0 25px 50px rgba(0, 0, 0, 0.1),
                0 0 0 1px rgba(255, 255, 255, 0.2),
                inset 0 1px 0 rgba(255, 255, 255, 0.4),
                inset 0 -1px 0 rgba(0, 0, 0, 0.05);
            position: relative;
            overflow: hidden;
        }

        .theme-light-admin .card:hover {
            transform: translateY(-5px) scale(1.02);
            background: rgba(255, 255, 255, 0.35);
            backdrop-filter: blur(30px);
            -webkit-backdrop-filter: blur(30px);
            box-shadow:
                0 35px 70px rgba(0, 0, 0, 0.15),
                0 0 0 1px rgba(255, 255, 255, 0.4),
                inset 0 1px 0 rgba(255, 255, 255, 0.6),
                inset 0 -1px 0 rgba(0, 0, 0, 0.1);
            border-color: rgba(255, 255, 255, 0.5);
        }

        .theme-light-admin .accent {
            color: #3b82f6;
        }

        .theme-light-admin .text-muted {
            color: #64748b;
        }

        /* Enhanced Logo styling for light theme */
        .logo-glow {
            position: relative;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(6, 182, 212, 0.1));
            border: 2px solid rgba(59, 130, 246, 0.2);
        }

        .logo-glow:hover {
            transform: scale(1.05);
            box-shadow: 0 10px 30px rgba(59, 130, 246, 0.3);
            border-color: rgba(59, 130, 246, 0.4);
        }

        .logo-bg {
            fill: #3b82f6;
            transition: fill 0.3s ease;
        }

        .logo-symbol {
            fill: #ffffff;
            transition: fill 0.3s ease;
        }

        /* Form input styling for light theme */
        .form-input {
            background: rgba(255, 255, 255, 0.9);
            border: 2px solid #e2e8f0;
            border-radius: 0.75rem;
            padding: 0.875rem 1rem;
            color: #1e293b;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .form-input:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
            background: rgba(255, 255, 255, 1);
        }

        .form-input::placeholder {
            color: #94a3b8;
        }

        /* Button styling for light theme */
        .btn-primary {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            color: white;
            border: none;
            border-radius: 0.75rem;
            padding: 0.875rem 2rem;
            font-weight: 600;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: 0 10px 20px rgba(59, 130, 246, 0.3);
        }

        /* Floating animation for the card */
        .floating-card {
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {

            0%,
            100% {
                transform: translateY(0px);
            }

            50% {
                transform: translateY(-10px);
            }
        }

        /* Background with Image */
        .bg-pattern {
            background-image:
                /* Light overlay for better readability */
                linear-gradient(135deg, rgba(248, 250, 252, 0.85) 0%, rgba(226, 232, 240, 0.85) 100%),
                /* Background image */
                url('../final/assets/image.png');
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            background-attachment: fixed;
        }
    </style>
</head>

<body class="theme-light-admin bg-pattern">
    <div class="min-h-screen flex items-center justify-center p-4">
        <div class="w-full max-w-md">
            <!-- Reset Password Card -->
            <div class="card floating-card">
                <!-- Logo Section -->
                <div class="text-center mb-8">
                    <div class="flex justify-center mb-4">
                        <div class="w-16 h-16 rounded-xl flex items-center justify-center shadow-lg logo-glow relative overflow-hidden">
                            <svg class="w-12 h-12 logo-svg" viewBox="0 0 4091.27 4091.73" xmlns="http://www.w3.org/2000/svg">
                                <g>
                                    <circle cx="2045.635" cy="2045.865" r="2045.635" class="logo-bg" />
                                    <path class="logo-symbol" fill-rule="nonzero" d="M2947.77 1754.38c40.72,-272.26 -166.56,-418.61 -450,-516.24l91.95 -368.8 -224.5 -55.94 -89.51 358.99c-59.02,-14.72 -119.63,-28.59 -179.87,-42.34l90.16 -361.46 -224.36 -55.94 -91.92 368.68c-48.84,-11.12 -96.81,-22.11 -143.35,-33.69l0.26 -1.16 -309.59 -77.31 -59.72 239.78c0,0 166.56,38.18 163.05,40.53 90.91,22.69 107.35,82.87 104.62,130.57l-104.74 420.15c6.26,1.59 14.38,3.89 23.34,7.49 -7.49,-1.86 -15.46,-3.89 -23.73,-5.87l-146.81 588.57c-11.11,27.62 -39.31,69.07 -102.87,53.33 2.25,3.26 -163.17,-40.72 -163.17,-40.72l-111.46 256.98 292.15,72.83c54.35,13.63 107.61,27.89 159.58,41.27l-92.83 373.03 224.24,55.94 91.95 -368.8c61.35,16.61 120.71,31.97 178.91,46.43l-91.69 367.33 224.51,55.94 92.83 -372.68c382.82,72.45 670.67,43.24 792.04 -303.14 98.00 -279.47 -4.86 -440.75 -206.26 -546.25 146.69,-33.83 257.18,-130.31 286.64 -329.61l-0.07 -0.05zm-512.93 719.26c-69.38,278.78 -538.76,128.08 -690.94,90.29l123.28 -494.2c152.17,37.99 640.17,113.17 567.67,403.91zm69.43 -723.3c-63.29,253.58 -453.96,124.75 -580.69,93.16l111.77 -448.21c126.73,31.59 534.85,90.55 468.94,355.05l-0.02 0z" />
                                </g>
                            </svg>
                        </div>
                    </div>
                    <h1 class="text-2xl font-bold text-slate-800 mb-2">
                        <?php echo $valid_token ? 'Set New Password' : 'Invalid Reset Link'; ?>
                    </h1>
                    <p class="text-muted">
                        <?php echo $valid_token ? 'Enter your new password below' : 'This reset link is invalid or has expired'; ?>
                    </p>
                </div>

                <?php if ($valid_token): ?>
                    <!-- Message Display Area -->
                    <div id="messageArea" class="mb-6 hidden">
                        <div id="messageContent" class="p-4 rounded-lg">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <i id="messageIcon" class="fas"></i>
                                </div>
                                <div class="ml-3">
                                    <p id="messageText" class="text-sm"></p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Reset Password Form -->
                    <form id="resetPasswordForm" class="space-y-6">
                        <input type="hidden" id="token" value="<?php echo htmlspecialchars($token); ?>">
                        <input type="hidden" id="email" value="<?php echo htmlspecialchars($email); ?>">

                        <!-- New Password Field -->
                        <div>
                            <label for="password" class="block text-sm font-semibold text-slate-700 mb-2">
                                <i class="fas fa-lock mr-2 accent"></i>New Password
                            </label>
                            <div class="relative">
                                <input type="password" id="password" name="password" required
                                    class="form-input w-full pr-12" placeholder="Enter your new password">
                                <button type="button" id="togglePassword"
                                    class="absolute right-3 top-1/2 transform -translate-y-1/2 text-slate-500 hover:text-slate-700 transition-colors">
                                    <i class="fas fa-eye" id="eyeIcon"></i>
                                </button>
                            </div>
                        </div>

                        <!-- Confirm Password Field -->
                        <div>
                            <label for="confirm_password" class="block text-sm font-semibold text-slate-700 mb-2">
                                <i class="fas fa-lock mr-2 accent"></i>Confirm New Password
                            </label>
                            <div class="relative">
                                <input type="password" id="confirm_password" name="confirm_password" required
                                    class="form-input w-full pr-12" placeholder="Confirm your new password">
                                <button type="button" id="toggleConfirmPassword"
                                    class="absolute right-3 top-1/2 transform -translate-y-1/2 text-slate-500 hover:text-slate-700 transition-colors">
                                    <i class="fas fa-eye" id="confirmEyeIcon"></i>
                                </button>
                            </div>
                        </div>

                        <!-- Reset Button -->
                        <button type="submit" class="btn-primary w-full">
                            <i class="fas fa-key mr-2"></i>
                            Update Password
                        </button>
                    </form>
                <?php else: ?>
                    <!-- Invalid Token Message -->
                    <div class="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <i class="fas fa-exclamation-triangle text-red-400"></i>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm text-red-800">
                                    The password reset link is invalid or has expired. Please request a new password reset.
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- Request New Reset Link -->
                    <a href="forgot_password.php" class="btn-primary w-full inline-block text-center">
                        <i class="fas fa-paper-plane mr-2"></i>
                        Request New Reset Link
                    </a>
                <?php endif; ?>

                <!-- Back to Login Link -->
                <div class="mt-6 text-center">
                    <p class="text-sm text-muted">
                        Remember your password?
                        <a href="login.php" class="accent hover:underline font-medium">Back to Login</a>
                    </p>
                </div>

                <!-- Footer -->
                <div class="mt-8 pt-6 border-t border-slate-200 text-center">
                    <p class="text-sm text-muted">
                        <i class="fas fa-shield-alt mr-1 accent"></i>
                        Secure Password Reset
                    </p>
                </div>
            </div>

            <!-- Copyright -->
            <div class="text-center mt-6">
                <p class="text-sm text-muted">
                    © 2024 CryptoApp. All rights reserved.
                </p>
            </div>
        </div>
    </div>

    <?php if ($valid_token): ?>
        <script>
            // Function to show messages
            function showMessage(message, type) {
                const messageArea = document.getElementById('messageArea');
                const messageContent = document.getElementById('messageContent');
                const messageIcon = document.getElementById('messageIcon');
                const messageText = document.getElementById('messageText');

                // Reset classes
                messageContent.className = 'p-4 rounded-lg';
                messageIcon.className = 'fas';

                if (type === 'success') {
                    messageContent.classList.add('bg-green-50', 'border', 'border-green-200');
                    messageIcon.classList.add('fa-check-circle', 'text-green-400');
                    messageText.className = 'text-sm text-green-800';
                } else {
                    messageContent.classList.add('bg-red-50', 'border', 'border-red-200');
                    messageIcon.classList.add('fa-exclamation-circle', 'text-red-400');
                    messageText.className = 'text-sm text-red-800';
                }

                messageText.textContent = message;
                messageArea.classList.remove('hidden');

                // Auto-hide after 5 seconds for error messages
                if (type === 'error') {
                    setTimeout(() => {
                        messageArea.classList.add('hidden');
                    }, 5000);
                }
            }

            // Toggle password visibility
            document.getElementById('togglePassword').addEventListener('click', function() {
                const passwordField = document.getElementById('password');
                const eyeIcon = document.getElementById('eyeIcon');

                if (passwordField.type === 'password') {
                    passwordField.type = 'text';
                    eyeIcon.classList.remove('fa-eye');
                    eyeIcon.classList.add('fa-eye-slash');
                } else {
                    passwordField.type = 'password';
                    eyeIcon.classList.remove('fa-eye-slash');
                    eyeIcon.classList.add('fa-eye');
                }
            });

            // Toggle confirm password visibility
            document.getElementById('toggleConfirmPassword').addEventListener('click', function() {
                const confirmPasswordField = document.getElementById('confirm_password');
                const confirmEyeIcon = document.getElementById('confirmEyeIcon');

                if (confirmPasswordField.type === 'password') {
                    confirmPasswordField.type = 'text';
                    confirmEyeIcon.classList.remove('fa-eye');
                    confirmEyeIcon.classList.add('fa-eye-slash');
                } else {
                    confirmPasswordField.type = 'password';
                    confirmEyeIcon.classList.remove('fa-eye-slash');
                    confirmEyeIcon.classList.add('fa-eye');
                }
            });

            // Handle form submission with AJAX
            document.getElementById('resetPasswordForm').addEventListener('submit', function(e) {
                e.preventDefault();

                const password = document.getElementById('password').value;
                const confirmPassword = document.getElementById('confirm_password').value;
                const token = document.getElementById('token').value;
                const email = document.getElementById('email').value;

                // Basic validation
                if (!password || !confirmPassword) {
                    showMessage('Please fill in all fields.', 'error');
                    return;
                }

                if (password !== confirmPassword) {
                    showMessage('Passwords do not match.', 'error');
                    return;
                }

                if (password.length < 6) {
                    showMessage('Password must be at least 6 characters long.', 'error');
                    return;
                }

                // Show loading state
                const submitButton = document.querySelector('button[type="submit"]');
                const originalText = submitButton.innerHTML;
                submitButton.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Updating Password...';
                submitButton.disabled = true;

                // Prepare form data
                const formData = new FormData();
                formData.append('password', password);
                formData.append('token', token);
                formData.append('email', email);

                // Send AJAX request
                fetch('reset_password_process.php', {
                        method: 'POST',
                        body: formData
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            showMessage(data.message, 'success');
                            // Redirect to login after a short delay
                            setTimeout(() => {
                                window.location.href = 'login.php?password_reset=success';
                            }, 2000);
                        } else {
                            showMessage(data.message, 'error');
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        showMessage('An error occurred. Please try again.', 'error');
                    })
                    .finally(() => {
                        // Reset button state
                        submitButton.innerHTML = originalText;
                        submitButton.disabled = false;
                    });
            });
        </script>
    <?php endif; ?>
</body>

</html>