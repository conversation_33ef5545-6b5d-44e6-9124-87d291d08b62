<?php
// Test forgot password functionality
include 'dbcon.php';
include 'email_helper.php';

// Test email address (you can change this to your email)
$test_email = '<EMAIL>';

echo "<h2>Testing Forgot Password Functionality</h2>";

// Check if user exists
$stmt = $conn->prepare('SELECT id, name FROM users WHERE email = ? AND is_active = 1');
$stmt->bind_param('s', $test_email);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows > 0) {
    $user = $result->fetch_assoc();
    echo "<p>✅ User found: " . htmlspecialchars($user['name']) . " (" . htmlspecialchars($test_email) . ")</p>";
    
    // Generate a secure reset token
    $reset_token = bin2hex(random_bytes(32));
    $expires_at = date('Y-m-d H:i:s', strtotime('+1 hour'));
    
    echo "<p>🔑 Generated reset token: " . substr($reset_token, 0, 20) . "...</p>";
    echo "<p>⏰ Token expires at: " . $expires_at . "</p>";
    
    // Store reset token in database
    $stmt = $conn->prepare('INSERT INTO password_resets (email, token, expires_at) VALUES (?, ?, ?) ON DUPLICATE KEY UPDATE token = VALUES(token), expires_at = VALUES(expires_at), created_at = NOW()');
    $stmt->bind_param('sss', $test_email, $reset_token, $expires_at);
    
    if ($stmt->execute()) {
        echo "<p>✅ Reset token stored in database</p>";
        
        // Create reset URL
        $reset_url = "http://" . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . "/reset_password.php?token=" . $reset_token;
        echo "<p>🔗 Reset URL: <a href='" . htmlspecialchars($reset_url) . "' target='_blank'>" . htmlspecialchars($reset_url) . "</a></p>";
        
        // Create email content
        $subject = 'Password Reset Link - CryptoApp';
        $html_message = createPasswordResetLinkEmail($user['name'], $reset_url);
        
        echo "<h3>📧 Email Content Preview:</h3>";
        echo "<div style='border: 1px solid #ccc; padding: 10px; max-height: 300px; overflow-y: auto;'>";
        echo $html_message;
        echo "</div>";
        
        // Test plain text version
        $plain_text = createPlainTextVersion($html_message, 'password_reset');
        echo "<h3>📝 Plain Text Version:</h3>";
        echo "<pre style='border: 1px solid #ccc; padding: 10px; background: #f5f5f5; white-space: pre-wrap;'>";
        echo htmlspecialchars($plain_text);
        echo "</pre>";
        
        echo "<p><strong>✅ All components working correctly!</strong></p>";
        echo "<p><strong>The system is now sending secure reset links instead of temporary passwords.</strong></p>";
        
    } else {
        echo "<p>❌ Failed to store reset token in database</p>";
    }
    
} else {
    echo "<p>❌ No user found with email: " . htmlspecialchars($test_email) . "</p>";
    echo "<p>💡 Please create a test user first or change the \$test_email variable in this file.</p>";
}

$conn->close();
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h2, h3 { color: #333; }
p { margin: 10px 0; }
pre { font-size: 12px; }
a { color: #007cba; }
</style>
