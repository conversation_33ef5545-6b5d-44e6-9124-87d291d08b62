<?php
session_start();

// Set admin session for testing
$_SESSION['admin_logged_in'] = true;
$_SESSION['admin_id'] = 1;

// Include database connection
include 'dbcon.php';

echo "<h2>🔍 Dashboard Database Query Debug</h2>";

// Test database connection
echo "<h3>📡 Database Connection Test:</h3>";
if ($conn->connect_error) {
    echo "<p style='color: red;'>❌ Connection failed: " . $conn->connect_error . "</p>";
} else {
    echo "<p style='color: green;'>✅ Database connected successfully</p>";
}

// Check if tables exist
echo "<h3>📋 Table Structure Check:</h3>";
$tables_to_check = ['users', 'deposit_requests', 'withdrawal_requests', 'packages', 'staking_records', 'package_history'];

foreach ($tables_to_check as $table) {
    $check_query = "SHOW TABLES LIKE '$table'";
    $result = $conn->query($check_query);
    if ($result && $result->num_rows > 0) {
        echo "<p style='color: green;'>✅ Table '$table' exists</p>";
        
        // Show table structure
        $desc_query = "DESCRIBE $table";
        $desc_result = $conn->query($desc_query);
        if ($desc_result) {
            echo "<details style='margin-left: 20px;'>";
            echo "<summary>View columns</summary>";
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
            echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th></tr>";
            while ($row = $desc_result->fetch_assoc()) {
                echo "<tr>";
                echo "<td>" . $row['Field'] . "</td>";
                echo "<td>" . $row['Type'] . "</td>";
                echo "<td>" . $row['Null'] . "</td>";
                echo "<td>" . $row['Key'] . "</td>";
                echo "</tr>";
            }
            echo "</table>";
            echo "</details>";
        }
    } else {
        echo "<p style='color: red;'>❌ Table '$table' does not exist</p>";
    }
}

// Test individual queries
echo "<h3>🧪 Query Tests:</h3>";

// Test users query
echo "<h4>👥 Users Query Test:</h4>";
$users_query = "SELECT user_id, full_name, created_at FROM users WHERE is_admin = 0 ORDER BY created_at DESC LIMIT 5";
echo "<p><strong>Query:</strong> <code>$users_query</code></p>";
$users_result = $conn->query($users_query);
if ($users_result) {
    echo "<p style='color: green;'>✅ Query executed successfully</p>";
    echo "<p><strong>Rows found:</strong> " . $users_result->num_rows . "</p>";
    if ($users_result->num_rows > 0) {
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>User ID</th><th>Full Name</th><th>Created At</th></tr>";
        while ($row = $users_result->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . $row['user_id'] . "</td>";
            echo "<td>" . htmlspecialchars($row['full_name']) . "</td>";
            echo "<td>" . $row['created_at'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
} else {
    echo "<p style='color: red;'>❌ Query failed: " . $conn->error . "</p>";
}

// Test deposits query
echo "<h4>💰 Deposits Query Test:</h4>";
$deposits_query = "SELECT dr.*, u.full_name FROM deposit_requests dr JOIN users u ON dr.user_id = u.user_id ORDER BY dr.created_at DESC LIMIT 5";
echo "<p><strong>Query:</strong> <code>$deposits_query</code></p>";
$deposits_result = $conn->query($deposits_query);
if ($deposits_result) {
    echo "<p style='color: green;'>✅ Query executed successfully</p>";
    echo "<p><strong>Rows found:</strong> " . $deposits_result->num_rows . "</p>";
    if ($deposits_result->num_rows > 0) {
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>ID</th><th>User</th><th>Amount</th><th>Status</th><th>Created At</th></tr>";
        while ($row = $deposits_result->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . $row['id'] . "</td>";
            echo "<td>" . htmlspecialchars($row['full_name']) . "</td>";
            echo "<td>$" . number_format($row['amount'], 2) . "</td>";
            echo "<td>" . $row['status'] . "</td>";
            echo "<td>" . $row['created_at'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
} else {
    echo "<p style='color: red;'>❌ Query failed: " . $conn->error . "</p>";
}

// Test withdrawals query
echo "<h4>💸 Withdrawals Query Test:</h4>";
$withdrawals_query = "SELECT wr.*, u.full_name FROM withdrawal_requests wr JOIN users u ON wr.user_id = u.user_id ORDER BY wr.created_at DESC LIMIT 5";
echo "<p><strong>Query:</strong> <code>$withdrawals_query</code></p>";
$withdrawals_result = $conn->query($withdrawals_query);
if ($withdrawals_result) {
    echo "<p style='color: green;'>✅ Query executed successfully</p>";
    echo "<p><strong>Rows found:</strong> " . $withdrawals_result->num_rows . "</p>";
    if ($withdrawals_result->num_rows > 0) {
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>ID</th><th>User</th><th>Amount</th><th>Status</th><th>Created At</th></tr>";
        while ($row = $withdrawals_result->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . $row['id'] . "</td>";
            echo "<td>" . htmlspecialchars($row['full_name']) . "</td>";
            echo "<td>$" . number_format($row['amount'], 2) . "</td>";
            echo "<td>" . $row['status'] . "</td>";
            echo "<td>" . $row['created_at'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
} else {
    echo "<p style='color: red;'>❌ Query failed: " . $conn->error . "</p>";
}

// Test basic statistics
echo "<h3>📊 Basic Statistics:</h3>";

$stats_queries = [
    'Total Users' => "SELECT COUNT(*) as count FROM users WHERE is_admin = 0",
    'Total Deposits' => "SELECT COUNT(*) as count FROM deposit_requests",
    'Total Withdrawals' => "SELECT COUNT(*) as count FROM withdrawal_requests",
    'Total Packages' => "SELECT COUNT(*) as count FROM packages",
    'Total Staking' => "SELECT COUNT(*) as count FROM staking_records"
];

foreach ($stats_queries as $label => $query) {
    $result = $conn->query($query);
    if ($result) {
        $count = $result->fetch_assoc()['count'];
        echo "<p><strong>$label:</strong> $count</p>";
    } else {
        echo "<p style='color: red;'><strong>$label:</strong> Query failed - " . $conn->error . "</p>";
    }
}

echo "<div style='background: #e0f2fe; border: 2px solid #0ea5e9; border-radius: 12px; padding: 20px; margin: 20px 0;'>";
echo "<h3 style='color: #0c4a6e; margin-top: 0;'>🎯 Next Steps:</h3>";
echo "<p style='color: #0369a1;'>Based on the results above:</p>";
echo "<ul style='color: #0369a1;'>";
echo "<li>If tables are missing, run the database schema setup</li>";
echo "<li>If queries fail, check column names and table structure</li>";
echo "<li>If no data is found, add some test data to the tables</li>";
echo "<li>Once issues are resolved, the dashboard will display live data</li>";
echo "</ul>";
echo "<p style='color: #0369a1;'><a href='admin_dashboard.php' style='background: #3b82f6; color: white; padding: 10px 20px; text-decoration: none; border-radius: 8px;'>🚀 Test Dashboard Again</a></p>";
echo "</div>";

$conn->close();
?>

<style>
body { 
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; 
    margin: 20px; 
    background: #f8fafc;
    line-height: 1.6;
}
h2, h3, h4 { color: #1f2937; }
code { 
    background: #f1f5f9; 
    padding: 2px 6px; 
    border-radius: 4px; 
    font-family: monospace;
    font-size: 0.9em;
}
table { 
    font-size: 0.9em; 
    max-width: 100%; 
    overflow-x: auto;
}
th, td { 
    padding: 8px 12px; 
    text-align: left; 
    border: 1px solid #e5e7eb;
}
th { 
    background: #f9fafb; 
    font-weight: 600;
}
details { 
    margin: 10px 0; 
}
summary { 
    cursor: pointer; 
    font-weight: 500; 
    color: #3b82f6;
}
</style>
