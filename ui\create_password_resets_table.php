<?php
include 'dbcon.php';

// Create password_resets table for reset tokens
$sql = "CREATE TABLE IF NOT EXISTS password_resets (
    id INT AUTO_INCREMENT PRIMARY KEY,
    email VARCHAR(255) NOT NULL,
    token VARCHAR(255) NOT NULL UNIQUE,
    expires_at DATETIME NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_email (email),
    INDEX idx_token (token),
    INDEX idx_expires_at (expires_at)
)";

if ($conn->query($sql) === TRUE) {
    echo "Password resets table created successfully or already exists.<br>";
} else {
    echo "Error creating password_resets table: " . $conn->error . "<br>";
}

// Create user_sessions table for remember me functionality
$sql = "CREATE TABLE IF NOT EXISTS user_sessions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    session_token VARCHAR(255) NOT NULL UNIQUE,
    expires_at DATETIME NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_session_token (session_token),
    INDEX idx_expires_at (expires_at)
)";

if ($conn->query($sql) === TRUE) {
    echo "User sessions table created successfully or already exists.<br>";
} else {
    echo "Error creating user_sessions table: " . $conn->error . "<br>";
}

// Create email_logs table for tracking sent emails
$sql = "CREATE TABLE IF NOT EXISTS email_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    recipient_email VARCHAR(255) NOT NULL,
    subject VARCHAR(500) NOT NULL,
    email_type ENUM('password_reset', 'welcome', 'notification') DEFAULT 'notification',
    status ENUM('sent', 'failed') NOT NULL,
    sent_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_recipient (recipient_email),
    INDEX idx_type (email_type),
    INDEX idx_status (status),
    INDEX idx_sent_at (sent_at)
)";

if ($conn->query($sql) === TRUE) {
    echo "Email logs table created successfully or already exists.<br>";
} else {
    echo "Error creating email_logs table: " . $conn->error . "<br>";
}

$conn->close();
echo "<br>Database setup completed!";
