<?php
session_start();

// Admin check
if (!isset($_SESSION['user_id']) || !isset($_SESSION['is_admin']) || !$_SESSION['is_admin']) {
    header("Location: login.html");
    exit();
}

// Set page title
$page_title = 'Manual Fund Transfer';

// Additional CSS for Select2 styling
$additional_css = '
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<style>
    /* Select2 Container Styling */
    .select2-container {
        width: 100% !important;
    }

    .select2-container .select2-selection--single {
        height: 48px !important;
        border: 2px solid #e5e7eb !important;
        border-radius: 0.5rem !important;
        background: rgba(255, 255, 255, 0.9) !important;
        transition: all 0.3s ease !important;
        padding: 0 !important;
    }

    .select2-container--default .select2-selection--single:focus,
    .select2-container--default.select2-container--focus .select2-selection--single {
        border-color: #3b82f6 !important;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
        background: rgba(255, 255, 255, 1) !important;
        outline: none !important;
    }

    .select2-container .select2-selection--single .select2-selection__rendered {
        line-height: 44px !important;
        padding-left: 1rem !important;
        padding-right: 2rem !important;
        color: #1f2937 !important;
        font-weight: 500 !important;
        font-size: 0.875rem !important;
    }

    .select2-container .select2-selection--single .select2-selection__placeholder {
        color: #9ca3af !important;
        font-weight: 400 !important;
    }

    .select2-container .select2-selection--single .select2-selection__arrow {
        height: 44px !important;
        right: 1rem !important;
        width: 20px !important;
    }

    .select2-container .select2-selection--single .select2-selection__arrow b {
        border-color: #6b7280 transparent transparent transparent !important;
        border-width: 6px 6px 0 6px !important;
    }

    /* Dropdown Styling */
    .select2-dropdown {
        border: 2px solid #e5e7eb !important;
        border-radius: 0.5rem !important;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1) !important;
        z-index: 9999 !important;
        position: absolute !important;
    }

    /* Fix dropdown positioning */
    .select2-container--open .select2-dropdown--below {
        border-top: none !important;
        border-top-left-radius: 0 !important;
        border-top-right-radius: 0 !important;
    }

    .select2-container--open .select2-dropdown--above {
        border-bottom: none !important;
        border-bottom-left-radius: 0 !important;
        border-bottom-right-radius: 0 !important;
    }

    .select2-search--dropdown {
        padding: 0.5rem !important;
    }

    .select2-search--dropdown .select2-search__field {
        border: 2px solid #e5e7eb !important;
        border-radius: 0.375rem !important;
        padding: 0.5rem 0.75rem !important;
        font-size: 0.875rem !important;
    }

    .select2-search--dropdown .select2-search__field:focus {
        border-color: #3b82f6 !important;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
        outline: none !important;
    }

    .select2-results__options {
        max-height: 200px !important;
    }

    .select2-results__option {
        padding: 0.75rem 1rem !important;
        font-weight: 500 !important;
        font-size: 0.875rem !important;
        color: #374151 !important;
        cursor: pointer !important;
    }

    .select2-results__option--highlighted {
        background-color: rgba(59, 130, 246, 0.1) !important;
        color: #1f2937 !important;
    }

    .select2-results__option--selected {
        background-color: #3b82f6 !important;
        color: white !important;
    }

    .select2-results__option[aria-selected=true] {
        background-color: #3b82f6 !important;
        color: white !important;
    }

    /* Enhanced styling for selected option text */
    .select2-results__option--selected .select2-result-user__name,
    .select2-results__option[aria-selected=true] .select2-result-user__name {
        color: white !important;
        font-weight: 700 !important;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;
    }

    .select2-results__option--selected .select2-result-user__id,
    .select2-results__option[aria-selected=true] .select2-result-user__id {
        color: #dbeafe !important;
        font-weight: 600 !important;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;
    }

    /* Loading state */
    .select2-results__option--loading {
        color: #6b7280 !important;
        font-style: italic !important;
    }

    /* No results */
    .select2-results__message {
        padding: 0.75rem 1rem !important;
        color: #6b7280 !important;
        font-style: italic !important;
    }

    /* Clear button */
    .select2-selection__clear {
        color: #6b7280 !important;
        font-size: 1.2rem !important;
        font-weight: bold !important;
        margin-right: 0.5rem !important;
    }

    .select2-selection__clear:hover {
        color: #ef4444 !important;
    }

    /* Custom result templates */
    .select2-result-user {
        padding: 0.5rem 0;
        line-height: 1.4;
    }

    .select2-result-user__name {
        font-weight: 600;
        color: #1f2937;
        font-size: 0.875rem;
        margin-bottom: 0.125rem;
    }

    .select2-result-user__id {
        font-size: 0.75rem;
        color: #6b7280;
        font-weight: 500;
    }

    /* Highlighted state styling for better contrast */
    .select2-results__option--highlighted .select2-result-user__name {
        color: #1e40af !important;
        font-weight: 700 !important;
    }

    .select2-results__option--highlighted .select2-result-user__id {
        color: #3730a3 !important;
        font-weight: 600 !important;
    }

    /* Ensure proper z-index for dropdown */
    .select2-container--open .select2-dropdown {
        z-index: 9999 !important;
    }

    /* Fix for admin layout */
    .admin-card .select2-container {
        z-index: 1;
    }

    /* Disabled state styling */
    .select2-container--disabled .select2-selection--single {
        background-color: #f3f4f6 !important;
        color: #9ca3af !important;
        cursor: not-allowed !important;
    }
</style>
';

// Start output buffering to capture the page content
ob_start();
?>
<!-- Page Header -->
<div class="flex flex-col lg:flex-row justify-between items-start lg:items-center space-y-4 lg:space-y-0 mb-6">
    <div>
        <h1 class="text-3xl font-bold text-gray-900">Manual Fund Transfer</h1>
        <p class="text-gray-600 mt-1">Transfer funds between user accounts manually</p>
    </div>
    <div class="flex items-center space-x-2 bg-orange-50 px-4 py-2 rounded-lg border border-orange-200">
        <i class="fas fa-exchange-alt text-orange-600"></i>
        <span class="text-sm font-medium text-orange-700">Fund Management</span>
    </div>
</div>

<!-- Transfer Form -->
<div class="admin-card p-8 fade-in">
    <div class="flex items-center space-x-3 mb-6">
        <div class="w-10 h-10 bg-gradient-to-br from-orange-500 to-red-600 rounded-xl flex items-center justify-center shadow-lg">
            <i class="fas fa-exchange-alt text-white text-lg"></i>
        </div>
        <div>
            <h2 class="text-xl font-semibold text-gray-900">Fund Transfer Form</h2>
            <p class="text-sm text-gray-600">Select sender, recipient, and amount to transfer</p>
        </div>
    </div>

    <div id="response-message" class="hidden p-4 mb-6 text-sm rounded-lg" role="alert"></div>

    <form id="transfer-form" class="space-y-6">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Sender Selection -->
            <div>
                <label for="sender_user_id" class="block text-sm font-semibold text-gray-700 mb-2">
                    <i class="fas fa-user-minus text-red-600 mr-2"></i>Sender
                </label>
                <select name="sender_user_id" id="sender_user_id" class="form-input w-full" required></select>
                <p class="mt-2 text-xs text-gray-500">
                    <i class="fas fa-info-circle mr-1"></i>
                    Search by user name or ID
                </p>
            </div>

            <!-- Recipient Selection -->
            <div>
                <label for="recipient_user_id" class="block text-sm font-semibold text-gray-700 mb-2">
                    <i class="fas fa-user-plus text-green-600 mr-2"></i>Recipient
                </label>
                <select name="recipient_user_id" id="recipient_user_id" class="form-input w-full" required></select>
                <p class="mt-2 text-xs text-gray-500">
                    <i class="fas fa-info-circle mr-1"></i>
                    Select sender first to enable recipient selection
                </p>
            </div>
        </div>

        <!-- Amount Input -->
        <div>
            <label for="amount" class="block text-sm font-semibold text-gray-700 mb-2">
                <i class="fas fa-dollar-sign text-blue-600 mr-2"></i>Amount to Transfer (USD)
            </label>
            <input type="number" name="amount" id="amount" step="0.0001" min="0"
                class="form-input w-full"
                placeholder="Enter amount to transfer" required>
            <p class="mt-2 text-xs text-gray-500">
                <i class="fas fa-exclamation-triangle text-orange-500 mr-1"></i>
                Ensure sender has sufficient balance before transfer
            </p>
        </div>

        <!-- Transfer Flow Visualization -->
        <div class="bg-gray-50 rounded-lg p-4 border border-gray-200">
            <div class="flex items-center justify-center space-x-4">
                <div class="flex items-center space-x-2">
                    <div class="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                        <i class="fas fa-user text-red-600 text-sm"></i>
                    </div>
                    <span class="text-sm font-medium text-gray-700" id="sender-display">Sender</span>
                </div>
                <div class="flex-1 flex items-center justify-center">
                    <i class="fas fa-arrow-right text-gray-400 text-xl"></i>
                </div>
                <div class="flex items-center space-x-2">
                    <span class="text-sm font-medium text-gray-700" id="recipient-display">Recipient</span>
                    <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                        <i class="fas fa-user text-green-600 text-sm"></i>
                    </div>
                </div>
            </div>
            <div class="text-center mt-2">
                <span class="text-lg font-bold text-blue-600" id="amount-display">$0.00</span>
            </div>
        </div>

        <!-- Submit Button -->
        <div class="flex justify-end pt-4 border-t border-gray-200">
            <button type="submit" id="submit-btn" class="btn-primary px-8 py-3 text-sm font-semibold">
                <i class="fas fa-exchange-alt mr-2"></i>
                Execute Transfer
            </button>
        </div>
    </form>
</div>
<?php
// Get the page content
$page_content = ob_get_clean();

// Additional JavaScript for Select2 and form functionality
$additional_js = '
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<script>
    $(document).ready(function() {
        const transferForm = document.getElementById("transfer-form");
        const responseMessage = document.getElementById("response-message");

        // Enhanced Select2 initialization with admin theme
        function initializeSenderSelect2() {
            $("#sender_user_id").select2({
                placeholder: "Search for a sender by name or ID",
                allowClear: true,
                width: "100%",
                dropdownParent: $("body"),
                dropdownAutoWidth: true,
                ajax: {
                    url: "/netvis/ui/api/get_users_for_select2.php",
                    dataType: "json",
                    delay: 250,
                    processResults: function (data) {
                        return { results: data.results || [] };
                    },
                    cache: true
                },
                minimumInputLength: 1,
                templateResult: function(user) {
                    if (user.loading) return "Searching...";
                    if (!user.id) return user.text;
                    return $("<div class=\"select2-result-user\">" +
                        "<div class=\"select2-result-user__name\">" + user.text + "</div>" +
                        "<div class=\"select2-result-user__id\">ID: " + user.id + "</div>" +
                        "</div>");
                },
                templateSelection: function(user) {
                    return user.text || user.id;
                }
            });
        }

        function initializeRecipientSelect2(senderId) {
            $("#recipient_user_id").select2({
                placeholder: "Search for a recipient by name or ID",
                allowClear: true,
                width: "100%",
                dropdownParent: $("body"),
                dropdownAutoWidth: true,
                ajax: {
                    url: "/netvis/ui/api/get_users_for_select2.php",
                    dataType: "json",
                    delay: 250,
                    data: function (params) {
                        return {
                            q: params.term,
                            sender_id: senderId
                        };
                    },
                    processResults: function (data) {
                        return { results: data.results || [] };
                    },
                    cache: true
                },
                minimumInputLength: 0,
                templateResult: function(user) {
                    if (user.loading) return "Searching...";
                    if (!user.id) return user.text;
                    return $("<div class=\"select2-result-user\">" +
                        "<div class=\"select2-result-user__name\">" + user.text + "</div>" +
                        "<div class=\"select2-result-user__id\">ID: " + user.id + "</div>" +
                        "</div>");
                },
                templateSelection: function(user) {
                    return user.text || user.id;
                }
            });
        }

        // Initialize form
        initializeSenderSelect2();
        $("#recipient_user_id").prop("disabled", true).select2({
            placeholder: "Select a sender first",
            width: "100%",
            dropdownParent: $("body")
        });

        // Update display functions
        function updateTransferDisplay() {
            const senderText = $("#sender_user_id option:selected").text() || "Sender";
            const recipientText = $("#recipient_user_id option:selected").text() || "Recipient";
            const amount = parseFloat($("#amount").val()) || 0;

            $("#sender-display").text(senderText);
            $("#recipient-display").text(recipientText);
            $("#amount-display").text("$" + amount.toFixed(2));
        }

        // Event handlers
        $("#sender_user_id").on("change", function() {
            const senderId = $(this).val();
            $("#recipient_user_id").val(null).trigger("change").prop("disabled", true);

            if (senderId) {
                $("#recipient_user_id").prop("disabled", false);
                initializeRecipientSelect2(senderId);
            } else {
                $("#recipient_user_id").select2({
                    placeholder: "Select a sender first",
                    width: "100%",
                    dropdownParent: $("body")
                });
            }
            updateTransferDisplay();
        });

        $("#recipient_user_id").on("change", updateTransferDisplay);
        $("#amount").on("input", updateTransferDisplay);

        // Enhanced form submission
        transferForm.addEventListener("submit", function(e) {
            e.preventDefault();
            const submitBtn = $("#submit-btn");
            const originalText = submitBtn.html();

            // Show loading state
            submitBtn.prop("disabled", true).html("<i class=\"fas fa-spinner fa-spin mr-2\"></i>Processing...");

            const formData = new FormData(this);
            const xhr = new XMLHttpRequest();
            xhr.open("POST", "/netvis/ui/api/admin_execute_transfer.php", true);

            xhr.onload = function() {
                let message = "An unknown error occurred.";
                let success = false;
                if (xhr.status >= 200 && xhr.status < 400) {
                    try {
                        const data = JSON.parse(xhr.responseText);
                        message = data.message;
                        success = data.success;
                    } catch (e) {
                        message = "Error parsing server response.";
                    }
                } else {
                    message = `Server error: ${xhr.status}`;
                }

                showFeedback(message, success ? "success" : "error");

                if (success) {
                    transferForm.reset();
                    $("#sender_user_id").val(null).trigger("change");
                    $("#recipient_user_id").val(null).trigger("change");
                    updateTransferDisplay();
                }
            };

            xhr.onerror = function() {
                showFeedback("Network error. Could not complete the request.", "error");
            };

            xhr.onloadend = function() {
                submitBtn.prop("disabled", false).html(originalText);
            };

            xhr.send(formData);
        });
        // Enhanced feedback function
        function showFeedback(message, type) {
            const feedbackMessage = $("#response-message");
            feedbackMessage.html("<i class=\"fas fa-" + (type === "success" ? "check-circle" : "exclamation-triangle") + " mr-2\"></i>" + message);

            if (type === "success") {
                feedbackMessage.attr("class", "p-4 mb-6 text-sm text-green-800 rounded-lg bg-green-50 border border-green-200");
            } else {
                feedbackMessage.attr("class", "p-4 mb-6 text-sm text-red-800 rounded-lg bg-red-50 border border-red-200");
            }

            feedbackMessage.removeClass("hidden");

            // Auto-hide after 5 seconds for success messages
            if (type === "success") {
                setTimeout(function() {
                    feedbackMessage.addClass("hidden");
                }, 5000);
            }
        }
    });
</script>
';

// Include the layout
include 'admin/includes/layout.php';
?>