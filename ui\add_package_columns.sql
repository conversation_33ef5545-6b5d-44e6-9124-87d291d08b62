-- ============================================
-- ALTER QUERIES FOR PACKAGE SOFT DELETE
-- ============================================
-- Execute these queries to add the required columns for soft delete functionality

-- Add deleted_at column (if not exists)
-- This column stores the timestamp when the package was deleted
ALTER TABLE packages ADD COLUMN deleted_at TIMESTAMP NULL DEFAULT NULL;

-- Add is_deleted column 
-- This column is a boolean flag (0 = active, 1 = deleted)
-- Default is 0 (active/visible to admin)
ALTER TABLE packages ADD COLUMN is_deleted TINYINT(1) NOT NULL DEFAULT 0;

-- ============================================
-- VERIFICATION QUERIES
-- ============================================
-- Run these to verify the columns were added successfully

-- Check table structure
DESCRIBE packages;

-- Check if columns exist
SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'packages' 
AND COLUMN_NAME IN ('deleted_at', 'is_deleted');

-- ============================================
-- USAGE EXAMPLES
-- ============================================

-- Soft delete a package (example)
-- UPDATE packages SET deleted_at = NOW(), is_deleted = 1 WHERE id = 1;

-- Restore a package (example)
-- UPDATE packages SET deleted_at = NULL, is_deleted = 0 WHERE id = 1;

-- View only active packages
-- SELECT * FROM packages WHERE is_deleted = 0;

-- View only deleted packages
-- SELECT * FROM packages WHERE is_deleted = 1;

-- ============================================
-- NOTES
-- ============================================
-- 1. deleted_at: Stores the exact timestamp when package was deleted
-- 2. is_deleted: Boolean flag for quick filtering (0 = visible, 1 = hidden)
-- 3. Both columns work together for complete soft delete functionality
-- 4. Default values ensure existing packages remain active/visible
