<?php
session_start();

if (!isset($_SESSION['user_id']) || !$_SESSION['is_admin']) {
    header("Location: login.php");
    exit();
}

// Set page title
$page_title = 'Add Fund to Deposit Wallet';

// Additional CSS for Select2 styling
$additional_css = '
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<style>
    /* Select2 Container Styling */
    .select2-container {
        width: 100% !important;
    }

    .select2-container .select2-selection--single {
        height: 48px !important;
        border: 2px solid #e5e7eb !important;
        border-radius: 0.5rem !important;
        background: rgba(255, 255, 255, 0.9) !important;
        transition: all 0.3s ease !important;
        padding: 0 !important;
    }

    .select2-container--default .select2-selection--single:focus,
    .select2-container--default.select2-container--focus .select2-selection--single {
        border-color: #3b82f6 !important;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
        background: rgba(255, 255, 255, 1) !important;
        outline: none !important;
    }

    .select2-container .select2-selection--single .select2-selection__rendered {
        line-height: 44px !important;
        padding-left: 1rem !important;
        padding-right: 2rem !important;
        color: #1f2937 !important;
        font-weight: 500 !important;
        font-size: 0.875rem !important;
    }

    .select2-container .select2-selection--single .select2-selection__placeholder {
        color: #9ca3af !important;
        font-weight: 400 !important;
    }

    .select2-container .select2-selection--single .select2-selection__arrow {
        height: 44px !important;
        right: 1rem !important;
        width: 20px !important;
    }

    .select2-container .select2-selection--single .select2-selection__arrow b {
        border-color: #6b7280 transparent transparent transparent !important;
        border-width: 6px 6px 0 6px !important;
    }

    /* Dropdown Styling */
    .select2-dropdown {
        border: 2px solid #e5e7eb !important;
        border-radius: 0.5rem !important;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1) !important;
        z-index: 9999 !important;
    }

    .select2-search--dropdown {
        padding: 0.5rem !important;
    }

    .select2-search--dropdown .select2-search__field {
        border: 2px solid #e5e7eb !important;
        border-radius: 0.375rem !important;
        padding: 0.5rem 0.75rem !important;
        font-size: 0.875rem !important;
    }

    .select2-search--dropdown .select2-search__field:focus {
        border-color: #3b82f6 !important;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
        outline: none !important;
    }

    .select2-results__options {
        max-height: 200px !important;
    }

    .select2-results__option {
        padding: 0.75rem 1rem !important;
        font-weight: 500 !important;
        font-size: 0.875rem !important;
        color: #374151 !important;
        cursor: pointer !important;
    }

    .select2-results__option--highlighted {
        background-color: rgba(59, 130, 246, 0.1) !important;
        color: #1f2937 !important;
    }

    .select2-results__option--selected {
        background-color: #3b82f6 !important;
        color: white !important;
    }

    .select2-results__option[aria-selected=true] {
        background-color: #3b82f6 !important;
        color: white !important;
    }

    /* Loading state */
    .select2-results__option--loading {
        color: #6b7280 !important;
        font-style: italic !important;
    }

    /* No results */
    .select2-results__message {
        padding: 0.75rem 1rem !important;
        color: #6b7280 !important;
        font-style: italic !important;
    }

    /* Clear button */
    .select2-selection__clear {
        color: #6b7280 !important;
        font-size: 1.2rem !important;
        font-weight: bold !important;
        margin-right: 0.5rem !important;
    }

    .select2-selection__clear:hover {
        color: #ef4444 !important;
    }

    /* Custom result templates */
    .select2-result-user {
        padding: 0.25rem 0;
    }

    .select2-result-user__name {
        font-weight: 600;
        color: #1f2937;
        font-size: 0.875rem;
    }

    .select2-result-user__id {
        font-size: 0.75rem;
        color: #6b7280;
        margin-top: 0.125rem;
    }

    /* Ensure proper z-index for dropdown */
    .select2-container--open .select2-dropdown {
        z-index: 9999 !important;
    }

    /* Fix for admin layout */
    .admin-card .select2-container {
        z-index: 1;
    }
</style>
';

// Start output buffering to capture the page content
ob_start();
?>
<!-- Page Header -->
<div class="flex flex-col lg:flex-row justify-between items-start lg:items-center space-y-4 lg:space-y-0 mb-6">
    <div>
        <h1 class="text-3xl font-bold text-gray-900">Add Fund to Deposit Wallet</h1>
        <p class="text-gray-600 mt-1">Add funds directly to a user's deposit wallet</p>
    </div>
    <div class="flex items-center space-x-2 bg-blue-50 px-4 py-2 rounded-lg border border-blue-200">
        <i class="fas fa-plus-circle text-blue-600"></i>
        <span class="text-sm font-medium text-blue-700">Fund Management</span>
    </div>
</div>

<!-- Add Fund Form -->
<div class="admin-card p-8 fade-in">
    <div class="flex items-center space-x-3 mb-6">
        <div class="w-10 h-10 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl flex items-center justify-center shadow-lg">
            <i class="fas fa-wallet text-white text-lg"></i>
        </div>
        <div>
            <h2 class="text-xl font-semibold text-gray-900">Fund Addition Form</h2>
            <p class="text-sm text-gray-600">Select user and enter amount to add to their deposit wallet</p>
        </div>
    </div>

    <div id="feedback-message" class="hidden p-4 mb-6 text-sm rounded-lg" role="alert"></div>

    <form id="add-fund-form" class="space-y-6">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- User Selection -->
            <div>
                <label for="user_id" class="block text-sm font-semibold text-gray-700 mb-2">
                    <i class="fas fa-user text-blue-600 mr-2"></i>Select User
                </label>
                <select id="user_id" name="user_id" class="form-input w-full" required></select>
                <p class="mt-2 text-xs text-gray-500">
                    <i class="fas fa-info-circle mr-1"></i>
                    Search by user name or ID
                </p>
            </div>

            <!-- Amount Input -->
            <div>
                <label for="amount" class="block text-sm font-semibold text-gray-700 mb-2">
                    <i class="fas fa-dollar-sign text-green-600 mr-2"></i>Amount (USD)
                </label>
                <input type="number" id="amount" name="amount" step="10" min="10"
                    class="form-input w-full"
                    placeholder="Enter amount (multiple of 10)" required>
                <p class="mt-2 text-xs text-gray-500">
                    <i class="fas fa-exclamation-triangle text-orange-500 mr-1"></i>
                    Amount must be a multiple of 10
                </p>
            </div>
        </div>

        <!-- Submit Button -->
        <div class="flex justify-end pt-4 border-t border-gray-200">
            <button type="submit" id="submit-btn" class="btn-primary px-8 py-3 text-sm font-semibold">
                <i class="fas fa-plus-circle mr-2"></i>
                Add Fund
            </button>
        </div>
    </form>
</div>

<?php
// Get the page content
$page_content = ob_get_clean();

// Additional JavaScript for Select2 and form functionality
$additional_js = '
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<script>
    $(document).ready(function() {
        // Initialize Select2 with admin theme styling
        $("#user_id").select2({
            placeholder: "Search for a user by name or ID",
            allowClear: true,
            width: "100%",
            dropdownParent: $("#user_id").parent(),
            ajax: {
                url: "/netvis/ui/api/get_users_for_select2.php",
                dataType: "json",
                delay: 250,
                processResults: function(data) {
                    return {
                        results: data.results || []
                    };
                },
                cache: true
            },
            minimumInputLength: 1,
            escapeMarkup: function(markup) {
                return markup;
            },
            templateResult: function(user) {
                if (user.loading) {
                    return "Searching...";
                }
                if (!user.id) {
                    return user.text;
                }
                return $("<div class=\"select2-result-user\">" +
                    "<div class=\"select2-result-user__name\">" + user.text + "</div>" +
                    "<div class=\"select2-result-user__id\">ID: " + user.id + "</div>" +
                    "</div>");
            },
            templateSelection: function(user) {
                return user.text || user.id;
            }
        });

        // Form submission handler
        $("#add-fund-form").on("submit", function(e) {
            e.preventDefault();
            const submitBtn = $("#submit-btn");
            const originalText = submitBtn.html();

            // Show loading state
            submitBtn.prop("disabled", true).html("<i class=\"fas fa-spinner fa-spin mr-2\"></i>Processing...");

            const userId = $("#user_id").val();
            const amount = parseFloat($("#amount").val());

            // Validation
            if (!userId || !amount) {
                showFeedback("Please select a user and enter an amount.", "error");
                submitBtn.prop("disabled", false).html(originalText);
                return;
            }

            if (amount <= 0 || amount % 10 !== 0) {
                showFeedback("Invalid amount. Please enter a positive multiple of 10.", "error");
                submitBtn.prop("disabled", false).html(originalText);
                return;
            }

            // AJAX request
            $.ajax({
                url: "/netvis/ui/api/admin_add_fund.php",
                type: "POST",
                contentType: "application/json",
                data: JSON.stringify({
                    user_id: userId,
                    amount: amount
                }),
                success: function(response) {
                    if (response.success) {
                        showFeedback("Fund added successfully!", "success");
                        $("#add-fund-form")[0].reset();
                        $("#user_id").val(null).trigger("change");

                        // Show success notification
                        showNotification("Fund added successfully!", "success");
                    } else {
                        showFeedback(response.message || "An unknown error occurred.", "error");
                    }
                },
                error: function() {
                    showFeedback("A network error occurred. Please try again.", "error");
                },
                complete: function() {
                    submitBtn.prop("disabled", false).html(originalText);
                }
            });
        });

        // Enhanced feedback function with admin theme styling
        function showFeedback(message, type) {
            const feedbackMessage = $("#feedback-message");
            feedbackMessage.html("<i class=\"fas fa-" + (type === "success" ? "check-circle" : "exclamation-triangle") + " mr-2\"></i>" + message);

            if (type === "success") {
                feedbackMessage.attr("class", "p-4 mb-6 text-sm text-green-800 rounded-lg bg-green-50 border border-green-200");
            } else {
                feedbackMessage.attr("class", "p-4 mb-6 text-sm text-red-800 rounded-lg bg-red-50 border border-red-200");
            }

            feedbackMessage.removeClass("hidden");

            // Auto-hide after 5 seconds for success messages
            if (type === "success") {
                setTimeout(function() {
                    feedbackMessage.addClass("hidden");
                }, 5000);
            }
        }
    });
</script>
';

// Include the layout
include 'admin/includes/layout.php';
?>