<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Wallet - CryptoApp</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <style>
        /* Define the custom theme and other base styles */
        body {
            font-family: 'Inter', sans-serif;
        }

        .theme-ocean {
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
            color: #e2e8f0;
        }

        .theme-ocean .card {
            background: rgba(30, 41, 59, 0.8);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(59, 130, 246, 0.1);
            border-radius: 1rem;
            transition: all 0.3s ease;
        }

        .theme-ocean .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        }

        .theme-ocean .accent {
            color: #3b82f6;
        }

        .theme-ocean .secondary {
            color: #06b6d4;
        }

        /* Stats Card Styles */
        .stats-card {
            background: rgba(30, 41, 59, 0.8);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(59, 130, 246, 0.1);
            border-radius: 1rem;
            padding: 1.5rem;
            transition: all 0.3s ease;
        }

        .stats-card:hover {
            transform: translateY(-5px);
            border-color: rgba(59, 130, 246, 0.4);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.4);
        }

        /* Wallet Card Styles */
        .wallet-card {
            background: linear-gradient(135deg, rgba(30, 41, 59, 0.9) 0%, rgba(15, 23, 42, 0.9) 100%);
            backdrop-filter: blur(15px);
            border: 1px solid rgba(59, 130, 246, 0.2);
            border-radius: 1.5rem;
            padding: 2rem;
            position: relative;
            overflow: hidden;
            transition: all 0.4s ease;
        }

        .wallet-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #3b82f6, #06b6d4);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .wallet-card:hover::before {
            opacity: 1;
        }

        .wallet-card:hover {
            transform: translateY(-8px);
            border-color: rgba(59, 130, 246, 0.4);
            box-shadow: 0 20px 50px rgba(0, 0, 0, 0.5);
        }

        /* Transaction Item Styles */
        .transaction-item {
            padding: 1rem;
            border-radius: 0.75rem;
            background: rgba(30, 41, 59, 0.3);
            border: 1px solid rgba(59, 130, 246, 0.1);
            transition: all 0.3s ease;
            margin-bottom: 0.75rem;
        }

        .transaction-item:hover {
            background: rgba(30, 41, 59, 0.5);
            border-color: rgba(59, 130, 246, 0.2);
            transform: translateX(4px);
        }

        /* Action Button Styles */
        .action-btn {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 0.5rem;
            padding: 1rem;
            background: rgba(30, 41, 59, 0.5);
            border: 1px solid rgba(59, 130, 246, 0.1);
            border-radius: 1rem;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .action-btn:hover {
            background: rgba(30, 41, 59, 0.8);
            border-color: rgba(59, 130, 246, 0.3);
            transform: translateY(-2px);
        }

        .action-icon-circle {
            width: 3rem;
            height: 3rem;
            border-radius: 50%;
            background: rgba(59, 130, 246, 0.1);
            border: 1px solid rgba(59, 130, 246, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .action-btn:hover .action-icon-circle {
            transform: scale(1.1);
            border-color: rgba(59, 130, 246, 0.4);
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.2);
        }

        .action-label {
            font-size: 0.875rem;
            font-weight: 600;
            color: #ffffff;
            text-align: center;
            transition: all 0.3s ease;
        }

        .action-btn:hover .action-label {
            color: #3b82f6;
        }

        /* Shimmer Animation */
        @keyframes shimmer {
            0% {
                transform: translateX(-100%);
            }

            100% {
                transform: translateX(100%);
            }
        }

        .animate-shimmer {
            animation: shimmer 2s infinite;
        }
    </style>
</head>

<body class="theme-ocean min-h-screen bg-slate-900">

    <!-- Main Wallet Container -->
    <div class="container mx-auto max-w-md min-h-screen bg-slate-900 flex flex-col">

        <!-- Sleek Top Navigation Bar -->
        <header
            class="sticky top-0 z-50 flex-shrink-0 flex items-center justify-between px-4 py-3 border-b border-slate-700/50 bg-slate-900/95 backdrop-blur-sm">
            <!-- Back Button & Title -->
            <div class="flex items-center gap-3">
                <button onclick="goBack()"
                    class="w-8 h-8 rounded-full bg-slate-800/50 flex items-center justify-center border border-slate-700 hover:bg-slate-700/50 transition-colors">
                    <i class="fas fa-arrow-left text-slate-400 text-sm"></i>
                </button>
                <div>
                    <h1 class="font-bold text-white text-lg">Wallet</h1>
                    <p class="text-xs text-slate-400">Manage your funds</p>
                </div>
            </div>
            <!-- User Avatar -->
            <div class="w-8 h-8 rounded-full bg-blue-500/20 flex items-center justify-center border border-blue-400">
                <i class="fas fa-user text-sm accent"></i>
            </div>
        </header>

        <!-- Main content with scrolling -->
        <div class="flex-grow overflow-y-auto px-6 py-6">

            <!-- Mobile-Optimized Dual Wallet System -->
            <div class="space-y-4 mb-6">
                <!-- Top-up Wallet -->
                <div class="wallet-card relative overflow-hidden">
                    <!-- Background Pattern -->
                    <div class="absolute inset-0 bg-gradient-to-br from-blue-500/5 via-transparent to-cyan-500/5"></div>
                    <div
                        class="absolute top-0 right-0 w-16 h-16 bg-gradient-to-br from-blue-400/10 to-transparent rounded-full -translate-y-8 translate-x-8">
                    </div>

                    <div class="relative">
                        <!-- Header -->
                        <div class="flex items-center justify-between mb-3">
                            <div class="flex items-center gap-2.5">
                                <div
                                    class="w-10 h-10 rounded-lg bg-gradient-to-br from-blue-500/20 to-cyan-600/20 backdrop-blur-sm border border-blue-400/30 flex items-center justify-center shadow-lg">
                                    <i class="fas fa-wallet text-blue-400"></i>
                                </div>
                                <div>
                                    <h3 class="font-bold text-white">Top-up Wallet</h3>
                                    <p class="text-xs text-slate-400">Deposits & Transfers</p>
                                </div>
                            </div>
                            <div
                                class="flex items-center gap-1 bg-blue-500/10 px-2 py-1 rounded-full border border-blue-500/20">
                                <div class="w-1.5 h-1.5 bg-blue-400 rounded-full animate-pulse"></div>
                                <span class="text-xs text-blue-400 font-medium">ACTIVE</span>
                            </div>
                        </div>

                        <!-- Balance Display -->
                        <div class="mb-4">
                            <div class="flex items-baseline gap-2 mb-2">
                                <span class="text-2xl sm:text-3xl font-bold text-white">$150.00</span>
                                <span class="text-sm text-slate-400">USD</span>
                            </div>
                            <div class="flex items-center gap-2 flex-wrap">
                                <span class="text-xs text-blue-400 font-semibold">Available for:</span>
                                <div class="flex gap-1">
                                    <span
                                        class="text-xs bg-blue-500/20 text-blue-300 px-2 py-1 rounded-full">Upgrade</span>
                                    <span
                                        class="text-xs bg-cyan-500/20 text-cyan-300 px-2 py-1 rounded-full">Transfer</span>
                                </div>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="grid grid-cols-2 gap-3">
                            <button onclick="addFunds()"
                                class="bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-500 hover:to-cyan-500 text-white font-semibold py-3 px-4 rounded-lg text-sm transition-all duration-300 shadow-lg flex items-center justify-center gap-2">
                                <i class="fas fa-plus"></i>
                                <span>Add Funds</span>
                            </button>
                            <button onclick="transferFunds()"
                                class="bg-gradient-to-r from-cyan-600/80 to-blue-600/80 hover:from-cyan-500 hover:to-blue-500 text-white font-semibold py-3 px-4 rounded-lg text-sm transition-all duration-300 border border-cyan-400/20 flex items-center justify-center gap-2">
                                <i class="fas fa-exchange-alt"></i>
                                <span>Transfer</span>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Withdrawal Wallet -->
                <div class="wallet-card relative overflow-hidden">
                    <!-- Background Pattern -->
                    <div class="absolute inset-0 bg-gradient-to-br from-cyan-500/5 via-transparent to-blue-500/5"></div>
                    <div
                        class="absolute top-0 right-0 w-16 h-16 bg-gradient-to-br from-cyan-400/10 to-transparent rounded-full -translate-y-8 translate-x-8">
                    </div>

                    <div class="relative">
                        <!-- Header -->
                        <div class="flex items-center justify-between mb-3">
                            <div class="flex items-center gap-2.5">
                                <div
                                    class="w-10 h-10 rounded-lg bg-gradient-to-br from-cyan-500/20 to-blue-600/20 backdrop-blur-sm border border-cyan-400/30 flex items-center justify-center shadow-lg">
                                    <i class="fas fa-piggy-bank text-cyan-400"></i>
                                </div>
                                <div>
                                    <h3 class="font-bold text-white">Withdrawal Wallet</h3>
                                    <p class="text-xs text-slate-400">Profits & Earnings</p>
                                </div>
                            </div>
                            <div
                                class="flex items-center gap-1 bg-cyan-500/10 px-2 py-1 rounded-full border border-cyan-500/20">
                                <div class="w-1.5 h-1.5 bg-cyan-400 rounded-full animate-pulse"></div>
                                <span class="text-xs text-cyan-400 font-medium">EARNING</span>
                            </div>
                        </div>

                        <!-- Balance Display -->
                        <div class="mb-4">
                            <div class="flex items-baseline gap-2 mb-3">
                                <span class="text-2xl sm:text-3xl font-bold text-white">$85.30</span>
                                <span class="text-sm text-slate-400">USD</span>
                            </div>
                            <div class="grid grid-cols-3 gap-2 text-center">
                                <div class="bg-slate-800/30 rounded-lg p-2 border border-slate-700/50">
                                    <p class="text-xs text-slate-400">Daily Profit</p>
                                    <p class="text-sm font-bold text-cyan-400">$0.75</p>
                                </div>
                                <div class="bg-slate-800/30 rounded-lg p-2 border border-slate-700/50">
                                    <p class="text-xs text-slate-400">Network</p>
                                    <p class="text-sm font-bold text-blue-400">$12.50</p>
                                </div>
                                <div class="bg-slate-800/30 rounded-lg p-2 border border-slate-700/50">
                                    <p class="text-xs text-slate-400">Bonus</p>
                                    <p class="text-sm font-bold text-purple-400">$5.25</p>
                                </div>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="grid grid-cols-2 gap-3">
                            <button onclick="withdrawFunds()"
                                class="bg-gradient-to-r from-cyan-600 to-blue-600 hover:from-cyan-500 hover:to-blue-500 text-white font-semibold py-3 px-4 rounded-lg text-sm transition-all duration-300 shadow-lg flex items-center justify-center gap-2">
                                <i class="fas fa-arrow-down"></i>
                                <span>Withdraw</span>
                            </button>
                            <button onclick="transferFromWithdrawal()"
                                class="bg-gradient-to-r from-blue-600/80 to-cyan-600/80 hover:from-blue-500 hover:to-cyan-500 text-white font-semibold py-3 px-4 rounded-lg text-sm transition-all duration-300 border border-blue-400/20 flex items-center justify-center gap-2">
                                <i class="fas fa-exchange-alt"></i>
                                <span>Transfer</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Current Package Status -->
            <div class="stats-card mb-6">
                <div class="flex items-center justify-between mb-4">
                    <div class="flex items-center gap-2">
                        <i class="fas fa-crown text-blue-400"></i>
                        <h3 class="font-bold text-white">Current Package</h3>
                    </div>
                    <div
                        class="flex items-center gap-1 bg-blue-500/10 px-2 py-1 rounded-full border border-blue-500/20">
                        <div class="w-2 h-2 bg-blue-400 rounded-full animate-pulse"></div>
                        <span class="text-xs text-blue-400 font-medium">ACTIVE</span>
                    </div>
                </div>

                <div
                    class="bg-gradient-to-r from-slate-800/50 to-slate-700/30 rounded-xl p-4 border border-slate-600/30">
                    <div class="flex items-center justify-between mb-3">
                        <div>
                            <h4 class="font-bold text-white text-lg">Standard Account</h4>
                            <p class="text-xs text-slate-400">Investment: $100 • Daily: 0.75%</p>
                        </div>
                        <div class="text-right">
                            <p class="text-lg font-bold text-cyan-400">$0.75</p>
                            <p class="text-xs text-slate-400">Daily Profit</p>
                        </div>
                    </div>

                    <button onclick="showPackageUpgrade()"
                        class="w-full bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-500 hover:to-cyan-500 text-white font-semibold py-2 px-4 rounded-lg text-sm transition-all duration-300">
                        Upgrade Package
                    </button>
                </div>
            </div>







            <!-- Recent Wallet Transactions -->
            <div class="stats-card mb-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="font-bold text-white">Recent Activity</h3>
                    <button onclick="viewAllTransactions()"
                        class="text-xs text-blue-400 hover:text-blue-300 font-medium transition-colors">View
                        All</button>
                </div>

                <!-- Scrollable Transaction Container -->
                <div
                    class="max-h-80 overflow-y-auto space-y-3 pr-2 scrollbar-thin scrollbar-thumb-slate-600 scrollbar-track-slate-800">
                    <!-- Daily Profit Transaction -->
                    <div class="transaction-item flex justify-between items-center">
                        <div class="flex items-center gap-3">
                            <div
                                class="w-10 h-10 rounded-full bg-cyan-500/20 flex items-center justify-center border border-cyan-500/30">
                                <i class="fas fa-coins text-cyan-400"></i>
                            </div>
                            <div>
                                <p class="font-semibold text-white">Daily Profit</p>
                                <p class="text-xs text-slate-400">July 05, 2025 - 00:01</p>
                            </div>
                        </div>
                        <div class="text-right">
                            <p class="font-semibold text-cyan-400">+$0.75</p>
                            <p class="text-xs text-cyan-300">Standard Package</p>
                        </div>
                    </div>

                    <!-- Network Income Transaction -->
                    <div class="transaction-item flex justify-between items-center">
                        <div class="flex items-center gap-3">
                            <div
                                class="w-10 h-10 rounded-full bg-blue-500/20 flex items-center justify-center border border-blue-500/30">
                                <i class="fas fa-users text-blue-400"></i>
                            </div>
                            <div>
                                <p class="font-semibold text-white">Network Income</p>
                                <p class="text-xs text-slate-400">July 04, 2025 - 18:22</p>
                            </div>
                        </div>
                        <div class="text-right">
                            <p class="font-semibold text-blue-400">+$2.50</p>
                            <p class="text-xs text-blue-300">Referral Bonus</p>
                        </div>
                    </div>

                    <!-- Deposit Transaction -->
                    <div class="transaction-item flex justify-between items-center">
                        <div class="flex items-center gap-3">
                            <div
                                class="w-10 h-10 rounded-full bg-green-500/20 flex items-center justify-center border border-green-500/30">
                                <i class="fas fa-arrow-down text-green-400"></i>
                            </div>
                            <div>
                                <p class="font-semibold text-white">Deposit</p>
                                <p class="text-xs text-slate-400">July 04, 2025 - 14:30</p>
                            </div>
                        </div>
                        <div class="text-right">
                            <p class="font-semibold text-green-400">+$100.00</p>
                            <p class="text-xs text-green-300">Bank Transfer</p>
                        </div>
                    </div>

                    <!-- Bonus Income Transaction -->
                    <div class="transaction-item flex justify-between items-center">
                        <div class="flex items-center gap-3">
                            <div
                                class="w-10 h-10 rounded-full bg-purple-500/20 flex items-center justify-center border border-purple-500/30">
                                <i class="fas fa-gift text-purple-400"></i>
                            </div>
                            <div>
                                <p class="font-semibold text-white">Bonus Income</p>
                                <p class="text-xs text-slate-400">July 04, 2025 - 12:15</p>
                            </div>
                        </div>
                        <div class="text-right">
                            <p class="font-semibold text-purple-400">+$5.25</p>
                            <p class="text-xs text-purple-300">Welcome Bonus</p>
                        </div>
                    </div>

                    <!-- Withdrawal Transaction -->
                    <div class="transaction-item flex justify-between items-center">
                        <div class="flex items-center gap-3">
                            <div
                                class="w-10 h-10 rounded-full bg-red-500/20 flex items-center justify-center border border-red-500/30">
                                <i class="fas fa-arrow-up text-red-400"></i>
                            </div>
                            <div>
                                <p class="font-semibold text-white">Withdrawal</p>
                                <p class="text-xs text-slate-400">July 03, 2025 - 16:45</p>
                            </div>
                        </div>
                        <div class="text-right">
                            <p class="font-semibold text-red-400">-$25.00</p>
                            <p class="text-xs text-red-300">Bank Account</p>
                        </div>
                    </div>

                    <!-- Daily Profit Transaction -->
                    <div class="transaction-item flex justify-between items-center">
                        <div class="flex items-center gap-3">
                            <div
                                class="w-10 h-10 rounded-full bg-cyan-500/20 flex items-center justify-center border border-cyan-500/30">
                                <i class="fas fa-coins text-cyan-400"></i>
                            </div>
                            <div>
                                <p class="font-semibold text-white">Daily Profit</p>
                                <p class="text-xs text-slate-400">July 03, 2025 - 00:01</p>
                            </div>
                        </div>
                        <div class="text-right">
                            <p class="font-semibold text-cyan-400">+$0.75</p>
                            <p class="text-xs text-cyan-300">Standard Package</p>
                        </div>
                    </div>

                    <!-- Transfer Transaction -->
                    <div class="transaction-item flex justify-between items-center">
                        <div class="flex items-center gap-3">
                            <div
                                class="w-10 h-10 rounded-full bg-orange-500/20 flex items-center justify-center border border-orange-500/30">
                                <i class="fas fa-exchange-alt text-orange-400"></i>
                            </div>
                            <div>
                                <p class="font-semibold text-white">Transfer Out</p>
                                <p class="text-xs text-slate-400">July 02, 2025 - 09:15</p>
                            </div>
                        </div>
                        <div class="text-right">
                            <p class="font-semibold text-orange-400">-$50.00</p>
                            <p class="text-xs text-orange-300">To User @john_doe</p>
                        </div>
                    </div>

                    <!-- Network Income Transaction -->
                    <div class="transaction-item flex justify-between items-center">
                        <div class="flex items-center gap-3">
                            <div
                                class="w-10 h-10 rounded-full bg-blue-500/20 flex items-center justify-center border border-blue-500/30">
                                <i class="fas fa-users text-blue-400"></i>
                            </div>
                            <div>
                                <p class="font-semibold text-white">Network Income</p>
                                <p class="text-xs text-slate-400">July 01, 2025 - 20:30</p>
                            </div>
                        </div>
                        <div class="text-right">
                            <p class="font-semibold text-blue-400">+$10.00</p>
                            <p class="text-xs text-blue-300">Level 2 Bonus</p>
                        </div>
                    </div>
                </div>
            </div>


        </div>

        <!-- Sticky Bottom Navigation Bar -->
        <div class="sticky bottom-0 z-40 flex-shrink-0">
            <div class="bg-slate-800/95 backdrop-blur-sm mx-4 mb-4 rounded-2xl border border-slate-700">
                <div class="flex justify-around items-center h-16">
                    <a href="1.html" class="flex flex-col items-center gap-1 text-slate-400 hover:accent">
                        <i class="fas fa-home"></i>
                        <span class="text-xs">Home</span>
                    </a>
                    <a href="wallet.html" class="flex flex-col items-center gap-1 accent">
                        <i class="fas fa-wallet"></i>
                        <span class="text-xs">Wallet</span>
                    </a>
                    <a href="team.html" class="flex flex-col items-center gap-1 text-slate-400 hover:accent">
                        <i class="fas fa-users"></i>
                        <span class="text-xs">Team</span>
                    </a>
                    <a href="profile.html" class="flex flex-col items-center gap-1 text-slate-400 hover:accent">
                        <i class="fas fa-user"></i>
                        <span class="text-xs">Profile</span>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Package Upgrade Modal -->
    <div id="packageModal" class="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
        style="display: none;">
        <div class="bg-slate-800 rounded-2xl w-full max-w-md max-h-[90vh] overflow-y-auto border border-slate-700 transform scale-95 opacity-0 transition-all duration-300"
            id="modalContent">
            <!-- Modal Header -->
            <div class="sticky top-0 bg-slate-800 border-b border-slate-700 px-6 py-4 rounded-t-2xl">
                <div class="flex items-center justify-between">
                    <h3 class="text-xl font-bold text-white">Upgrade Package</h3>
                    <button onclick="closePackageModal()"
                        class="w-8 h-8 rounded-full bg-slate-700 hover:bg-slate-600 flex items-center justify-center transition-colors">
                        <i class="fas fa-times text-slate-400"></i>
                    </button>
                </div>
                <p class="text-sm text-slate-400 mt-1">Choose your investment package</p>
            </div>

            <!-- Modal Content -->
            <div class="p-6 space-y-4">
                <!-- Trial Account -->
                <div
                    class="package-card bg-slate-700/30 border border-slate-600/50 rounded-lg p-4 hover:border-blue-500/30 transition-colors">
                    <div class="flex items-center justify-between mb-3">
                        <div>
                            <h4 class="font-bold text-white">Trial Account</h4>
                            <p class="text-xs text-slate-400">0.5% daily profit</p>
                        </div>
                        <div class="text-right">
                            <p class="text-lg font-bold text-blue-400">$10</p>
                            <p class="text-xs text-slate-400">$0.05/day</p>
                        </div>
                    </div>
                    <button onclick="upgradePackage('trial', 10)"
                        class="w-full bg-gradient-to-r from-slate-600 to-slate-700 hover:from-slate-500 hover:to-slate-600 text-white font-semibold py-2 px-4 rounded-lg text-sm transition-all duration-300">
                        Upgrade to Trial
                    </button>
                </div>

                <!-- Basic Account -->
                <div
                    class="package-card bg-slate-700/30 border border-slate-600/50 rounded-lg p-4 hover:border-blue-500/30 transition-colors">
                    <div class="flex items-center justify-between mb-3">
                        <div>
                            <h4 class="font-bold text-white">Basic Account</h4>
                            <p class="text-xs text-slate-400">0.5% daily profit</p>
                        </div>
                        <div class="text-right">
                            <p class="text-lg font-bold text-blue-400">$50</p>
                            <p class="text-xs text-slate-400">$0.25/day</p>
                        </div>
                    </div>
                    <button onclick="upgradePackage('basic', 50)"
                        class="w-full bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-500 hover:to-cyan-500 text-white font-semibold py-2 px-4 rounded-lg text-sm transition-all duration-300">
                        Upgrade to Basic
                    </button>
                </div>

                <!-- Standard Account -->
                <div
                    class="package-card bg-gradient-to-r from-blue-500/10 to-cyan-500/10 border border-blue-500/30 rounded-lg p-4">
                    <div class="flex items-center justify-between mb-3">
                        <div>
                            <h4 class="font-bold text-white flex items-center gap-2">
                                Standard Account
                                <span class="text-xs bg-blue-500/20 text-blue-400 px-2 py-1 rounded-full">CURRENT</span>
                            </h4>
                            <p class="text-xs text-slate-400">0.75% daily profit</p>
                        </div>
                        <div class="text-right">
                            <p class="text-lg font-bold text-cyan-400">$100</p>
                            <p class="text-xs text-slate-400">$0.75/day</p>
                        </div>
                    </div>
                    <button disabled
                        class="w-full bg-slate-600 text-slate-400 font-semibold py-2 px-4 rounded-lg text-sm cursor-not-allowed">
                        Current Package
                    </button>
                </div>

                <!-- Premium Account -->
                <div
                    class="package-card bg-slate-700/30 border border-slate-600/50 rounded-lg p-4 hover:border-cyan-500/30 transition-colors">
                    <div class="flex items-center justify-between mb-3">
                        <div>
                            <h4 class="font-bold text-white">Premium Account</h4>
                            <p class="text-xs text-slate-400">1% daily profit</p>
                        </div>
                        <div class="text-right">
                            <p class="text-lg font-bold text-cyan-400">$500</p>
                            <p class="text-xs text-slate-400">$5/day</p>
                        </div>
                    </div>
                    <button onclick="upgradePackage('premium', 500)"
                        class="w-full bg-gradient-to-r from-cyan-600 to-blue-600 hover:from-cyan-500 hover:to-blue-500 text-white font-semibold py-2 px-4 rounded-lg text-sm transition-all duration-300">
                        Upgrade to Premium
                    </button>
                </div>

                <!-- Platinum Account -->
                <div
                    class="package-card bg-slate-700/30 border border-slate-600/50 rounded-lg p-4 hover:border-purple-500/30 transition-colors">
                    <div class="flex items-center justify-between mb-3">
                        <div>
                            <h4 class="font-bold text-white">Platinum Account</h4>
                            <p class="text-xs text-slate-400">1.25% daily profit</p>
                        </div>
                        <div class="text-right">
                            <p class="text-lg font-bold text-purple-400">$1,000</p>
                            <p class="text-xs text-slate-400">$12.5/day</p>
                        </div>
                    </div>
                    <button onclick="upgradePackage('platinum', 1000)"
                        class="w-full bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-500 hover:to-blue-500 text-white font-semibold py-2 px-4 rounded-lg text-sm transition-all duration-300">
                        Upgrade to Platinum
                    </button>
                </div>

                <!-- Elite Club Account -->
                <div
                    class="package-card bg-gradient-to-r from-yellow-500/10 to-orange-500/10 border border-yellow-500/30 rounded-lg p-4">
                    <div class="flex items-center justify-between mb-3">
                        <div>
                            <h4 class="font-bold text-white flex items-center gap-2">
                                Elite Club Account
                                <i class="fas fa-crown text-yellow-400 text-sm"></i>
                            </h4>
                            <p class="text-xs text-slate-400">1.5% daily profit</p>
                        </div>
                        <div class="text-right">
                            <p class="text-lg font-bold text-yellow-400">$2,500</p>
                            <p class="text-xs text-slate-400">$37.5/day</p>
                        </div>
                    </div>
                    <button onclick="upgradePackage('elite', 2500)"
                        class="w-full bg-gradient-to-r from-yellow-600 to-orange-600 hover:from-yellow-500 hover:to-orange-500 text-white font-semibold py-2 px-4 rounded-lg text-sm transition-all duration-300">
                        Upgrade to Elite Club
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        function goBack() {
            window.history.back();
        }

        function addFunds() {
            window.location.href = 'add-funds.html';
        }

        function transferFunds() {
            window.location.href = 'transfer.html';
        }

        function withdrawFunds() {
            window.location.href = 'withdraw.html';
        }

        function transferFromWithdrawal() {
            window.location.href = 'transfer.html?from=withdrawal';
        }

        function showTopupActions() {
            alert('Top-up Wallet Actions:\n• Add funds to your account\n• Transfer to other users\n• Upgrade packages');
        }

        function showWithdrawActions() {
            alert('Withdrawal Wallet Actions:\n• Withdraw to bank account\n• Withdraw to crypto wallet\n• View earnings breakdown');
        }

        function showTransferActions() {
            alert('Transfer funds from Top-up Wallet to other users');
        }

        function showEarningsBreakdown() {
            alert('Earnings Breakdown:\n\n• Daily Profit: $0.75 (from Standard package)\n• Network Income: $12.50 (from referrals)\n• Bonus Income: $5.25 (from promotions)\n\nTotal: $18.50 this period');
        }

        // Handle URL parameters for direct navigation
        function handleUrlParams() {
            const urlParams = new URLSearchParams(window.location.search);
            const tab = urlParams.get('tab');

            switch (tab) {
                case 'deposit':
                    // Trigger add funds action
                    setTimeout(() => {
                        addFunds();
                    }, 500);
                    break;
                case 'transfer':
                    // Trigger transfer action
                    setTimeout(() => {
                        transferFunds();
                    }, 500);
                    break;
                default:
                    // Stay on wallet page
                    break;
            }
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function () {
            handleUrlParams();
        });

        function showPackageUpgrade() {
            window.location.href = 'upgrade.html';
        }



        function viewAllTransactions() {
            window.location.href = 'transaction-history.html';
        }

        function showAction(action) {
            alert(`${action.charAt(0).toUpperCase() + action.slice(1)} functionality will be implemented here.`);
        }
    </script>

</body>

</html>