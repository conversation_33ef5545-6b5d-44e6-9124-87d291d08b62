<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header("Location: login.html");
    exit();
}

include 'dbcon.php';

$user_id = $_SESSION['user_id'];
$message = '';
$error = '';

// Get current test staking records for this user
$sql = "SELECT * FROM staking_records WHERE user_id = ? AND status = 'active' ORDER BY created_at DESC LIMIT 5";
$stmt = $conn->prepare($sql);
$stmt->bind_param("i", $user_id);
$stmt->execute();
$result = $stmt->get_result();
$staking_records = $result->fetch_all(MYSQLI_ASSOC);
$stmt->close();

if ($_POST && isset($_POST['staking_id'])) {
    $staking_id = intval($_POST['staking_id']);
    
    try {
        // Update the freeze_end_date to 1 week ago (already expired)
        $new_freeze_date = date('Y-m-d H:i:s', strtotime('-1 week'));
        
        $sql = "UPDATE staking_records SET freeze_end_date = ? WHERE id = ? AND user_id = ?";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("sii", $new_freeze_date, $staking_id, $user_id);
        
        if ($stmt->execute()) {
            $message = "✅ Test staking record updated successfully!<br>
                       🔓 New unlock date: " . date('M j, Y', strtotime($new_freeze_date)) . " (ALREADY EXPIRED)<br>
                       ✨ Status should now show 'Ready to Claim'<br><br>
                       🎯 <strong>Go to <a href='staking.php' class='text-blue-600 underline'>Staking Page</a> to see the updated status!</strong>";
        } else {
            $error = "Failed to update staking record: " . $stmt->error;
        }
        $stmt->close();
        
        // Refresh the records list
        $sql = "SELECT * FROM staking_records WHERE user_id = ? AND status = 'active' ORDER BY created_at DESC LIMIT 5";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("i", $user_id);
        $stmt->execute();
        $result = $stmt->get_result();
        $staking_records = $result->fetch_all(MYSQLI_ASSOC);
        $stmt->close();
        
    } catch (Exception $e) {
        $error = "Error: " . $e->getMessage();
    }
}

$conn->close();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fix Test Staking - NetVis</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100">
    <?php include 'user_nav.php'; ?>

    <main class="max-w-6xl mx-auto py-6 sm:px-6 lg:px-8">
        <div class="bg-white shadow-lg rounded-lg p-8">
            <div class="mb-6">
                <h2 class="text-3xl font-bold text-gray-800">Fix Test Staking Records</h2>
                <p class="text-gray-600 mt-2">Update existing staking records to have expired freeze periods for testing.</p>
            </div>

            <?php if ($message): ?>
                <div class="bg-green-50 border border-green-200 text-green-800 px-4 py-3 rounded-lg mb-6">
                    <?php echo $message; ?>
                </div>
            <?php endif; ?>

            <?php if ($error): ?>
                <div class="bg-red-50 border border-red-200 text-red-800 px-4 py-3 rounded-lg mb-6">
                    <?php echo $error; ?>
                </div>
            <?php endif; ?>

            <!-- Current Staking Records -->
            <div class="mb-8">
                <h3 class="text-xl font-semibold text-gray-800 mb-4">Your Active Staking Records</h3>
                
                <?php if (empty($staking_records)): ?>
                    <div class="bg-gray-50 p-6 rounded-lg text-center">
                        <p class="text-gray-500">No active staking records found.</p>
                        <a href="create_test_staking.php" class="text-blue-600 underline">Create a test staking record first</a>
                    </div>
                <?php else: ?>
                    <div class="overflow-x-auto border border-gray-200 rounded-lg">
                        <table class="min-w-full bg-white">
                            <thead class="bg-gray-100">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-600 uppercase">ID</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-600 uppercase">Amount</th>
                                    <th class="px-6 py-3 text-center text-xs font-medium text-gray-600 uppercase">Coins</th>
                                    <th class="px-6 py-3 text-center text-xs font-medium text-gray-600 uppercase">Created</th>
                                    <th class="px-6 py-3 text-center text-xs font-medium text-gray-600 uppercase">Unlock Date</th>
                                    <th class="px-6 py-3 text-center text-xs font-medium text-gray-600 uppercase">Status</th>
                                    <th class="px-6 py-3 text-center text-xs font-medium text-gray-600 uppercase">Action</th>
                                </tr>
                            </thead>
                            <tbody class="divide-y divide-gray-200">
                                <?php foreach ($staking_records as $record): ?>
                                    <?php
                                    $is_unlocked = strtotime($record['freeze_end_date']) <= time();
                                    $status_class = $is_unlocked ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800';
                                    $status_text = $is_unlocked ? 'Ready to Claim' : 'Locked';
                                    ?>
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                            #<?php echo $record['id']; ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right">
                                            $<?php echo number_format($record['amount_usd'], 2); ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-center">
                                            <?php echo number_format($record['coins_staked']); ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">
                                            <?php echo date('M j, Y', strtotime($record['created_at'])); ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">
                                            <?php echo date('M j, Y', strtotime($record['freeze_end_date'])); ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-center">
                                            <span class="px-2 py-1 text-xs font-medium rounded-full <?php echo $status_class; ?>">
                                                <?php echo $status_text; ?>
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-center">
                                            <?php if (!$is_unlocked): ?>
                                                <form method="POST" style="display: inline;">
                                                    <input type="hidden" name="staking_id" value="<?php echo $record['id']; ?>">
                                                    <button type="submit" class="px-3 py-1 bg-blue-600 text-white text-xs rounded-md hover:bg-blue-700">
                                                        Make Claimable
                                                    </button>
                                                </form>
                                            <?php else: ?>
                                                <span class="px-3 py-1 bg-green-100 text-green-800 text-xs rounded-md">
                                                    Ready!
                                                </span>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Instructions -->
            <div class="bg-blue-50 p-6 rounded-lg">
                <h4 class="text-blue-800 font-semibold mb-2">📋 Instructions:</h4>
                <ol class="text-blue-700 text-sm space-y-1 list-decimal list-inside">
                    <li>Find staking records that show "Locked" status</li>
                    <li>Click "Make Claimable" to update the unlock date to the past</li>
                    <li>Go to the <a href="staking.php" class="underline">Staking Page</a> to see the "Claim" button</li>
                    <li>Test the claim functionality and admin approval process</li>
                </ol>
            </div>

            <!-- Quick Links -->
            <div class="mt-6 flex space-x-4">
                <a href="staking.php" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
                    Go to Staking Page
                </a>
                <a href="admin_staking_claims.php" class="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700">
                    Admin Staking Claims
                </a>
                <a href="create_test_staking.php" class="bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700">
                    Create New Test Record
                </a>
            </div>
        </div>
    </main>
</body>
</html>
