<?php
include 'dbcon.php';

echo "<h2>🔍 ACTUAL Database Column Inspector</h2>";
echo "<p>Checking what columns actually exist in your live database vs schema.sql</p>";

// Function to show table structure
function showTableStructure($conn, $tableName) {
    echo "<h3>📋 Table: $tableName</h3>";
    
    $desc_query = "DESCRIBE $tableName";
    $desc_result = $conn->query($desc_query);
    
    if ($desc_result) {
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%; font-size: 0.9em;'>";
        echo "<tr style='background: #f3f4f6;'><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
        
        $columns = [];
        while ($row = $desc_result->fetch_assoc()) {
            $columns[] = $row['Field'];
            echo "<tr>";
            echo "<td><strong>" . $row['Field'] . "</strong></td>";
            echo "<td>" . $row['Type'] . "</td>";
            echo "<td>" . $row['Null'] . "</td>";
            echo "<td>" . $row['Key'] . "</td>";
            echo "<td>" . ($row['Default'] ?? 'NULL') . "</td>";
            echo "<td>" . ($row['Extra'] ?? '') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        return $columns;
    } else {
        echo "<p style='color: red;'>❌ Table '$tableName' not found or query failed: " . $conn->error . "</p>";
        return [];
    }
}

// Check all relevant tables
$tables = ['users', 'deposit_requests', 'withdrawal_requests'];

$all_columns = [];
foreach ($tables as $table) {
    $all_columns[$table] = showTableStructure($conn, $table);
}

// Show comparison with schema.sql expectations
echo "<div style='background: #fef3c7; border: 2px solid #f59e0b; border-radius: 12px; padding: 20px; margin: 20px 0;'>";
echo "<h3 style='color: #92400e; margin-top: 0;'>⚠️ Schema vs Reality Comparison</h3>";

echo "<h4 style='color: #b45309;'>Users Table:</h4>";
echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%; font-size: 0.9em;'>";
echo "<tr style='background: #f3f4f6;'><th>Expected (schema.sql)</th><th>Actual (your DB)</th><th>Status</th></tr>";

$expected_users = ['id', 'package', 'sponser_id', 'user_id', 'binanace_address', 'name', 'email', 'phone', 'password', 'deposit_wallet_balance', 'withdrawal_wallet_balance', 'total_earnings_received', 'is_admin', 'is_active', 'plan_status', 'created_at', 'updated_at', 'deleted_at'];

foreach ($expected_users as $col) {
    echo "<tr>";
    echo "<td>$col</td>";
    if (in_array($col, $all_columns['users'])) {
        echo "<td style='color: green;'>✅ $col</td>";
        echo "<td style='color: green;'>EXISTS</td>";
    } else {
        echo "<td style='color: red;'>❌ Missing</td>";
        echo "<td style='color: red;'>NOT FOUND</td>";
    }
    echo "</tr>";
}

// Show extra columns in actual DB
$extra_users = array_diff($all_columns['users'], $expected_users);
if (!empty($extra_users)) {
    echo "<tr style='background: #e0f2fe;'>";
    echo "<td colspan='2'><strong>Extra columns in your DB:</strong></td>";
    echo "<td>" . implode(', ', $extra_users) . "</td>";
    echo "</tr>";
}

echo "</table>";

echo "<h4 style='color: #b45309;'>Deposit Requests Table:</h4>";
echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%; font-size: 0.9em;'>";
echo "<tr style='background: #f3f4f6;'><th>Expected (schema.sql)</th><th>Actual (your DB)</th><th>Status</th></tr>";

$expected_deposits = ['id', 'user_id', 'amount', 'receipt_path', 'status', 'requested_at', 'reviewed_by', 'reviewed_at', 'admin_notes'];

foreach ($expected_deposits as $col) {
    echo "<tr>";
    echo "<td>$col</td>";
    if (in_array($col, $all_columns['deposit_requests'])) {
        echo "<td style='color: green;'>✅ $col</td>";
        echo "<td style='color: green;'>EXISTS</td>";
    } else {
        echo "<td style='color: red;'>❌ Missing</td>";
        echo "<td style='color: red;'>NOT FOUND</td>";
    }
    echo "</tr>";
}

// Show extra columns
$extra_deposits = array_diff($all_columns['deposit_requests'], $expected_deposits);
if (!empty($extra_deposits)) {
    echo "<tr style='background: #e0f2fe;'>";
    echo "<td colspan='2'><strong>Extra columns in your DB:</strong></td>";
    echo "<td>" . implode(', ', $extra_deposits) . "</td>";
    echo "</tr>";
}

echo "</table>";

echo "</div>";

// Generate correct queries based on actual columns
echo "<div style='background: #dcfce7; border: 2px solid #16a34a; border-radius: 12px; padding: 20px; margin: 20px 0;'>";
echo "<h3 style='color: #15803d; margin-top: 0;'>🔧 CORRECTED Queries for Your Actual Database</h3>";

// Find the correct date column for users
$user_date_column = 'id'; // fallback
if (in_array('created_at', $all_columns['users'])) {
    $user_date_column = 'created_at';
} elseif (in_array('reg_date', $all_columns['users'])) {
    $user_date_column = 'reg_date';
} elseif (in_array('updated_at', $all_columns['users'])) {
    $user_date_column = 'updated_at';
}

// Find the correct date column for deposits
$deposit_date_column = 'id'; // fallback
if (in_array('requested_at', $all_columns['deposit_requests'])) {
    $deposit_date_column = 'requested_at';
} elseif (in_array('created_at', $all_columns['deposit_requests'])) {
    $deposit_date_column = 'created_at';
} elseif (in_array('request_date', $all_columns['deposit_requests'])) {
    $deposit_date_column = 'request_date';
}

// Find the correct date column for withdrawals
$withdrawal_date_column = 'id'; // fallback
if (in_array('requested_at', $all_columns['withdrawal_requests'])) {
    $withdrawal_date_column = 'requested_at';
} elseif (in_array('created_at', $all_columns['withdrawal_requests'])) {
    $withdrawal_date_column = 'created_at';
} elseif (in_array('request_date', $all_columns['withdrawal_requests'])) {
    $withdrawal_date_column = 'request_date';
}

echo "<h4 style='color: #166534;'>📝 Working Queries for Your Database:</h4>";

echo "<div style='background: #f0fdf4; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
echo "<h5 style='color: #166534; margin-top: 0;'>1. Recent Users Query:</h5>";
echo "<code style='display: block; background: white; padding: 10px; border-radius: 4px; color: #166534;'>";
echo "SELECT id, name, user_id, package, $user_date_column FROM users WHERE is_admin = 0 ORDER BY $user_date_column DESC LIMIT 5";
echo "</code>";
echo "</div>";

echo "<div style='background: #f0fdf4; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
echo "<h5 style='color: #166534; margin-top: 0;'>2. Recent Deposits Query:</h5>";
echo "<code style='display: block; background: white; padding: 10px; border-radius: 4px; color: #166534;'>";
echo "SELECT dr.id, dr.user_id, dr.amount, dr.status, dr.$deposit_date_column, u.name, u.user_id as public_user_id FROM deposit_requests dr JOIN users u ON dr.user_id = u.id ORDER BY dr.$deposit_date_column DESC LIMIT 5";
echo "</code>";
echo "</div>";

echo "<div style='background: #f0fdf4; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
echo "<h5 style='color: #166534; margin-top: 0;'>3. Recent Withdrawals Query:</h5>";
echo "<code style='display: block; background: white; padding: 10px; border-radius: 4px; color: #166534;'>";
echo "SELECT wr.id, wr.user_id, wr.amount, wr.status, wr.$withdrawal_date_column, u.name, u.user_id as public_user_id FROM withdrawal_requests wr JOIN users u ON wr.user_id = u.id ORDER BY wr.$withdrawal_date_column DESC LIMIT 5";
echo "</code>";
echo "</div>";

echo "</div>";

echo "<p style='text-align: center; margin-top: 30px;'>";
echo "<a href='admin_dashboard.php' style='background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%); color: white; padding: 15px 30px; text-decoration: none; border-radius: 12px; font-weight: 600; box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);'>🎯 Test Dashboard with Correct Columns</a>";
echo "</p>";

$conn->close();
?>

<style>
body { 
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; 
    margin: 20px; 
    background: #f8fafc;
    line-height: 1.6;
}
h2, h3, h4, h5 { color: #1f2937; }
table { 
    font-size: 0.9em; 
    max-width: 100%; 
    overflow-x: auto;
}
th, td { 
    padding: 6px 10px; 
    text-align: left; 
    border: 1px solid #e5e7eb;
}
th { 
    background: #f9fafb; 
    font-weight: 600;
}
code { 
    background: #f1f5f9; 
    padding: 2px 6px; 
    border-radius: 4px; 
    font-family: monospace;
    font-size: 0.9em;
}
</style>
