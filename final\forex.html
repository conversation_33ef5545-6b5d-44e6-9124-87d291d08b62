<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Forex Cross Rates - CryptoInvest Pro</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <style>
        body {
            font-family: 'Inter', sans-serif;
        }

        .glassmorphism {
            backdrop-filter: blur(15px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }

        /* Blue Cyan Theme */
        .theme-dark-premium {
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
            color: white;
        }

        .theme-dark-premium .card {
            background: rgba(30, 41, 59, 0.8);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(6, 182, 212, 0.2);
        }

        .theme-dark-premium .accent {
            color: #06b6d4;
        }

        .theme-dark-premium .secondary {
            color: #3b82f6;
        }

        /* Professional Header Enhancements */
        header {
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
        }

        .logo-glow {
            position: relative;
            transition: all 0.3s ease;
        }

        .logo-glow::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            border-radius: 14px;
            background: linear-gradient(45deg, transparent, #06b6d4, transparent);
            opacity: 0.1;
            animation: logoRotate 4s linear infinite;
            z-index: -1;
        }

        .logo-glow:hover::before {
            opacity: 0.4;
        }

        @keyframes logoRotate {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }

        .logo-svg {
            transition: all 0.3s ease;
        }

        .logo-bg {
            fill: #06b6d4;
            transition: fill 0.3s ease;
        }

        .logo-symbol {
            fill: #ffffff;
            transition: fill 0.3s ease;
        }

        .widget-container {
            min-height: 500px;
            border-radius: 12px;
            overflow: hidden;
        }
    </style>
</head>

<body class="theme-dark-premium min-h-screen">
    <!-- Main Dashboard Container -->
    <div class="container mx-auto max-w-md min-h-screen bg-slate-900 flex flex-col">
        <!-- Header -->
        <header class="sticky top-0 z-50 backdrop-blur-xl bg-slate-900/95 border-b border-slate-700/50">
            <div class="container mx-auto max-w-md px-4 py-3">
                <div class="flex items-center justify-between">
                    <!-- Back Button & Title -->
                    <div class="flex items-center gap-3">
                        <button onclick="goBack()"
                            class="w-8 h-8 rounded-full bg-slate-800/50 flex items-center justify-center border border-slate-600 hover:bg-slate-700/50 transition-colors">
                            <i class="fas fa-arrow-left text-sm text-slate-300"></i>
                        </button>
                        <div>
                            <h1 class="text-lg font-bold text-white">Forex Trading</h1>
                            <p class="text-xs text-slate-400">Live market rates</p>
                        </div>
                    </div>

                    <!-- Right Actions -->
                    <div class="flex items-center gap-2">
                        <button
                            class="w-8 h-8 rounded-full bg-cyan-500/20 flex items-center justify-center border border-cyan-400">
                            <i class="fas fa-chart-line text-sm text-cyan-400"></i>
                        </button>
                    </div>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="container mx-auto max-w-md px-4 py-6 flex-grow overflow-y-auto">
            <!-- Page Header -->
            <div class="mb-6">
                <h2 class="text-2xl font-bold text-cyan-400 mb-2">Forex Cross Rates</h2>
                <p class="text-slate-400 text-sm">Real-time foreign exchange rates and currency pairs</p>
            </div>

            <!-- Forex Widget Container -->
            <div class="bg-slate-800/60 backdrop-blur-sm border border-slate-700/50 rounded-2xl p-4 mb-6">
                <div class="flex items-center justify-between mb-4">
                    <div class="flex items-center gap-3">
                        <div
                            class="w-10 h-10 bg-gradient-to-r from-cyan-600 to-blue-600 rounded-full flex items-center justify-center">
                            <i class="fas fa-chart-line text-lg text-white"></i>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold text-white">Live Forex Rates</h3>
                            <p class="text-slate-400 text-xs">Major currency pairs</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <p class="text-xs text-slate-400">Powered by</p>
                        <p class="text-xs font-semibold text-cyan-400">TradingView</p>
                    </div>
                </div>

                <!-- TradingView Widget -->
                <div class="widget-container bg-slate-900/50 rounded-xl overflow-hidden">
                    <iframe scrolling="no" allowtransparency="true" frameborder="0"
                        src="https://www.tradingview-widget.com/embed-widget/forex-cross-rates/?locale=en#%7B%22width%22%3A%22100%25%22%2C%22height%22%3A%22100%25%22%2C%22currencies%22%3A%5B%22EUR%22%2C%22USD%22%2C%22JPY%22%2C%22GBP%22%2C%22CHF%22%2C%22AUD%22%2C%22CAD%22%2C%22NZD%22%2C%22CNY%22%5D%2C%22colorTheme%22%3A%22dark%22%2C%22utm_source%22%3A%22cryptoapp.com%22%2C%22utm_medium%22%3A%22widget%22%2C%22utm_campaign%22%3A%22forex-cross-rates%22%2C%22page-uri%22%3A%22cryptoapp.com%2Fforex%22%7D"
                        title="forex cross-rates TradingView widget" lang="en"
                        style="user-select: none; box-sizing: border-box; display: block; height: 500px; width: 100%;">
                    </iframe>
                </div>
            </div>

            <!-- Additional Info Cards -->
            <div class="grid grid-cols-1 gap-4 mb-20">
                <div class="bg-slate-800/60 backdrop-blur-sm border border-slate-700/50 rounded-xl p-4 text-center">
                    <i class="fas fa-globe text-2xl text-cyan-400 mb-3"></i>
                    <h4 class="font-semibold text-white mb-2">Global Markets</h4>
                    <p class="text-slate-400 text-sm">Access major currency pairs from around the world</p>
                </div>
                <div class="bg-slate-800/60 backdrop-blur-sm border border-slate-700/50 rounded-xl p-4 text-center">
                    <i class="fas fa-clock text-2xl text-blue-400 mb-3"></i>
                    <h4 class="font-semibold text-white mb-2">Real-Time Data</h4>
                    <p class="text-slate-400 text-sm">Live forex rates updated in real-time</p>
                </div>
                <div class="bg-slate-800/60 backdrop-blur-sm border border-slate-700/50 rounded-xl p-4 text-center">
                    <i class="fas fa-chart-bar text-2xl text-cyan-400 mb-3"></i>
                    <h4 class="font-semibold text-white mb-2">Professional Tools</h4>
                    <p class="text-slate-400 text-sm">Advanced trading tools and analytics</p>
                </div>
            </div>
        </main>

        <!-- Sticky Bottom Navigation Bar -->
        <div class="sticky bottom-0 z-40 flex-shrink-0">
            <div class="bg-slate-800/95 backdrop-blur-sm mx-4 mb-4 rounded-2xl border border-slate-700">
                <div class="flex justify-around items-center h-16">
                    <a href="1.html"
                        class="flex flex-col items-center gap-1 text-slate-400 hover:text-cyan-400 transition-colors">
                        <i class="fas fa-home"></i>
                        <span class="text-xs">Home</span>
                    </a>
                    <a href="wallet.html"
                        class="flex flex-col items-center gap-1 text-slate-400 hover:text-cyan-400 transition-colors">
                        <i class="fas fa-wallet"></i>
                        <span class="text-xs">Wallet</span>
                    </a>
                    <a href="staking.html"
                        class="flex flex-col items-center gap-1 text-slate-400 hover:text-cyan-400 transition-colors">
                        <i class="fas fa-layer-group"></i>
                        <span class="text-xs">Stack</span>
                    </a>
                    <a href="team.html"
                        class="flex flex-col items-center gap-1 text-slate-400 hover:text-cyan-400 transition-colors">
                        <i class="fas fa-users"></i>
                        <span class="text-xs">Team</span>
                    </a>
                    <a href="profile.html"
                        class="flex flex-col items-center gap-1 text-slate-400 hover:text-cyan-400 transition-colors">
                        <i class="fas fa-user"></i>
                        <span class="text-xs">Profile</span>
                    </a>
                </div>
            </div>
        </div>

        <script>
            function goBack() {
                window.history.back();
            }
        </script>
    </div>
</body>

</html>