<?php
session_start();

if (!isset($_SESSION['user_id']) || !$_SESSION['is_admin']) {
    header('Location: login.php');
    exit();
}

include 'dbcon.php';

// Handle form submission for creating a user
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'create') {
    $name = $_POST['name'];
    $email = $_POST['email'];
    $phone = $_POST['phone'];
    $package = $_POST['package'];
    $sponser_user_code = $_POST['sponser_id'];
    $sponser_id = null;

    if (!empty($sponser_user_code)) {
        // Find sponsor by their user_id code to get their numeric id
        $stmt = $conn->prepare('SELECT id FROM users WHERE user_id = ?');
        $stmt->bind_param('s', $sponser_user_code);
        $stmt->execute();
        $result = $stmt->get_result();
        if ($result->num_rows > 0) {
            $sponsor = $result->fetch_assoc();
            $sponser_id = $sponsor['id'];
        }
        $stmt->close();
    }
    $binanace_address = $_POST['binanace_address'];
    $is_admin = isset($_POST['is_admin']) ? 1 : 0;
    $password = password_hash($_POST['password'], PASSWORD_DEFAULT);

    // Generate a unique user_id code
    $initials = strtoupper(substr(preg_replace('/\s+/', '', $name), 0, 2));
    $timestamp_part = substr(time(), -6);
    $user_id_code = $initials . $timestamp_part;

    // Ensure it's unique
    $is_unique = false;
    while (!$is_unique) {
        $check_stmt = $conn->prepare('SELECT id FROM users WHERE user_id = ?');
        $check_stmt->bind_param('s', $user_id_code);
        $check_stmt->execute();
        if ($check_stmt->get_result()->num_rows == 0) {
            $is_unique = true;
        } else {
            $user_id_code = $initials . substr(time(), -6) . rand(10, 99);
        }
        $check_stmt->close();
    }

    $stmt = $conn->prepare('INSERT INTO users (name, email, phone, password, package, sponser_id, binanace_address, user_id, is_admin, is_active) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 1)');
    $stmt->bind_param('sssssissi', $name, $email, $phone, $password, $package, $sponser_id, $binanace_address, $user_id_code, $is_admin);
    $stmt->execute();
    $new_user_id = $conn->insert_id;
    $stmt->close();

    // --- Level Commission Distribution Logic ---

    // 1. Get package amount and record initial investment in wallet
    $package_amount = 0;
    $pkg_stmt = $conn->prepare('SELECT package_amount FROM packages WHERE package_name = ?');
    $pkg_stmt->bind_param('s', $package);
    $pkg_stmt->execute();
    $pkg_result = $pkg_stmt->get_result();
    if ($pkg_row = $pkg_result->fetch_assoc()) {
        $package_amount = $pkg_row['package_amount'];
    }
    $pkg_stmt->close();

    if ($package_amount > 0) {
        // Record investment for the new user
        $invest_stmt = $conn->prepare("INSERT INTO wallet (user_id, amount, type, description) VALUES (?, ?, 'investment', ?)");
        $investment_description = 'Initial investment for package: ' . $package;
        $invest_stmt->bind_param('ids', $new_user_id, $package_amount, $investment_description);
        $invest_stmt->execute();
        $invest_stmt->close();

        // 2. Distribute level commissions
        $levels_result = $conn->query('SELECT level_number, distribution_percentage FROM levels WHERE is_active = 1 ORDER BY level_number ASC');
        $levels = [];
        while ($level_row = $levels_result->fetch_assoc()) {
            $levels[$level_row['level_number']] = $level_row['distribution_percentage'];
        }

        $current_sponsor_id = $sponser_id;
        for ($level = 1; $level <= 3; $level++) {
            if (!$current_sponsor_id || !isset($levels[$level])) {
                break;  // No more sponsors or no commission for this level
            }

            $commission_percentage = $levels[$level];
            $commission_amount = ($package_amount * $commission_percentage) / 100;

            if ($commission_amount > 0) {
                // Add commission to sponsor's wallet
                $commission_desc = 'Level ' . $level . ' commission from user ' . $user_id_code;
                $comm_stmt = $conn->prepare("INSERT INTO wallet (user_id, amount, type, description) VALUES (?, ?, 'level_commission', ?)");
                $comm_stmt->bind_param('ids', $current_sponsor_id, $commission_amount, $commission_desc);
                $comm_stmt->execute();
                $comm_stmt->close();

                // Update sponsor's main wallet balance
                $update_bal_stmt = $conn->prepare('UPDATE users SET wallet_balance = wallet_balance + ? WHERE id = ?');
                $update_bal_stmt->bind_param('di', $commission_amount, $current_sponsor_id);
                $update_bal_stmt->execute();
                $update_bal_stmt->close();
            }

            // Get the next sponsor up the chain
            $next_sponsor_stmt = $conn->prepare('SELECT sponser_id FROM users WHERE id = ?');
            $next_sponsor_stmt->bind_param('i', $current_sponsor_id);
            $next_sponsor_stmt->execute();
            $next_sponsor_result = $next_sponsor_stmt->get_result();
            if ($next_sponsor_row = $next_sponsor_result->fetch_assoc()) {
                $current_sponsor_id = $next_sponsor_row['sponser_id'];
            } else {
                $current_sponsor_id = null;  // End of the line
            }
            $next_sponsor_stmt->close();
        }
    }

    header('Location: admin.php');
    exit();
}

// Fetch packages for the dropdown
$packages_result = $conn->query('SELECT id, package_name, package_amount FROM packages WHERE is_active = 1 ORDER BY package_name');
$packages = [];
if ($packages_result && $packages_result->num_rows > 0) {
    while ($row = $packages_result->fetch_assoc()) {
        $packages[] = $row;
    }
}

$conn->close();
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin - Create User</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>

<body class="bg-gray-100">
    <?php include 'admin_nav.php'; ?>

    <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div class="bg-white shadow-md rounded-lg p-8">
            <h2 class="text-2xl font-bold mb-6">Create New User</h2>
            <form action="create_user.php" method="POST">
                <input type="hidden" name="action" value="create">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="name" class="block text-sm font-medium text-gray-700">Full Name</label>
                        <input type="text" name="name" id="name" required class="mt-1 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
                    </div>
                    <div>
                        <label for="email" class="block text-sm font-medium text-gray-700">Email Address</label>
                        <input type="email" name="email" id="email" required class="mt-1 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
                    </div>
                    <div>
                        <label for="phone" class="block text-sm font-medium text-gray-700">Phone Number</label>
                        <input type="text" name="phone" id="phone" class="mt-1 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
                    </div>
                    <div>
                        <label for="package" class="block text-sm font-medium text-gray-700">Package</label>
                        <select name="package" id="package" required class="mt-1 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
                            <option value="">Select a package</option>
                            <?php foreach ($packages as $pkg): ?>
                                <option value="<?php echo htmlspecialchars($pkg['package_name']); ?>">
                                    <?php echo htmlspecialchars($pkg['package_name']); ?> ($<?php echo htmlspecialchars(number_format($pkg['package_amount'], 2)); ?>)
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div>
                        <label for="sponser_id" class="block text-sm font-medium text-gray-700">Sponser ID</label>
                        <input type="text" name="sponser_id" id="sponser_id" class="mt-1 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
                    </div>
                    <div>
                        <label for="binanace_address" class="block text-sm font-medium text-gray-700">Binance Address</label>
                        <input type="text" name="binanace_address" id="binanace_address" class="mt-1 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
                    </div>
                    <div>
                        <label for="password" class="block text-sm font-medium text-gray-700">Password</label>
                        <input type="password" name="password" id="password" required class="mt-1 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
                    </div>
                    <div class="flex items-center">
                        <input type="checkbox" name="is_admin" id="is_admin" value="1" class="h-4 w-4 text-indigo-600 border-gray-300 rounded">
                        <label for="is_admin" class="ml-2 block text-sm text-gray-900">Make Admin</label>
                    </div>
                </div>
                <div class="mt-6 flex justify-between">
                    <button type="submit" class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">Create User</button>
                    <a href="admin.php" class="inline-flex justify-center py-2 px-4 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">Cancel</a>
                </div>
            </form>
        </div>
    </div>
    <script>
        window.addEventListener('DOMContentLoaded', (event) => {
            const urlParams = new URLSearchParams(window.location.search);
            const sponsorId = urlParams.get('sponser_id');
            if (sponsorId) {
                const sponsorInput = document.getElementById('sponser_id');
                if (sponsorInput) {
                    sponsorInput.value = sponsorId;
                }
            }
        });
    </script>
</body>

</html>