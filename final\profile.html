<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Profile - Crypto Wallet</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <style>
        body {
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
            min-height: 100vh;
        }

        .stats-card {
            background: rgba(30, 41, 59, 0.4);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(59, 130, 246, 0.2);
            border-radius: 0.75rem;
            padding: 1rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }

        .profile-card {
            background: rgba(15, 23, 42, 0.8);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(59, 130, 246, 0.3);
            border-radius: 0.75rem;
            padding: 1rem;
            transition: all 0.3s ease;
        }

        .profile-card:hover {
            border-color: rgba(6, 182, 212, 0.6);
            background: rgba(15, 23, 42, 0.9);
            transform: translateY(-2px);
        }

        .input-field {
            background: rgba(15, 23, 42, 0.8);
            border: 1px solid rgba(59, 130, 246, 0.3);
            border-radius: 0.5rem;
            padding: 0.75rem;
            color: white;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .input-field:focus {
            outline: none;
            border-color: rgba(6, 182, 212, 0.6);
            box-shadow: 0 0 0 3px rgba(6, 182, 212, 0.15);
            background: rgba(15, 23, 42, 0.9);
        }

        .input-field:hover {
            border-color: rgba(6, 182, 212, 0.4);
            background: rgba(15, 23, 42, 0.85);
        }

        .input-field::placeholder {
            color: rgba(148, 163, 184, 0.5);
        }

        .btn-primary {
            background: linear-gradient(135deg, #3b82f6, #06b6d4);
            border: none;
            border-radius: 0.5rem;
            padding: 0.75rem 1.5rem;
            color: white;
            font-weight: 600;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #2563eb, #0891b2);
            transform: translateY(-1px);
        }

        /* Bottom Navigation Styles */
        .accent {
            color: #06b6d4 !important;
        }

        .hover\:accent:hover {
            color: #06b6d4 !important;
        }
        }

        .stats-card:hover {
            transform: translateY(-5px);
            border-color: rgba(59, 130, 246, 0.4);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.4);
        }

        /* Profile Card */
        .profile-card {
            background: linear-gradient(135deg, rgba(30, 41, 59, 0.9) 0%, rgba(15, 23, 42, 0.9) 100%);
            backdrop-filter: blur(15px);
            border: 1px solid rgba(59, 130, 246, 0.2);
            border-radius: 1.5rem;
            padding: 2rem;
            position: relative;
            overflow: hidden;
            transition: all 0.4s ease;
        }

        .profile-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #3b82f6, #06b6d4);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .profile-card:hover::before {
            opacity: 1;
        }

        .profile-card:hover {
            transform: translateY(-8px);
            border-color: rgba(59, 130, 246, 0.4);
            box-shadow: 0 20px 50px rgba(0, 0, 0, 0.5);
        }

        /* Menu Item */
        .menu-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 1rem;
            background: rgba(30, 41, 59, 0.3);
            border: 1px solid rgba(59, 130, 246, 0.1);
            border-radius: 0.75rem;
            transition: all 0.3s ease;
            cursor: pointer;
            margin-bottom: 0.75rem;
        }

        .menu-item:hover {
            background: rgba(30, 41, 59, 0.5);
            border-color: rgba(59, 130, 246, 0.2);
            transform: translateX(4px);
        }

        /* Toggle Switch */
        .toggle-switch {
            position: relative;
            width: 44px;
            height: 24px;
            background: #374151;
            border-radius: 12px;
            transition: background 0.3s ease;
            cursor: pointer;
        }

        .toggle-switch.active {
            background: linear-gradient(135deg, #3b82f6, #06b6d4);
        }

        .toggle-switch::after {
            content: '';
            position: absolute;
            top: 2px;
            left: 2px;
            width: 20px;
            height: 20px;
            background: white;
            border-radius: 50%;
            transition: transform 0.3s ease;
        }

        .toggle-switch.active::after {
            transform: translateX(20px);
        }
    </style>
</head>

<body class="text-white">
    <div class="min-h-screen flex flex-col">

        <!-- Header -->
        <div class="sticky top-0 z-50 bg-slate-800/95 backdrop-blur-sm border-b border-slate-700">
            <div class="flex items-center justify-between px-6 py-4">
                <div class="flex items-center gap-4">
                    <button onclick="goBack()"
                        class="w-10 h-10 rounded-full bg-slate-700 hover:bg-slate-600 flex items-center justify-center transition-colors">
                        <i class="fas fa-arrow-left text-slate-300"></i>
                    </button>
                    <div>
                        <h1 class="text-xl font-bold text-white">Profile</h1>
                        <p class="text-sm text-slate-400">Account & Settings</p>
                    </div>
                </div>
                <div class="flex items-center gap-2">
                    <button onclick="showSettings()"
                        class="w-10 h-10 rounded-full bg-slate-700 hover:bg-slate-600 flex items-center justify-center transition-colors">
                        <i class="fas fa-cog text-slate-300"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Main content with scrolling -->
        <div class="flex-grow overflow-y-auto px-6 py-6">

            <!-- Profile Header Card -->
            <div class="profile-card mb-6">
                <div class="text-center">
                    <!-- Profile Avatar -->
                    <div class="relative mx-auto w-20 h-20 mb-4">
                        <div
                            class="w-full h-full rounded-full bg-gradient-to-br from-blue-500 to-cyan-500 flex items-center justify-center text-white font-bold text-2xl border-4 border-slate-700">
                            JD
                        </div>
                        <div
                            class="absolute -bottom-1 -right-1 w-6 h-6 bg-green-500 rounded-full border-2 border-slate-900 flex items-center justify-center">
                            <i class="fas fa-check text-white text-xs"></i>
                        </div>
                    </div>

                    <!-- Profile Info -->
                    <h2 class="text-xl font-bold text-white mb-1">John Doe</h2>
                    <p class="text-sm text-slate-400 mb-3"><EMAIL></p>

                    <!-- Membership Badge -->
                    <div
                        class="inline-flex items-center gap-2 bg-gradient-to-r from-blue-500/10 to-cyan-500/10 px-3 py-1.5 rounded-full border border-blue-500/20">
                        <i class="fas fa-crown text-blue-400 text-sm"></i>
                        <span class="text-sm text-blue-400 font-semibold">Premium Member</span>
                    </div>
                </div>
            </div>

            <!-- Account Stats -->
            <div class="grid grid-cols-3 gap-4 mb-6">
                <div class="stats-card text-center">
                    <p class="text-xs text-slate-400 mb-1">Member Since</p>
                    <p class="text-lg font-bold text-white">Jan 2024</p>
                </div>
                <div class="stats-card text-center">
                    <p class="text-xs text-slate-400 mb-1">Total Earned</p>
                    <p class="text-lg font-bold text-cyan-400">$2,450</p>
                </div>
                <div class="stats-card text-center">
                    <p class="text-xs text-slate-400 mb-1">Referrals</p>
                    <p class="text-lg font-bold text-blue-400">24</p>
                </div>
            </div>

            <!-- Account Settings -->
            <div class="stats-card mb-6">
                <h3 class="font-bold text-white mb-4">Account Settings</h3>

                <div class="space-y-3">
                    <!-- Personal Information -->
                    <div class="menu-item" onclick="window.location.href='profile-personal.html'">
                        <div class="flex items-center gap-3">
                            <i class="fas fa-user text-blue-400"></i>
                            <span class="text-sm text-slate-300">Personal Information</span>
                        </div>
                        <i class="fas fa-chevron-right text-slate-400 text-sm"></i>
                    </div>

                    <!-- Security Settings -->
                    <div class="menu-item" onclick="window.location.href='profile-security.html'">
                        <div class="flex items-center gap-3">
                            <i class="fas fa-shield-alt text-green-400"></i>
                            <span class="text-sm text-slate-300">Security</span>
                        </div>
                        <i class="fas fa-chevron-right text-slate-400 text-sm"></i>
                    </div>

                    <!-- Payment Methods -->
                    <div class="menu-item" onclick="window.location.href='profile-payment.html'">
                        <div class="flex items-center gap-3">
                            <i class="fas fa-wallet text-cyan-400"></i>
                            <span class="text-sm text-slate-300">Payment Methods</span>
                        </div>
                        <i class="fas fa-chevron-right text-slate-400 text-sm"></i>
                    </div>
                </div>
            </div>

            <!-- Support & Help -->
            <div class="stats-card mb-6">
                <h3 class="font-bold text-white mb-4">Support & Help</h3>

                <div class="space-y-3">
                    <!-- Help Center -->
                    <div class="menu-item" onclick="window.location.href='help-center.html'">
                        <div class="flex items-center gap-3">
                            <i class="fas fa-question-circle text-blue-400"></i>
                            <span class="text-sm text-slate-300">Help Center</span>
                        </div>
                        <i class="fas fa-chevron-right text-slate-400 text-sm"></i>
                    </div>

                    <!-- Contact Support -->
                    <div class="menu-item" onclick="window.location.href='contact-support.html'">
                        <div class="flex items-center gap-3">
                            <i class="fas fa-headset text-cyan-400"></i>
                            <span class="text-sm text-slate-300">Contact Support</span>
                        </div>
                        <i class="fas fa-chevron-right text-slate-400 text-sm"></i>
                    </div>

                    <!-- Terms & Privacy -->
                    <div class="menu-item" onclick="window.location.href='terms-privacy.html'">
                        <div class="flex items-center gap-3">
                            <i class="fas fa-file-contract text-blue-400"></i>
                            <span class="text-sm text-slate-300">Terms & Privacy</span>
                        </div>
                        <i class="fas fa-chevron-right text-slate-400 text-sm"></i>
                    </div>

                    <!-- About -->
                    <div class="menu-item" onclick="window.location.href='about-app.html'">
                        <div class="flex items-center gap-3">
                            <i class="fas fa-info-circle text-cyan-400"></i>
                            <span class="text-sm text-slate-300">About Crypto App</span>
                        </div>
                        <i class="fas fa-chevron-right text-slate-400 text-sm"></i>
                    </div>
                </div>
            </div>

            <!-- Logout Button -->
            <div class="stats-card mb-6">
                <button onclick="logout()"
                    class="w-full flex items-center justify-center gap-3 p-4 bg-red-500/10 border border-red-500/20 rounded-lg text-red-400 hover:bg-red-500/20 transition-colors">
                    <i class="fas fa-sign-out-alt"></i>
                    <span class="font-semibold">Logout</span>
                </button>
            </div>
        </div>

        <!-- Sticky Bottom Navigation Bar -->
        <div class="sticky bottom-0 z-40 flex-shrink-0">
            <div class="bg-slate-800/95 backdrop-blur-sm mx-4 mb-4 rounded-2xl border border-slate-700">
                <div class="flex justify-around items-center h-16">
                    <a href="1.html" class="flex flex-col items-center gap-1 text-slate-400 hover:accent">
                        <i class="fas fa-home"></i>
                        <span class="text-xs">Home</span>
                    </a>
                    <a href="wallet.html" class="flex flex-col items-center gap-1 text-slate-400 hover:accent">
                        <i class="fas fa-wallet"></i>
                        <span class="text-xs">Wallet</span>
                    </a>
                    <a href="team.html" class="flex flex-col items-center gap-1 text-slate-400 hover:accent">
                        <i class="fas fa-users"></i>
                        <span class="text-xs">Team</span>
                    </a>
                    <a href="profile.html" class="flex flex-col items-center gap-1 accent">
                        <i class="fas fa-user"></i>
                        <span class="text-xs">Profile</span>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script>
        function goBack() {
            window.history.back();
        }

        function showSetting(type) {
            alert(`${type.charAt(0).toUpperCase() + type.slice(1)} settings will be implemented here.`);
        }

        function showHelp(type) {
            alert(`${type.charAt(0).toUpperCase() + type.slice(1)} section will be implemented here.`);
        }

        function toggleNotifications(element) {
            element.classList.toggle('active');
        }

        function toggleDarkMode(element) {
            element.classList.toggle('active');
        }

        function logout() {
            if (confirm('Are you sure you want to logout?')) {
                alert('Logout functionality will be implemented here.');
            }
        }
    </script>

</body>

</html>