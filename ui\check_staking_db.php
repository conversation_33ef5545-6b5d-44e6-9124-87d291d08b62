<?php
session_start();

if (!isset($_SESSION['user_id'])) {
    header("Location: login.php");
    exit();
}

include 'dbcon.php';

echo "<h2>Staking Database Check</h2>";

// Check table structure
echo "<h3>1. Table Structure:</h3>";
$describe = "DESCRIBE staking_records";
$result = $conn->query($describe);

if ($result) {
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";

    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $row['Field'] . "</td>";
        echo "<td>" . $row['Type'] . "</td>";
        echo "<td>" . $row['Null'] . "</td>";
        echo "<td>" . $row['Key'] . "</td>";
        echo "<td>" . $row['Default'] . "</td>";
        echo "<td>" . $row['Extra'] . "</td>";
        echo "</tr>";
    }
    echo "</table><br>";
}

// Check current records
echo "<h3>2. Current Staking Records:</h3>";
$user_id = $_SESSION['user_id'];
$records_sql = "SELECT id, user_id, coins_staked, amount_usd, stake_type, freeze_end_date, status, created_at FROM staking_records WHERE user_id = ? ORDER BY created_at DESC";
$stmt = $conn->prepare($records_sql);
$stmt->bind_param("i", $user_id);
$stmt->execute();
$records_result = $stmt->get_result();

if ($records_result->num_rows > 0) {
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>ID</th><th>Coins</th><th>Amount</th><th>Type</th><th>Freeze End</th><th>Status</th><th>Is Unlocked?</th><th>Created</th></tr>";

    while ($record = $records_result->fetch_assoc()) {
        $is_unlocked = strtotime($record['freeze_end_date']) <= time();
        echo "<tr>";
        echo "<td>" . $record['id'] . "</td>";
        echo "<td>" . $record['coins_staked'] . "</td>";
        echo "<td>$" . $record['amount_usd'] . "</td>";
        echo "<td>" . $record['stake_type'] . "</td>";
        echo "<td>" . $record['freeze_end_date'] . "</td>";
        echo "<td><strong>" . $record['status'] . "</strong></td>";
        echo "<td>" . ($is_unlocked ? '<span style="color: green;">YES</span>' : '<span style="color: red;">NO</span>') . "</td>";
        echo "<td>" . $record['created_at'] . "</td>";
        echo "</tr>";
    }
    echo "</table><br>";
} else {
    echo "No staking records found.<br><br>";
}

// Check if claim_pending status exists
echo "<h3>3. Status ENUM Check:</h3>";
$enum_sql = "SHOW COLUMNS FROM staking_records LIKE 'status'";
$enum_result = $conn->query($enum_sql);
if ($enum_result) {
    $enum_row = $enum_result->fetch_assoc();
    echo "<strong>Status ENUM values:</strong> " . $enum_row['Type'] . "<br>";

    if (strpos($enum_row['Type'], 'claim_pending') !== false) {
        echo "<span style='color: green;'>✅ claim_pending status is available</span><br>";
    } else {
        echo "<span style='color: red;'>❌ claim_pending status is MISSING</span><br>";
        echo "<strong>Need to run:</strong> <code>ALTER TABLE staking_records MODIFY status enum('active','completed','cancelled','claim_pending') NOT NULL DEFAULT 'active';</code><br>";
    }
}

echo "<br><h3>4. Current Time Check:</h3>";
echo "Current server time: " . date('Y-m-d H:i:s') . "<br>";
echo "Current timestamp: " . time() . "<br>";

$stmt->close();
$conn->close();
?>

<br><br>
<a href="staking.php">← Back to Staking Page</a> |
<a href="fix_test_staking.php">Fix Test Records</a>