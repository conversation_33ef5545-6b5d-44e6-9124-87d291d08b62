<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Finance Dashboard</title>

    <!-- Tailwind CSS for styling -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Google Fonts: Inter -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap" rel="stylesheet">

    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css"
        xintegrity="sha512-SnH5WK+bZxgPHs44uWIX+LLJAJ9/2PkPKZ5QiAj6Ta86w+fsb2TkcmfRyVX3pBnMFcV7oQPJkl9QevSCWr3W6A=="
        crossorigin="anonymous" referrerpolicy="no-referrer" />

    <style>
        body {
            font-family: 'Inter', sans-serif;
            background: #020617;
            /* Deep slate background */
        }

        .premium-theme {
            background: #0f172a;
            /* Dark blue-slate background */
            color: #e2e8f0;
            position: relative;
            overflow: hidden;
            /* To contain pseudo-elements */
        }

        /* Aurora background effect */
        .premium-theme::before {
            content: '';
            position: absolute;
            top: -20%;
            left: -20%;
            width: 60%;
            height: 60%;
            background: radial-gradient(circle, rgba(29, 78, 216, 0.15), transparent 70%);
            filter: blur(100px);
            z-index: 0;
            animation: move-aurora 20s infinite alternate;
        }

        .premium-theme::after {
            content: '';
            position: absolute;
            bottom: -20%;
            right: -20%;
            width: 60%;
            height: 60%;
            background: radial-gradient(circle, rgba(13, 148, 136, 0.15), transparent 70%);
            filter: blur(100px);
            z-index: 0;
            animation: move-aurora-2 25s infinite alternate;
        }

        @keyframes move-aurora {
            from {
                transform: translate(0, 0);
            }

            to {
                transform: translate(100px, 50px);
            }
        }

        @keyframes move-aurora-2 {
            from {
                transform: translate(0, 0);
            }

            to {
                transform: translate(-100px, -50px);
            }
        }


        .glass-card {
            background: rgba(30, 41, 59, 0.5);
            /* Semi-transparent background */
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid rgba(59, 130, 246, 0.1);
            border-radius: 1.5rem;
            /* Softer, larger radius */
            padding: 1.25rem;
            transition: all 0.3s ease-in-out;
            position: relative;
            z-index: 1;
        }

        .glass-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            border-color: rgba(59, 130, 246, 0.3);
        }

        .text-gradient-accent {
            background: linear-gradient(90deg, #60a5fa, #3b82f6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .text-gradient-secondary {
            background: linear-gradient(90deg, #2dd4bf, #0d9488);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        /* Custom scrollbar */
        ::-webkit-scrollbar {
            width: 4px;
        }

        ::-webkit-scrollbar-track {
            background: transparent;
        }

        ::-webkit-scrollbar-thumb {
            background: #3b82f6;
            border-radius: 10px;
        }

        /* Bottom Nav active state */
        .bottom-nav a.active {
            color: #3b82f6;
        }

        .bottom-nav a.active .nav-icon {
            background: rgba(59, 130, 246, 0.1);
            border-radius: 50%;
        }
    </style>
</head>

<body class="flex justify-center items-center min-h-screen p-4">

    <!-- Mobile phone container -->
    <div
        class="w-full max-w-sm h-[850px] bg-slate-900 rounded-[40px] shadow-2xl overflow-hidden border-4 border-gray-800 flex flex-col premium-theme">

        <!-- Phone Notch -->
        <div class="flex-shrink-0 px-6 pt-4 relative z-10">
            <div class="relative h-7">
                <div class="absolute top-0 left-1/2 -translate-x-1/2 w-24 h-5 bg-gray-800 rounded-b-xl"></div>
            </div>
        </div>

        <!-- Top Navigation Bar -->
        <header class="flex-shrink-0 flex items-center justify-between px-6 py-4 relative z-10">
            <!-- Company Logo -->
            <div class="flex items-center gap-3">
                <i class="fab fa-bitcoin fa-2x text-teal-400"></i>
                <span class="font-extrabold text-white text-xl tracking-tight">CryptoApp</span>
            </div>
            <!-- User Avatar -->
            <div
                class="w-11 h-11 rounded-full bg-slate-700/50 flex items-center justify-center border-2 border-slate-600 p-0.5">
                <img src="https://placehold.co/40x40/3b82f6/ffffff?text=JD" class="rounded-full" alt="User Avatar" />
            </div>
        </header>

        <!-- Main content with scrolling -->
        <main class="flex-grow overflow-y-auto px-6 py-4 space-y-5 relative z-10">

            <!-- Offer Message -->
            <div class="glass-card text-center">
                <p class="font-semibold text-gradient-accent animate-pulse">
                    LIMITED TIME OFFER: Get 20% Bonus!
                </p>
            </div>

            <!-- Stats Grid -->
            <div class="grid grid-cols-2 gap-5">
                <div class="glass-card text-center">
                    <p class="text-sm font-medium text-slate-400">Total Balance</p>
                    <p class="text-3xl font-bold text-white mt-2">$100.00</p>
                </div>
                <div class="glass-card text-center">
                    <p class="text-sm font-medium text-slate-400">Today's P/L</p>
                    <p class="text-3xl font-bold text-gradient-secondary mt-2">$0.75</p>
                </div>
                <div class="glass-card col-span-2 text-center">
                    <p class="text-sm font-medium text-slate-400">Total Profit</p>
                    <p class="text-4xl font-extrabold text-gradient-secondary mt-2">$110.75</p>
                </div>
            </div>

            <!-- Stacking Info -->
            <div class="glass-card">
                <div class="flex justify-between items-center">
                    <div>
                        <p class="text-sm font-medium text-slate-400">Stacking Value</p>
                        <p class="text-2xl font-bold text-white">$52.00</p>
                    </div>
                    <div class="text-right">
                        <p class="text-sm font-medium text-slate-400">BTC Rate</p>
                        <p class="text-lg font-semibold text-slate-300">1 ≈ $0.45</p>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="grid grid-cols-4 gap-3 text-center">
                <a href="#"
                    class="flex flex-col items-center gap-2 p-3 rounded-2xl hover:bg-slate-700/50 transition-all">
                    <div class="w-14 h-14 glass-card !p-0 flex items-center justify-center !rounded-full">
                        <i class="fas fa-download fa-lg text-blue-400"></i>
                    </div>
                    <span class="font-semibold text-xs text-white">Deposit</span>
                </a>
                <a href="#"
                    class="flex flex-col items-center gap-2 p-3 rounded-2xl hover:bg-slate-700/50 transition-all">
                    <div class="w-14 h-14 glass-card !p-0 flex items-center justify-center !rounded-full">
                        <i class="fas fa-plus fa-lg text-blue-400"></i>
                    </div>
                    <span class="font-semibold text-xs text-white">Topup</span>
                </a>
                <a href="#"
                    class="flex flex-col items-center gap-2 p-3 rounded-2xl hover:bg-slate-700/50 transition-all">
                    <div class="w-14 h-14 glass-card !p-0 flex items-center justify-center !rounded-full">
                        <i class="fas fa-exchange-alt fa-lg text-blue-400"></i>
                    </div>
                    <span class="font-semibold text-xs text-white">Transfer</span>
                </a>
                <a href="#"
                    class="flex flex-col items-center gap-2 p-3 rounded-2xl hover:bg-slate-700/50 transition-all">
                    <div class="w-14 h-14 glass-card !p-0 flex items-center justify-center !rounded-full">
                        <i class="fas fa-layer-group fa-lg text-teal-400"></i>
                    </div>
                    <span class="font-semibold text-xs text-white">Stack</span>
                </a>
            </div>

            <!-- Recent Transactions -->
            <div class="glass-card">
                <h3 class="font-bold text-white text-lg mb-4">Recent Transactions</h3>
                <ul class="space-y-4">
                    <li class="flex justify-between items-center">
                        <div class="flex items-center gap-4">
                            <div class="w-11 h-11 rounded-full bg-green-500/10 flex items-center justify-center">
                                <i class="fas fa-arrow-down text-green-400"></i>
                            </div>
                            <div>
                                <p class="font-semibold text-white">Deposit</p>
                                <p class="text-sm text-slate-400">July 03, 2025</p>
                            </div>
                        </div>
                        <p class="font-bold text-green-400 text-lg">+$50.00</p>
                    </li>
                    <li class="flex justify-between items-center">
                        <div class="flex items-center gap-4">
                            <div class="w-11 h-11 rounded-full bg-red-500/10 flex items-center justify-center">
                                <i class="fas fa-arrow-up text-red-400"></i>
                            </div>
                            <div>
                                <p class="font-semibold text-white">Withdrawal</p>
                                <p class="text-sm text-slate-400">July 01, 2025</p>
                            </div>
                        </div>
                        <p class="font-bold text-red-400 text-lg">-$25.50</p>
                    </li>
                    <li class="flex justify-between items-center">
                        <div class="flex items-center gap-4">
                            <div class="w-11 h-11 rounded-full bg-sky-500/10 flex items-center justify-center">
                                <i class="fas fa-sync-alt text-sky-400"></i>
                            </div>
                            <div>
                                <p class="font-semibold text-white">Staking Reward</p>
                                <p class="text-sm text-slate-400">June 30, 2025</p>
                            </div>
                        </div>
                        <p class="font-bold text-sky-400 text-lg">+$2.00</p>
                    </li>
                </ul>
            </div>
        </main>

        <!-- Sticky Bottom Navigation Bar -->
        <footer class="flex-shrink-0 relative z-10">
            <div class="bg-slate-800/50 backdrop-blur-xl mx-4 mb-4 rounded-2xl border border-slate-700">
                <div class="flex justify-around items-center h-20 bottom-nav">
                    <a href="#" class="flex flex-col items-center gap-1.5 transition-colors active">
                        <div class="w-12 h-12 flex items-center justify-center nav-icon transition-all">
                            <i class="fas fa-home fa-lg"></i>
                        </div>
                        <span class="text-xs font-bold">Home</span>
                    </a>
                    <a href="#"
                        class="flex flex-col items-center gap-1.5 text-slate-400 hover:text-white transition-colors">
                        <div class="w-12 h-12 flex items-center justify-center nav-icon transition-all">
                            <i class="fas fa-wallet fa-lg"></i>
                        </div>
                        <span class="text-xs font-bold">Wallet</span>
                    </a>
                    <a href="#"
                        class="flex flex-col items-center gap-1.5 text-slate-400 hover:text-white transition-colors">
                        <div class="w-12 h-12 flex items-center justify-center nav-icon transition-all">
                            <i class="fas fa-users fa-lg"></i>
                        </div>
                        <span class="text-xs font-bold">Team</span>
                    </a>
                    <a href="#"
                        class="flex flex-col items-center gap-1.5 text-slate-400 hover:text-white transition-colors">
                        <div class="w-12 h-12 flex items-center justify-center nav-icon transition-all">
                            <i class="fas fa-user fa-lg"></i>
                        </div>
                        <span class="text-xs font-bold">Profile</span>
                    </a>
                </div>
            </div>
        </footer>
    </div>

</body>

</html>