<?php
session_start();

if (!isset($_SESSION['user_id']) || !$_SESSION['is_admin']) {
    header('Location: login.html');
    exit();
}

include 'dbcon.php';

$package_id = $_GET['id'] ?? null;

if (!$package_id) {
    header('Location: package_manager.php');
    exit();
}

// Fetch package details
$stmt = $conn->prepare('SELECT * FROM packages WHERE id = ? AND deleted_at IS NULL');
$stmt->bind_param('i', $package_id);
$stmt->execute();
$result = $stmt->get_result();
$package = $result->fetch_assoc();
$stmt->close();

if (!$package) {
    echo 'Package not found.';
    exit();
}

// Handle form submission for update
if ($_SERVER['REQUEST_METHOD'] === 'POST' && $_POST['action'] === 'update') {
    $package_name = $_POST['package_name'];
    $package_amount = $_POST['package_amount'];
    $profit_percentage = $_POST['profit_percentage'];

    $stmt = $conn->prepare('UPDATE packages SET package_name = ?, package_amount = ?, profit_percentage = ? WHERE id = ?');
    $stmt->bind_param('sddi', $package_name, $package_amount, $profit_percentage, $package_id);
    $stmt->execute();
    $stmt->close();

    header('Location: package_manager.php');
    exit();
}

$conn->close();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Edit Package</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100">
    <nav class="bg-white shadow-md">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex">
                    <div class="flex-shrink-0 flex items-center">
                        <a href="admin.php" class="text-xl font-bold">Crypto Admin</a>
                    </div>
                </div>
                <div class="flex items-center">
                    <a href="admin.php" class="text-gray-700 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium">User Management</a>
                    <a href="package_manager.php" class="text-gray-700 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium">Package Master</a>
                    <a href="logout.php" class="text-gray-700 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium">Logout</a>
                </div>
            </div>
        </div>
    </nav>

    <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div class="bg-white shadow-md rounded-lg p-8">
            <h2 class="text-2xl font-bold mb-6">Edit Package</h2>
            <form action="edit_package.php?id=<?php echo htmlspecialchars($package['id']); ?>" method="POST">
                <input type="hidden" name="action" value="update">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                        <label for="package_name" class="block text-sm font-medium text-gray-700">Package Name</label>
                        <input type="text" name="package_name" id="package_name" value="<?php echo htmlspecialchars($package['package_name']); ?>" required class="mt-1 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
                    </div>
                    <div>
                        <label for="package_amount" class="block text-sm font-medium text-gray-700">Amount ($)</label>
                        <input type="number" name="package_amount" id="package_amount" value="<?php echo htmlspecialchars($package['package_amount']); ?>" step="0.01" required class="mt-1 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
                    </div>
                    <div>
                        <label for="profit_percentage" class="block text-sm font-medium text-gray-700">Profit (%)</label>
                        <input type="number" name="profit_percentage" id="profit_percentage" value="<?php echo htmlspecialchars($package['profit_percentage']); ?>" step="0.01" required class="mt-1 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
                    </div>
                </div>
                <div class="mt-6 flex justify-between">
                    <button type="submit" class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        Save Changes
                    </button>
                    <a href="package_manager.php" class="inline-flex justify-center py-2 px-4 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        Cancel
                    </a>
                </div>
            </form>
        </div>
    </div>
</body>
</html>