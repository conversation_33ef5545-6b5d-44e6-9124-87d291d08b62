<?php

$servername = 'localhost';
$username = 'root';
$password = '';
$dbname = 'crypto';

// Create connection
$conn = new mysqli($servername, $username, $password, $dbname);

// Check connection
if ($conn->connect_error) {
    die('Connection failed: ' . $conn->connect_error);
}

// Default levels to seed
$default_levels = [
    ['level_number' => 1, 'distribution_percentage' => 5.00],
    ['level_number' => 2, 'distribution_percentage' => 3.00],
    ['level_number' => 3, 'distribution_percentage' => 2.00],
];

$stmt = $conn->prepare("INSERT INTO levels (level_number, distribution_percentage) VALUES (?, ?) ON DUPLICATE KEY UPDATE level_number=level_number");

foreach ($default_levels as $level) {
    // We use INSERT IGNORE or ON DUPLICATE KEY to avoid errors if the level already exists.
    // Here, we assume level_number is a unique key.
    $stmt->bind_param("id", $level['level_number'], $level['distribution_percentage']);
    if ($stmt->execute()) {
        echo "Level " . $level['level_number'] . " processed successfully.\n";
    } else {
        // This part will handle cases where the insert fails for reasons other than duplicate key if the logic was INSERT without IGNORE.
        // With ON DUPLICATE KEY UPDATE, it shouldn't fail unless there's another issue.
        echo "Could not process Level " . $level['level_number'] . ". It might already exist or there was an error: " . $stmt->error . "\n";
    }
}

$stmt->close();
$conn->close();

echo "\nSeeding complete.\n";
