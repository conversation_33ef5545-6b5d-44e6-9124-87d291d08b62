<?php
// Prevent any output before JSON
ini_set('display_errors', 0);
error_reporting(0);

session_start();
header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Please log in to claim staking.']);
    exit();
}

include '../dbcon.php';

// Check database connection
if ($conn->connect_error) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Database connection failed.']);
    exit();
}

$user_id = $_SESSION['user_id'];
$data = json_decode(file_get_contents('php://input'), true);

$staking_id = intval($data['staking_id'] ?? 0);

// Validation
if ($staking_id <= 0) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Invalid staking ID.']);
    exit();
}

try {
    // Start transaction
    $conn->begin_transaction();

    // 1. Get staking record and verify ownership
    $sql_get_staking = "SELECT * FROM staking_records WHERE id = ? AND user_id = ?";
    $stmt_get_staking = $conn->prepare($sql_get_staking);
    
    if (!$stmt_get_staking) {
        throw new Exception('Failed to prepare staking query: ' . $conn->error);
    }
    
    $stmt_get_staking->bind_param("ii", $staking_id, $user_id);
    
    if (!$stmt_get_staking->execute()) {
        throw new Exception('Failed to get staking record: ' . $stmt_get_staking->error);
    }
    
    $staking_result = $stmt_get_staking->get_result();
    
    if ($staking_result->num_rows === 0) {
        throw new Exception('Staking record not found or you do not have permission to claim it.');
    }
    
    $staking = $staking_result->fetch_assoc();
    $stmt_get_staking->close();

    // 2. Verify staking is eligible for claiming
    if ($staking['status'] !== 'active') {
        throw new Exception('This staking is not active and cannot be claimed.');
    }

    $is_unlocked = strtotime($staking['freeze_end_date']) <= time();
    if (!$is_unlocked) {
        throw new Exception('This staking is still locked. Unlock date: ' . date('M j, Y', strtotime($staking['freeze_end_date'])));
    }

    // 3. Update staking status to claim_pending
    $sql_update_staking = "UPDATE staking_records SET status = 'claim_pending' WHERE id = ?";
    $stmt_update_staking = $conn->prepare($sql_update_staking);
    
    if (!$stmt_update_staking) {
        throw new Exception('Failed to prepare staking update: ' . $conn->error);
    }
    
    $stmt_update_staking->bind_param("i", $staking_id);
    
    if (!$stmt_update_staking->execute()) {
        throw new Exception('Failed to update staking status: ' . $stmt_update_staking->error);
    }
    
    $stmt_update_staking->close();

    // 4. Log the claim request in wallet history
    $description = "Staking claim requested - \${$staking['amount_usd']} ({$staking['coins_staked']} coins) - Pending admin approval";
    $log_sql = "INSERT INTO wallet_history (user_id, amount, type, wallet_type, description) VALUES (?, 0, 'staking_claim_request', 'withdrawal', ?)";
    $stmt_log = $conn->prepare($log_sql);
    
    if (!$stmt_log) {
        throw new Exception('Failed to prepare wallet history: ' . $conn->error);
    }
    
    $stmt_log->bind_param("is", $user_id, $description);
    
    if (!$stmt_log->execute()) {
        throw new Exception('Failed to log claim request: ' . $stmt_log->error);
    }
    
    $stmt_log->close();

    // 5. Commit transaction
    $conn->commit();

    // Return success response
    http_response_code(200);
    echo json_encode([
        'success' => true, 
        'message' => 'Claim request submitted successfully. Waiting for admin approval.',
        'data' => [
            'staking_id' => $staking_id,
            'amount_usd' => $staking['amount_usd'],
            'coins_staked' => $staking['coins_staked'],
            'status' => 'claim_pending'
        ]
    ]);
    exit();

} catch (Exception $e) {
    $conn->rollback();
    error_log('Staking Claim Error: ' . $e->getMessage());
    http_response_code(400);
    echo json_encode([
        'success' => false, 
        'message' => $e->getMessage()
    ]);
    exit();
} finally {
    // Close statements safely
    if (isset($stmt_get_staking) && $stmt_get_staking !== false) {
        $stmt_get_staking->close();
    }
    if (isset($stmt_update_staking) && $stmt_update_staking !== false) {
        $stmt_update_staking->close();
    }
    if (isset($stmt_log) && $stmt_log !== false) {
        $stmt_log->close();
    }
    if (isset($conn) && $conn !== false) {
        $conn->close();
    }
}
?>
