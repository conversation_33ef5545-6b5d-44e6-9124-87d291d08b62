<?php
session_start();

// If user has a remember token, remove it from database and cookie
if (isset($_COOKIE['remember_token'])) {
    include 'dbcon.php';

    $remember_token = $_COOKIE['remember_token'];

    // Remove the token from database
    $stmt = $conn->prepare('DELETE FROM user_sessions WHERE session_token = ?');
    $stmt->bind_param('s', $remember_token);
    $stmt->execute();
    $stmt->close();
    $conn->close();

    // Remove the cookie
    setcookie('remember_token', '', [
        'expires' => time() - 3600,
        'path' => '/',
        'secure' => false, // Set to true in production with HTTPS
        'httponly' => true,
        'samesite' => 'Strict'
    ]);
}

// Destroy the session
session_unset();
session_destroy();

// Redirect to login page
header('Location: login.php?logout=success');
exit();
