<?php
session_start();

if (!isset($_SESSION['user_id'])) {
    header('Content-Type: application/json');
    echo json_encode(['error' => 'User not authenticated']);
    exit();
}

include 'dbcon.php';

// Step 1: Fetch all users and their package amounts.
$sql = '
    SELECT 
        u.id, u.sponser_id, p.package_amount
    FROM users u
    LEFT JOIN packages p ON u.package = p.package_name
    WHERE u.is_active = 1 AND u.deleted_at IS NULL
';
$result = $conn->query($sql);

$users_by_id = [];
if ($result) {
    while ($row = $result->fetch_assoc()) {
        $users_by_id[$row['id']] = $row;
        $users_by_id[$row['id']]['children'] = [];
    }
}

// Step 2: Build the full hierarchy in memory.
foreach ($users_by_id as $id => &$user) {
    if ($user['sponser_id'] !== null && isset($users_by_id[$user['sponser_id']])) {
        $users_by_id[$user['sponser_id']]['children'][] = &$user;
    }
}
unset($user);

// Step 3: Recursive function to calculate investment from the pre-built array.
function calculate_investment_from_tree($nodes)
{
    $total = 0;
    foreach ($nodes as $node) {
        $total += $node['package_amount'] ?? 0;
        if (!empty($node['children'])) {
            $total += calculate_investment_from_tree($node['children']);
        }
    }
    return $total;
}

// Step 4: Get the logged-in user's downline and calculate the total.
$current_user_id = $_SESSION['user_id'];
$user_downline = [];
if (isset($users_by_id[$current_user_id])) {
    $user_downline = $users_by_id[$current_user_id]['children'];
}

$teamInvestment = calculate_investment_from_tree($user_downline);

// Step 5: Fetch ALL active bonuses and the user's claims for them.
$bonuses_query = '
    SELECT 
        b.id, b.bonus_name, b.team_investment, b.bonus_fund,
        bc.status AS claim_status
    FROM bonuses b
    LEFT JOIN bonus_claims bc ON b.id = bc.bonus_id AND bc.user_id = ?
    WHERE b.is_active = 1 
    ORDER BY b.bonus_fund ASC
';

$stmt = $conn->prepare($bonuses_query);
$stmt->bind_param('i', $current_user_id);
$stmt->execute();
$bonuses_result = $stmt->get_result();

$all_bonuses = [];
if ($bonuses_result) {
    while ($bonus = $bonuses_result->fetch_assoc()) {
        $bonus['is_achieved'] = ($teamInvestment >= $bonus['team_investment']);
        $all_bonuses[] = $bonus;
    }
}
$stmt->close();

$conn->close();

header('Content-Type: application/json');
echo json_encode([
    'team_investment' => $teamInvestment,
    'bonuses' => $all_bonuses
]);
