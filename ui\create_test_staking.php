<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header("Location: login.html");
    exit();
}

include 'dbcon.php';

$user_id = $_SESSION['user_id'];
$COIN_PRICE = 0.45;

// Get user details
$user_sql = "SELECT name, withdrawal_wallet_balance FROM users WHERE id = ?";
$stmt = $conn->prepare($user_sql);
$stmt->bind_param("i", $user_id);
$stmt->execute();
$result = $stmt->get_result();
$user = $result->fetch_assoc();
$stmt->close();

$message = '';
$error = '';

if ($_POST) {
    $test_amount = floatval($_POST['test_amount'] ?? 50); // Default $50 for testing
    $test_coins = floor($test_amount / $COIN_PRICE);

    try {
        // Create a test staking record with freeze period already expired (1 week ago)
        $freeze_end_date = date('Y-m-d H:i:s', strtotime('-1 week')); // Already expired
        $created_at = date('Y-m-d H:i:s', strtotime('-7 months')); // Created 7 months ago

        $sql = "INSERT INTO staking_records (user_id, coins_staked, amount_usd, stake_type, freeze_end_date, status, created_at) 
                VALUES (?, ?, ?, 'withdrawal', ?, 'active', ?)";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("iidss", $user_id, $test_coins, $test_amount, $freeze_end_date, $created_at);

        if ($stmt->execute()) {
            $staking_id = $conn->insert_id;

            // Also log in wallet history for completeness
            $description = "TEST: Staked \$$test_amount ($test_coins coins) for 6 months (ALREADY UNLOCKED FOR TESTING)";
            $log_sql = "INSERT INTO wallet_history (user_id, amount, type, wallet_type, description, created_at) VALUES (?, ?, 'staking', 'withdrawal', ?, ?)";
            $stmt_log = $conn->prepare($log_sql);
            $negative_amount = -$test_amount;
            $stmt_log->bind_param("idss", $user_id, $negative_amount, $description, $created_at);
            $stmt_log->execute();
            $stmt_log->close();

            $message = "✅ Test staking record created successfully!<br>
                       📊 Staking ID: $staking_id<br>
                       💰 Amount: \$$test_amount ($test_coins coins)<br>
                       📅 Created: 7 months ago<br>
                       🔓 Unlock Date: 1 month ago (ALREADY UNLOCKED)<br>
                       ✨ Status: Active (Ready to claim)<br><br>
                       🎯 <strong>Go to <a href='staking.php' class='text-blue-600 underline'>Staking Page</a> to test the claim functionality!</strong>";
        } else {
            $error = "Failed to create test staking record: " . $stmt->error;
        }
        $stmt->close();
    } catch (Exception $e) {
        $error = "Error: " . $e->getMessage();
    }
}

$conn->close();
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Create Test Staking - NetVis</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>

<body class="bg-gray-100">
    <?php include 'user_nav.php'; ?>

    <main class="max-w-4xl mx-auto py-6 sm:px-6 lg:px-8">
        <div class="bg-white shadow-lg rounded-lg p-8">
            <div class="mb-6">
                <h2 class="text-3xl font-bold text-gray-800">Create Test Staking Record</h2>
                <p class="text-gray-600 mt-2">This will create a test staking record that is already unlocked for testing the claim functionality.</p>
            </div>

            <?php if ($message): ?>
                <div class="bg-green-50 border border-green-200 text-green-800 px-4 py-3 rounded-lg mb-6">
                    <?php echo $message; ?>
                </div>
            <?php endif; ?>

            <?php if ($error): ?>
                <div class="bg-red-50 border border-red-200 text-red-800 px-4 py-3 rounded-lg mb-6">
                    <?php echo $error; ?>
                </div>
            <?php endif; ?>

            <!-- Current User Info -->
            <div class="bg-blue-50 p-4 rounded-lg mb-6">
                <h3 class="text-lg font-semibold text-blue-800 mb-2">Current User Info</h3>
                <p><strong>Name:</strong> <?php echo htmlspecialchars($user['name']); ?></p>
                <p><strong>User ID:</strong> <?php echo $user_id; ?></p>
                <p><strong>Current Withdrawal Balance:</strong> $<?php echo number_format($user['withdrawal_wallet_balance'], 2); ?></p>
            </div>

            <!-- Test Creation Form -->
            <form method="POST" class="space-y-6">
                <div>
                    <label for="test_amount" class="block text-sm font-medium text-gray-700 mb-2">Test Staking Amount (USD)</label>
                    <input type="number" id="test_amount" name="test_amount" value="50" min="10" step="10" required
                        class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                    <p class="text-sm text-gray-500 mt-1">This amount will be used for testing. Default: $50</p>
                </div>

                <div class="bg-yellow-50 border border-yellow-200 p-4 rounded-lg">
                    <h4 class="text-yellow-800 font-semibold mb-2">⚠️ Test Record Details:</h4>
                    <ul class="text-yellow-700 text-sm space-y-1">
                        <li>• <strong>Created Date:</strong> 7 months ago (simulated)</li>
                        <li>• <strong>Freeze End Date:</strong> 1 month ago (already expired)</li>
                        <li>• <strong>Status:</strong> Active (ready to claim)</li>
                        <li>• <strong>Wallet Type:</strong> Withdrawal wallet</li>
                        <li>• <strong>Purpose:</strong> Testing claim and admin approval workflow</li>
                    </ul>
                </div>

                <button type="submit" class="w-full bg-indigo-600 text-white py-3 px-4 rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 font-medium">
                    Create Test Staking Record
                </button>
            </form>

            <!-- Testing Instructions -->
            <div class="mt-8 bg-gray-50 p-6 rounded-lg">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">🧪 Testing Instructions</h3>
                <ol class="list-decimal list-inside space-y-2 text-gray-700">
                    <li><strong>Create Test Record:</strong> Click the button above to create a test staking record</li>
                    <li><strong>Go to Staking Page:</strong> Visit <a href="staking.php" class="text-blue-600 underline">staking.php</a> to see the test record</li>
                    <li><strong>Check Status:</strong> The record should show "Ready to Claim" status with a green "Claim" button</li>
                    <li><strong>Click Claim:</strong> Click the "Claim" button to submit a claim request</li>
                    <li><strong>Check Status Change:</strong> Status should change to "Claim Pending" with orange badge</li>
                    <li><strong>Admin Approval:</strong> Go to <a href="admin_staking_claims.php" class="text-blue-600 underline">admin staking claims</a> (as admin)</li>
                    <li><strong>Approve Claim:</strong> Click "Approve" to process the claim</li>
                    <li><strong>Verify Wallet:</strong> Check that the amount was added to withdrawal wallet</li>
                    <li><strong>Final Status:</strong> Status should change to "Completed" with "Claimed" badge</li>
                </ol>
            </div>

            <!-- Quick Links -->
            <div class="mt-6 flex space-x-4">
                <a href="staking.php" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
                    Go to Staking Page
                </a>
                <a href="admin_staking_claims.php" class="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700">
                    Admin Staking Claims
                </a>
                <a href="wallet.php" class="bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700">
                    Check Wallet Balance
                </a>
            </div>
        </div>
    </main>
</body>

</html>