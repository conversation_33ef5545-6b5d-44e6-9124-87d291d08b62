<?php
session_start();

// Check if user is admin
if (!isset($_SESSION['user_id']) || !$_SESSION['is_admin']) {
    header("Location: login.php");
    exit();
}

include 'dbcon.php';

echo "<h2>Test Balance Sync - Package History Updates</h2>";
echo "<p>This script demonstrates how package history is updated with the exact balance from users table.</p>";

// Show the SQL queries being used
echo "<h3>1. SQL Queries Used in calculate_earnings.php</h3>";
echo "<div style='background: #f5f5f5; padding: 15px; border: 1px solid #ddd; margin: 10px 0;'>";
echo "<h4>For User Daily Profits:</h4>";
echo "<code>UPDATE package_history SET total_earnings_received = (SELECT total_earnings_received FROM users WHERE id = ?) WHERE user_id = ? AND status = 'active'</code>";
echo "<br><br>";
echo "<h4>For Sponsor Network Commissions:</h4>";
echo "<code>UPDATE package_history SET total_earnings_received = (SELECT total_earnings_received FROM users WHERE id = ?) WHERE user_id = ? AND status = 'active'</code>";
echo "</div>";

// Test the sync manually
echo "<h3>2. Manual Balance Sync Test</h3>";
if (isset($_POST['test_sync'])) {
    echo "<div style='background: #e8f5e8; padding: 15px; border: 1px solid #4CAF50; margin: 10px 0;'>";
    echo "<h4>Running Manual Balance Sync...</h4>";

    // Get users with active packages
    $users_sql = "SELECT u.id, u.name, u.user_id as user_identifier, u.total_earnings_received as user_balance,
                  ph.id as history_id, ph.total_earnings_received as history_balance
                  FROM users u 
                  JOIN package_history ph ON u.id = ph.user_id 
                  WHERE ph.status = 'active' AND u.plan_status = 'active'
                  LIMIT 5";

    $users_result = $conn->query($users_sql);

    if ($users_result->num_rows > 0) {
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>User</th><th>Before - User Balance</th><th>Before - History Balance</th><th>After - History Balance</th><th>Status</th></tr>";

        while ($user = $users_result->fetch_assoc()) {
            $user_id = $user['id'];
            $user_balance = $user['user_balance'];
            $history_balance_before = $user['history_balance'];

            // Update package history with current user balance
            $update_sql = "UPDATE package_history SET total_earnings_received = (SELECT total_earnings_received FROM users WHERE id = ?) WHERE user_id = ? AND status = 'active'";
            $stmt = $conn->prepare($update_sql);
            $stmt->bind_param("ii", $user_id, $user_id);
            $stmt->execute();
            $stmt->close();

            // Get updated history balance
            $check_sql = "SELECT total_earnings_received FROM package_history WHERE user_id = ? AND status = 'active'";
            $check_stmt = $conn->prepare($check_sql);
            $check_stmt->bind_param("i", $user_id);
            $check_stmt->execute();
            $check_result = $check_stmt->get_result();
            $history_balance_after = $check_result->fetch_assoc()['total_earnings_received'];
            $check_stmt->close();

            $status = (abs($user_balance - $history_balance_after) < 0.01) ? "✅ Synced" : "❌ Failed";

            echo "<tr>";
            echo "<td>{$user['name']} ({$user['user_identifier']})</td>";
            echo "<td>$" . number_format($user_balance, 2) . "</td>";
            echo "<td>$" . number_format($history_balance_before, 2) . "</td>";
            echo "<td>$" . number_format($history_balance_after, 2) . "</td>";
            echo "<td>$status</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>No active users with package history found.</p>";
    }
    echo "</div>";
}

echo "<form method='POST'>";
echo "<button type='submit' name='test_sync' style='background: #4CAF50; color: white; padding: 10px; border: none; cursor: pointer;'>Test Manual Balance Sync</button>";
echo "</form>";

// Show current sync status
echo "<h3>3. Current Sync Status</h3>";
$sync_check_sql = "SELECT u.id, u.name, u.user_id as user_identifier, 
                   u.total_earnings_received as user_balance,
                   ph.total_earnings_received as history_balance,
                   ABS(u.total_earnings_received - ph.total_earnings_received) as difference
                   FROM users u 
                   JOIN package_history ph ON u.id = ph.user_id 
                   WHERE ph.status = 'active' AND u.plan_status = 'active'
                   ORDER BY difference DESC
                   LIMIT 10";

$sync_result = $conn->query($sync_check_sql);
if ($sync_result->num_rows > 0) {
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>User</th><th>User Balance</th><th>History Balance</th><th>Difference</th><th>Status</th></tr>";
    while ($row = $sync_result->fetch_assoc()) {
        $is_synced = $row['difference'] < 0.01;
        $status = $is_synced ? "✅ In Sync" : "❌ Out of Sync";
        $row_color = $is_synced ? "#e8f5e8" : "#ffe8e8";

        echo "<tr style='background-color: $row_color;'>";
        echo "<td>{$row['name']} ({$row['user_identifier']})</td>";
        echo "<td>$" . number_format($row['user_balance'], 2) . "</td>";
        echo "<td>$" . number_format($row['history_balance'], 2) . "</td>";
        echo "<td>$" . number_format($row['difference'], 2) . "</td>";
        echo "<td>$status</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>No active users with package history found.</p>";
}

// Explanation
echo "<h3>4. How It Works</h3>";
echo "<div style='background: #f0f8ff; padding: 15px; border: 1px solid #4169E1; margin: 10px 0;'>";
echo "<h4>🔄 Updated Process:</h4>";
echo "<ol>";
echo "<li><strong>Daily Earnings Calculation Runs</strong></li>";
echo "<li><strong>User receives daily profit</strong> → User's total_earnings_received in users table is updated</li>";
echo "<li><strong>Package history is updated</strong> → Fetches the current balance from users table and updates package_history.total_earnings_received</li>";
echo "<li><strong>Sponsor receives network commission</strong> → Sponsor's total_earnings_received in users table is updated</li>";
echo "<li><strong>Sponsor's package history is updated</strong> → Fetches the current balance from users table and updates package_history.total_earnings_received</li>";
echo "</ol>";
echo "<br>";
echo "<h4>✅ Benefits:</h4>";
echo "<ul>";
echo "<li><strong>Perfect Sync:</strong> Package history always matches users table exactly</li>";
echo "<li><strong>No Accumulation Errors:</strong> Fetches actual balance instead of adding amounts</li>";
echo "<li><strong>Real-time Accuracy:</strong> Package history reflects current state immediately</li>";
echo "<li><strong>Audit Trail:</strong> Complete accuracy for reporting and compliance</li>";
echo "</ul>";
echo "</div>";

// Show the difference between old and new approach
echo "<h3>5. Old vs New Approach</h3>";
echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th style='width: 50%; background: #ffeeee;'>❌ Old Approach</th><th style='width: 50%; background: #eeffee;'>✅ New Approach</th></tr>";
echo "<tr>";
echo "<td style='padding: 15px; vertical-align: top;'>";
echo "<strong>Incremental Updates:</strong><br>";
echo "• Added daily profit amount to package history<br>";
echo "• Added commission amount to package history<br>";
echo "• Risk of sync errors over time<br>";
echo "• Could accumulate rounding errors<br>";
echo "<br><code>UPDATE package_history SET total_earnings_received = total_earnings_received + ?</code>";
echo "</td>";
echo "<td style='padding: 15px; vertical-align: top;'>";
echo "<strong>Balance Sync:</strong><br>";
echo "• Fetches current balance from users table<br>";
echo "• Updates package history with exact balance<br>";
echo "• Always perfectly in sync<br>";
echo "• No accumulation errors possible<br>";
echo "<br><code>UPDATE package_history SET total_earnings_received = (SELECT total_earnings_received FROM users WHERE id = ?)</code>";
echo "</td>";
echo "</tr>";
echo "</table>";

$conn->close();
