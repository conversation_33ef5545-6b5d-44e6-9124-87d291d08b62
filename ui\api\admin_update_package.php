<?php
session_start();

header('Content-Type: application/json');

if (!isset($_SESSION['user_id']) || !$_SESSION['is_admin']) {
    http_response_code(403);
    echo json_encode(['error' => 'Admin access required.']);
    exit();
}

$data = json_decode(file_get_contents('php://input'), true);
$user_id = $data['user_id'] ?? null;
$package_id = $data['package_id'] ?? null;
$deduct_wallet = $data['deduct_wallet'] ?? false;

if (!$user_id || !$package_id) {
    http_response_code(400);
    echo json_encode(['error' => 'User ID and Package ID are required.']);
    exit();
}

include '../dbcon.php';
if ($conn->connect_error) {
    http_response_code(500);
    die(json_encode(['error' => 'Database connection failed.']));
}

$admin_id = $_SESSION['user_id'];

// Start transaction
$conn->begin_transaction();

try {
    // 1. Get package details
    $package_sql = "SELECT package_name, package_amount FROM packages WHERE id = ? AND is_active = 1";
    $stmt = $conn->prepare($package_sql);
    $stmt->bind_param("i", $package_id);
    $stmt->execute();
    $package_result = $stmt->get_result();
    if ($package_result->num_rows === 0) {
        throw new Exception('Package not found or is inactive.');
    }
    $package = $package_result->fetch_assoc();
    $package_name = $package['package_name'];
    $package_amount = $package['package_amount'];

    // 2. Get user's current package for comparison
    $user_sql = "SELECT p.package_amount as current_package_amount FROM users u LEFT JOIN packages p ON u.package = p.package_name WHERE u.id = ?";
    $stmt = $conn->prepare($user_sql);
    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    $user_result = $stmt->get_result();
    $user = $user_result->fetch_assoc();
    $current_package_amount = $user['current_package_amount'] ?? 0;

    // 3. Check if it's an upgrade
    if ($package_amount <= $current_package_amount) {
        throw new Exception('This package is not an upgrade. Please select a package with a higher value.');
    }

    // 4. Update user's package
    $update_user_sql = "UPDATE users SET package = ? WHERE id = ?";
    $stmt = $conn->prepare($update_user_sql);
    $stmt->bind_param("si", $package_name, $user_id);
    $stmt->execute();

    if ($deduct_wallet) {
        $upgrade_cost = $package_amount - $current_package_amount;

        if ($upgrade_cost > 0) {
            // Get user's deposit balance with lock
            $user_sql = "SELECT deposit_wallet_balance FROM users WHERE id = ? FOR UPDATE";
            $stmt = $conn->prepare($user_sql);
            $stmt->bind_param("i", $user_id);
            $stmt->execute();
            $user_result = $stmt->get_result();
            $user = $user_result->fetch_assoc();
            $current_deposit_balance = $user['deposit_wallet_balance'];

            if ($current_deposit_balance < $upgrade_cost) {
                throw new Exception('Insufficient deposit wallet balance for package upgrade.');
            }

            // Deduct from deposit wallet
            $update_balance_sql = "UPDATE users SET deposit_wallet_balance = deposit_wallet_balance - ? WHERE id = ?";
            $stmt = $conn->prepare($update_balance_sql);
            $stmt->bind_param("di", $upgrade_cost, $user_id);
            $stmt->execute();

            // Log transaction in wallet_history
            $description = "Upgrade to '{$package_name}' package (deducted by Admin)";
            $type = 'package_upgrade';
            $wallet_type = 'deposit'; // Deduction from deposit wallet
            $insert_wallet_sql = "INSERT INTO wallet_history (user_id, amount, type, description, wallet_type, created_by) VALUES (?, ?, ?, ?, ?, ?)";
            $stmt = $conn->prepare($insert_wallet_sql);
            $stmt->bind_param("idsssi", $user_id, $upgrade_cost, $type, $description, $wallet_type, $admin_id);
            $stmt->execute();
        }
    }

    // Commit the transaction
    $conn->commit();

    echo json_encode(['success' => true]);

} catch (Exception $e) {
    $conn->rollback();
    http_response_code(400);
    echo json_encode(['error' => $e->getMessage()]);
} finally {
    $stmt->close();
    $conn->close();
}
?>
