<?php
session_start();
$_SESSION['admin_logged_in'] = true;
$_SESSION['admin_id'] = 1;

include 'dbcon.php';

echo "<h2>🎯 Testing All Corrected Dashboard Queries</h2>";

echo "<div style='background: #dcfce7; border: 2px solid #16a34a; border-radius: 12px; padding: 20px; margin: 20px 0;'>";
echo "<h3 style='color: #15803d; margin-top: 0;'>✅ All Query Fixes Applied</h3>";
echo "<ul style='color: #166534;'>";
echo "<li>✅ <strong>Packages:</strong> Removed non-existent 'status' column filter</li>";
echo "<li>✅ <strong>Users:</strong> Using 'reg_date' instead of 'created_at'</li>";
echo "<li>✅ <strong>Staking:</strong> Using correct 'amount_usd' and 'status' columns</li>";
echo "<li>✅ <strong>Deposits/Withdrawals:</strong> Using correct 'status' column</li>";
echo "</ul>";
echo "</div>";

// Test all corrected queries
$corrected_queries = [
    'Total Users' => "SELECT COUNT(*) as total FROM users WHERE is_admin = 0",
    'Active Users' => "SELECT COUNT(*) as active FROM users WHERE is_admin = 0 AND plan_status = 'active'",
    'Expired Users' => "SELECT COUNT(*) as expired FROM users WHERE is_admin = 0 AND plan_status = 'expired'",
    'New Users Today' => "SELECT COUNT(*) as new_today FROM users WHERE is_admin = 0 AND DATE(reg_date) = CURDATE()",
    'Total Deposits' => "SELECT COALESCE(SUM(amount), 0) as total FROM deposit_requests WHERE status = 'approved'",
    'Total Withdrawals' => "SELECT COALESCE(SUM(amount), 0) as total FROM withdrawal_requests WHERE status = 'approved'",
    'Pending Deposits' => "SELECT COUNT(*) as pending FROM deposit_requests WHERE status = 'pending'",
    'Pending Withdrawals' => "SELECT COUNT(*) as pending FROM withdrawal_requests WHERE status = 'pending'",
    'Total Packages (FIXED)' => "SELECT COUNT(*) as total FROM packages",
    'Total Investments' => "SELECT COALESCE(SUM(package_amount), 0) as total FROM package_history",
    'Active Staking Count' => "SELECT COUNT(*) as active FROM staking_records WHERE status = 'active'",
    'Active Staking Amount' => "SELECT COALESCE(SUM(amount_usd), 0) as total_amount FROM staking_records WHERE status = 'active'",
    'Total Staking (All)' => "SELECT COUNT(*) as count, COALESCE(SUM(amount_usd), 0) as total FROM staking_records",
    'Completed Staking' => "SELECT COUNT(*) as count, COALESCE(SUM(amount_usd), 0) as total FROM staking_records WHERE status = 'completed'",
    'Deposit Wallet Balance' => "SELECT COALESCE(SUM(deposit_wallet_balance), 0) as total FROM users WHERE is_admin = 0",
    'Withdrawal Wallet Balance' => "SELECT COALESCE(SUM(withdrawal_wallet_balance), 0) as total FROM users WHERE is_admin = 0"
];

echo "<h3>🧪 All Dashboard Queries Test:</h3>";
echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>";
echo "<tr style='background: #f3f4f6;'><th>Query Name</th><th>Result</th><th>Status</th></tr>";

$dashboard_data = [];
$all_success = true;

foreach ($corrected_queries as $name => $query) {
    echo "<tr>";
    echo "<td><strong>$name</strong></td>";
    
    $result = $conn->query($query);
    if ($result) {
        $data = $result->fetch_assoc();
        $value = $data[array_keys($data)[0]]; // Get first column value
        $dashboard_data[$name] = $value;
        
        if (strpos($name, 'Amount') !== false || strpos($name, 'Deposits') !== false || strpos($name, 'Withdrawals') !== false || strpos($name, 'Investments') !== false || strpos($name, 'Balance') !== false) {
            echo "<td style='color: green; font-weight: bold;'>$" . number_format($value, 2) . "</td>";
        } else {
            echo "<td style='color: green; font-weight: bold;'>" . number_format($value) . "</td>";
        }
        echo "<td style='color: green;'>✅ SUCCESS</td>";
    } else {
        $dashboard_data[$name] = 0;
        echo "<td style='color: red;'>ERROR: " . $conn->error . "</td>";
        echo "<td style='color: red;'>❌ FAILED</td>";
        $all_success = false;
    }
    echo "</tr>";
}

echo "</table>";

// Test recent activities
echo "<h3>📋 Recent Activities Test:</h3>";

$activity_queries = [
    'Recent Users' => "SELECT id, name, user_id, package, reg_date FROM users WHERE is_admin = 0 ORDER BY reg_date DESC LIMIT 3",
    'Recent Deposits' => "SELECT dr.id, dr.user_id, dr.amount, dr.status, u.name FROM deposit_requests dr JOIN users u ON dr.user_id = u.id ORDER BY dr.id DESC LIMIT 3",
    'Recent Withdrawals' => "SELECT wr.id, wr.user_id, wr.amount, wr.status, u.name FROM withdrawal_requests wr JOIN users u ON wr.user_id = u.id ORDER BY wr.id DESC LIMIT 3"
];

foreach ($activity_queries as $name => $query) {
    echo "<h4>$name:</h4>";
    $result = $conn->query($query);
    if ($result) {
        echo "<p style='color: green;'>✅ SUCCESS! Rows: " . $result->num_rows . "</p>";
    } else {
        echo "<p style='color: red;'>❌ FAILED: " . $conn->error . "</p>";
        $all_success = false;
    }
}

// Show final status
if ($all_success) {
    echo "<div style='background: #dcfce7; border: 2px solid #16a34a; border-radius: 12px; padding: 20px; margin: 20px 0;'>";
    echo "<h3 style='color: #15803d; margin-top: 0;'>🎉 ALL QUERIES WORKING!</h3>";
    echo "<p style='color: #166534;'>All dashboard queries are now working correctly. The main dashboard should display data properly.</p>";
    
    echo "<h4 style='color: #166534;'>📊 Your Platform Summary:</h4>";
    echo "<ul style='color: #166534;'>";
    echo "<li><strong>Total Users:</strong> " . number_format($dashboard_data['Total Users']) . "</li>";
    echo "<li><strong>Active Users:</strong> " . number_format($dashboard_data['Active Users']) . "</li>";
    echo "<li><strong>Total Deposits:</strong> $" . number_format($dashboard_data['Total Deposits'], 2) . "</li>";
    echo "<li><strong>Total Withdrawals:</strong> $" . number_format($dashboard_data['Total Withdrawals'], 2) . "</li>";
    echo "<li><strong>Active Staking:</strong> $" . number_format($dashboard_data['Active Staking Amount'], 2) . "</li>";
    echo "<li><strong>Total Packages:</strong> " . number_format($dashboard_data['Total Packages (FIXED)']) . "</li>";
    echo "</ul>";
    
    echo "<p style='color: #166534; text-align: center; margin-top: 20px;'>";
    echo "<a href='admin_dashboard.php' style='background: linear-gradient(135deg, #16a34a 0%, #15803d 100%); color: white; padding: 15px 30px; text-decoration: none; border-radius: 12px; font-weight: 600; box-shadow: 0 4px 15px rgba(22, 163, 74, 0.3);'>🚀 Open Working Dashboard</a>";
    echo "</p>";
    
    echo "</div>";
} else {
    echo "<div style='background: #fef2f2; border: 2px solid #ef4444; border-radius: 12px; padding: 20px; margin: 20px 0;'>";
    echo "<h3 style='color: #dc2626; margin-top: 0;'>❌ Some Queries Failed</h3>";
    echo "<p style='color: #b91c1c;'>Check the failed queries above and fix the column names or table structure.</p>";
    echo "</div>";
}

$conn->close();
?>

<style>
body { 
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; 
    margin: 20px; 
    background: #f8fafc;
    line-height: 1.6;
}
h2, h3, h4 { color: #1f2937; }
table { 
    font-size: 0.9em; 
    max-width: 100%; 
    overflow-x: auto;
}
th, td { 
    padding: 8px 12px; 
    text-align: left; 
    border: 1px solid #e5e7eb;
}
th { 
    background: #f9fafb; 
    font-weight: 600;
}
</style>
