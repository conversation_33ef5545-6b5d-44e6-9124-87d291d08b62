<?php
// Simple debug script to test the purchase API directly
session_start();

if (!isset($_SESSION['user_id'])) {
    echo "Error: User not logged in";
    exit();
}

// Enable error reporting for debugging
ini_set('display_errors', 1);
error_reporting(E_ALL);

echo "<h2>Debug Purchase API</h2>";

// Test 1: Check if the API file can be included without errors
echo "<h3>Test 1: API File Syntax Check</h3>";
try {
    ob_start();
    $api_content = file_get_contents('api/purchase_package.php');
    ob_end_clean();
    
    if (strpos($api_content, 'TO <?php') !== false) {
        echo "❌ Found syntax error: 'TO <?php' at beginning of file<br>";
    } else {
        echo "✅ No obvious syntax errors found<br>";
    }
    
    // Check for common issues
    if (strpos($api_content, '$package[\'profit_percentage\']') !== false && 
        strpos($api_content, 'SELECT package_name, package_amount FROM') !== false) {
        echo "❌ Found issue: profit_percentage used but not selected in query<br>";
    } else {
        echo "✅ profit_percentage query looks correct<br>";
    }
    
} catch (Exception $e) {
    echo "❌ Error reading API file: " . $e->getMessage() . "<br>";
}

// Test 2: Check database connection
echo "<h3>Test 2: Database Connection</h3>";
try {
    include 'dbcon.php';
    if ($conn->connect_error) {
        echo "❌ Database connection failed: " . $conn->connect_error . "<br>";
    } else {
        echo "✅ Database connection successful<br>";
    }
} catch (Exception $e) {
    echo "❌ Database connection error: " . $e->getMessage() . "<br>";
}

// Test 3: Check required tables
echo "<h3>Test 3: Required Tables Check</h3>";
$required_tables = ['packages', 'users', 'package_history', 'wallet_history'];
foreach ($required_tables as $table) {
    $result = $conn->query("SHOW TABLES LIKE '$table'");
    if ($result->num_rows > 0) {
        echo "✅ Table '$table' exists<br>";
    } else {
        echo "❌ Table '$table' missing<br>";
    }
}

// Test 4: Check packages data
echo "<h3>Test 4: Packages Data</h3>";
$packages_result = $conn->query("SELECT COUNT(*) as count FROM packages WHERE is_active = 1");
if ($packages_result) {
    $count = $packages_result->fetch_assoc()['count'];
    if ($count > 0) {
        echo "✅ Found $count active packages<br>";
        
        // Show sample package
        $sample = $conn->query("SELECT * FROM packages WHERE is_active = 1 LIMIT 1")->fetch_assoc();
        echo "Sample package: ID={$sample['id']}, Name={$sample['package_name']}, Amount={$sample['package_amount']}<br>";
    } else {
        echo "❌ No active packages found<br>";
    }
} else {
    echo "❌ Error querying packages: " . $conn->error . "<br>";
}

// Test 5: Check user data
echo "<h3>Test 5: User Data</h3>";
$user_id = $_SESSION['user_id'];
$user_result = $conn->query("SELECT * FROM users WHERE id = $user_id");
if ($user_result && $user_result->num_rows > 0) {
    $user = $user_result->fetch_assoc();
    echo "✅ User found: ID={$user['id']}, Name={$user['name']}<br>";
    echo "Deposit Balance: {$user['deposit_wallet_balance']}<br>";
    echo "Withdrawal Balance: {$user['withdrawal_wallet_balance']}<br>";
    echo "Current Package: " . ($user['package'] ?? 'None') . "<br>";
} else {
    echo "❌ User not found or error: " . $conn->error . "<br>";
}

// Test 6: Direct API test
echo "<h3>Test 6: Direct API Test</h3>";
if (isset($_POST['direct_test'])) {
    echo "<h4>Running Direct API Test...</h4>";
    
    // Capture any output
    ob_start();
    
    // Simulate the API call by including the file
    $_POST_backup = $_POST;
    $_SERVER_backup = $_SERVER;
    
    // Set up the environment as if it's an API call
    $_SERVER['REQUEST_METHOD'] = 'POST';
    $_SERVER['CONTENT_TYPE'] = 'application/json';
    
    // Create fake input
    $fake_input = json_encode(['package_id' => 1, 'wallet_type' => 'deposit']);
    
    // Temporarily override php://input
    $temp_file = tempnam(sys_get_temp_dir(), 'api_test');
    file_put_contents($temp_file, $fake_input);
    
    try {
        // This is a simplified test - in reality, we'd need to mock php://input
        echo "API test would require more complex setup. Use the browser test instead.<br>";
    } catch (Exception $e) {
        echo "Error during API test: " . $e->getMessage() . "<br>";
    }
    
    // Restore
    $_POST = $_POST_backup;
    $_SERVER = $_SERVER_backup;
    unlink($temp_file);
    
    $output = ob_get_clean();
    if (!empty($output)) {
        echo "API Output:<br><pre>$output</pre>";
    }
}

echo "<form method='POST'>";
echo "<button type='submit' name='direct_test' style='background: #ff9800; color: white; padding: 10px; border: none; cursor: pointer;'>Run Direct API Test</button>";
echo "</form>";

// Test 7: JavaScript test
echo "<h3>Test 7: JavaScript Test</h3>";
echo "<button onclick='testAPI()' style='background: #2196F3; color: white; padding: 10px; border: none; cursor: pointer;'>Test API via JavaScript</button>";
echo "<div id='js-result' style='margin-top: 10px; padding: 10px; background: #f5f5f5; border: 1px solid #ddd;'></div>";

echo "<script>
function testAPI() {
    const resultDiv = document.getElementById('js-result');
    resultDiv.innerHTML = 'Testing API...';
    
    fetch('/netvis/ui/api/purchase_package.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            package_id: 1,
            wallet_type: 'deposit'
        })
    })
    .then(response => {
        console.log('Response status:', response.status);
        console.log('Response headers:', response.headers);
        return response.text(); // Get as text first to see raw response
    })
    .then(text => {
        console.log('Raw response:', text);
        resultDiv.innerHTML = '<strong>Raw Response:</strong><br><pre>' + text + '</pre>';
        
        // Try to parse as JSON
        try {
            const data = JSON.parse(text);
            resultDiv.innerHTML += '<br><strong>Parsed JSON:</strong><br><pre>' + JSON.stringify(data, null, 2) + '</pre>';
        } catch (e) {
            resultDiv.innerHTML += '<br><strong>JSON Parse Error:</strong> ' + e.message;
        }
    })
    .catch(error => {
        console.error('Fetch error:', error);
        resultDiv.innerHTML = '<strong>Network Error:</strong> ' + error.message;
    });
}
</script>";

echo "<h3>Summary</h3>";
echo "<p>This debug script checks for common issues that could cause the 'network error' when upgrading packages.</p>";
echo "<p>The most likely causes are:</p>";
echo "<ul>";
echo "<li>Syntax errors in the API file (like 'TO <?php')</li>";
echo "<li>Missing database tables</li>";
echo "<li>Database connection issues</li>";
echo "<li>Missing or inactive packages</li>";
echo "<li>Insufficient user wallet balance</li>";
echo "</ul>";

if (isset($conn)) {
    $conn->close();
}
?>
