<?php
session_start();

// Admin authentication check
if (!isset($_SESSION['user_id']) || !$_SESSION['is_admin']) {
    header("Location: login.html");
    exit();
}

include 'dbcon.php';

$status_filter = $_GET['status'] ?? 'pending';
$allowed_statuses = ['pending', 'approved', 'rejected'];
if (!in_array($status_filter, $allowed_statuses)) {
    $status_filter = 'pending';
}

$sql = "SELECT wr.*, u.name as user_name, u.user_id as user_identifier 
        FROM withdrawal_requests wr 
        JOIN users u ON wr.user_id = u.id 
        WHERE wr.status = ? 
        ORDER BY wr.requested_at DESC";
$stmt = $conn->prepare($sql);
$stmt->bind_param("s", $status_filter);
$stmt->execute();
$requests_result = $stmt->get_result();

// Page configuration
$page_title = "Withdrawal Requests Management";
$additional_css = '
<style>
    /* Enhanced table styling */
    .admin-table {
        width: 100%;
        border-collapse: separate;
        border-spacing: 0;
        background: white;
    }

    .admin-table thead {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    .admin-table thead th {
        color: white;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.05em;
        padding: 1rem 1.5rem;
        font-size: 0.75rem;
        border: none;
        position: relative;
    }

    .admin-table thead th:first-child {
        text-align: left;
        padding-left: 2rem;
    }

    .admin-table thead th:not(:first-child) {
        text-align: center;
    }

    .admin-table tbody tr {
        transition: all 0.2s ease;
        border-bottom: 1px solid #e2e8f0;
    }

    .admin-table tbody tr:hover {
        background-color: #f8fafc;
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    .admin-table tbody tr:last-child {
        border-bottom: none;
    }

    .admin-table tbody td {
        padding: 1rem 1.5rem;
        vertical-align: middle;
        border: none;
        background-color: white;
    }

    .admin-table tbody td:first-child {
        text-align: left;
        padding-left: 2rem;
    }

    .admin-table tbody td:not(:first-child) {
        text-align: center;
    }

    /* Ensure proper table layout */
    .admin-table {
        table-layout: fixed;
    }

    .admin-table th:nth-child(1),
    .admin-table td:nth-child(1) {
        width: 20%;
    }

    .admin-table th:nth-child(2),
    .admin-table td:nth-child(2) {
        width: 15%;
    }

    .admin-table th:nth-child(3),
    .admin-table td:nth-child(3) {
        width: 25%;
    }

    .admin-table th:nth-child(4),
    .admin-table td:nth-child(4) {
        width: 15%;
    }

    .admin-table th:nth-child(5),
    .admin-table td:nth-child(5) {
        width: 10%;
    }

    .admin-table th:nth-child(6),
    .admin-table td:nth-child(6) {
        width: 15%;
    }

    /* Date column - left aligned */
    .admin-table tbody td:first-child {
        text-align: left !important;
    }

    .admin-table tbody td:first-child > div {
        justify-content: flex-start !important;
        align-items: flex-start !important;
        text-align: left !important;
    }

    .admin-table tbody td:first-child > div > div {
        text-align: left !important;
    }

    /* All other columns - center aligned */
    .admin-table tbody td:not(:first-child) > div {
        justify-content: center;
    }

    /* Better spacing for table content */
    .admin-table tbody td > div {
        min-height: 2.5rem;
        display: flex;
        align-items: center;
    }

    /* Ensure consistent row height */
    .admin-table tbody tr {
        height: 4rem;
    }

    /* Smooth table appearance */
    .admin-table thead th {
        border-bottom: 2px solid rgba(255, 255, 255, 0.1);
        white-space: nowrap;
    }

    .admin-table tbody tr:hover td {
        background-color: #f8fafc;
    }

    /* Status badges */
    .status-badge {
        display: inline-flex;
        align-items: center;
        padding: 0.25rem 0.75rem;
        border-radius: 9999px;
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.05em;
    }

    .status-pending {
        background-color: #fef3c7;
        color: #92400e;
        border: 1px solid #fbbf24;
    }

    .status-approved {
        background-color: #d1fae5;
        color: #065f46;
        border: 1px solid #10b981;
    }

    .status-rejected {
        background-color: #fee2e2;
        color: #991b1b;
        border: 1px solid #ef4444;
    }

    /* Filter styling */
    .filter-container {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 0.75rem;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
    }

    /* Action buttons */
    .action-btn {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 2rem;
        height: 2rem;
        border-radius: 0.375rem;
        font-weight: 500;
        font-size: 0.875rem;
        transition: all 0.2s ease;
        border: none;
        cursor: pointer;
    }

    .action-btn-approve {
        background: rgba(34, 197, 94, 0.1);
        color: #22c55e;
        border: 1px solid rgba(34, 197, 94, 0.2);
    }

    .action-btn-approve:hover {
        background: rgba(34, 197, 94, 0.2);
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(34, 197, 94, 0.2);
    }

    .action-btn-reject {
        background: rgba(239, 68, 68, 0.1);
        color: #ef4444;
        border: 1px solid rgba(239, 68, 68, 0.2);
    }

    .action-btn-reject:hover {
        background: rgba(239, 68, 68, 0.2);
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(239, 68, 68, 0.2);
    }
</style>
';

// Start output buffering to capture the page content
ob_start();

?>
<!-- Page Header -->
<div class="flex flex-col lg:flex-row justify-between items-start lg:items-center space-y-4 lg:space-y-0 mb-6">
    <div>
        <h1 class="text-3xl font-bold text-gray-900">Withdrawal Requests Management</h1>
        <p class="text-gray-600 mt-1">Review and manage user withdrawal requests</p>
    </div>
    <div class="flex items-center space-x-2 bg-blue-50 px-4 py-2 rounded-lg border border-blue-200">
        <i class="fas fa-money-bill-wave text-blue-600"></i>
        <span class="text-sm font-medium text-blue-700">Withdrawal Management</span>
    </div>
</div>

<!-- Filter Section -->
<div class="filter-container">
    <div class="flex items-center justify-between">
        <div class="flex items-center space-x-3">
            <div class="w-10 h-10 bg-white bg-opacity-20 rounded-xl flex items-center justify-center">
                <i class="fas fa-filter text-white text-lg"></i>
            </div>
            <div>
                <h3 class="text-lg font-semibold text-white">Filter Requests</h3>
                <p class="text-blue-100 text-sm">Filter by request status</p>
            </div>
        </div>
        <div class="flex items-center space-x-4">
            <div class="flex items-center space-x-2">
                <a href="?status=pending" class="<?php echo $status_filter === 'pending' ? 'bg-white text-blue-600' : 'bg-white bg-opacity-20 text-white hover:bg-opacity-30'; ?> px-4 py-2 rounded-lg font-medium text-sm transition-all duration-200">
                    <i class="fas fa-clock mr-2"></i>Pending
                </a>
                <a href="?status=approved" class="<?php echo $status_filter === 'approved' ? 'bg-white text-blue-600' : 'bg-white bg-opacity-20 text-white hover:bg-opacity-30'; ?> px-4 py-2 rounded-lg font-medium text-sm transition-all duration-200">
                    <i class="fas fa-check mr-2"></i>Approved
                </a>
                <a href="?status=rejected" class="<?php echo $status_filter === 'rejected' ? 'bg-white text-blue-600' : 'bg-white bg-opacity-20 text-white hover:bg-opacity-30'; ?> px-4 py-2 rounded-lg font-medium text-sm transition-all duration-200">
                    <i class="fas fa-times mr-2"></i>Rejected
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Section -->
<?php
// Get statistics for current filter
$stats_sql = "SELECT
    COUNT(*) as total_requests,
    SUM(amount) as total_amount,
    COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_count,
    COUNT(CASE WHEN status = 'approved' THEN 1 END) as approved_count,
    COUNT(CASE WHEN status = 'rejected' THEN 1 END) as rejected_count
    FROM withdrawal_requests";

$stats_result = $conn->query($stats_sql);
$stats = $stats_result->fetch_assoc();

// Get current filter stats
$current_stats_sql = "SELECT COUNT(*) as count, SUM(amount) as amount FROM withdrawal_requests WHERE status = ?";
$current_stmt = $conn->prepare($current_stats_sql);
$current_stmt->bind_param("s", $status_filter);
$current_stmt->execute();
$current_stats = $current_stmt->get_result()->fetch_assoc();

$stmt->close();
$current_stmt->close();
$conn->close();
?>

<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
    <div class="admin-card p-6">
        <div class="flex items-center">
            <div class="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">
                <i class="fas fa-list text-blue-600 text-xl"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">Total Requests</p>
                <p class="text-2xl font-bold text-gray-900"><?php echo number_format($stats['total_requests']); ?></p>
            </div>
        </div>
    </div>

    <div class="admin-card p-6">
        <div class="flex items-center">
            <div class="w-12 h-12 bg-yellow-100 rounded-xl flex items-center justify-center">
                <i class="fas fa-clock text-yellow-600 text-xl"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">Pending</p>
                <p class="text-2xl font-bold text-gray-900"><?php echo number_format($stats['pending_count']); ?></p>
            </div>
        </div>
    </div>

    <div class="admin-card p-6">
        <div class="flex items-center">
            <div class="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center">
                <i class="fas fa-check text-green-600 text-xl"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">Approved</p>
                <p class="text-2xl font-bold text-gray-900"><?php echo number_format($stats['approved_count']); ?></p>
            </div>
        </div>
    </div>

    <div class="admin-card p-6">
        <div class="flex items-center">
            <div class="w-12 h-12 bg-red-100 rounded-xl flex items-center justify-center">
                <i class="fas fa-times text-red-600 text-xl"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">Rejected</p>
                <p class="text-2xl font-bold text-gray-900"><?php echo number_format($stats['rejected_count']); ?></p>
            </div>
        </div>
    </div>
</div>

<!-- Current Filter Summary -->
<div class="admin-card p-6 mb-6">
    <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
            <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl flex items-center justify-center">
                <i class="fas fa-chart-bar text-white text-xl"></i>
            </div>
            <div>
                <h3 class="text-lg font-semibold text-gray-900">
                    <?php echo ucfirst($status_filter); ?> Requests Summary
                </h3>
                <p class="text-sm text-gray-600">
                    <?php echo number_format($current_stats['count']); ?> requests •
                    Total: $<?php echo number_format($current_stats['amount'] ?? 0, 2); ?>
                </p>
            </div>
        </div>
        <div class="status-badge status-<?php echo $status_filter; ?>">
            <?php echo ucfirst($status_filter); ?>
        </div>
    </div>
</div>

<div id="feedback-message" class="hidden p-4 mb-4 text-sm rounded-lg" role="alert"></div>

<!-- Requests Table -->
<div class="admin-card p-0 overflow-hidden">
    <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center space-x-3">
            <div class="w-8 h-8 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-lg flex items-center justify-center">
                <i class="fas fa-money-bill-wave text-white text-sm"></i>
            </div>
            <div>
                <h3 class="text-lg font-semibold text-gray-900">Withdrawal Requests</h3>
                <p class="text-sm text-gray-600"><?php echo ucfirst($status_filter); ?> withdrawal requests from users</p>
            </div>
        </div>
    </div>

    <div class="overflow-x-auto" style="border-radius: 0 0 0.75rem 0.75rem;">
        <table class="admin-table">
            <thead>
                <tr>
                    <th>User</th>
                    <th>Amount</th>
                    <th>Binance Address</th>
                    <th>Requested</th>
                    <th>Status</th>
                    <?php if ($status_filter === 'pending'): ?>
                        <th>Actions</th>
                    <?php endif; ?>
                </tr>
            </thead>
            <tbody>
                <?php if ($requests_result->num_rows > 0): ?>
                    <?php while ($request = $requests_result->fetch_assoc()): ?>
                        <tr id="request-row-<?php echo $request['id']; ?>">
                            <td>
                                <div class="flex items-center space-x-3">
                                    <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                        <i class="fas fa-user text-blue-600 text-xs"></i>
                                    </div>
                                    <div>
                                        <div class="text-sm font-medium text-gray-900">
                                            <?php echo htmlspecialchars($request['user_name']); ?>
                                        </div>
                                        <div class="text-xs text-gray-500">
                                            ID: <?php echo htmlspecialchars($request['user_identifier']); ?>
                                        </div>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <span class="text-lg font-bold text-green-600">
                                    $<?php echo number_format($request['amount'], 2); ?>
                                </span>
                            </td>
                            <td>
                                <div class="flex items-center space-x-2">
                                    <div class="w-6 h-6 bg-yellow-100 rounded flex items-center justify-center">
                                        <i class="fas fa-wallet text-yellow-600 text-xs"></i>
                                    </div>
                                    <div class="text-sm font-mono text-gray-900 break-all max-w-xs">
                                        <?php echo htmlspecialchars($request['binance_address']); ?>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div class="flex flex-col items-center">
                                    <div class="text-sm font-medium text-gray-900 mb-1">
                                        <?php echo date('M j, Y', strtotime($request['requested_at'])); ?>
                                    </div>
                                    <div class="text-xs text-gray-500">
                                        <?php echo date('g:i A', strtotime($request['requested_at'])); ?>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <span class="status-badge status-<?php echo $request['status']; ?>">
                                    <?php echo ucfirst($request['status']); ?>
                                </span>
                            </td>
                            <?php if ($status_filter === 'pending'): ?>
                                <td>
                                    <div class="flex items-center justify-center space-x-2">
                                        <button class="action-btn action-btn-approve"
                                            onclick="showApprovalModal(<?php echo $request['id']; ?>, '<?php echo htmlspecialchars($request['user_name']); ?>', <?php echo $request['amount']; ?>)"
                                            title="Approve Request">
                                            <i class="fas fa-check text-sm"></i>
                                        </button>
                                        <button class="action-btn action-btn-reject"
                                            onclick="showRejectionModal(<?php echo $request['id']; ?>, '<?php echo htmlspecialchars($request['user_name']); ?>', <?php echo $request['amount']; ?>)"
                                            title="Reject Request">
                                            <i class="fas fa-times text-sm"></i>
                                        </button>
                                    </div>
                                </td>
                            <?php endif; ?>
                        </tr>
                    <?php endwhile; ?>
                <?php else: ?>
                    <tr>
                        <td colspan="<?php echo $status_filter === 'pending' ? '6' : '5'; ?>" style="text-align: center; padding: 3rem;">
                            <div class="flex flex-col items-center">
                                <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                                    <i class="fas fa-money-bill-wave text-gray-400 text-2xl"></i>
                                </div>
                                <h3 class="text-lg font-medium text-gray-900 mb-2">No <?php echo ucfirst($status_filter); ?> Requests</h3>
                                <p class="text-sm text-gray-500 mb-4">No withdrawal requests found for the selected status.</p>
                            </div>
                        </td>
                    </tr>
                <?php endif; ?>
            </tbody>
        </table>
    </div>
</div>

<?php
// Capture the page content
$page_content = ob_get_clean();

// Include the admin layout
include 'admin/includes/layout.php';
?>

<!-- Approval Modal -->
<div id="approval-modal" class="modal-overlay fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900">Approve Withdrawal</h3>
                <button type="button" onclick="closeModal('approval-modal')" class="text-gray-400 hover:text-gray-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>

            <div class="mb-4">
                <p class="text-sm text-gray-600">Are you sure you want to approve this withdrawal request?</p>
                <div class="mt-2 p-3 bg-gray-50 rounded">
                    <p><strong>User:</strong> <span id="approve-user-name"></span></p>
                    <p><strong>Amount:</strong> $<span id="approve-amount"></span></p>
                </div>
            </div>

            <div class="mb-4">
                <label for="approve-transaction-hash" class="block text-sm font-medium text-gray-700">Transaction Hash (Optional)</label>
                <input type="text" id="approve-transaction-hash" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" placeholder="Enter blockchain transaction hash">
            </div>

            <div class="mb-4">
                <label for="approve-notes" class="block text-sm font-medium text-gray-700">Admin Notes (Optional)</label>
                <textarea id="approve-notes" rows="3" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" placeholder="Add any notes about this approval"></textarea>
            </div>

            <div class="flex space-x-3">
                <button type="button" onclick="closeModal('approval-modal')" class="flex-1 px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors">
                    Cancel
                </button>
                <button type="button" onclick="processWithdrawal('approve')" class="flex-1 px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors">
                    Approve
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Rejection Modal -->
<div id="rejection-modal" class="modal-overlay fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900">Reject Withdrawal</h3>
                <button type="button" onclick="closeModal('rejection-modal')" class="text-gray-400 hover:text-gray-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>

            <div class="mb-4">
                <p class="text-sm text-gray-600">Are you sure you want to reject this withdrawal request?</p>
                <div class="mt-2 p-3 bg-gray-50 rounded">
                    <p><strong>User:</strong> <span id="reject-user-name"></span></p>
                    <p><strong>Amount:</strong> $<span id="reject-amount"></span></p>
                </div>
            </div>

            <div class="mb-4">
                <label for="reject-notes" class="block text-sm font-medium text-gray-700">Reason for Rejection <span class="text-red-500">*</span></label>
                <textarea id="reject-notes" rows="3" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" placeholder="Please provide a reason for rejecting this withdrawal" required></textarea>
            </div>

            <div class="flex space-x-3">
                <button type="button" onclick="closeModal('rejection-modal')" class="flex-1 px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors">
                    Cancel
                </button>
                <button type="button" onclick="processWithdrawal('reject')" class="flex-1 px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors">
                    Reject
                </button>
            </div>
        </div>
    </div>
</div>

<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>
    let currentRequestId = null;

    function showApprovalModal(requestId, userName, amount) {
        currentRequestId = requestId;
        document.getElementById('approve-user-name').textContent = userName;
        document.getElementById('approve-amount').textContent = amount.toFixed(2);
        document.getElementById('approval-modal').classList.remove('hidden');
    }

    function showRejectionModal(requestId, userName, amount) {
        currentRequestId = requestId;
        document.getElementById('reject-user-name').textContent = userName;
        document.getElementById('reject-amount').textContent = amount.toFixed(2);
        document.getElementById('rejection-modal').classList.remove('hidden');
    }

    function closeModal(modalId) {
        document.getElementById(modalId).classList.add('hidden');
        // Clear form fields
        if (modalId === 'approval-modal') {
            document.getElementById('approve-transaction-hash').value = '';
            document.getElementById('approve-notes').value = '';
        } else if (modalId === 'rejection-modal') {
            document.getElementById('reject-notes').value = '';
        }
        // Reset current request ID
        currentRequestId = null;
    }

    function processWithdrawal(action) {
        if (!currentRequestId) return;

        let notes = '';
        let transactionHash = '';

        if (action === 'approve') {
            notes = document.getElementById('approve-notes').value;
            transactionHash = document.getElementById('approve-transaction-hash').value;
        } else if (action === 'reject') {
            notes = document.getElementById('reject-notes').value;
            if (!notes.trim()) {
                showFeedback('Please provide a reason for rejection.', 'error');
                return;
            }
        }

        $.ajax({
            url: '/netvis/ui/api/process_withdrawal_request.php',
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({
                request_id: currentRequestId,
                action: action,
                notes: notes,
                transaction_hash: transactionHash
            }),
            success: function(response) {
                console.log('Admin withdrawal response:', response);
                console.log('Action:', action);

                if (response.success) {
                    // Close modal immediately
                    const modalToClose = action === 'approve' ? 'approval-modal' : 'rejection-modal';
                    console.log('Closing modal:', modalToClose);
                    closeModal(modalToClose);

                    // Show success message
                    console.log('Showing success message for:', action);
                    showFeedback(`Withdrawal request ${action}ed successfully. Page will refresh shortly...`, 'success');

                    // Refresh page after 2 seconds to show updated data
                    setTimeout(function() {
                        console.log('Refreshing admin page...');
                        window.location.reload();
                    }, 2000);
                } else {
                    console.log('Request failed:', response.message);
                    showFeedback(response.message || 'An error occurred.', 'error');
                }
            },
            error: function(jqXHR) {
                console.log('Admin withdrawal error details:', jqXHR);
                console.log('Status:', jqXHR.status);
                console.log('Response text:', jqXHR.responseText);
                console.log('Response JSON:', jqXHR.responseJSON);

                // Check if this is actually a success response treated as error
                if (jqXHR.responseText && (jqXHR.responseText.includes('successfully') || jqXHR.responseText.includes('success'))) {
                    console.log('Detected success in error response, treating as success');

                    // Close modal
                    const modalToClose = action === 'approve' ? 'approval-modal' : 'rejection-modal';
                    console.log('Closing modal from error handler:', modalToClose);
                    closeModal(modalToClose);

                    // Show success message
                    showFeedback(`Withdrawal request ${action}ed successfully. Page will refresh shortly...`, 'success');

                    // Refresh page after 2 seconds to show updated data
                    setTimeout(function() {
                        console.log('Refreshing admin page from error handler...');
                        window.location.reload();
                    }, 2000);
                    return;
                }

                let errorMsg = 'A network error occurred. Please try again.';

                if (jqXHR.responseJSON && jqXHR.responseJSON.message) {
                    errorMsg = jqXHR.responseJSON.message;
                } else if (jqXHR.responseText) {
                    errorMsg = 'Server error occurred. Please try again.';
                }

                showFeedback(errorMsg, 'error');
            }
        });
    }

    function showFeedback(message, type) {
        const feedbackMessage = $('#feedback-message');
        feedbackMessage.text(message);

        // Clear all previous classes
        feedbackMessage.removeClass('hidden bg-red-50 bg-green-50 text-red-800 text-green-800 border-red-200 border-green-200');

        // Add appropriate classes based on type
        if (type === 'success') {
            feedbackMessage.addClass('p-4 mb-4 text-sm text-green-800 rounded-lg bg-green-50 border border-green-200');
        } else {
            feedbackMessage.addClass('p-4 mb-4 text-sm text-red-800 rounded-lg bg-red-50 border border-red-200');
        }

        // Auto-hide after 5 seconds (but not for success messages that trigger page refresh)
        if (type !== 'success' || !message.includes('refresh')) {
            setTimeout(() => {
                feedbackMessage.addClass('hidden');
            }, 5000);
        }
    }

    // Add keyboard support for closing modals
    $(document).keydown(function(e) {
        if (e.key === 'Escape') {
            // Close any open modal
            if (!$('#approval-modal').hasClass('hidden')) {
                closeModal('approval-modal');
            }
            if (!$('#rejection-modal').hasClass('hidden')) {
                closeModal('rejection-modal');
            }
        }
    });

    // Add click outside modal to close
    $(document).on('click', '.modal-overlay', function(e) {
        if (e.target === this) {
            if ($(this).attr('id') === 'approval-modal') {
                closeModal('approval-modal');
            } else if ($(this).attr('id') === 'rejection-modal') {
                closeModal('rejection-modal');
            }
        }
    });
</script>
</body>

</html>