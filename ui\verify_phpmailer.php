<?php
// Verify PHPMailer installation and configuration
echo "<h2>PHPMailer Verification</h2>";

// Check if Composer autoloader exists
if (file_exists('vendor/autoload.php')) {
    echo "✅ Composer autoloader found<br>";

    // Include autoloader
    require_once 'vendor/autoload.php';

    // Check if PHPMailer classes are available
    if (class_exists('PHPMailer\PHPMailer\PHPMailer')) {
        echo "✅ PHPMailer class available<br>";

        // Test basic PHPMailer functionality
        try {
            $mail = new PHPMailer\PHPMailer\PHPMailer(true);
            echo "✅ PHPMailer instance created successfully<br>";

            // Load email configuration
            $config = include 'email_config.php';
            echo "✅ Email configuration loaded<br>";

            // Test SMTP configuration (without actually sending)
            $mail->isSMTP();
            $mail->Host = $config['smtp_host'];
            $mail->SMTPAuth = true;
            $mail->Username = $config['smtp_username'];
            $mail->Password = $config['smtp_password'];
            $mail->SMTPSecure = PHPMailer\PHPMailer\PHPMailer::ENCRYPTION_SMTPS;
            $mail->Port = $config['smtp_port'];

            echo "✅ SMTP configuration set<br>";
            echo "<br><strong>Configuration Details:</strong><br>";
            echo "Host: " . htmlspecialchars($config['smtp_host']) . "<br>";
            echo "Port: " . htmlspecialchars($config['smtp_port']) . "<br>";
            echo "Username: " . htmlspecialchars($config['smtp_username']) . "<br>";
            echo "From: " . htmlspecialchars($config['from_email']) . "<br>";
        } catch (Exception $e) {
            echo "❌ Error creating PHPMailer instance: " . $e->getMessage() . "<br>";
        }
    } else {
        echo "❌ PHPMailer class not found<br>";
        echo "Run: composer require phpmailer/phpmailer<br>";
    }
} else {
    echo "❌ Composer autoloader not found<br>";
    echo "Make sure you have run 'composer install' in the project root<br>";
}

echo "<br><a href='test_email.php'>Test Email Sending</a> | <a href='login.php'>Back to Login</a>";
