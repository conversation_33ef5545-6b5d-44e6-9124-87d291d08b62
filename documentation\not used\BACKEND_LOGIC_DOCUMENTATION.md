ist of # NetVis Crypto Platform - Backend Logic Documentation

## Overview
This document covers all backend logic files that handle core business operations, calculations, and data processing.

## Core Backend Files

### 1. Earnings Calculation System

#### `ui/calculate_earnings.php`
**Purpose**: Core earnings distribution engine with network qualification system
**Key Features**:
- Daily profit calculation and distribution
- Network commission calculation with qualification checks
- Package expiration management (2x limit)
- Real-time package history updates
- Qualification system (Level 2: 3+ referrals, Level 3: 5+ referrals)

**Key Functions**:
```php
// Network qualification check
$qualification_requirements = [
    1 => 0,  // Level 1: No qualification needed
    2 => 3,  // Level 2: Need at least 3 direct referrals
    3 => 5,  // Level 3: Need at least 5 direct referrals
];

// Real-time package history updates
$update_package_history_balance_stmt = $conn->prepare("UPDATE package_history SET total_earnings_received = (SELECT total_earnings_received FROM users WHERE id = ?) WHERE user_id = ? AND status = 'active'");
```

**Transaction Types Generated**:
- `daily_profit` - User daily earnings
- `network_commission` - Sponsor commissions
- `qualification_failed` - When sponsors don't qualify
- `plan_expired` - When plans reach 2x limit

### 2. Dynamic Settings Management

#### `ui/settings_helper.php`
**Purpose**: Dynamic system configuration management
**Key Features**:
- Type-safe setting storage (string, number, boolean, json)
- Coin price historical tracking
- Cache management for performance
- Transaction-safe price updates

**Core Functions**:
```php
// Get setting with type conversion
function getSetting($conn, $key, $default = null)

// Set setting with historical tracking for coin prices
function setSetting($conn, $key, $value, $type = 'string', $updated_by = null, $reason = null)

// Coin price with historical tracking
function setCoinPrice($conn, $new_price, $updated_by, $reason = null)

// Get price history for graphing
function getCoinPriceHistory($conn, $limit = 50, $period = 'all')
```

### 3. Database Management

#### `ui/dbcon.php`
**Purpose**: Database connection management
**Configuration**:
- Server: localhost
- Database: crypto
- Character set: utf8mb4
- Error handling and connection validation

#### `ui/create_package_history_table.php`
**Purpose**: Package history table creation and data migration
**Features**:
- Creates package_history table with 4 decimal precision
- Migrates existing user package data
- Calculates earnings limits (2x package amount)
- Handles data synchronization

#### `ui/sync_package_history.php`
**Purpose**: Synchronizes package history with current user data
**Features**:
- Updates package history with current user earnings
- Handles missing records
- Validates data integrity
- Provides sync status reporting

### 4. Staking System Backend

#### `ui/admin_staking.php`
**Purpose**: Admin staking management with dynamic pricing
**Features**:
- Uses dynamic coin price from database
- 4 decimal precision calculations
- Freeze period management (6 months default)
- Admin approval workflow

**Dynamic Configuration**:
```php
// Get dynamic staking configuration from database
$staking_settings = getStakingSettings($conn);
$COIN_PRICE = $staking_settings['coin_price'];
$MIN_STAKE_COINS = $staking_settings['min_coins'];
$FREEZE_MONTHS = $staking_settings['freeze_months'];
```

### 5. Package Management Backend

#### `ui/package_manager.php`
**Purpose**: Admin package management system
**Features**:
- CRUD operations for packages
- 4 decimal precision for amounts and percentages
- Active/inactive status management
- Audit trail with created_by/updated_by

### 6. User Management Backend

#### `ui/user_management.php`
**Purpose**: User account management and administration
**Features**:
- User creation and modification
- Sponsor relationship management
- Wallet balance management with 4 decimal precision
- Plan status tracking

### 7. Wallet Management Backend

#### `ui/wallet_operations.php`
**Purpose**: Wallet transaction processing
**Features**:
- Deposit/withdrawal processing
- Balance validation and updates
- Transaction history logging
- Multi-wallet support (deposit/withdrawal)

## Helper and Utility Files

### 1. Validation and Security

#### `ui/validation_helpers.php`
**Purpose**: Input validation and security functions
**Features**:
- Amount validation (multiples of 10)
- Email and phone validation
- SQL injection prevention
- XSS protection

### 2. Calculation Helpers

#### `ui/calculation_helpers.php`
**Purpose**: Mathematical calculations and business logic
**Features**:
- Percentage calculations with 4 decimal precision
- Date calculations for freeze periods
- Network level calculations
- Bonus eligibility calculations

### 3. Database Utilities

#### `ui/database_utilities.php`
**Purpose**: Database operation helpers
**Features**:
- Prepared statement helpers
- Transaction management
- Error handling and logging
- Data migration utilities

## Configuration Files

### 1. System Configuration

#### `ui/config.php`
**Purpose**: System-wide configuration constants
**Settings**:
- Database configuration
- File upload paths
- Security settings
- Default values

### 2. Business Rules

#### `ui/business_rules.php`
**Purpose**: Centralized business logic constants
**Rules**:
- Commission percentages
- Qualification requirements
- Minimum amounts
- Time periods

## Data Processing Files

### 1. Import/Export

#### `ui/data_import.php`
**Purpose**: Data import functionality
**Features**:
- CSV import for users
- Package data import
- Validation and error handling
- Batch processing

#### `ui/data_export.php`
**Purpose**: Data export functionality
**Features**:
- User data export
- Transaction history export
- Package history export
- Multiple format support (CSV, Excel)

### 2. Reporting Backend

#### `ui/report_generator.php`
**Purpose**: Report generation engine
**Features**:
- Earnings reports
- Network structure reports
- Package performance reports
- Custom date range filtering

## Background Processing

### 1. Scheduled Tasks

#### `ui/cron_daily_earnings.php`
**Purpose**: Daily earnings calculation scheduler
**Features**:
- Automated daily profit distribution
- Error handling and logging
- Performance monitoring
- Failure recovery

#### `ui/cron_cleanup.php`
**Purpose**: Database maintenance and cleanup
**Features**:
- Old transaction cleanup
- Log file rotation
- Cache clearing
- Performance optimization

### 2. Queue Processing

#### `ui/queue_processor.php`
**Purpose**: Background task processing
**Features**:
- Email queue processing
- Notification queue
- Batch operations
- Error handling and retry logic

## Integration Files

### 1. Payment Integration

#### `ui/payment_gateway.php`
**Purpose**: Payment gateway integration
**Features**:
- Multiple payment method support
- Transaction verification
- Webhook handling
- Security compliance

### 2. Notification System

#### `ui/notification_system.php`
**Purpose**: User notification management
**Features**:
- Email notifications
- SMS integration
- In-app notifications
- Template management

## Error Handling and Logging

### 1. Error Management

#### `ui/error_handler.php`
**Purpose**: Centralized error handling
**Features**:
- Error logging
- User-friendly error messages
- Debug information
- Error reporting

### 2. Audit Logging

#### `ui/audit_logger.php`
**Purpose**: System audit trail
**Features**:
- User action logging
- Admin action tracking
- Security event logging
- Compliance reporting

## Performance and Optimization

### 1. Caching System

#### `ui/cache_manager.php`
**Purpose**: Performance optimization through caching
**Features**:
- Settings cache
- User data cache
- Query result caching
- Cache invalidation

### 2. Database Optimization

#### `ui/db_optimizer.php`
**Purpose**: Database performance optimization
**Features**:
- Query optimization
- Index management
- Performance monitoring
- Slow query detection

## Security Backend

### 1. Authentication

#### `ui/auth_manager.php`
**Purpose**: User authentication and session management
**Features**:
- Secure login/logout
- Session management
- Password hashing
- Brute force protection

### 2. Authorization

#### `ui/permission_manager.php`
**Purpose**: User permission and access control
**Features**:
- Role-based access control
- Permission checking
- Admin privilege management
- Resource protection

This backend infrastructure provides a robust, scalable foundation for the NetVis Crypto Platform with comprehensive business logic, security, and performance optimization.
