<?php
session_start();

// Check if user is admin
if (!isset($_SESSION['user_id']) || !$_SESSION['is_admin']) {
    header("Location: login.php");
    exit();
}

include 'dbcon.php';

echo "<h2>Test Earnings Calculation with Qualification System</h2>";
echo "<p>This script tests the earnings calculation with the new network qualification system to ensure no errors occur.</p>";

// Show current system status
echo "<h3>1. System Status Check</h3>";

// Check if users have packages
$users_with_packages_sql = "SELECT COUNT(*) as count FROM users WHERE package IS NOT NULL AND package != '' AND is_active = 1 AND is_admin = 0 AND deleted_at IS NULL";
$users_result = $conn->query($users_with_packages_sql);
$users_count = $users_result->fetch_assoc()['count'];

echo "<p><strong>Active users with packages:</strong> $users_count</p>";

if ($users_count == 0) {
    echo "<div style='background: #ffe8e8; padding: 15px; border: 1px solid #ff0000; margin: 10px 0;'>";
    echo "<p>❌ No active users with packages found. Cannot test earnings calculation.</p>";
    echo "<p>Please ensure there are users with active packages in the system.</p>";
    echo "</div>";
    exit();
}

// Show qualification status before earnings
echo "<h3>2. Pre-Earnings Qualification Status</h3>";
$qualification_sql = "SELECT u.id, u.name, u.user_id as user_identifier, u.package,
                      (SELECT COUNT(*) FROM users ref WHERE ref.sponser_id = u.id AND ref.is_active = 1 AND ref.deleted_at IS NULL AND ref.package IS NOT NULL AND ref.package != '') as direct_referrals
                      FROM users u 
                      WHERE u.is_active = 1 AND u.is_admin = 0 AND u.deleted_at IS NULL AND u.package IS NOT NULL AND u.package != ''
                      ORDER BY direct_referrals DESC
                      LIMIT 10";

$qual_result = $conn->query($qualification_sql);
if ($qual_result->num_rows > 0) {
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr style='background: #f0f0f0;'><th>User</th><th>Package</th><th>Direct Referrals</th><th>Level 1</th><th>Level 2</th><th>Level 3</th></tr>";

    while ($user = $qual_result->fetch_assoc()) {
        $direct_count = $user['direct_referrals'];
        $level1 = "✅ Qualified";
        $level2 = $direct_count >= 3 ? "✅ Qualified" : "❌ Need " . (3 - $direct_count);
        $level3 = $direct_count >= 5 ? "✅ Qualified" : "❌ Need " . (5 - $direct_count);

        echo "<tr>";
        echo "<td>{$user['name']} ({$user['user_identifier']})</td>";
        echo "<td>{$user['package']}</td>";
        echo "<td style='text-align: center;'>$direct_count</td>";
        echo "<td style='text-align: center;'>$level1</td>";
        echo "<td style='text-align: center;'>$level2</td>";
        echo "<td style='text-align: center;'>$level3</td>";
        echo "</tr>";
    }
    echo "</table>";
}

// Test earnings calculation
echo "<h3>3. Run Earnings Calculation Test</h3>";
if (isset($_POST['run_test'])) {
    echo "<div style='background: #e8f5e8; padding: 15px; border: 1px solid #4CAF50; margin: 10px 0;'>";
    echo "<h4>🔄 Running Earnings Calculation...</h4>";

    // Capture any output or errors
    ob_start();
    $error_occurred = false;

    try {
        // Include the earnings calculation script
        include 'calculate_earnings.php';
        $output = ob_get_clean();

        echo "<p>✅ <strong>Earnings calculation completed successfully!</strong></p>";

        if (!empty($output)) {
            echo "<h5>Output:</h5>";
            echo "<pre style='background: #f5f5f5; padding: 10px; border: 1px solid #ddd;'>$output</pre>";
        }
    } catch (Exception $e) {
        $error_occurred = true;
        $error_message = $e->getMessage();
        ob_end_clean();

        echo "<p>❌ <strong>Error occurred during earnings calculation:</strong></p>";
        echo "<pre style='background: #ffe8e8; padding: 10px; border: 1px solid #ff0000; color: red;'>$error_message</pre>";
    }

    if (!$error_occurred) {
        // Show recent wallet history to verify earnings were distributed
        echo "<h5>Recent Transactions (Last 10):</h5>";
        $recent_sql = "SELECT wh.*, u.name, u.user_id as user_identifier 
                       FROM wallet_history wh 
                       JOIN users u ON wh.user_id = u.id 
                       WHERE wh.type IN ('daily_profit', 'network_commission', 'qualification_failed', 'plan_expired')
                       ORDER BY wh.created_at DESC 
                       LIMIT 10";

        $recent_result = $conn->query($recent_sql);
        if ($recent_result->num_rows > 0) {
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
            echo "<tr style='background: #f0f0f0;'><th>User</th><th>Amount</th><th>Type</th><th>Description</th><th>Time</th></tr>";
            while ($transaction = $recent_result->fetch_assoc()) {
                $type_color = '';
                switch ($transaction['type']) {
                    case 'daily_profit':
                        $type_color = 'color: green;';
                        break;
                    case 'network_commission':
                        $type_color = 'color: blue;';
                        break;
                    case 'qualification_failed':
                        $type_color = 'color: orange;';
                        break;
                    case 'plan_expired':
                        $type_color = 'color: red;';
                        break;
                }

                echo "<tr>";
                echo "<td>{$transaction['name']} ({$transaction['user_identifier']})</td>";
                echo "<td>$" . number_format($transaction['amount'], 2) . "</td>";
                echo "<td style='$type_color'>{$transaction['type']}</td>";
                echo "<td>{$transaction['description']}</td>";
                echo "<td>" . date('H:i:s', strtotime($transaction['created_at'])) . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p>No recent transactions found.</p>";
        }

        // Show qualification failures specifically
        echo "<h5>Qualification Failures (if any):</h5>";
        $failures_sql = "SELECT wh.*, u.name, u.user_id as user_identifier 
                         FROM wallet_history wh 
                         JOIN users u ON wh.user_id = u.id 
                         WHERE wh.type = 'qualification_failed' 
                         ORDER BY wh.created_at DESC 
                         LIMIT 5";

        $failures_result = $conn->query($failures_sql);
        if ($failures_result->num_rows > 0) {
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
            echo "<tr style='background: #fff3cd;'><th>User</th><th>Reason</th><th>Time</th></tr>";
            while ($failure = $failures_result->fetch_assoc()) {
                echo "<tr>";
                echo "<td>{$failure['name']} ({$failure['user_identifier']})</td>";
                echo "<td>{$failure['description']}</td>";
                echo "<td>" . date('H:i:s', strtotime($failure['created_at'])) . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p>✅ No qualification failures - all eligible users received their commissions!</p>";
        }
    }

    echo "</div>";
}

echo "<form method='POST'>";
echo "<button type='submit' name='run_test' style='background: #4CAF50; color: white; padding: 15px 30px; border: none; cursor: pointer; font-size: 16px; border-radius: 5px;'>🚀 Run Earnings Calculation Test</button>";
echo "</form>";

// Show system information
echo "<h3>4. System Information</h3>";
echo "<div style='background: #f0f8ff; padding: 15px; border: 1px solid #4169E1; margin: 10px 0;'>";
echo "<h4>📊 Qualification System Details:</h4>";
echo "<ul>";
echo "<li><strong>Level 1:</strong> 10% commission - No qualification needed (always receive)</li>";
echo "<li><strong>Level 2:</strong> 5% commission - Need 3+ direct referrals</li>";
echo "<li><strong>Level 3:</strong> 3% commission - Need 5+ direct referrals</li>";
echo "</ul>";
echo "<br>";
echo "<h4>🔧 What This Test Does:</h4>";
echo "<ol>";
echo "<li>Checks system status and user qualification levels</li>";
echo "<li>Runs the earnings calculation with qualification checks</li>";
echo "<li>Shows recent transactions including qualification failures</li>";
echo "<li>Verifies the system works without errors</li>";
echo "</ol>";
echo "</div>";

$conn->close();
