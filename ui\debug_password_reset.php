<?php
// Debug password reset functionality
include 'dbcon.php';

echo "<h2>🔍 Password Reset Debug Tool</h2>";

// Check if password_resets table exists
$result = $conn->query("SHOW TABLES LIKE 'password_resets'");
if ($result->num_rows > 0) {
    echo "<p>✅ password_resets table exists</p>";
    
    // Show table structure
    $result = $conn->query("DESCRIBE password_resets");
    echo "<h3>📋 Table Structure:</h3>";
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        foreach ($row as $value) {
            echo "<td>" . htmlspecialchars($value ?? '') . "</td>";
        }
        echo "</tr>";
    }
    echo "</table>";
    
    // Show current records
    $result = $conn->query("SELECT id, email, LEFT(token, 20) as token_preview, expires_at, created_at FROM password_resets ORDER BY created_at DESC LIMIT 10");
    echo "<h3>📊 Recent Reset Tokens:</h3>";
    if ($result->num_rows > 0) {
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>ID</th><th>Email</th><th>Token Preview</th><th>Expires At</th><th>Created At</th><th>Status</th></tr>";
        while ($row = $result->fetch_assoc()) {
            $now = new DateTime();
            $expires = new DateTime($row['expires_at']);
            $status = $expires > $now ? "✅ Valid" : "❌ Expired";
            
            echo "<tr>";
            echo "<td>" . htmlspecialchars($row['id']) . "</td>";
            echo "<td>" . htmlspecialchars($row['email']) . "</td>";
            echo "<td>" . htmlspecialchars($row['token_preview']) . "...</td>";
            echo "<td>" . htmlspecialchars($row['expires_at']) . "</td>";
            echo "<td>" . htmlspecialchars($row['created_at']) . "</td>";
            echo "<td>" . $status . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>No reset tokens found in database.</p>";
    }
    
} else {
    echo "<p>❌ password_resets table does not exist!</p>";
    echo "<p>💡 Please run create_password_resets_table.php first</p>";
}

// Test token validation if token is provided
if (isset($_GET['token']) && !empty($_GET['token'])) {
    $token = $_GET['token'];
    echo "<h3>🔍 Testing Token: " . htmlspecialchars(substr($token, 0, 20)) . "...</h3>";
    
    $stmt = $conn->prepare('SELECT email, expires_at, created_at FROM password_resets WHERE token = ?');
    $stmt->bind_param('s', $token);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        $row = $result->fetch_assoc();
        echo "<p>✅ Token found in database</p>";
        echo "<p><strong>Email:</strong> " . htmlspecialchars($row['email']) . "</p>";
        echo "<p><strong>Created:</strong> " . htmlspecialchars($row['created_at']) . "</p>";
        echo "<p><strong>Expires:</strong> " . htmlspecialchars($row['expires_at']) . "</p>";
        
        $now = new DateTime();
        $expires = new DateTime($row['expires_at']);
        
        if ($expires > $now) {
            echo "<p>✅ Token is still valid</p>";
            $diff = $now->diff($expires);
            echo "<p><strong>Time remaining:</strong> " . $diff->format('%i minutes %s seconds') . "</p>";
        } else {
            echo "<p>❌ Token has expired</p>";
            $diff = $expires->diff($now);
            echo "<p><strong>Expired:</strong> " . $diff->format('%i minutes %s seconds') . " ago</p>";
        }
        
        // Test the exact query used in reset_password.php
        $stmt2 = $conn->prepare('SELECT email FROM password_resets WHERE token = ? AND expires_at > NOW()');
        $stmt2->bind_param('s', $token);
        $stmt2->execute();
        $result2 = $stmt2->get_result();
        
        if ($result2->num_rows > 0) {
            echo "<p>✅ Token passes validation query</p>";
        } else {
            echo "<p>❌ Token fails validation query (likely expired)</p>";
        }
        
    } else {
        echo "<p>❌ Token not found in database</p>";
    }
}

// Show current server time
echo "<h3>⏰ Server Time Info:</h3>";
echo "<p><strong>Current Server Time:</strong> " . date('Y-m-d H:i:s') . "</p>";
echo "<p><strong>Timezone:</strong> " . date_default_timezone_get() . "</p>";

// Test URL generation
echo "<h3>🔗 URL Generation Test:</h3>";
$test_token = "test123token";
$test_url = "http://" . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . "/reset_password.php?token=" . $test_token;
echo "<p><strong>Generated URL:</strong> <a href='" . htmlspecialchars($test_url) . "'>" . htmlspecialchars($test_url) . "</a></p>";

$conn->close();
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h2, h3 { color: #333; }
p { margin: 10px 0; }
table { margin: 10px 0; }
th, td { padding: 8px; text-align: left; }
th { background-color: #f2f2f2; }
a { color: #007cba; }
</style>

<p><strong>💡 Usage:</strong></p>
<ul>
<li>Run this script to see the current state of password reset tokens</li>
<li>Add <code>?token=YOUR_TOKEN_HERE</code> to test a specific token</li>
<li>Check if the password_resets table exists and has the correct structure</li>
</ul>
