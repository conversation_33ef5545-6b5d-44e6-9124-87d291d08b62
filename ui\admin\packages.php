<?php
session_start();
// require_once 'includes/config.php';

// Check if admin is logged in
// if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
//     header('Location: login.php');
//     exit();
// }

$page_title = "Package Management";
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - CryptoApp Admin</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
</head>

<body class="bg-gray-50">
    <?php include 'includes/sidebar.php'; ?>

    <div class="flex-1 ml-0 lg:ml-72 transition-all duration-300" id="mainContent">
        <?php include 'includes/header.php'; ?>

        <main class="p-6">
            <!-- Page Header -->
            <div class="mb-8">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-3xl font-bold text-gray-900">Package Management</h1>
                        <p class="text-gray-600 mt-2">Create and manage investment packages</p>
                    </div>
                    <div class="flex items-center space-x-3">
                        <div class="bg-white rounded-lg px-4 py-2 shadow-sm border border-gray-200">
                            <span class="text-sm text-gray-500">Total Packages:</span>
                            <span class="font-semibold text-gray-900 ml-2" id="totalPackages">6</span>
                        </div>
                        <div class="bg-white rounded-lg px-4 py-2 shadow-sm border border-gray-200">
                            <span class="text-sm text-gray-500">Active:</span>
                            <span class="font-semibold text-green-600 ml-2" id="activePackages">6</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Create New Package Card -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-200 mb-8">
                <div class="p-6 border-b border-gray-200">
                    <h2 class="text-xl font-semibold text-gray-900 flex items-center">
                        <i class="fas fa-plus-circle text-blue-500 mr-3"></i>
                        Create New Package
                    </h2>
                </div>
                <div class="p-6">
                    <form id="packageForm" class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Package Name</label>
                            <input type="text" id="packageName" name="packageName"
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                                placeholder="Enter package name" required>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Amount ($)</label>
                            <input type="number" id="packageAmount" name="packageAmount"
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                                placeholder="Enter amount" min="1" step="0.01" required>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Profit (%)</label>
                            <input type="number" id="packageProfit" name="packageProfit"
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                                placeholder="Enter profit percentage" min="0" max="100" step="0.01" required>
                        </div>
                        <div class="md:col-span-3">
                            <button type="submit"
                                class="bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white px-8 py-3 rounded-lg font-medium transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl">
                                <i class="fas fa-plus mr-2"></i>
                                Create Package
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Manage Packages Card -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-200">
                <div class="p-6 border-b border-gray-200">
                    <h2 class="text-xl font-semibold text-gray-900 flex items-center">
                        <i class="fas fa-cogs text-green-500 mr-3"></i>
                        Manage Packages
                    </h2>
                </div>
                <div class="overflow-x-auto">
                    <table class="w-full">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                                <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                                <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Profit %</th>
                                <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created By</th>
                                <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Last Updated</th>
                                <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200" id="packagesTableBody">
                            <!-- Package rows will be populated here -->
                        </tbody>
                    </table>
                </div>
            </div>
        </main>
    </div>

    <!-- Edit Package Modal -->
    <div id="editPackageModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center">
        <div class="bg-white rounded-xl shadow-2xl max-w-md w-full mx-4">
            <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">Edit Package</h3>
            </div>
            <div class="p-6">
                <form id="editPackageForm">
                    <input type="hidden" id="editPackageId">
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Package Name</label>
                            <input type="text" id="editPackageName"
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                required>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Amount ($)</label>
                            <input type="number" id="editPackageAmount"
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                min="1" step="0.01" required>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Profit (%)</label>
                            <input type="number" id="editPackageProfit"
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                min="0" max="100" step="0.01" required>
                        </div>
                    </div>
                    <div class="flex justify-end space-x-3 mt-6">
                        <button type="button" onclick="closeEditModal()"
                            class="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors">
                            Cancel
                        </button>
                        <button type="submit"
                            class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg transition-colors">
                            Update Package
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        // Sample package data
        let packages = [{
                id: 1,
                name: 'Elite Club',
                amount: 2500.00,
                profit: 1.50,
                status: 'Active',
                createdBy: 'Admin',
                lastUpdated: 'N/A'
            },
            {
                id: 2,
                name: 'Platinum',
                amount: 1000.00,
                profit: 1.25,
                status: 'Active',
                createdBy: 'Admin',
                lastUpdated: 'N/A'
            },
            {
                id: 3,
                name: 'Premium',
                amount: 500.00,
                profit: 1.00,
                status: 'Active',
                createdBy: 'Admin',
                lastUpdated: 'N/A'
            },
            {
                id: 4,
                name: 'Standard',
                amount: 100.00,
                profit: 0.75,
                status: 'Active',
                createdBy: 'Admin',
                lastUpdated: 'N/A'
            },
            {
                id: 5,
                name: 'Basic',
                amount: 50.00,
                profit: 0.50,
                status: 'Active',
                createdBy: 'Admin',
                lastUpdated: 'N/A'
            },
            {
                id: 6,
                name: 'Trial',
                amount: 10.00,
                profit: 0.25,
                status: 'Active',
                createdBy: 'Admin',
                lastUpdated: 'N/A'
            }
        ];

        // Render packages table
        function renderPackagesTable() {
            const tbody = document.getElementById('packagesTableBody');
            tbody.innerHTML = '';

            packages.forEach(pkg => {
                const row = document.createElement('tr');
                row.className = 'hover:bg-gray-50 transition-colors';
                row.innerHTML = `
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="font-medium text-gray-900">${pkg.name}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-gray-900 font-semibold">$${pkg.amount.toFixed(2)}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-gray-900 font-semibold">${pkg.profit}%</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="inline-flex px-3 py-1 text-xs font-semibold rounded-full ${pkg.status === 'Active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}">
                            ${pkg.status}
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-gray-600">${pkg.createdBy}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-gray-600">${pkg.lastUpdated}</td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="flex space-x-2">
                            <button onclick="editPackage(${pkg.id})" 
                                    class="text-blue-600 hover:text-blue-800 font-medium text-sm transition-colors">
                                Edit
                            </button>
                            <button onclick="togglePackageStatus(${pkg.id})" 
                                    class="text-red-600 hover:text-red-800 font-medium text-sm transition-colors">
                                ${pkg.status === 'Active' ? 'Deactivate' : 'Activate'}
                            </button>
                        </div>
                    </td>
                `;
                tbody.appendChild(row);
            });

            // Update counters
            document.getElementById('totalPackages').textContent = packages.length;
            document.getElementById('activePackages').textContent = packages.filter(p => p.status === 'Active').length;
        }

        // Create new package
        document.getElementById('packageForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const name = document.getElementById('packageName').value;
            const amount = parseFloat(document.getElementById('packageAmount').value);
            const profit = parseFloat(document.getElementById('packageProfit').value);

            // Add new package
            const newPackage = {
                id: packages.length + 1,
                name: name,
                amount: amount,
                profit: profit,
                status: 'Active',
                createdBy: 'Admin',
                lastUpdated: new Date().toLocaleDateString()
            };

            packages.push(newPackage);
            renderPackagesTable();

            // Reset form
            this.reset();

            // Show success message
            Swal.fire({
                icon: 'success',
                title: 'Package Created!',
                text: `${name} package has been created successfully.`,
                timer: 2000,
                showConfirmButton: false
            });
        });

        // Edit package
        function editPackage(id) {
            const pkg = packages.find(p => p.id === id);
            if (pkg) {
                document.getElementById('editPackageId').value = pkg.id;
                document.getElementById('editPackageName').value = pkg.name;
                document.getElementById('editPackageAmount').value = pkg.amount;
                document.getElementById('editPackageProfit').value = pkg.profit;
                document.getElementById('editPackageModal').classList.remove('hidden');
            }
        }

        // Close edit modal
        function closeEditModal() {
            document.getElementById('editPackageModal').classList.add('hidden');
        }

        // Update package
        document.getElementById('editPackageForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const id = parseInt(document.getElementById('editPackageId').value);
            const name = document.getElementById('editPackageName').value;
            const amount = parseFloat(document.getElementById('editPackageAmount').value);
            const profit = parseFloat(document.getElementById('editPackageProfit').value);

            const packageIndex = packages.findIndex(p => p.id === id);
            if (packageIndex !== -1) {
                packages[packageIndex] = {
                    ...packages[packageIndex],
                    name: name,
                    amount: amount,
                    profit: profit,
                    lastUpdated: new Date().toLocaleDateString()
                };

                renderPackagesTable();
                closeEditModal();

                Swal.fire({
                    icon: 'success',
                    title: 'Package Updated!',
                    text: `${name} package has been updated successfully.`,
                    timer: 2000,
                    showConfirmButton: false
                });
            }
        });

        // Toggle package status
        function togglePackageStatus(id) {
            const pkg = packages.find(p => p.id === id);
            if (pkg) {
                const newStatus = pkg.status === 'Active' ? 'Inactive' : 'Active';
                const action = newStatus === 'Active' ? 'activate' : 'deactivate';

                Swal.fire({
                    title: `${action.charAt(0).toUpperCase() + action.slice(1)} Package?`,
                    text: `Are you sure you want to ${action} the ${pkg.name} package?`,
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: newStatus === 'Active' ? '#10b981' : '#ef4444',
                    cancelButtonColor: '#6b7280',
                    confirmButtonText: `Yes, ${action} it!`
                }).then((result) => {
                    if (result.isConfirmed) {
                        pkg.status = newStatus;
                        pkg.lastUpdated = new Date().toLocaleDateString();
                        renderPackagesTable();

                        Swal.fire({
                            icon: 'success',
                            title: `Package ${action}d!`,
                            text: `${pkg.name} package has been ${action}d.`,
                            timer: 2000,
                            showConfirmButton: false
                        });
                    }
                });
            }
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            renderPackagesTable();
        });

        // Close modal when clicking outside
        document.getElementById('editPackageModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeEditModal();
            }
        });
    </script>
</body>

</html>