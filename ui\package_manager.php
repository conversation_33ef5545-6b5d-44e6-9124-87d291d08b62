<?php
session_start();

if (!isset($_SESSION['user_id']) || !$_SESSION['is_admin']) {
    header('Location: login.php');
    exit();
}

include 'dbcon.php';

// SQL to create packages table if it doesn't exist
$sql = 'CREATE TABLE IF NOT EXISTS `packages` (
  `id` int(6) UNSIGNED NOT NULL AUTO_INCREMENT,
  `package_name` varchar(255) NOT NULL,
  `package_amount` decimal(10,2) NOT NULL,
  `profit_percentage` decimal(5,2) NOT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `created_by` int(6) UNSIGNED NOT NULL,
  `updated_by` int(6) UNSIGNED DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `created_by` (`created_by`),
  <PERSON>EY `updated_by` (`updated_by`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;';
$conn->query($sql);

// Handle form submissions for CRUD operations
$action = $_POST['action'] ?? '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $admin_id = $_SESSION['user_id'];

    switch ($action) {
        case 'create':
            $package_name = $_POST['package_name'];
            $package_amount = $_POST['package_amount'];
            $profit_percentage = $_POST['profit_percentage'];

            $stmt = $conn->prepare('INSERT INTO packages (package_name, package_amount, profit_percentage, created_by) VALUES (?, ?, ?, ?)');
            $stmt->bind_param('sddi', $package_name, $package_amount, $profit_percentage, $admin_id);
            $stmt->execute();
            $stmt->close();
            break;

        case 'toggle_active':
            $id = $_POST['id'];
            $current_status = $_POST['current_status'];
            $new_status = $current_status ? 0 : 1;

            $stmt = $conn->prepare('UPDATE packages SET is_active = ?, updated_by = ? WHERE id = ?');
            $stmt->bind_param('iii', $new_status, $admin_id, $id);
            $stmt->execute();
            $stmt->close();
            break;
    }
    header('Location: package_manager.php');
    exit();
}

// Fetch all packages, joining with users table to get creator and updater names
$sql = 'SELECT p.*, creator.name as creator_name, updater.name as updater_name
        FROM packages p
        JOIN users creator ON p.created_by = creator.id
        LEFT JOIN users updater ON p.updated_by = updater.id
        ORDER BY p.id DESC';
$result = $conn->query($sql);

// Page configuration
$page_title = "Package Management";
$additional_css = '
<style>
    .form-input {
        border: 1px solid #d1d5db;
        border-radius: 0.5rem;
        padding: 0.5rem 0.75rem;
        width: 100%;
        transition: border-color 0.2s ease;
    }

    .form-input:focus {
        outline: none;
        border-color: #3b82f6;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }

    .submit-btn {
        background: #3b82f6;
        color: white;
        border: none;
        padding: 0.5rem 1rem;
        border-radius: 0.5rem;
        font-weight: 500;
        transition: background-color 0.2s ease;
        cursor: pointer;
    }

    .submit-btn:hover {
        background: #2563eb;
    }

    .admin-table {
        width: 100%;
        border-collapse: collapse;
    }

    .admin-table thead {
        background: #f8fafc;
        border-bottom: 2px solid #e2e8f0;
    }

    .admin-table thead th {
        color: #374151;
        font-weight: 600;
        padding: 0.75rem;
        text-align: left;
        font-size: 0.875rem;
    }

    .admin-table tbody tr {
        border-bottom: 1px solid #e2e8f0;
    }

    .admin-table tbody tr:hover {
        background-color: #f8fafc;
    }

    .admin-table tbody td {
        padding: 0.75rem;
        vertical-align: middle;
    }

    /* Status badges */
    .status-badge {
        display: inline-flex;
        align-items: center;
        padding: 0.25rem 0.75rem;
        border-radius: 9999px;
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.05em;
    }

    .status-active {
        background-color: #d1fae5;
        color: #065f46;
        border: 1px solid #10b981;
    }

    .status-inactive {
        background-color: #fee2e2;
        color: #991b1b;
        border: 1px solid #ef4444;
    }

    .action-btn {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        padding: 0.375rem;
        border-radius: 0.375rem;
        font-size: 0.875rem;
        transition: all 0.2s ease;
        border: 1px solid;
        cursor: pointer;
        text-decoration: none;
        margin: 0 0.125rem;
        width: 2rem;
        height: 2rem;
    }

    .action-btn-edit {
        background: #eff6ff;
        color: #3b82f6;
        border-color: #dbeafe;
    }

    .action-btn-edit:hover {
        background: #dbeafe;
    }

    .action-btn-toggle {
        background: #fef2f2;
        color: #ef4444;
        border-color: #fecaca;
    }

    .action-btn-toggle:hover {
        background: #fecaca;
    }

    .action-btn-toggle.activate {
        background: #f0fdf4;
        color: #22c55e;
        border-color: #bbf7d0;
    }

    .action-btn-toggle.activate:hover {
        background: #bbf7d0;
    }
</style>
';

// Start output buffering to capture the page content
ob_start();
?>

<!-- Page Header -->
<div class="flex flex-col lg:flex-row justify-between items-start lg:items-center space-y-4 lg:space-y-0 mb-6">
    <div>
        <h1 class="text-3xl font-bold text-gray-900">Package Management</h1>
        <p class="text-gray-600 mt-1">Create and manage investment packages</p>
    </div>
    <div class="flex items-center space-x-2 bg-blue-50 px-4 py-2 rounded-lg border border-blue-200">
        <i class="fas fa-box text-blue-600"></i>
        <span class="text-sm font-medium text-blue-700">Package Manager</span>
    </div>
</div>

<!-- Package Statistics -->
<?php
// Calculate package statistics
$total_packages = $result->num_rows;
$active_packages = 0;
$inactive_packages = 0;
$total_value = 0;

// Reset result pointer to count statistics
$result->data_seek(0);
while ($row = $result->fetch_assoc()) {
    if ($row['is_active']) {
        $active_packages++;
    } else {
        $inactive_packages++;
    }
    $total_value += $row['package_amount'];
}
// Reset result pointer for table display
$result->data_seek(0);
?>

<div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
    <div class="admin-card p-4">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm text-gray-600">Total Packages</p>
                <p class="text-xl font-bold text-gray-900"><?php echo $total_packages; ?></p>
            </div>
            <div class="flex items-center space-x-3 text-sm">
                <span class="flex items-center text-green-600">
                    <i class="fas fa-check-circle mr-1"></i><?php echo $active_packages; ?> Active
                </span>
                <span class="flex items-center text-red-600">
                    <i class="fas fa-times-circle mr-1"></i><?php echo $inactive_packages; ?> Inactive
                </span>
            </div>
        </div>
    </div>

    <div class="admin-card p-4">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm text-gray-600">Total Value</p>
                <p class="text-xl font-bold text-gray-900">$<?php echo number_format($total_value, 2); ?></p>
            </div>
            <div class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                <i class="fas fa-dollar-sign text-purple-600"></i>
            </div>
        </div>
    </div>
</div>

<!-- Create Package Form -->
<div class="admin-card p-4 mb-6">
    <h3 class="text-lg font-semibold text-gray-900 mb-4">
        <i class="fas fa-plus mr-2 text-blue-600"></i>Create New Package
    </h3>

    <form action="package_manager.php" method="POST">
        <input type="hidden" name="action" value="create">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
                <input type="text" name="package_name" required class="form-input" placeholder="Package Name">
            </div>
            <div>
                <input type="number" name="package_amount" step="0.01" required class="form-input" placeholder="Amount ($)">
            </div>
            <div>
                <input type="number" name="profit_percentage" step="0.01" required class="form-input" placeholder="Profit (%)">
            </div>
            <div>
                <button type="submit" class="submit-btn w-full">
                    <i class="fas fa-plus mr-1"></i>Create
                </button>
            </div>
        </div>
    </form>
</div>

<!-- Packages Table -->
<div class="admin-card p-0 overflow-hidden">
    <div class="px-4 py-3 border-b border-gray-200">
        <h3 class="text-lg font-semibold text-gray-900">
            <i class="fas fa-list mr-2 text-blue-600"></i>Manage Packages
        </h3>
    </div>

    <div class="overflow-x-auto">
        <table class="admin-table">
            <thead>
                <tr>
                    <th>Package</th>
                    <th>Amount</th>
                    <th>Profit %</th>
                    <th>Status</th>
                    <th>Created</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                <?php if ($result->num_rows > 0): ?>
                    <?php while ($row = $result->fetch_assoc()): ?>
                        <tr class="<?php echo $row['is_active'] ? '' : 'opacity-60'; ?>">
                            <td>
                                <div class="font-medium text-gray-900">
                                    <?php echo htmlspecialchars($row['package_name']); ?>
                                </div>
                            </td>
                            <td>
                                <span class="font-bold text-green-600">
                                    $<?php echo number_format($row['package_amount'], 2); ?>
                                </span>
                            </td>
                            <td>
                                <span class="font-medium text-purple-600">
                                    <?php echo number_format($row['profit_percentage'], 2); ?>%
                                </span>
                            </td>
                            <td>
                                <span class="status-badge <?php echo $row['is_active'] ? 'status-active' : 'status-inactive'; ?>">
                                    <?php echo $row['is_active'] ? 'Active' : 'Inactive'; ?>
                                </span>
                            </td>
                            <td>
                                <div class="text-sm text-gray-900">
                                    <div><?php echo htmlspecialchars($row['creator_name']); ?></div>
                                    <div class="text-xs text-gray-500"><?php echo date('M j, Y', strtotime($row['created_at'])); ?></div>
                                </div>
                            </td>
                            <td>
                                <div class="flex items-center space-x-1">
                                    <a href="edit_package.php?id=<?php echo $row['id']; ?>" class="action-btn action-btn-edit">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <form action="package_manager.php" method="POST" class="inline-block">
                                        <input type="hidden" name="action" value="toggle_active">
                                        <input type="hidden" name="id" value="<?php echo $row['id']; ?>">
                                        <input type="hidden" name="current_status" value="<?php echo $row['is_active']; ?>">
                                        <button type="submit" class="action-btn action-btn-toggle <?php echo $row['is_active'] ? '' : 'activate'; ?>">
                                            <i class="fas fa-<?php echo $row['is_active'] ? 'times' : 'check'; ?>"></i>
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                    <?php endwhile; ?>
                <?php else: ?>
                    <tr>
                        <td colspan="6" style="text-align: center; padding: 2rem;">
                            <div class="text-gray-500">
                                <i class="fas fa-box text-2xl mb-2"></i>
                                <p>No packages found. Create your first package above.</p>
                            </div>
                        </td>
                    </tr>
                <?php endif; ?>
            </tbody>
        </table>
    </div>
</div>

<?php
// Capture the page content
$page_content = ob_get_clean();

// Close database connection
$conn->close();

// Include the admin layout
include 'admin/includes/layout.php';
?>