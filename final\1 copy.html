<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Finance Dashboard</title>

    <!-- Tailwind CSS for styling -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Google Fonts: Inter -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">

    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css"
        xintegrity="sha512-SnH5WK+bZxgPHs44uWIX+LLJAJ9/2PkPKZ5QiAj6Ta86w+fsb2TkcmfRyVX3pBnMFcV7oQPJkl9QevSCWr3W6A=="
        crossorigin="anonymous" referrerpolicy="no-referrer" />


    <style>
        /* Define the custom theme and other base styles */
        body {
            font-family: 'Inter', sans-serif;
        }

        .theme-ocean {
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
            color: #e2e8f0;
            /* Using a softer white for better readability */
        }

        .theme-ocean .card {
            background: rgba(30, 41, 59, 0.8);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            /* For Safari */
            border: 1px solid rgba(59, 130, 246, 0.2);
            border-radius: 1rem;
            /* Consistent rounded corners */
            padding: 1rem;
            transition: all 0.3s ease;
        }

        .theme-ocean .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        }

        .theme-ocean .accent {
            color: #3b82f6;
        }

        .theme-ocean .secondary {
            color: #06b6d4;
        }

        /* Marquee animation for scrolling text */
        .marquee {
            white-space: nowrap;
            overflow: hidden;
            box-sizing: border-box;
        }

        .marquee-content {
            display: inline-block;
            padding-left: 100%;
            animation: marquee 15s linear infinite;
        }

        @keyframes marquee {
            0% {
                transform: translateX(0);
            }

            100% {
                transform: translateX(-100%);
            }
        }

        /* Custom scrollbar for better aesthetics */
        ::-webkit-scrollbar {
            width: 5px;
        }

        ::-webkit-scrollbar-track {
            background: #1e293b;
        }

        ::-webkit-scrollbar-thumb {
            background: #3b82f6;
            border-radius: 10px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #2563eb;
        }
    </style>
</head>

<body class="theme-ocean flex justify-center items-center min-h-screen p-4">

    <!-- Mobile phone container -->
    <div
        class="w-full max-w-sm h-[850px] bg-slate-900 rounded-[40px] shadow-2xl overflow-hidden border-4 border-gray-800 flex flex-col">

        <!-- Phone Notch -->
        <div class="flex-shrink-0 px-6 pt-4">
            <div class="relative h-7 bg-slate-900">
                <div class="absolute top-0 left-1/2 -translate-x-1/2 w-24 h-5 bg-gray-800 rounded-b-xl"></div>
            </div>
        </div>

        <!-- Top Navigation Bar -->
        <header class="flex-shrink-0 flex items-center justify-between px-6 py-3 border-b border-slate-700/50">
            <!-- Company Logo -->
            <div class="flex items-center gap-2">
                <i class="fab fa-bitcoin fa-2x secondary"></i>
                <span class="font-bold text-white text-lg">CryptoApp</span>
            </div>
            <!-- User Avatar -->
            <div
                class="w-10 h-10 rounded-full bg-blue-500/20 flex items-center justify-center border-2 border-blue-400">
                <i class="fas fa-user fa-lg accent"></i>
            </div>
        </header>


        <!-- Main content with scrolling -->
        <div class="flex-grow overflow-y-auto px-6 py-4">

            <!-- Offer Message -->
            <div class="card marquee text-center mb-4">
                <p class="marquee-content accent font-semibold">
                    LIMITED TIME OFFER: Get 20% Bonus on First Deposit! 🎉
                </p>
            </div>

            <!-- Stats Grid -->
            <div class="grid grid-cols-2 gap-4 mb-4">
                <div class="card text-center">
                    <p class="text-sm text-slate-400">Standard Account</p>
                    <p class="text-2xl font-bold text-white mt-1">$100.00</p>
                </div>
                <div class="card text-center">
                    <p class="text-sm text-slate-400">Today's Profit</p>
                    <p class="text-2xl font-bold secondary mt-1">$0.75</p>
                </div>
                <div class="card text-center">
                    <p class="text-sm text-slate-400">Total Profit</p>
                    <p class="text-2xl font-bold secondary mt-1">$110.75</p>
                </div>
                <div class="card text-center">
                    <p class="text-sm text-slate-400">Wallet Balance</p>
                    <p class="text-2xl font-bold text-white mt-1">$0.00</p>
                </div>
            </div>

            <!-- Stacking Info -->
            <div class="card mb-4">
                <div class="flex justify-between items-center">
                    <div>
                        <p class="text-sm text-slate-400">Stacking</p>
                        <p class="text-xl font-bold text-white">$50.00</p>
                    </div>
                    <div class="text-center">
                        <img src="https://img.icons8.com/fluency/48/bitcoin.png" alt="BTC" class="h-8 w-8 mx-auto" />
                        <p class="text-xs text-slate-400 mt-1">1 BTC = $0.45</p>
                    </div>
                    <div class="text-right">
                        <p class="text-sm text-slate-400">Stacking Value</p>
                        <p class="text-xl font-bold secondary">$52.00</p>
                    </div>
                </div>
                <div class="mt-3 text-xs text-slate-500">
                    <p>Registration: 01/01/24 10:30:00</p>
                    <p>Last Topup: 15/06/24 18:45:15</p>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="grid grid-cols-2 gap-4 mb-4">
                <button class="card flex flex-col items-center justify-center p-4 gap-2">
                    <i class="fas fa-download fa-lg accent"></i>
                    <span class="font-semibold text-white">Deposit</span>
                </button>
                <button class="card flex flex-col items-center justify-center p-4 gap-2">
                    <i class="fas fa-plus-circle fa-lg accent"></i>
                    <span class="font-semibold text-white">Topup</span>
                </button>
                <button class="card flex flex-col items-center justify-center p-4 gap-2">
                    <i class="fas fa-exchange-alt fa-lg accent"></i>
                    <span class="font-semibold text-white">Transfer</span>
                </button>
                <button class="card flex flex-col items-center justify-center p-4 gap-2">
                    <i class="fas fa-layer-group fa-lg secondary"></i>
                    <span class="font-semibold text-white">Stacking Now</span>
                </button>
            </div>

            <!-- Recent Transactions -->
            <div class="card">
                <h3 class="font-bold text-white mb-3">Recent Transactions</h3>
                <ul class="space-y-3">
                    <li class="flex justify-between items-center">
                        <div class="flex items-center gap-3">
                            <div class="w-10 h-10 rounded-full bg-green-500/20 flex items-center justify-center">
                                <i class="fas fa-arrow-down text-green-400"></i>
                            </div>
                            <div>
                                <p class="font-semibold text-white">Deposit from Binance</p>
                                <p class="text-xs text-slate-400">July 03, 2025</p>
                            </div>
                        </div>
                        <p class="font-semibold text-green-400">+$50.00</p>
                    </li>
                    <li class="flex justify-between items-center">
                        <div class="flex items-center gap-3">
                            <div class="w-10 h-10 rounded-full bg-red-500/20 flex items-center justify-center">
                                <i class="fas fa-arrow-up text-red-400"></i>
                            </div>
                            <div>
                                <p class="font-semibold text-white">Withdrawal to Bank</p>
                                <p class="text-xs text-slate-400">July 01, 2025</p>
                            </div>
                        </div>
                        <p class="font-semibold text-red-400">-$25.50</p>
                    </li>
                    <li class="flex justify-between items-center">
                        <div class="flex items-center gap-3">
                            <div class="w-10 h-10 rounded-full bg-sky-500/20 flex items-center justify-center">
                                <i class="fas fa-sync-alt text-sky-400"></i>
                            </div>
                            <div>
                                <p class="font-semibold text-white">Staking Reward</p>
                                <p class="text-xs text-slate-400">June 30, 2025</p>
                            </div>
                        </div>
                        <p class="font-semibold text-sky-400">+$2.00</p>
                    </li>
                </ul>
            </div>
        </div>

        <!-- Sticky Bottom Navigation Bar -->
        <div class="flex-shrink-0">
            <div class="bg-slate-800/80 backdrop-blur-sm mx-4 mb-4 rounded-2xl border border-slate-700">
                <div class="flex justify-around items-center h-16">
                    <a href="#" class="flex flex-col items-center gap-1 accent">
                        <i class="fas fa-home"></i>
                        <span class="text-xs">Home</span>
                    </a>
                    <a href="#" class="flex flex-col items-center gap-1 text-slate-400 hover:accent">
                        <i class="fas fa-wallet"></i>
                        <span class="text-xs">Wallet</span>
                    </a>
                    <a href="#" class="flex flex-col items-center gap-1 text-slate-400 hover:accent">
                        <i class="fas fa-users"></i>
                        <span class="text-xs">Team</span>
                    </a>
                    <a href="#" class="flex flex-col items-center gap-1 text-slate-400 hover:accent">
                        <i class="fas fa-user"></i>
                        <span class="text-xs">Profile</span>
                    </a>
                </div>
            </div>
        </div>
    </div>

</body>

</html>