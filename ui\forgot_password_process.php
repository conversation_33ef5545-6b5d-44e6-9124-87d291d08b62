<?php
header('Content-Type: application/json');
include 'dbcon.php';
include 'email_helper.php';

$response = ['success' => false, 'message' => ''];

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $email = $_POST['email'] ?? '';

    // Basic validation
    if (empty($email)) {
        $response['message'] = 'Email address is required.';
        echo json_encode($response);
        exit;
    }

    // Validate email format
    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $response['message'] = 'Please enter a valid email address.';
        echo json_encode($response);
        exit;
    }

    // Check if email exists in database
    $stmt = $conn->prepare('SELECT id, name FROM users WHERE email = ? AND is_active = 1');
    $stmt->bind_param('s', $email);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows > 0) {
        $user = $result->fetch_assoc();

        // Generate a secure reset token
        $reset_token = bin2hex(random_bytes(32)); // 64 character secure token
        $expires_at = date('Y-m-d H:i:s', strtotime('+1 hour')); // Token expires in 1 hour

        // Store reset token in database
        $stmt = $conn->prepare('INSERT INTO password_resets (email, token, expires_at) VALUES (?, ?, ?) ON DUPLICATE KEY UPDATE token = VALUES(token), expires_at = VALUES(expires_at), created_at = NOW()');
        $stmt->bind_param('sss', $email, $reset_token, $expires_at);

        if ($stmt->execute()) {
            // Create reset URL
            $reset_url = "http://" . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . "/reset_password.php?token=" . $reset_token;

            // Create email content
            $subject = 'Password Reset Link - CryptoApp';
            $html_message = createPasswordResetLinkEmail($user['name'], $reset_url);

            // Send email
            if (sendCryptoAppEmail($email, $user['name'], $subject, $html_message, 'password_reset')) {
                $response['success'] = true;
                $response['message'] = 'A password reset link has been sent to your email address. Please check your inbox and click the link to reset your password.';
            } else {
                $response['message'] = 'Failed to send email. Please try again later.';
            }
        } else {
            $response['message'] = 'Failed to generate reset token. Please try again.';
        }
        $stmt->close();
    } else {
        // Don't reveal whether email exists or not for security
        $response['success'] = true;
        $response['message'] = 'If an account with that email exists, a password reset link has been sent.';
    }
} else {
    $response['message'] = 'Invalid request method.';
}

$conn->close();
echo json_encode($response);
