<?php
session_start();

if (!isset($_SESSION['user_id'])) {
    header("Location: login.php");
    exit();
}

include 'dbcon.php';
include 'settings_helper.php';

$user_id = $_SESSION['user_id'];

// Get dynamic settings from database
$staking_settings = getStakingSettings($conn);
$COIN_PRICE = $staking_settings['coin_price'];
$MIN_STAKE_AMOUNT = $staking_settings['min_usd_amount'];

// Fetch user's wallet balances
$user_sql = "SELECT deposit_wallet_balance, withdrawal_wallet_balance, name FROM users WHERE id = ?";
$stmt = $conn->prepare($user_sql);
$stmt->bind_param("i", $user_id);
$stmt->execute();
$result = $stmt->get_result();
$user = $result->fetch_assoc();

$deposit_balance = $user['deposit_wallet_balance'] ?? 0;
$withdrawal_balance = $user['withdrawal_wallet_balance'] ?? 0;
$user_name = $user['name'] ?? '';

$stmt->close();

// Fetch user's active staking records
$staking_sql = "SELECT * FROM staking_records WHERE user_id = ? ORDER BY created_at DESC";
$stmt = $conn->prepare($staking_sql);
$stmt->bind_param("i", $user_id);
$stmt->execute();
$staking_result = $stmt->get_result();

$stmt->close();
$conn->close();
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Staking - NetVis</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>

<body class="bg-gray-100">
    <?php include 'user_nav.php'; ?>

    <main class="max-w-6xl mx-auto py-6 sm:px-6 lg:px-8">
        <div class="bg-white shadow-lg rounded-lg p-8">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-3xl font-bold text-gray-800">Staking</h2>
                <div class="text-sm text-gray-600">
                    <span class="font-medium">Coin Price:</span> $<?php echo number_format($COIN_PRICE, 2); ?> |
                    <span class="font-medium">Min Stake:</span> $<?php echo $MIN_STAKE_AMOUNT; ?>
                </div>
            </div>

            <div id="feedback-message" class="hidden p-4 mb-4 text-sm rounded-lg" role="alert"></div>

            <!-- Wallet Balances -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                <div class="bg-blue-50 p-6 rounded-lg border border-blue-200">
                    <h3 class="text-lg font-semibold text-blue-800 mb-2">Deposit Wallet</h3>
                    <p class="text-2xl font-bold text-blue-900">$<?php echo number_format($deposit_balance, 4); ?></p>
                    <p class="text-sm text-blue-600 mt-1">≈ <?php echo number_format($deposit_balance / $COIN_PRICE, 0); ?> coins</p>
                </div>
                <div class="bg-green-50 p-6 rounded-lg border border-green-200">
                    <h3 class="text-lg font-semibold text-green-800 mb-2">Withdrawal Wallet</h3>
                    <p class="text-2xl font-bold text-green-900">$<?php echo number_format($withdrawal_balance, 4); ?></p>
                    <p class="text-sm text-green-600 mt-1">≈ <?php echo number_format($withdrawal_balance / $COIN_PRICE, 0); ?> coins</p>
                </div>
            </div>

            <!-- Staking Form -->
            <div class="bg-gray-50 p-6 rounded-lg mb-8">
                <h3 class="text-xl font-semibold text-gray-800 mb-4">Stake Your Coins</h3>
                <p class="text-sm text-gray-600 mb-6">
                    Stake your funds for 6 months and earn rewards. Minimum staking: $<?php echo $MIN_STAKE_AMOUNT; ?> (multiples of $<?php echo $MIN_STAKE_AMOUNT; ?>).
                </p>

                <form id="staking-form" class="space-y-6">
                    <!-- Wallet Selection -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-3">Select Wallet to Stake From</label>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div class="relative">
                                <input type="radio" id="deposit_wallet" name="wallet_type" value="deposit" required
                                    class="sr-only peer">
                                <label for="deposit_wallet" class="flex items-center justify-between w-full p-4 text-gray-500 bg-white border-2 border-gray-200 rounded-lg cursor-pointer peer-checked:border-blue-600 peer-checked:text-blue-600 hover:text-gray-600 hover:bg-gray-50">
                                    <div>
                                        <div class="w-full text-lg font-semibold">Deposit Wallet</div>
                                        <div class="w-full text-sm">Balance: $<?php echo number_format($deposit_balance, 2); ?></div>
                                        <div class="w-full text-xs text-gray-400">≈ <?php echo number_format($deposit_balance / $COIN_PRICE, 0); ?> coins</div>
                                    </div>
                                    <div class="ml-3">
                                        <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                        </svg>
                                    </div>
                                </label>
                            </div>

                            <div class="relative">
                                <input type="radio" id="withdrawal_wallet" name="wallet_type" value="withdrawal" required
                                    class="sr-only peer">
                                <label for="withdrawal_wallet" class="flex items-center justify-between w-full p-4 text-gray-500 bg-white border-2 border-gray-200 rounded-lg cursor-pointer peer-checked:border-green-600 peer-checked:text-green-600 hover:text-gray-600 hover:bg-gray-50">
                                    <div>
                                        <div class="w-full text-lg font-semibold">Withdrawal Wallet</div>
                                        <div class="w-full text-sm">Balance: $<?php echo number_format($withdrawal_balance, 2); ?></div>
                                        <div class="w-full text-xs text-gray-400">≈ <?php echo number_format($withdrawal_balance / $COIN_PRICE, 0); ?> coins</div>
                                    </div>
                                    <div class="ml-3">
                                        <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                        </svg>
                                    </div>
                                </label>
                            </div>
                        </div>
                    </div>

                    <!-- Stake Amount -->
                    <div>
                        <label for="stake_amount" class="block text-sm font-medium text-gray-700 mb-2">Stake Amount (USD)</label>
                        <input type="number" id="stake_amount" name="stake_amount" min="<?php echo $MIN_STAKE_AMOUNT; ?>" step="<?php echo $MIN_STAKE_AMOUNT; ?>" required
                            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                            placeholder="Enter amount (multiples of $<?php echo $MIN_STAKE_AMOUNT; ?>)">
                    </div>

                    <div class="bg-blue-50 p-4 rounded-lg">
                        <div class="flex justify-between items-center text-sm">
                            <span class="text-gray-600">Coins to Stake:</span>
                            <span id="coins-to-stake" class="font-semibold text-blue-800">0 coins</span>
                        </div>
                        <div class="flex justify-between items-center text-sm mt-1">
                            <span class="text-gray-600">Lock Period:</span>
                            <span class="font-semibold text-blue-800">6 Months</span>
                        </div>
                        <div class="flex justify-between items-center text-sm mt-1">
                            <span class="text-gray-600">Unlock Date:</span>
                            <span id="unlock-date" class="font-semibold text-blue-800">-</span>
                        </div>
                    </div>

                    <button type="submit" id="stake-btn" class="w-full bg-indigo-600 text-white py-3 px-4 rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 font-medium">
                        Stake Coins
                    </button>
                </form>
            </div>

            <!-- Staking History -->
            <div class="bg-white">
                <h3 class="text-xl font-semibold text-gray-800 mb-4">Your Staking History</h3>

                <div class="overflow-x-auto">
                    <table class="min-w-full bg-white border border-gray-200 rounded-lg">
                        <thead class="bg-gray-100">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">Coins Staked</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-600 uppercase tracking-wider">Amount (USD)</th>
                                <th class="px-6 py-3 text-center text-xs font-medium text-gray-600 uppercase tracking-wider">Wallet Type</th>
                                <th class="px-6 py-3 text-center text-xs font-medium text-gray-600 uppercase tracking-wider">Staked Date</th>
                                <th class="px-6 py-3 text-center text-xs font-medium text-gray-600 uppercase tracking-wider">Unlock Date</th>
                                <th class="px-6 py-3 text-center text-xs font-medium text-gray-600 uppercase tracking-wider">Status</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-600 uppercase tracking-wider">Profit Earned</th>
                                <th class="px-6 py-3 text-center text-xs font-medium text-gray-600 uppercase tracking-wider">Action</th>
                            </tr>
                        </thead>
                        <tbody class="divide-y divide-gray-200">
                            <?php if ($staking_result->num_rows > 0): ?>
                                <?php while ($stake = $staking_result->fetch_assoc()): ?>
                                    <?php
                                    $is_unlocked = strtotime($stake['freeze_end_date']) <= time();
                                    $can_claim = $stake['status'] === 'active' && $is_unlocked;
                                    $is_claim_pending = $stake['status'] === 'claim_pending';
                                    $is_completed = $stake['status'] === 'completed';
                                    $was_rejected = $stake['status'] === 'active' && $stake['created_by_admin'] && $is_unlocked;

                                    // Debug output
                                    echo "<!-- Debug: ID={$stake['id']}, Status={$stake['status']}, Freeze={$stake['freeze_end_date']}, Now=" . date('Y-m-d H:i:s') . ", Unlocked=" . ($is_unlocked ? 'YES' : 'NO') . ", CanClaim=" . ($can_claim ? 'YES' : 'NO') . ", WasRejected=" . ($was_rejected ? 'YES' : 'NO') . " -->";

                                    if ($is_completed) {
                                        $status_class = 'bg-gray-100 text-gray-800';
                                        $status_text = 'Completed';
                                    } elseif ($is_claim_pending) {
                                        $status_class = 'bg-orange-100 text-orange-800';
                                        $status_text = 'Claim Pending';
                                    } elseif ($was_rejected) {
                                        $status_class = 'bg-red-100 text-red-800';
                                        $status_text = 'Previously Rejected - Can Claim Again';
                                    } elseif ($can_claim) {
                                        $status_class = 'bg-green-100 text-green-800';
                                        $status_text = 'Ready to Claim';
                                    } elseif ($stake['status'] === 'active') {
                                        $status_class = 'bg-yellow-100 text-yellow-800';
                                        $status_text = 'Locked';
                                    } else {
                                        $status_class = 'bg-gray-100 text-gray-800';
                                        $status_text = ucfirst($stake['status']);
                                    }
                                    ?>
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                            <?php echo number_format($stake['coins_staked']); ?> coins
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right">
                                            $<?php echo number_format($stake['amount_usd'], 2); ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-center">
                                            <span class="px-2 py-1 text-xs font-medium rounded-full <?php echo $stake['stake_type'] === 'deposit' ? 'bg-blue-100 text-blue-800' : 'bg-green-100 text-green-800'; ?>">
                                                <?php echo ucfirst($stake['stake_type']); ?>
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">
                                            <?php echo date('M j, Y', strtotime($stake['created_at'])); ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">
                                            <?php echo date('M j, Y', strtotime($stake['freeze_end_date'])); ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-center">
                                            <span class="px-2 py-1 text-xs font-medium rounded-full <?php echo $status_class; ?>">
                                                <?php echo $status_text; ?>
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right">
                                            $<?php echo number_format($stake['profit_earned'], 4); ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-center">
                                            <?php if ($can_claim || $was_rejected): ?>
                                                <button onclick="claimStaking(<?php echo $stake['id']; ?>, <?php echo $stake['amount_usd']; ?>)"
                                                    class="px-3 py-1 <?php echo $was_rejected ? 'bg-red-600 hover:bg-red-700' : 'bg-green-600 hover:bg-green-700'; ?> text-white text-xs rounded-md focus:outline-none focus:ring-2 focus:ring-green-500">
                                                    <?php echo $was_rejected ? 'Claim Again' : 'Claim'; ?>
                                                </button>
                                            <?php elseif ($is_claim_pending): ?>
                                                <span class="px-3 py-1 bg-orange-100 text-orange-800 text-xs rounded-md">
                                                    Pending Approval
                                                </span>
                                            <?php elseif ($is_completed): ?>
                                                <span class="px-3 py-1 bg-gray-100 text-gray-600 text-xs rounded-md">
                                                    Claimed
                                                </span>
                                            <?php else: ?>
                                                <span class="px-3 py-1 bg-gray-100 text-gray-500 text-xs rounded-md">
                                                    <?php echo $is_unlocked ? 'Available' : 'Locked'; ?>
                                                </span>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endwhile; ?>
                            <?php else: ?>
                                <tr>
                                    <td colspan="8" class="px-6 py-4 text-center text-gray-500">
                                        No staking records found. Start staking to see your history here.
                                    </td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </main>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        const COIN_PRICE = <?php echo $COIN_PRICE; ?>;
        const MIN_STAKE_AMOUNT = <?php echo $MIN_STAKE_AMOUNT; ?>;
        const DEPOSIT_BALANCE = <?php echo $deposit_balance; ?>;
        const WITHDRAWAL_BALANCE = <?php echo $withdrawal_balance; ?>;

        $(document).ready(function() {
            // Update coins calculation when amount input changes
            $('#stake_amount').on('input', function() {
                const amountUSD = parseFloat($(this).val()) || 0;
                const coins = Math.floor(amountUSD / COIN_PRICE);
                $('#coins-to-stake').text(coins + ' coins');

                // Calculate unlock date (6 months from now)
                if (amountUSD > 0) {
                    const unlockDate = new Date();
                    unlockDate.setMonth(unlockDate.getMonth() + 6);
                    $('#unlock-date').text(unlockDate.toLocaleDateString('en-US', {
                        year: 'numeric',
                        month: 'short',
                        day: 'numeric'
                    }));
                } else {
                    $('#unlock-date').text('-');
                }
            });

            // Handle form submission
            $('#staking-form').on('submit', function(e) {
                e.preventDefault();

                const walletType = $('input[name="wallet_type"]:checked').val();
                const stakeAmountUSD = parseFloat($('#stake_amount').val()) || 0;
                const stakeCoins = Math.floor(stakeAmountUSD / COIN_PRICE);

                // Validation
                if (!walletType) {
                    showFeedback('Please select a wallet to stake from.', 'error');
                    return;
                }

                if (stakeAmountUSD < MIN_STAKE_AMOUNT) {
                    showFeedback(`Minimum staking amount is $${MIN_STAKE_AMOUNT}.`, 'error');
                    return;
                }

                if (stakeAmountUSD % MIN_STAKE_AMOUNT !== 0) {
                    showFeedback(`Staking amount must be in multiples of $${MIN_STAKE_AMOUNT}.`, 'error');
                    return;
                }

                // Check wallet balance
                const availableBalance = walletType === 'deposit' ? DEPOSIT_BALANCE : WITHDRAWAL_BALANCE;
                if (stakeAmountUSD > availableBalance) {
                    showFeedback(`Insufficient balance in ${walletType} wallet. Available: $${availableBalance.toFixed(2)}`, 'error');
                    return;
                }

                // Disable submit button
                const submitBtn = $('#stake-btn');
                submitBtn.prop('disabled', true).text('Processing...');

                // Submit staking request
                $.ajax({
                    url: '/netvis/ui/api/submit_staking_request.php',
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({
                        wallet_type: walletType,
                        stake_amount_usd: stakeAmountUSD
                    }),
                    success: function(response) {
                        console.log('Staking response:', response);

                        if (response.success) {
                            showFeedback('Your coins have been staked successfully. Page will reload shortly...', 'success');

                            // Reload page after 2 seconds
                            setTimeout(function() {
                                window.location.reload();
                            }, 2000);
                        } else {
                            showFeedback(response.message || 'An error occurred while staking.', 'error');
                        }
                    },
                    error: function(jqXHR) {
                        console.log('Staking error:', jqXHR);
                        console.log('Status:', jqXHR.status);
                        console.log('Response text:', jqXHR.responseText);
                        console.log('Response JSON:', jqXHR.responseJSON);

                        let errorMsg = 'A network error occurred. Please try again.';

                        if (jqXHR.responseJSON && jqXHR.responseJSON.message) {
                            errorMsg = jqXHR.responseJSON.message;
                        } else if (jqXHR.responseText) {
                            try {
                                const response = JSON.parse(jqXHR.responseText);
                                if (response.message) {
                                    errorMsg = response.message;
                                }
                            } catch (e) {
                                console.log('Failed to parse response:', e);
                                errorMsg = 'Server error occurred. Please try again.';
                            }
                        }

                        showFeedback(errorMsg, 'error');
                    },
                    complete: function() {
                        submitBtn.prop('disabled', false).text('Stake Coins');
                    }
                });
            });
        });

        // Global claim staking function
        function claimStaking(stakingId, amount) {
            // Check if this is a previously rejected claim by looking at the button text
            const button = event.target;
            const isRejectedClaim = button.textContent.includes('Again');

            const confirmMessage = isRejectedClaim ?
                `Are you sure you want to claim $${amount.toFixed(2)} again? Your previous claim was rejected by admin, but you can try again.` :
                `Are you sure you want to claim $${amount.toFixed(2)} from your staking? This will send a request to admin for approval.`;

            if (!confirm(confirmMessage)) {
                return;
            }

            $.ajax({
                url: '/netvis/ui/api/claim_staking.php',
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({
                    staking_id: stakingId
                }),
                success: function(response) {
                    console.log('Claim response:', response);

                    if (response.success) {
                        showFeedback('Claim request submitted successfully. Waiting for admin approval.', 'success');

                        // Reload page after 2 seconds
                        setTimeout(function() {
                            window.location.reload();
                        }, 2000);
                    } else {
                        showFeedback(response.message || 'An error occurred while claiming.', 'error');
                    }
                },
                error: function(jqXHR) {
                    console.log('Claim error:', jqXHR);
                    console.log('Status:', jqXHR.status);
                    console.log('Response text:', jqXHR.responseText);

                    let errorMsg = 'A network error occurred. Please try again.';

                    if (jqXHR.responseJSON && jqXHR.responseJSON.message) {
                        errorMsg = jqXHR.responseJSON.message;
                    } else if (jqXHR.responseText) {
                        try {
                            const response = JSON.parse(jqXHR.responseText);
                            if (response.message) {
                                errorMsg = response.message;
                            }
                        } catch (e) {
                            console.log('Failed to parse response:', e);
                            errorMsg = 'Server error occurred. Please try again.';
                        }
                    }

                    showFeedback(errorMsg, 'error');
                }
            });
        }

        // Global feedback function
        function showFeedback(message, type) {
            const feedbackMessage = $('#feedback-message');
            feedbackMessage.text(message);

            // Clear all previous classes
            feedbackMessage.removeClass('hidden bg-red-50 bg-green-50 text-red-800 text-green-800 border-red-200 border-green-200');

            // Add appropriate classes based on type
            if (type === 'success') {
                feedbackMessage.addClass('p-4 mb-4 text-sm text-green-800 rounded-lg bg-green-50 border border-green-200');
            } else {
                feedbackMessage.addClass('p-4 mb-4 text-sm text-red-800 rounded-lg bg-red-50 border border-red-200');
            }

            // Auto-hide after 5 seconds (but not for success messages that trigger reload)
            if (type !== 'success' || !message.includes('reload')) {
                setTimeout(function() {
                    feedbackMessage.addClass('hidden');
                }, 5000);
            }
        }
    </script>
</body>

</html>