<?php
session_start();

// Admin authentication check
if (!isset($_SESSION['user_id']) || !$_SESSION['is_admin']) {
    header("Location: login.html");
    exit();
}

include 'dbcon.php';

$status_filter = $_GET['status'] ?? 'claim_pending';
$allowed_statuses = ['claim_pending', 'completed', 'active'];
if (!in_array($status_filter, $allowed_statuses)) {
    $status_filter = 'claim_pending';
}

// Build query based on status filter
if ($status_filter === 'active') {
    // For rejected claims, we need to find active records that were previously rejected
    // We'll look for active records that have been reviewed by admin (indicating they were rejected)
    $sql = "SELECT sr.*, u.name as user_name, u.user_id as user_identifier
            FROM staking_records sr
            JOIN users u ON sr.user_id = u.id
            WHERE sr.status = 'active' AND sr.created_by_admin IS NOT NULL
            ORDER BY sr.created_at DESC";
    $stmt = $conn->prepare($sql);
    $stmt->execute();
} else {
    $sql = "SELECT sr.*, u.name as user_name, u.user_id as user_identifier
            FROM staking_records sr
            JOIN users u ON sr.user_id = u.id
            WHERE sr.status = ?
            ORDER BY sr.created_at DESC";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("s", $status_filter);
    $stmt->execute();
}
$claims_result = $stmt->get_result();

// Page configuration
$page_title = "Staking Claims Management";
$additional_css = '
<style>
    /* Enhanced table styling */
    .admin-table {
        width: 100%;
        border-collapse: separate;
        border-spacing: 0;
        background: white;
    }

    .admin-table thead {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    .admin-table thead th {
        color: white;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.05em;
        padding: 1rem 1.5rem;
        font-size: 0.75rem;
        border: none;
        position: relative;
    }

    .admin-table thead th:first-child {
        text-align: left;
        padding-left: 2rem;
    }

    .admin-table thead th:not(:first-child) {
        text-align: center;
    }

    .admin-table tbody tr {
        transition: all 0.2s ease;
        border-bottom: 1px solid #e2e8f0;
    }

    .admin-table tbody tr:hover {
        background-color: #f8fafc;
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    .admin-table tbody tr:last-child {
        border-bottom: none;
    }

    .admin-table tbody td {
        padding: 1rem 1.5rem;
        vertical-align: middle;
        border: none;
        background-color: white;
    }

    .admin-table tbody td:first-child {
        text-align: left;
        padding-left: 2rem;
    }

    .admin-table tbody td:not(:first-child) {
        text-align: center;
    }

    .admin-table tbody tr:hover td {
        background-color: #f8fafc;
    }

    /* Status badges */
    .status-badge {
        display: inline-flex;
        align-items: center;
        padding: 0.25rem 0.75rem;
        border-radius: 9999px;
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.05em;
    }

    .status-claim_pending {
        background-color: #fef3c7;
        color: #92400e;
        border: 1px solid #fbbf24;
    }

    .status-completed {
        background-color: #d1fae5;
        color: #065f46;
        border: 1px solid #10b981;
    }

    .status-active {
        background-color: #fee2e2;
        color: #991b1b;
        border: 1px solid #ef4444;
    }

    /* Filter styling */
    .filter-container {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 0.75rem;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
    }

    /* Action buttons */
    .action-btn {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 2rem;
        height: 2rem;
        border-radius: 0.375rem;
        font-weight: 500;
        font-size: 0.875rem;
        transition: all 0.2s ease;
        border: none;
        cursor: pointer;
    }

    .action-btn-approve {
        background: rgba(34, 197, 94, 0.1);
        color: #22c55e;
        border: 1px solid rgba(34, 197, 94, 0.2);
    }

    .action-btn-approve:hover {
        background: rgba(34, 197, 94, 0.2);
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(34, 197, 94, 0.2);
    }

    .action-btn-reject {
        background: rgba(239, 68, 68, 0.1);
        color: #ef4444;
        border: 1px solid rgba(239, 68, 68, 0.2);
    }

    .action-btn-reject:hover {
        background: rgba(239, 68, 68, 0.2);
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(239, 68, 68, 0.2);
    }
</style>
';

// Start output buffering to capture the page content
ob_start();

$stmt->close();
$conn->close();
?>

<!-- Page Header -->
<div class="flex flex-col lg:flex-row justify-between items-start lg:items-center space-y-4 lg:space-y-0 mb-6">
    <div>
        <h1 class="text-3xl font-bold text-gray-900">Staking Claims Management</h1>
        <p class="text-gray-600 mt-1">Review and manage user staking claim requests</p>
    </div>
    <div class="flex items-center space-x-2 bg-blue-50 px-4 py-2 rounded-lg border border-blue-200">
        <i class="fas fa-coins text-blue-600"></i>
        <span class="text-sm font-medium text-blue-700">Staking Claims</span>
    </div>
</div>

<!-- Filter Section -->
<div class="filter-container">
    <div class="flex items-center justify-between">
        <div class="flex items-center space-x-3">
            <div class="w-10 h-10 bg-white bg-opacity-20 rounded-xl flex items-center justify-center">
                <i class="fas fa-filter text-white text-lg"></i>
            </div>
            <div>
                <h3 class="text-lg font-semibold text-white">Filter Claims</h3>
                <p class="text-blue-100 text-sm">Filter by claim status</p>
            </div>
        </div>
        <div class="flex items-center space-x-4">
            <div class="flex items-center space-x-2">
                <a href="?status=claim_pending" class="<?php echo $status_filter === 'claim_pending' ? 'bg-white text-blue-600' : 'bg-white bg-opacity-20 text-white hover:bg-opacity-30'; ?> px-4 py-2 rounded-lg font-medium text-sm transition-all duration-200">
                    <i class="fas fa-clock mr-2"></i>Pending Claims
                </a>
                <a href="?status=completed" class="<?php echo $status_filter === 'completed' ? 'bg-white text-blue-600' : 'bg-white bg-opacity-20 text-white hover:bg-opacity-30'; ?> px-4 py-2 rounded-lg font-medium text-sm transition-all duration-200">
                    <i class="fas fa-check mr-2"></i>Approved
                </a>
                <a href="?status=active" class="<?php echo $status_filter === 'active' ? 'bg-white text-blue-600' : 'bg-white bg-opacity-20 text-white hover:bg-opacity-30'; ?> px-4 py-2 rounded-lg font-medium text-sm transition-all duration-200">
                    <i class="fas fa-times mr-2"></i>Rejected
                </a>
            </div>
        </div>
    </div>
</div>

<div id="feedback-message" class="hidden p-4 mb-4 text-sm rounded-lg" role="alert"></div>

<!-- Staking Claims Table -->
<div class="admin-card p-0 overflow-hidden">
    <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center space-x-3">
            <div class="w-8 h-8 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-lg flex items-center justify-center">
                <i class="fas fa-coins text-white text-sm"></i>
            </div>
            <div>
                <h3 class="text-lg font-semibold text-gray-900">Staking Claims</h3>
                <p class="text-sm text-gray-600">Review and manage staking claim requests</p>
            </div>
        </div>
    </div>

    <div class="overflow-x-auto">
        <table class="admin-table">
            <thead>
                <tr>
                    <th>User</th>
                    <th>Amount (USD)</th>
                    <th>Coins</th>
                    <th>Wallet Type</th>
                    <th>Staked Date</th>
                    <th>Unlock Date</th>
                    <th>Status</th>
                    <?php if ($status_filter === 'claim_pending'): ?>
                        <th>Actions</th>
                    <?php endif; ?>
                </tr>
            </thead>
            <tbody>
                <?php if ($claims_result->num_rows > 0): ?>
                    <?php while ($claim = $claims_result->fetch_assoc()): ?>
                        <tr id="claim-row-<?php echo $claim['id']; ?>">
                            <td>
                                <div class="flex items-center space-x-3">
                                    <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                        <i class="fas fa-user text-blue-600 text-xs"></i>
                                    </div>
                                    <div>
                                        <div class="text-sm font-medium text-gray-900"><?php echo htmlspecialchars($claim['user_name']); ?></div>
                                        <div class="text-xs text-gray-500">ID: <?php echo htmlspecialchars($claim['user_identifier']); ?></div>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div class="flex items-center justify-center space-x-2">
                                    <div class="w-6 h-6 bg-green-100 rounded flex items-center justify-center">
                                        <i class="fas fa-dollar-sign text-green-600 text-xs"></i>
                                    </div>
                                    <span class="text-lg font-bold text-green-600">
                                        $<?php echo number_format($claim['amount_usd'], 2); ?>
                                    </span>
                                </div>
                            </td>
                            <td>
                                <div class="flex items-center justify-center space-x-2">
                                    <div class="w-6 h-6 bg-yellow-100 rounded flex items-center justify-center">
                                        <i class="fas fa-coins text-yellow-600 text-xs"></i>
                                    </div>
                                    <span class="text-sm font-medium text-gray-900">
                                        <?php echo number_format($claim['coins_staked']); ?>
                                    </span>
                                </div>
                            </td>
                            <td>
                                <span class="status-badge <?php echo $claim['stake_type'] === 'deposit' ? 'status-claim_pending' : 'status-completed'; ?>">
                                    <?php echo ucfirst($claim['stake_type']); ?>
                                </span>
                            </td>
                            <td>
                                <div class="text-sm text-gray-900">
                                    <div class="font-medium"><?php echo date('M j, Y', strtotime($claim['created_at'])); ?></div>
                                    <div class="text-xs text-gray-500"><?php echo date('g:i A', strtotime($claim['created_at'])); ?></div>
                                </div>
                            </td>
                            <td>
                                <div class="text-sm text-gray-900">
                                    <div class="font-medium"><?php echo date('M j, Y', strtotime($claim['freeze_end_date'])); ?></div>
                                    <div class="text-xs text-gray-500"><?php echo date('g:i A', strtotime($claim['freeze_end_date'])); ?></div>
                                </div>
                            </td>
                            <td>
                                <?php
                                if ($status_filter === 'active' && $claim['created_by_admin']) {
                                    // This is a rejected claim
                                    $status_badge_class = 'status-active';
                                    $status_text = 'Rejected';
                                } else {
                                    $status_badge_classes = [
                                        'claim_pending' => 'status-claim_pending',
                                        'completed' => 'status-completed',
                                        'active' => 'status-active'
                                    ];
                                    $status_badge_class = $status_badge_classes[$claim['status']] ?? 'status-active';
                                    $status_text = $claim['status'] === 'claim_pending' ? 'Pending' : ($claim['status'] === 'completed' ? 'Approved' : ucfirst($claim['status']));
                                }
                                ?>
                                <span class="status-badge <?php echo $status_badge_class; ?>">
                                    <?php echo $status_text; ?>
                                </span>
                            </td>
                            <?php if ($status_filter === 'claim_pending'): ?>
                                <td>
                                    <div class="flex items-center justify-center space-x-2">
                                        <button onclick="approveClaim(<?php echo $claim['id']; ?>, '<?php echo htmlspecialchars($claim['user_name']); ?>', <?php echo $claim['amount_usd']; ?>)"
                                            class="action-btn action-btn-approve" title="Approve Claim">
                                            <i class="fas fa-check"></i>
                                        </button>
                                        <button onclick="rejectClaim(<?php echo $claim['id']; ?>, '<?php echo htmlspecialchars($claim['user_name']); ?>', <?php echo $claim['amount_usd']; ?>)"
                                            class="action-btn action-btn-reject" title="Reject Claim">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                </td>
                            <?php endif; ?>
                        </tr>
                    <?php endwhile; ?>
                <?php else: ?>
                    <tr>
                        <td colspan="<?php echo $status_filter === 'claim_pending' ? '8' : '7'; ?>" style="text-align: center; padding: 3rem;">
                            <div class="flex flex-col items-center">
                                <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                                    <i class="fas fa-coins text-gray-400 text-2xl"></i>
                                </div>
                                <h3 class="text-lg font-medium text-gray-900 mb-2">No Claims Found</h3>
                                <p class="text-sm text-gray-500 mb-4">
                                    No <?php
                                        if ($status_filter === 'claim_pending') {
                                            echo 'pending';
                                        } elseif ($status_filter === 'completed') {
                                            echo 'approved';
                                        } else {
                                            echo 'rejected';
                                        }
                                        ?> staking claims found.
                                </p>
                            </div>
                        </td>
                    </tr>
                <?php endif; ?>
            </tbody>
        </table>
    </div>
</div>

<?php
// Capture the page content
$page_content = ob_get_clean();

// Include the admin layout
include 'admin/includes/layout.php';
?>

<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>
    function approveClaim(claimId, userName, amount) {
        if (!confirm(`Are you sure you want to approve the staking claim for ${userName}?\n\nAmount: $${amount.toFixed(2)}\n\nThis will add the amount to their withdrawal wallet.`)) {
            return;
        }

        processStakingClaim(claimId, 'approve');
    }

    function rejectClaim(claimId, userName, amount) {
        const reason = prompt(`Please provide a reason for rejecting ${userName}'s staking claim of $${amount.toFixed(2)}:`);
        if (!reason || !reason.trim()) {
            alert('Rejection reason is required.');
            return;
        }

        processStakingClaim(claimId, 'reject', reason);
    }

    function processStakingClaim(claimId, action, reason = '') {
        $.ajax({
            url: '/netvis/ui/api/process_staking_claim.php',
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({
                claim_id: claimId,
                action: action,
                reason: reason
            }),
            success: function(response) {
                console.log('Staking claim response:', response);

                if (response.success) {
                    showFeedback(`Staking claim ${action}ed successfully. Page will refresh shortly...`, 'success');

                    // Refresh page after 2 seconds
                    setTimeout(function() {
                        window.location.reload();
                    }, 2000);
                } else {
                    showFeedback(response.message || 'An error occurred.', 'error');
                }
            },
            error: function(jqXHR) {
                console.log('Staking claim error:', jqXHR);
                console.log('Status:', jqXHR.status);
                console.log('Response text:', jqXHR.responseText);
                console.log('Response JSON:', jqXHR.responseJSON);

                // Check if this is actually a success response treated as error
                if (jqXHR.responseText && (jqXHR.responseText.includes('successfully') || jqXHR.responseText.includes('success'))) {
                    console.log('Detected success in error response, treating as success');

                    showFeedback(`Staking claim ${action}ed successfully. Page will refresh shortly...`, 'success');

                    // Refresh page after 2 seconds
                    setTimeout(function() {
                        window.location.reload();
                    }, 2000);
                    return;
                }

                let errorMsg = 'A network error occurred. Please try again.';

                if (jqXHR.responseJSON && jqXHR.responseJSON.message) {
                    errorMsg = jqXHR.responseJSON.message;
                } else if (jqXHR.responseText) {
                    try {
                        const response = JSON.parse(jqXHR.responseText);
                        if (response.message) {
                            errorMsg = response.message;
                        }
                    } catch (e) {
                        console.log('Failed to parse response:', e);
                        errorMsg = 'Server error occurred. Please try again.';
                    }
                }

                showFeedback(errorMsg, 'error');
            }
        });
    }

    function showFeedback(message, type) {
        const feedbackMessage = $('#feedback-message');
        feedbackMessage.text(message);

        // Clear all previous classes
        feedbackMessage.removeClass('hidden bg-red-50 bg-green-50 text-red-800 text-green-800 border-red-200 border-green-200');

        // Add appropriate classes based on type
        if (type === 'success') {
            feedbackMessage.addClass('p-4 mb-4 text-sm text-green-800 rounded-lg bg-green-50 border border-green-200');
        } else {
            feedbackMessage.addClass('p-4 mb-4 text-sm text-red-800 rounded-lg bg-red-50 border border-red-200');
        }

        // Auto-hide after 5 seconds (but not for success messages that trigger reload)
        if (type !== 'success' || !message.includes('refresh')) {
            setTimeout(function() {
                feedbackMessage.addClass('hidden');
            }, 5000);
        }
    }
</script>
</body>

</html>