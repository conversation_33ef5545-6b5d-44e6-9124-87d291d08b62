<?php
session_start();

// Admin check - assumes a 'role' session variable is set to 'admin' upon login
if (!isset($_SESSION['user_id']) || !isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
    header("Location: login.php"); // Redirect to login if not admin
    exit();
}
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin - Fund Transfer Log</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>

<body class="bg-gray-100 font-sans">
    <div class="min-h-screen bg-gray-100">
        <nav class="bg-blue-600 p-4 text-white">
            <div class="container mx-auto flex justify-between">
                <h1 class="text-xl font-bold">Admin Panel</h1>
                <a href="logout.php" class="font-semibold hover:underline">Logout</a>
            </div>
        </nav>

        <main class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
            <div class="bg-white shadow-lg rounded-lg p-8">
                <h2 class="text-3xl font-bold text-gray-800 mb-6">All Internal Fund Transfers</h2>

                <div class="overflow-x-auto border border-gray-200 rounded-lg">
                    <table class="min-w-full bg-white">
                        <thead class="bg-gray-100">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">Date</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">Sender</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">Recipient</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-600 uppercase tracking-wider">Amount</th>
                            </tr>
                        </thead>
                        <tbody id="log-body">
                            <!-- Log data will be loaded here by JavaScript -->
                        </tbody>
                    </table>
                </div>
            </div>
        </main>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            function loadAllTransferHistory() {
                const xhr = new XMLHttpRequest();
                xhr.open('GET', '/netvis/ui/api/get_all_transfer_history.php', true);
                xhr.onload = function() {
                    const logBody = document.getElementById('log-body');
                    if (xhr.status >= 200 && xhr.status < 400) {
                        try {
                            const data = JSON.parse(xhr.responseText);
                            logBody.innerHTML = '';
                            if (data.length === 0) {
                                logBody.innerHTML = '<tr><td colspan="4" class="text-center p-4 text-gray-500">No transfer logs found.</td></tr>';
                            } else {
                                data.forEach(item => {
                                    const row = `<tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">${new Date(item.created_at).toLocaleString()}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-800">${item.sender_name}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-800">${item.recipient_name}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-right font-semibold text-red-600">$${parseFloat(item.amount).toFixed(4)}</td>
                                </tr>`;
                                    logBody.innerHTML += row;
                                });
                            }
                        } catch (e) {
                            console.error('Error parsing JSON:', e, xhr.responseText);
                            logBody.innerHTML = '<tr><td colspan="4" class="text-center p-4 text-red-500">Error loading logs. Invalid data from server.</td></tr>';
                        }
                    } else {
                        console.error('Server error:', xhr.status, xhr.responseText);
                        logBody.innerHTML = '<tr><td colspan="4" class="text-center p-4 text-red-500">Error loading logs. Server returned an error.</td></tr>';
                    }
                };
                xhr.onerror = function() {
                    console.error('Network error.');
                    const logBody = document.getElementById('log-body');
                    logBody.innerHTML = '<tr><td colspan="4" class="text-center p-4 text-red-500">Error loading logs. Could not connect to the server.</td></tr>';
                };
                xhr.send();
            }

            loadAllTransferHistory();
        });
    </script>
</body>

</html>