<?php

/**
 * Settings Helper Functions
 * Provides functions to get and set system settings dynamically
 */

/**
 * Get a setting value from the database
 * @param mysqli $conn Database connection
 * @param string $key Setting key
 * @param mixed $default Default value if setting not found
 * @return mixed Setting value
 */
function getSetting($conn, $key, $default = null)
{
    static $settings_cache = [];

    // Check cache first
    if (isset($settings_cache[$key])) {
        return $settings_cache[$key];
    }

    $sql = "SELECT setting_value, setting_type FROM system_settings WHERE setting_key = ?";
    $stmt = $conn->prepare($sql);

    if (!$stmt) {
        return $default;
    }

    $stmt->bind_param("s", $key);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows > 0) {
        $row = $result->fetch_assoc();
        $value = $row['setting_value'];
        $type = $row['setting_type'];

        // Convert value based on type
        switch ($type) {
            case 'number':
                $value = is_numeric($value) ? (float)$value : $default;
                break;
            case 'boolean':
                $value = filter_var($value, FILTER_VALIDATE_BOOLEAN);
                break;
            case 'json':
                $decoded = json_decode($value, true);
                $value = ($decoded !== null) ? $decoded : $default;
                break;
            case 'string':
            default:
                // Keep as string
                break;
        }

        // Cache the value
        $settings_cache[$key] = $value;
        $stmt->close();
        return $value;
    }

    $stmt->close();
    return $default;
}

/**
 * Set a setting value in the database
 * @param mysqli $conn Database connection
 * @param string $key Setting key
 * @param mixed $value Setting value
 * @param string $type Setting type (string, number, boolean, json)
 * @param int $updated_by User ID who updated the setting
 * @param string $reason Reason for the change (for coin price history)
 * @return bool Success status
 */
function setSetting($conn, $key, $value, $type = 'string', $updated_by = null, $reason = null)
{
    // Convert value to string based on type
    switch ($type) {
        case 'number':
            $value = (string)(float)$value;
            break;
        case 'boolean':
            $value = $value ? '1' : '0';
            break;
        case 'json':
            $value = json_encode($value);
            break;
        case 'string':
        default:
            $value = (string)$value;
            break;
    }

    // Special handling for coin price to track history
    if ($key === 'staking_coin_price') {
        return setCoinPrice($conn, (float)$value, $updated_by, $reason);
    }

    $sql = "INSERT INTO system_settings (setting_key, setting_value, setting_type, updated_by)
            VALUES (?, ?, ?, ?)
            ON DUPLICATE KEY UPDATE
            setting_value = VALUES(setting_value),
            setting_type = VALUES(setting_type),
            updated_by = VALUES(updated_by)";

    $stmt = $conn->prepare($sql);

    if (!$stmt) {
        return false;
    }

    $stmt->bind_param("sssi", $key, $value, $type, $updated_by);
    $success = $stmt->execute();
    $stmt->close();

    // Clear cache for this key
    static $settings_cache = [];
    unset($settings_cache[$key]);

    return $success;
}

/**
 * Get all staking-related settings
 * @param mysqli $conn Database connection
 * @return array Associative array of staking settings
 */
function getStakingSettings($conn)
{
    return [
        'coin_price' => getSetting($conn, 'staking_coin_price', 0.45),
        'min_coins' => getSetting($conn, 'staking_min_coins', 10),
        'freeze_months' => getSetting($conn, 'staking_freeze_months', 6),
        'min_usd_amount' => getSetting($conn, 'staking_min_usd_amount', 10)
    ];
}

/**
 * Get multiple settings at once
 * @param mysqli $conn Database connection
 * @param array $keys Array of setting keys
 * @return array Associative array of key => value pairs
 */
function getMultipleSettings($conn, $keys)
{
    $settings = [];

    if (empty($keys)) {
        return $settings;
    }

    $placeholders = str_repeat('?,', count($keys) - 1) . '?';
    $sql = "SELECT setting_key, setting_value, setting_type FROM system_settings WHERE setting_key IN ($placeholders)";

    $stmt = $conn->prepare($sql);

    if (!$stmt) {
        return $settings;
    }

    $types = str_repeat('s', count($keys));
    $stmt->bind_param($types, ...$keys);
    $stmt->execute();
    $result = $stmt->get_result();

    while ($row = $result->fetch_assoc()) {
        $key = $row['setting_key'];
        $value = $row['setting_value'];
        $type = $row['setting_type'];

        // Convert value based on type
        switch ($type) {
            case 'number':
                $value = is_numeric($value) ? (float)$value : 0;
                break;
            case 'boolean':
                $value = filter_var($value, FILTER_VALIDATE_BOOLEAN);
                break;
            case 'json':
                $decoded = json_decode($value, true);
                $value = ($decoded !== null) ? $decoded : [];
                break;
        }

        $settings[$key] = $value;
    }

    $stmt->close();
    return $settings;
}

/**
 * Check if settings table exists and create it if not
 * @param mysqli $conn Database connection
 * @return bool Success status
 */
function ensureSettingsTable($conn)
{
    $check_sql = "SHOW TABLES LIKE 'system_settings'";
    $result = $conn->query($check_sql);

    if ($result->num_rows > 0) {
        return true; // Table already exists
    }

    // Create the table
    $create_sql = "
    CREATE TABLE IF NOT EXISTS `system_settings` (
      `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
      `setting_key` varchar(100) NOT NULL UNIQUE,
      `setting_value` text NOT NULL,
      `setting_type` enum('string','number','boolean','json') NOT NULL DEFAULT 'string',
      `description` text DEFAULT NULL,
      `updated_by` int(11) UNSIGNED DEFAULT NULL,
      `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
      `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
      PRIMARY KEY (`id`),
      UNIQUE KEY `unique_setting_key` (`setting_key`),
      KEY `fk_settings_updated_by` (`updated_by`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
    ";

    return $conn->query($create_sql);
}

/**
 * Set coin price with historical tracking
 * @param mysqli $conn Database connection
 * @param float $new_price New coin price
 * @param int $updated_by User ID who updated the price
 * @param string $reason Reason for the price change
 * @return bool Success status
 */
function setCoinPrice($conn, $new_price, $updated_by, $reason = null)
{
    // Start transaction
    $conn->begin_transaction();

    try {
        // Get current price
        $current_price = getSetting($conn, 'staking_coin_price', 0.45);

        // Calculate changes
        $change_amount = $new_price - $current_price;
        $change_percentage = $current_price > 0 ? ($change_amount / $current_price) * 100 : 0;

        // Insert into price history
        $history_sql = "INSERT INTO coin_price_history (price, previous_price, change_amount, change_percentage, updated_by, reason) VALUES (?, ?, ?, ?, ?, ?)";
        $history_stmt = $conn->prepare($history_sql);

        if (!$history_stmt) {
            throw new Exception('Failed to prepare history insert: ' . $conn->error);
        }

        $history_stmt->bind_param("ddddis", $new_price, $current_price, $change_amount, $change_percentage, $updated_by, $reason);

        if (!$history_stmt->execute()) {
            throw new Exception('Failed to insert price history: ' . $history_stmt->error);
        }
        $history_stmt->close();

        // Update system settings
        $settings_sql = "INSERT INTO system_settings (setting_key, setting_value, setting_type, updated_by)
                        VALUES ('staking_coin_price', ?, 'number', ?)
                        ON DUPLICATE KEY UPDATE
                        setting_value = VALUES(setting_value),
                        updated_by = VALUES(updated_by)";

        $settings_stmt = $conn->prepare($settings_sql);

        if (!$settings_stmt) {
            throw new Exception('Failed to prepare settings update: ' . $conn->error);
        }

        $price_string = (string)$new_price;
        $settings_stmt->bind_param("si", $price_string, $updated_by);

        if (!$settings_stmt->execute()) {
            throw new Exception('Failed to update settings: ' . $settings_stmt->error);
        }
        $settings_stmt->close();

        // Commit transaction
        $conn->commit();

        // Clear cache
        static $settings_cache = [];
        unset($settings_cache['staking_coin_price']);

        return true;
    } catch (Exception $e) {
        $conn->rollback();
        return false;
    }
}

/**
 * Get coin price history for graphing
 * @param mysqli $conn Database connection
 * @param int $limit Number of records to return
 * @param string $period Time period (day, week, month, year, all)
 * @return array Array of price history records
 */
function getCoinPriceHistory($conn, $limit = 50, $period = 'all')
{
    $where_clause = '';

    switch ($period) {
        case 'day':
            $where_clause = "WHERE created_at >= DATE_SUB(NOW(), INTERVAL 1 DAY)";
            break;
        case 'week':
            $where_clause = "WHERE created_at >= DATE_SUB(NOW(), INTERVAL 1 WEEK)";
            break;
        case 'month':
            $where_clause = "WHERE created_at >= DATE_SUB(NOW(), INTERVAL 1 MONTH)";
            break;
        case 'year':
            $where_clause = "WHERE created_at >= DATE_SUB(NOW(), INTERVAL 1 YEAR)";
            break;
        case 'all':
        default:
            // No where clause for all records
            break;
    }

    $sql = "SELECT cph.*, u.name as updated_by_name
            FROM coin_price_history cph
            LEFT JOIN users u ON cph.updated_by = u.id
            $where_clause
            ORDER BY cph.created_at DESC
            LIMIT ?";

    $stmt = $conn->prepare($sql);

    if (!$stmt) {
        return [];
    }

    $stmt->bind_param("i", $limit);
    $stmt->execute();
    $result = $stmt->get_result();

    $history = [];
    while ($row = $result->fetch_assoc()) {
        $history[] = $row;
    }

    $stmt->close();
    return $history;
}
