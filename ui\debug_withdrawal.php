<?php
// Debug script to test withdrawal API directly
session_start();

// Set admin session for testing
$_SESSION['user_id'] = 1; // Replace with actual user ID
$_SESSION['is_admin'] = true;

include 'dbcon.php';

echo "<h2>Withdrawal Debug Test</h2>";

// Test 1: Check if withdrawal_requests table exists and has data
echo "<h3>1. Checking withdrawal_requests table:</h3>";
$sql = "SELECT * FROM withdrawal_requests WHERE status = 'pending' LIMIT 5";
$result = $conn->query($sql);

if ($result) {
    echo "✅ withdrawal_requests table accessible<br>";
    echo "Pending requests: " . $result->num_rows . "<br><br>";
    
    if ($result->num_rows > 0) {
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>ID</th><th>User ID</th><th>Amount</th><th>Status</th><th>Requested At</th></tr>";
        while ($row = $result->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . $row['id'] . "</td>";
            echo "<td>" . $row['user_id'] . "</td>";
            echo "<td>$" . $row['amount'] . "</td>";
            echo "<td>" . $row['status'] . "</td>";
            echo "<td>" . $row['requested_at'] . "</td>";
            echo "</tr>";
        }
        echo "</table><br>";
    }
} else {
    echo "❌ Error accessing withdrawal_requests: " . $conn->error . "<br><br>";
}

// Test 2: Check wallet_history table structure
echo "<h3>2. Checking wallet_history table structure:</h3>";
$describe = "DESCRIBE wallet_history";
$result = $conn->query($describe);

if ($result) {
    echo "✅ wallet_history table accessible<br>";
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $row['Field'] . "</td>";
        echo "<td>" . $row['Type'] . "</td>";
        echo "<td>" . $row['Null'] . "</td>";
        echo "<td>" . $row['Key'] . "</td>";
        echo "<td>" . $row['Default'] . "</td>";
        echo "</tr>";
    }
    echo "</table><br>";
} else {
    echo "❌ Error accessing wallet_history: " . $conn->error . "<br><br>";
}

// Test 3: Test wallet_history insert
echo "<h3>3. Testing wallet_history insert:</h3>";
try {
    $test_user_id = 1;
    $test_amount = -10.00;
    $test_description = "Debug test withdrawal";
    
    $log_sql = "INSERT INTO wallet_history (user_id, amount, type, wallet_type, description) VALUES (?, ?, 'withdrawal', 'withdrawal', ?)";
    $stmt = $conn->prepare($log_sql);
    
    if (!$stmt) {
        echo "❌ Failed to prepare statement: " . $conn->error . "<br>";
    } else {
        $stmt->bind_param("ids", $test_user_id, $test_amount, $test_description);
        
        if ($stmt->execute()) {
            echo "✅ wallet_history insert successful<br>";
            
            // Clean up
            $cleanup = "DELETE FROM wallet_history WHERE description = 'Debug test withdrawal'";
            $conn->query($cleanup);
            echo "✅ Test record cleaned up<br>";
        } else {
            echo "❌ wallet_history insert failed: " . $stmt->error . "<br>";
        }
        $stmt->close();
    }
} catch (Exception $e) {
    echo "❌ Exception during wallet_history test: " . $e->getMessage() . "<br>";
}

// Test 4: Check users table for withdrawal_wallet_balance column
echo "<h3>4. Checking users table structure:</h3>";
$describe_users = "DESCRIBE users";
$result = $conn->query($describe_users);

if ($result) {
    $has_withdrawal_balance = false;
    while ($row = $result->fetch_assoc()) {
        if ($row['Field'] === 'withdrawal_wallet_balance') {
            $has_withdrawal_balance = true;
            echo "✅ withdrawal_wallet_balance column exists: " . $row['Type'] . "<br>";
            break;
        }
    }
    
    if (!$has_withdrawal_balance) {
        echo "❌ withdrawal_wallet_balance column missing from users table<br>";
    }
} else {
    echo "❌ Error checking users table: " . $conn->error . "<br>";
}

$conn->close();
?>

<br><br>
<a href="withdrawal.php">← Back to Withdrawal Page</a> | 
<a href="admin_withdrawal_requests.php">Admin Withdrawal Requests →</a>
