<?php
session_start();

// Check if user is admin
if (!isset($_SESSION['user_id']) || !$_SESSION['is_admin']) {
    header("Location: login.php");
    exit();
}

include 'dbcon.php';

$message = '';
$error = '';

if ($_POST) {
    $action = $_POST['action'] ?? '';
    $user_id = intval($_POST['user_id'] ?? 0);

    try {
        if ($action === 'set_near_limit' && $user_id > 0) {
            // Get user's package amount
            $sql = "SELECT u.package, p.package_amount FROM users u LEFT JOIN packages p ON u.package = p.package_name WHERE u.id = ?";
            $stmt = $conn->prepare($sql);
            $stmt->bind_param("i", $user_id);
            $stmt->execute();
            $result = $stmt->get_result();
            $user = $result->fetch_assoc();
            $stmt->close();

            if ($user && $user['package_amount']) {
                $package_amount = $user['package_amount'];
                $near_limit_earnings = ($package_amount * 2) * 0.9; // 90% of limit

                // Update user's earnings to 90% of limit
                $update_sql = "UPDATE users SET total_earnings_received = ?, plan_status = 'active' WHERE id = ?";
                $stmt = $conn->prepare($update_sql);
                $stmt->bind_param("di", $near_limit_earnings, $user_id);

                if ($stmt->execute()) {
                    $message = "✅ User earnings set to $" . number_format($near_limit_earnings, 2) . " (90% of limit)";
                } else {
                    $error = "❌ Failed to update user earnings: " . $stmt->error;
                }
                $stmt->close();
            } else {
                $error = "❌ User not found or no package assigned";
            }
        }

        if ($action === 'expire_plan' && $user_id > 0) {
            // Get user's package amount and set earnings to exactly 2x
            $sql = "SELECT u.package, p.package_amount FROM users u LEFT JOIN packages p ON u.package = p.package_name WHERE u.id = ?";
            $stmt = $conn->prepare($sql);
            $stmt->bind_param("i", $user_id);
            $stmt->execute();
            $result = $stmt->get_result();
            $user = $result->fetch_assoc();
            $stmt->close();

            if ($user && $user['package_amount']) {
                $package_amount = $user['package_amount'];
                $limit_earnings = $package_amount * 2; // Exactly 2x limit

                // Expire the plan
                $update_sql = "UPDATE users SET total_earnings_received = ?, plan_status = 'expired', plan_expired_at = NOW() WHERE id = ?";
                $stmt = $conn->prepare($update_sql);
                $stmt->bind_param("di", $limit_earnings, $user_id);

                if ($stmt->execute()) {
                    $message = "✅ Plan expired for user. Earnings set to $" . number_format($limit_earnings, 2) . " (2x limit reached)";
                } else {
                    $error = "❌ Failed to expire plan: " . $stmt->error;
                }
                $stmt->close();
            } else {
                $error = "❌ User not found or no package assigned";
            }
        }

        if ($action === 'reset_plan' && $user_id > 0) {
            // Reset plan to active with zero earnings
            $update_sql = "UPDATE users SET total_earnings_received = 0, plan_status = 'active', plan_expired_at = NULL WHERE id = ?";
            $stmt = $conn->prepare($update_sql);
            $stmt->bind_param("i", $user_id);

            if ($stmt->execute()) {
                $message = "✅ Plan reset to active with zero earnings";
            } else {
                $error = "❌ Failed to reset plan: " . $stmt->error;
            }
            $stmt->close();
        }
    } catch (Exception $e) {
        $error = "Error: " . $e->getMessage();
    }
}

// Get all users with packages for testing
$users_sql = "SELECT u.id, u.name, u.user_id, u.package, u.total_earnings_received, u.plan_status, u.plan_expired_at, p.package_amount 
              FROM users u 
              LEFT JOIN packages p ON u.package = p.package_name 
              WHERE u.is_admin = 0 AND u.deleted_at IS NULL 
              ORDER BY u.id DESC";
$users_result = $conn->query($users_sql);
$users = $users_result->fetch_all(MYSQLI_ASSOC);

$conn->close();
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Plan Expiration - NetVis</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>

<body class="bg-gray-100">
    <?php include 'admin_nav.php'; ?>

    <main class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div class="bg-white shadow-lg rounded-lg p-8">
            <div class="mb-6">
                <h2 class="text-3xl font-bold text-gray-800">Test Plan Expiration System</h2>
                <p class="text-gray-600 mt-2">Test the 2x plan limit functionality by manually setting user earnings.</p>
            </div>

            <?php if ($message): ?>
                <div class="bg-green-50 border border-green-200 text-green-800 px-4 py-3 rounded-lg mb-6">
                    <?php echo $message; ?>
                </div>
            <?php endif; ?>

            <?php if ($error): ?>
                <div class="bg-red-50 border border-red-200 text-red-800 px-4 py-3 rounded-lg mb-6">
                    <?php echo $error; ?>
                </div>
            <?php endif; ?>

            <!-- Testing Instructions -->
            <div class="bg-blue-50 p-6 rounded-lg mb-6">
                <h3 class="text-blue-800 font-semibold mb-3">🧪 Testing Instructions</h3>
                <div class="text-blue-700 text-sm space-y-2">
                    <p><strong>1. Set Near Limit:</strong> Sets user earnings to 90% of their 2x package limit</p>
                    <p><strong>2. Expire Plan:</strong> Sets earnings to exactly 2x package amount and expires the plan</p>
                    <p><strong>3. Reset Plan:</strong> Resets earnings to zero and activates the plan</p>
                    <p><strong>4. Test Earnings:</strong> Run earnings calculation to see the system in action</p>
                </div>
            </div>

            <!-- Users Table -->
            <div class="mb-6">
                <h3 class="text-xl font-semibold text-gray-800 mb-4">Users for Testing</h3>

                <div class="overflow-x-auto border border-gray-200 rounded-lg">
                    <table class="min-w-full bg-white">
                        <thead class="bg-gray-100">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-600 uppercase">User</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-600 uppercase">Package</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-600 uppercase">Package Amount</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-600 uppercase">Earnings</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-600 uppercase">Limit (2x)</th>
                                <th class="px-6 py-3 text-center text-xs font-medium text-gray-600 uppercase">Status</th>
                                <th class="px-6 py-3 text-center text-xs font-medium text-gray-600 uppercase">Progress</th>
                                <th class="px-6 py-3 text-center text-xs font-medium text-gray-600 uppercase">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="divide-y divide-gray-200">
                            <?php foreach ($users as $user): ?>
                                <?php
                                $package_amount = $user['package_amount'] ?? 0;
                                $earnings = $user['total_earnings_received'] ?? 0;
                                $limit = $package_amount * 2;
                                $progress = $package_amount > 0 ? ($earnings / $limit) * 100 : 0;
                                ?>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div>
                                            <div class="text-sm font-medium text-gray-900"><?php echo htmlspecialchars($user['name']); ?></div>
                                            <div class="text-sm text-gray-500">ID: <?php echo htmlspecialchars($user['user_id']); ?></div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        <?php echo htmlspecialchars($user['package'] ?? 'No package'); ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right">
                                        $<?php echo number_format($package_amount, 2); ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right">
                                        $<?php echo number_format($earnings, 2); ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right">
                                        $<?php echo number_format($limit, 2); ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-center">
                                        <?php if ($user['plan_status'] === 'expired'): ?>
                                            <span class="px-2 py-1 text-xs font-medium rounded-full bg-red-100 text-red-800">Expired</span>
                                        <?php else: ?>
                                            <span class="px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800">Active</span>
                                        <?php endif; ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-center text-sm text-gray-900">
                                        <?php echo number_format($progress, 1); ?>%
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-center text-sm font-medium">
                                        <?php if ($package_amount > 0): ?>
                                            <div class="flex space-x-1">
                                                <form method="POST" style="display: inline;">
                                                    <input type="hidden" name="action" value="set_near_limit">
                                                    <input type="hidden" name="user_id" value="<?php echo $user['id']; ?>">
                                                    <button type="submit" class="text-yellow-600 hover:text-yellow-900 text-xs">90%</button>
                                                </form>
                                                <form method="POST" style="display: inline;">
                                                    <input type="hidden" name="action" value="expire_plan">
                                                    <input type="hidden" name="user_id" value="<?php echo $user['id']; ?>">
                                                    <button type="submit" class="text-red-600 hover:text-red-900 text-xs">Expire</button>
                                                </form>
                                                <form method="POST" style="display: inline;">
                                                    <input type="hidden" name="action" value="reset_plan">
                                                    <input type="hidden" name="user_id" value="<?php echo $user['id']; ?>">
                                                    <button type="submit" class="text-green-600 hover:text-green-900 text-xs">Reset</button>
                                                </form>
                                            </div>
                                        <?php else: ?>
                                            <span class="text-gray-400 text-xs">No package</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="flex space-x-4">
                <a href="calculate_earnings.php" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
                    Run Earnings Calculation
                </a>
                <a href="wallet.php" class="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700">
                    View User Wallet
                </a>
                <a href="admin.php" class="bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700">
                    Admin Dashboard
                </a>
            </div>
        </div>
    </main>
</body>

</html>