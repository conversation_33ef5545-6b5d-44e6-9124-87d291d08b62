<?php
include 'dbcon.php';

// Create database if it doesn't exist
$sql = "CREATE DATABASE IF NOT EXISTS $dbname";
$conn->query($sql);

// Select the database
$conn->select_db($dbname);

// SQL to create table if it doesn't exist
$sql = 'CREATE TABLE IF NOT EXISTS `users` (
  `id` int(6) UNSIGNED NOT NULL AUTO_INCREMENT,
  `package` varchar(30) NOT NULL,
  `sponser_id` varchar(30) NOT NULL,
  `binanace_address` varchar(255) DEFAULT NULL,
  `name` varchar(50) DEFAULT NULL,
  `email` varchar(50) DEFAULT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `password` varchar(255) DEFAULT NULL,
  `is_admin` tinyint(1) NOT NULL DEFAULT 0,
  `updated_by` int(6) UNSIGNED DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `reg_date` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `email` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;';
$conn->query($sql);

// Get data from form
$package = $_POST['package'];
$sponser_id = $_POST['sponser_id'];
$ninace_bep_id = $_POST['ninace_bep_id'];
$name = $_POST['name'];
$email = $_POST['email'];
$phone = $_POST['phone'];
$password = password_hash($_POST['password'], PASSWORD_DEFAULT);

// Prepare and bind
$stmt = $conn->prepare('INSERT INTO users (package, sponser_id, binanace_address, name, email, phone, password) VALUES (?, ?, ?, ?, ?, ?, ?)');
$stmt->bind_param('sssssss', $package, $sponser_id, $binanace_address, $name, $email, $phone, $password);

if ($stmt->execute()) {
  // Redirect to login page on success
  header('Location: login.php');
  exit();
} else {
  // Handle potential errors, like a duplicate email
  echo 'Error during registration. The email might already be in use.';
}

$stmt->close();
$conn->close();
