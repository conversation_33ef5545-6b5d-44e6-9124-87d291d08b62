<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Level 2 Team - Crypto Wallet</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <style>
        body {
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
            min-height: 100vh;
        }

        .stats-card {
            background: rgba(30, 41, 59, 0.4);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(59, 130, 246, 0.2);
            border-radius: 0.75rem;
            padding: 1rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }

        .member-card {
            background: rgba(30, 41, 59, 0.6);
            border: 1px solid rgba(59, 130, 246, 0.2);
            border-radius: 0.5rem;
            padding: 0.75rem;
            transition: all 0.3s ease;
        }

        .member-card:hover {
            border-color: rgba(6, 182, 212, 0.4);
            background: rgba(30, 41, 59, 0.8);
            transform: translateY(-2px);
        }

        /* Bottom Navigation Styles */
        .accent {
            color: #06b6d4 !important;
        }

        .hover\:accent:hover {
            color: #06b6d4 !important;
        }
    </style>
</head>

<body class="text-white">
    <div class="min-h-screen flex flex-col">
        <!-- Header -->
        <div class="sticky top-0 z-50 bg-slate-800/95 backdrop-blur-sm border-b border-slate-700">
            <div class="flex items-center justify-between px-6 py-4">
                <div class="flex items-center gap-4">
                    <button onclick="goBack()"
                        class="w-10 h-10 rounded-full bg-slate-700 hover:bg-slate-600 flex items-center justify-center transition-colors">
                        <i class="fas fa-arrow-left text-slate-300"></i>
                    </button>
                    <div>
                        <h1 class="text-xl font-bold text-white">Level 2 Team</h1>
                        <p class="text-sm text-slate-400">Second Level • 5% Commission</p>
                    </div>
                </div>
                <div class="flex items-center gap-2">
                    <div class="text-right">
                        <p class="text-xs text-slate-400">Total Earnings</p>
                        <p class="text-lg font-bold text-cyan-400">$285</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-grow overflow-y-auto px-4 sm:px-6 py-4">
            <!-- Level Summary -->
            <div class="stats-card mb-4">
                <div class="flex items-center gap-2 mb-4">
                    <div
                        class="w-10 h-10 rounded-lg bg-gradient-to-r from-cyan-600 to-blue-600 flex items-center justify-center">
                        <span class="text-white font-bold text-sm">L2</span>
                    </div>
                    <div>
                        <h2 class="text-lg font-bold text-white">Level 2 Summary</h2>
                        <p class="text-xs text-slate-400">Second level referrals and earnings</p>
                    </div>
                </div>
                <div class="grid grid-cols-3 gap-4">
                    <div class="text-center">
                        <p class="text-2xl font-bold text-cyan-400">12</p>
                        <p class="text-xs text-slate-400">Members</p>
                    </div>
                    <div class="text-center">
                        <p class="text-2xl font-bold text-blue-400">5%</p>
                        <p class="text-xs text-slate-400">Commission</p>
                    </div>
                    <div class="text-center">
                        <p class="text-2xl font-bold text-cyan-400">$285</p>
                        <p class="text-xs text-slate-400">Total Earned</p>
                    </div>
                </div>
            </div>

            <!-- Team Members List -->
            <div class="stats-card mb-4">
                <div class="flex items-center gap-2 mb-4">
                    <div
                        class="w-10 h-10 rounded-lg bg-gradient-to-r from-blue-600 to-cyan-600 flex items-center justify-center">
                        <i class="fas fa-users text-white"></i>
                    </div>
                    <div>
                        <h2 class="text-lg font-bold text-white">Team Members</h2>
                        <p class="text-xs text-slate-400">Your second level referrals</p>
                    </div>
                </div>

                <div class="space-y-3">
                    <!-- Member 1 -->
                    <div class="member-card" onclick="viewUserDownline('kevin-lee', 1)">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center gap-3">
                                <div
                                    class="w-12 h-12 rounded-full bg-gradient-to-r from-cyan-600 to-blue-600 flex items-center justify-center text-white font-bold">
                                    KL
                                </div>
                                <div>
                                    <p class="text-base font-semibold text-white">Kevin Lee</p>
                                    <p class="text-xs text-slate-400">Joined: 3 days ago</p>
                                    <p class="text-xs text-cyan-400">Package: Standard ($100)</p>
                                    <p class="text-xs text-slate-500">Referred by: John Doe</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <p class="text-lg font-bold text-cyan-400">$15</p>
                                <p class="text-xs text-slate-400">Earned</p>
                                <p class="text-xs text-cyan-400">Active</p>
                            </div>
                        </div>
                        <div class="mt-2 text-xs text-slate-500 flex items-center gap-1">
                            <i class="fas fa-mouse-pointer"></i> Click to view 1-level downline
                        </div>
                    </div>

                    <!-- Member 2 -->
                    <div class="member-card">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center gap-3">
                                <div
                                    class="w-12 h-12 rounded-full bg-gradient-to-r from-cyan-600 to-blue-600 flex items-center justify-center text-white font-bold">
                                    JC
                                </div>
                                <div>
                                    <p class="text-base font-semibold text-white">Jessica Chen</p>
                                    <p class="text-xs text-slate-400">Joined: 1 week ago</p>
                                    <p class="text-xs text-cyan-400">Package: Premium ($250)</p>
                                    <p class="text-xs text-slate-500">Referred by: Alice Smith</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <p class="text-lg font-bold text-cyan-400">$28</p>
                                <p class="text-xs text-slate-400">Earned</p>
                                <p class="text-xs text-cyan-400">Active</p>
                            </div>
                        </div>
                    </div>

                    <!-- Member 3 -->
                    <div class="member-card">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center gap-3">
                                <div
                                    class="w-12 h-12 rounded-full bg-gradient-to-r from-blue-600 to-cyan-600 flex items-center justify-center text-white font-bold">
                                    AR
                                </div>
                                <div>
                                    <p class="text-base font-semibold text-white">Alex Rodriguez</p>
                                    <p class="text-xs text-slate-400">Joined: 2 weeks ago</p>
                                    <p class="text-xs text-cyan-400">Package: Large ($500)</p>
                                    <p class="text-xs text-slate-500">Referred by: Mike Brown</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <p class="text-lg font-bold text-cyan-400">$35</p>
                                <p class="text-xs text-slate-400">Earned</p>
                                <p class="text-xs text-cyan-400">Active</p>
                            </div>
                        </div>
                    </div>

                    <!-- Member 4 -->
                    <div class="member-card">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center gap-3">
                                <div
                                    class="w-12 h-12 rounded-full bg-gradient-to-r from-cyan-600 to-blue-600 flex items-center justify-center text-white font-bold">
                                    MK
                                </div>
                                <div>
                                    <p class="text-base font-semibold text-white">Maria Kim</p>
                                    <p class="text-xs text-slate-400">Joined: 3 weeks ago</p>
                                    <p class="text-xs text-cyan-400">Package: Standard ($100)</p>
                                    <p class="text-xs text-slate-500">Referred by: Sarah Johnson</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <p class="text-lg font-bold text-cyan-400">$22</p>
                                <p class="text-xs text-slate-400">Earned</p>
                                <p class="text-xs text-cyan-400">Active</p>
                            </div>
                        </div>
                    </div>

                    <!-- Member 5 -->
                    <div class="member-card">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center gap-3">
                                <div
                                    class="w-12 h-12 rounded-full bg-gradient-to-r from-blue-600 to-cyan-600 flex items-center justify-center text-white font-bold">
                                    TH
                                </div>
                                <div>
                                    <p class="text-base font-semibold text-white">Tom Harris</p>
                                    <p class="text-xs text-slate-400">Joined: 1 month ago</p>
                                    <p class="text-xs text-cyan-400">Package: Trial ($10)</p>
                                    <p class="text-xs text-slate-500">Referred by: David Wilson</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <p class="text-lg font-bold text-cyan-400">$8</p>
                                <p class="text-xs text-slate-400">Earned</p>
                                <p class="text-xs text-cyan-400">Active</p>
                            </div>
                        </div>
                    </div>

                    <!-- Member 6 -->
                    <div class="member-card">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center gap-3">
                                <div
                                    class="w-12 h-12 rounded-full bg-gradient-to-r from-cyan-600 to-blue-600 flex items-center justify-center text-white font-bold">
                                    NP
                                </div>
                                <div>
                                    <p class="text-base font-semibold text-white">Nina Patel</p>
                                    <p class="text-xs text-slate-400">Joined: 1 month ago</p>
                                    <p class="text-xs text-cyan-400">Package: Premium ($250)</p>
                                    <p class="text-xs text-slate-500">Referred by: Emma Miller</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <p class="text-lg font-bold text-cyan-400">$32</p>
                                <p class="text-xs text-slate-400">Earned</p>
                                <p class="text-xs text-cyan-400">Active</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sticky Bottom Navigation Bar -->
        <div class="sticky bottom-0 z-40 flex-shrink-0">
            <div class="bg-slate-800/95 backdrop-blur-sm mx-4 mb-4 rounded-2xl border border-slate-700">
                <div class="flex justify-around items-center h-16">
                    <a href="1.html" class="flex flex-col items-center gap-1 text-slate-400 hover:accent">
                        <i class="fas fa-home"></i>
                        <span class="text-xs">Home</span>
                    </a>
                    <a href="wallet.html" class="flex flex-col items-center gap-1 text-slate-400 hover:accent">
                        <i class="fas fa-wallet"></i>
                        <span class="text-xs">Wallet</span>
                    </a>
                    <a href="team.html" class="flex flex-col items-center gap-1 accent">
                        <i class="fas fa-users"></i>
                        <span class="text-xs">Team</span>
                    </a>
                    <a href="profile.html" class="flex flex-col items-center gap-1 text-slate-400 hover:accent">
                        <i class="fas fa-user"></i>
                        <span class="text-xs">Profile</span>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script>
        function goBack() {
            window.history.back();
        }

        function viewUserDownline(userId, maxLevels) {
            window.location.href = `user-downline.html?user=${userId}&levels=${maxLevels}&from=2`;
        }
    </script>
</body>

</html>