<?php
ini_set('display_errors', 0);
ini_set('log_errors', 1);
ini_set('error_log', 'c:/xampp/htdocs/netvis/php_errors.log');
error_reporting(E_ALL);
session_start();
header('Content-Type: application/json');

if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'message' => 'Authentication required.']);
    exit();
}

include '../dbcon.php';

$sender_id = $_SESSION['user_id'];
$recipient_user_id = $_POST['recipient_user_id'] ?? '';
$amount = (float) ($_POST['amount'] ?? 0);
$wallet_type = $_POST['wallet_type'] ?? '';
$sender_password = $_POST['password'] ?? '';

if (empty($recipient_user_id) || $amount <= 0 || empty($sender_password) || empty($wallet_type)) {
    echo json_encode(['success' => false, 'message' => 'All fields are required.']);
    exit();
}

// Validate amount is multiple of 10
if ($amount < 10) {
    echo json_encode(['success' => false, 'message' => 'Minimum transfer amount is $10.']);
    exit();
}

if (fmod($amount, 10) != 0) {
    echo json_encode(['success' => false, 'message' => 'Amount must be a multiple of $10.']);
    exit();
}

// Validate wallet type
if (!in_array($wallet_type, ['deposit', 'withdrawal'])) {
    echo json_encode(['success' => false, 'message' => 'Invalid wallet type.']);
    exit();
}

// 1. Verify sender's password and get their details
$stmt = $conn->prepare('SELECT id, deposit_wallet_balance, withdrawal_wallet_balance, sponser_id FROM users WHERE id = ?');
if ($stmt === false) {
    echo json_encode(['success' => false, 'message' => 'Database error: ' . $conn->error]);
    exit();
}
$stmt->bind_param('i', $sender_id);
if (!$stmt->execute()) {
    echo json_encode(['success' => false, 'message' => 'Database error on fetching sender: ' . $stmt->error]);
    exit();
}
$stmt->store_result();
if ($stmt->num_rows === 0) {
    echo json_encode(['success' => false, 'message' => 'Sender not found. This should not happen.']);
    exit();
}
$stmt->bind_result($id, $deposit_wallet_balance, $withdrawal_wallet_balance, $sponser_id);
$stmt->fetch();
$sender = [
    'id' => $id,
    'deposit_wallet_balance' => $deposit_wallet_balance,
    'withdrawal_wallet_balance' => $withdrawal_wallet_balance,
    'sponser_id' => $sponser_id
];
$stmt->close();

// Verify sender's password
$stmt = $conn->prepare('SELECT password FROM users WHERE id = ?');
if ($stmt === false) {
    echo json_encode(['success' => false, 'message' => 'Database error: ' . $conn->error]);
    exit();
}
$stmt->bind_param('i', $sender_id);
if (!$stmt->execute()) {
    echo json_encode(['success' => false, 'message' => 'Database error on fetching sender password: ' . $stmt->error]);
    exit();
}
$stmt->store_result();
if ($stmt->num_rows === 0) {
    echo json_encode(['success' => false, 'message' => 'Sender not found. This should not happen.']);
    exit();
}
$stmt->bind_result($password_hash);
$stmt->fetch();
if (!$sender || !password_verify($sender_password, $password_hash)) {
    echo json_encode(['success' => false, 'message' => 'Invalid password.']);
    exit();
}
$stmt->close();

// 2. Check for sufficient balance in selected wallet
$selected_balance = ($wallet_type === 'deposit') ? $sender['deposit_wallet_balance'] : $sender['withdrawal_wallet_balance'];
if ($selected_balance < $amount) {
    echo json_encode(['success' => false, 'message' => "Insufficient balance in {$wallet_type} wallet."]);
    exit();
}

// 3. Get recipient's details
$stmt = $conn->prepare('SELECT id, sponser_id, name FROM users WHERE user_id = ?');
if ($stmt === false) {
    echo json_encode(['success' => false, 'message' => 'Database error: ' . $conn->error]);
    exit();
}
$stmt->bind_param('s', $recipient_user_id);
if (!$stmt->execute()) {
    echo json_encode(['success' => false, 'message' => 'Database error on fetching recipient: ' . $stmt->error]);
    exit();
}
$stmt->store_result();
$recipient = null;
if ($stmt->num_rows > 0) {
    $stmt->bind_result($id, $sponser_id, $name);
    $stmt->fetch();
    $recipient = [
        'id' => $id,
        'sponser_id' => $sponser_id,
        'name' => $name
    ];
}

if (!$recipient) {
    echo json_encode(['success' => false, 'message' => 'Recipient not found.']);
    exit();
}
$recipient_id = $recipient['id'];

if ($sender_id === $recipient_id) {
    echo json_encode(['success' => false, 'message' => 'You cannot transfer funds to yourself.']);
    exit();
}

// 4. Auto-detect and validate relationship (Downline or Direct Sponsor)
$is_valid_recipient = false;
// Case 1: Recipient is the sender's direct downline (sender is the sponsor)
if ($recipient['sponser_id'] == $sender_id) {
    $is_valid_recipient = true;
}
// Case 2: Recipient is the sender's direct sponsor
if ($sender['sponser_id'] == $recipient_id) {
    $is_valid_recipient = true;
}

// Note: A more robust downline check is needed for a full implementation.
// For now, we are only checking for direct sponsor/sponsee relationship for 'downline' type.
if (!$is_valid_recipient) {
    echo json_encode(['success' => false, 'message' => 'Invalid recipient. Transfer is only allowed to your direct sponsor or direct downline.']);
    exit();
}

// 5. Start transaction
$conn->begin_transaction();

try {
    // Debit from sender's selected wallet
    $new_sender_balance = $selected_balance - $amount;
    $wallet_field = $wallet_type . '_wallet_balance';
    $stmt1 = $conn->prepare("UPDATE users SET {$wallet_field} = ? WHERE id = ?");
    if ($stmt1 === false)
        throw new Exception('Failed to prepare statement 1: ' . $conn->error);
    $stmt1->bind_param('di', $new_sender_balance, $sender_id);
    if (!$stmt1->execute())
        throw new Exception('Failed to execute statement 1: ' . $stmt1->error);
    $stmt1->close();

    // Credit to recipient
    $stmt2 = $conn->prepare('UPDATE users SET deposit_wallet_balance = deposit_wallet_balance + ? WHERE id = ?');
    if ($stmt2 === false)
        throw new Exception('Failed to prepare statement 2: ' . $conn->error);
    $stmt2->bind_param('di', $amount, $recipient_id);
    if (!$stmt2->execute())
        throw new Exception('Failed to execute statement 2: ' . $stmt2->error);
    $stmt2->close();

    // Log the transaction for the sender (debit)
    $desc_sender = "Fund transfer to {$recipient['name']} from {$wallet_type} wallet";
    $stmt_log_debit = $conn->prepare("INSERT INTO wallet_history (user_id, amount, type, wallet_type, description, related_user_id) VALUES (?, ?, 'fund_transfer_debit', ?, ?, ?)");
    $debit_amount = -$amount;
    $stmt_log_debit->bind_param('idssi', $sender_id, $debit_amount, $wallet_type, $desc_sender, $recipient_id);
    if (!$stmt_log_debit->execute())
        throw new Exception('Failed to execute statement log debit: ' . $stmt_log_debit->error);
    $stmt_log_debit->close();

    // Log the transaction for the recipient (credit)
    $sender_name_stmt = $conn->prepare('SELECT name FROM users WHERE id = ?');
    if ($sender_name_stmt === false)
        throw new Exception('Failed to prepare statement for sender name: ' . $conn->error);
    $sender_name_stmt->bind_param('i', $sender_id);
    if (!$sender_name_stmt->execute())
        throw new Exception('Failed to execute statement for sender name: ' . $sender_name_stmt->error);
    $sender_name_stmt->store_result();
    $sender_name_stmt->bind_result($sender_name);
    $sender_name_stmt->fetch();
    $sender_name_stmt->close();

    $desc_recipient = "Fund transfer from {$sender_name}";
    $stmt_log_credit = $conn->prepare("INSERT INTO wallet_history (user_id, amount, type, wallet_type, description, related_user_id) VALUES (?, ?, 'fund_transfer_credit', 'deposit', ?, ?)");
    $stmt_log_credit->bind_param('idsi', $recipient_id, $amount, $desc_recipient, $sender_id);
    if (!$stmt_log_credit->execute())
        throw new Exception('Failed to execute statement log credit: ' . $stmt_log_credit->error);
    $stmt_log_credit->close();

    // Commit the transaction
    $conn->commit();
    echo json_encode(['success' => true, 'message' => 'Transfer successful!']);
} catch (Exception $e) {
    $conn->rollback();
    error_log('Transaction Error: ' . $e->getMessage());  // Log the specific error
    echo json_encode(['success' => false, 'message' => 'An error occurred during the transaction.']);
}

$stmt->close();
$conn->close();
