<?php
session_start();

if (!isset($_SESSION['user_id']) || !$_SESSION['is_admin']) {
    header("Location: login.html");
    exit();
}

include 'dbcon.php';

$status_filter = $_GET['status'] ?? 'pending';
$allowed_statuses = ['pending', 'approved', 'rejected'];
if (!in_array($status_filter, $allowed_statuses)) {
    $status_filter = 'pending';
}

$sql = "SELECT dr.*, u.name as user_name, u.user_id as user_identifier FROM deposit_requests dr JOIN users u ON dr.user_id = u.id WHERE dr.status = ? ORDER BY dr.requested_at DESC";
$stmt = $conn->prepare($sql);
$stmt->bind_param("s", $status_filter);
$stmt->execute();
$requests_result = $stmt->get_result();

// Page configuration
$page_title = "Deposit Requests Management";
$additional_css = '
<style>
    /* Enhanced table styling */
    .admin-table {
        width: 100%;
        border-collapse: separate;
        border-spacing: 0;
        background: white;
    }

    .admin-table thead {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    .admin-table thead th {
        color: white;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.05em;
        padding: 1rem 1.5rem;
        font-size: 0.75rem;
        border: none;
        position: relative;
    }

    .admin-table thead th:first-child {
        text-align: left;
        padding-left: 2rem;
    }

    .admin-table thead th:not(:first-child) {
        text-align: center;
    }

    .admin-table tbody tr {
        transition: all 0.2s ease;
        border-bottom: 1px solid #e2e8f0;
    }

    .admin-table tbody tr:hover {
        background-color: #f8fafc;
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    .admin-table tbody tr:last-child {
        border-bottom: none;
    }

    .admin-table tbody td {
        padding: 1rem 1.5rem;
        vertical-align: middle;
        border: none;
        background-color: white;
    }

    .admin-table tbody td:first-child {
        text-align: left;
        padding-left: 2rem;
    }

    .admin-table tbody td:not(:first-child) {
        text-align: center;
    }

    /* Ensure proper table layout */
    .admin-table {
        table-layout: fixed;
    }

    .admin-table th:nth-child(1),
    .admin-table td:nth-child(1) {
        width: 20%;
    }

    .admin-table th:nth-child(2),
    .admin-table td:nth-child(2) {
        width: 25%;
    }

    .admin-table th:nth-child(3),
    .admin-table td:nth-child(3) {
        width: 15%;
    }

    .admin-table th:nth-child(4),
    .admin-table td:nth-child(4) {
        width: 15%;
    }

    .admin-table th:nth-child(5),
    .admin-table td:nth-child(5) {
        width: 25%;
    }

    /* Remove any conflicting styles */
    .admin-table * {
        box-sizing: border-box;
    }

    /* Smooth table appearance */
    .admin-table thead th {
        border-bottom: 2px solid rgba(255, 255, 255, 0.1);
        white-space: nowrap;
    }

    .admin-table tbody tr:hover td {
        background-color: #f8fafc;
    }

    /* Better spacing for table content */
    .admin-table tbody td > div {
        min-height: 2.5rem;
        display: flex;
        align-items: center;
    }

    /* Date column - left aligned */
    .admin-table tbody td:first-child {
        text-align: left !important;
    }

    .admin-table tbody td:first-child > div {
        justify-content: flex-start !important;
        align-items: flex-start !important;
        text-align: left !important;
    }

    .admin-table tbody td:first-child > div > div {
        text-align: left !important;
    }

    /* All other columns - center aligned */
    .admin-table tbody td:not(:first-child) > div {
        justify-content: center;
    }

    /* Ensure consistent row height */
    .admin-table tbody tr {
        height: 4rem;
    }

    /* Status badges */
    .status-badge {
        display: inline-flex;
        align-items: center;
        padding: 0.25rem 0.75rem;
        border-radius: 9999px;
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.05em;
    }

    .status-pending {
        background-color: #fef3c7;
        color: #92400e;
        border: 1px solid #fbbf24;
    }

    .status-approved {
        background-color: #d1fae5;
        color: #065f46;
        border: 1px solid #10b981;
    }

    .status-rejected {
        background-color: #fee2e2;
        color: #991b1b;
        border: 1px solid #ef4444;
    }

    /* Filter styling */
    .filter-container {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 0.75rem;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
    }

    /* Action buttons */
    .action-btn {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 2rem;
        height: 2rem;
        border-radius: 0.375rem;
        font-weight: 500;
        font-size: 0.875rem;
        transition: all 0.2s ease;
        border: none;
        cursor: pointer;
    }

    .action-btn-view {
        background: rgba(59, 130, 246, 0.1);
        color: #3b82f6;
        border: 1px solid rgba(59, 130, 246, 0.2);
    }

    .action-btn-view:hover {
        background: rgba(59, 130, 246, 0.2);
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
    }

    .action-btn-approve {
        background: rgba(34, 197, 94, 0.1);
        color: #22c55e;
        border: 1px solid rgba(34, 197, 94, 0.2);
    }

    .action-btn-approve:hover {
        background: rgba(34, 197, 94, 0.2);
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(34, 197, 94, 0.2);
    }

    .action-btn-reject {
        background: rgba(239, 68, 68, 0.1);
        color: #ef4444;
        border: 1px solid rgba(239, 68, 68, 0.2);
    }

    .action-btn-reject:hover {
        background: rgba(239, 68, 68, 0.2);
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(239, 68, 68, 0.2);
    }
</style>
';

// Start output buffering to capture the page content
ob_start();

?>
<!-- Page Header -->
<div class="flex flex-col lg:flex-row justify-between items-start lg:items-center space-y-4 lg:space-y-0 mb-6">
    <div>
        <h1 class="text-3xl font-bold text-gray-900">Deposit Requests Management</h1>
        <p class="text-gray-600 mt-1">Review and manage user deposit requests</p>
    </div>
    <div class="flex items-center space-x-2 bg-blue-50 px-4 py-2 rounded-lg border border-blue-200">
        <i class="fas fa-credit-card text-blue-600"></i>
        <span class="text-sm font-medium text-blue-700">Deposit Management</span>
    </div>
</div>

<div id="feedback-message" class="hidden p-4 mb-4 text-sm rounded-lg" role="alert"></div>

<!-- Filter Section -->
<div class="filter-container">
    <div class="flex items-center justify-between">
        <div class="flex items-center space-x-3">
            <div class="w-10 h-10 bg-white bg-opacity-20 rounded-xl flex items-center justify-center">
                <i class="fas fa-filter text-white text-lg"></i>
            </div>
            <div>
                <h3 class="text-lg font-semibold text-white">Filter Requests</h3>
                <p class="text-blue-100 text-sm">Filter by request status</p>
            </div>
        </div>
        <div class="flex items-center space-x-4">
            <div class="flex items-center space-x-2">
                <a href="?status=pending" class="<?php echo $status_filter === 'pending' ? 'bg-white text-blue-600' : 'bg-white bg-opacity-20 text-white hover:bg-opacity-30'; ?> px-4 py-2 rounded-lg font-medium text-sm transition-all duration-200">
                    <i class="fas fa-clock mr-2"></i>Pending
                </a>
                <a href="?status=approved" class="<?php echo $status_filter === 'approved' ? 'bg-white text-blue-600' : 'bg-white bg-opacity-20 text-white hover:bg-opacity-30'; ?> px-4 py-2 rounded-lg font-medium text-sm transition-all duration-200">
                    <i class="fas fa-check mr-2"></i>Approved
                </a>
                <a href="?status=rejected" class="<?php echo $status_filter === 'rejected' ? 'bg-white text-blue-600' : 'bg-white bg-opacity-20 text-white hover:bg-opacity-30'; ?> px-4 py-2 rounded-lg font-medium text-sm transition-all duration-200">
                    <i class="fas fa-times mr-2"></i>Rejected
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Section -->
<?php
// Get statistics for current filter
$stats_sql = "SELECT
    COUNT(*) as total_requests,
    SUM(amount) as total_amount,
    COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_count,
    COUNT(CASE WHEN status = 'approved' THEN 1 END) as approved_count,
    COUNT(CASE WHEN status = 'rejected' THEN 1 END) as rejected_count
    FROM deposit_requests";

$stats_result = $conn->query($stats_sql);
$stats = $stats_result->fetch_assoc();

// Get current filter stats
$current_stats_sql = "SELECT COUNT(*) as count, SUM(amount) as amount FROM deposit_requests WHERE status = ?";
$current_stmt = $conn->prepare($current_stats_sql);
$current_stmt->bind_param("s", $status_filter);
$current_stmt->execute();
$current_stats = $current_stmt->get_result()->fetch_assoc();
?>

<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
    <div class="admin-card p-6">
        <div class="flex items-center">
            <div class="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">
                <i class="fas fa-list text-blue-600 text-xl"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">Total Requests</p>
                <p class="text-2xl font-bold text-gray-900"><?php echo number_format($stats['total_requests']); ?></p>
            </div>
        </div>
    </div>

    <div class="admin-card p-6">
        <div class="flex items-center">
            <div class="w-12 h-12 bg-yellow-100 rounded-xl flex items-center justify-center">
                <i class="fas fa-clock text-yellow-600 text-xl"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">Pending</p>
                <p class="text-2xl font-bold text-gray-900"><?php echo number_format($stats['pending_count']); ?></p>
            </div>
        </div>
    </div>

    <div class="admin-card p-6">
        <div class="flex items-center">
            <div class="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center">
                <i class="fas fa-check text-green-600 text-xl"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">Approved</p>
                <p class="text-2xl font-bold text-gray-900"><?php echo number_format($stats['approved_count']); ?></p>
            </div>
        </div>
    </div>

    <div class="admin-card p-6">
        <div class="flex items-center">
            <div class="w-12 h-12 bg-red-100 rounded-xl flex items-center justify-center">
                <i class="fas fa-times text-red-600 text-xl"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">Rejected</p>
                <p class="text-2xl font-bold text-gray-900"><?php echo number_format($stats['rejected_count']); ?></p>
            </div>
        </div>
    </div>
</div>

<!-- Current Filter Summary -->
<div class="admin-card p-6 mb-6">
    <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
            <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl flex items-center justify-center">
                <i class="fas fa-chart-bar text-white text-xl"></i>
            </div>
            <div>
                <h3 class="text-lg font-semibold text-gray-900">
                    <?php echo ucfirst($status_filter); ?> Requests Summary
                </h3>
                <p class="text-sm text-gray-600">
                    <?php echo number_format($current_stats['count']); ?> requests •
                    Total: $<?php echo number_format($current_stats['amount'] ?? 0, 2); ?>
                </p>
            </div>
        </div>
        <div class="status-badge status-<?php echo $status_filter; ?>">
            <?php echo ucfirst($status_filter); ?>
        </div>
    </div>
</div>

<!-- Requests Table -->
<div class="admin-card p-0 overflow-hidden">
    <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center space-x-3">
            <div class="w-8 h-8 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-lg flex items-center justify-center">
                <i class="fas fa-credit-card text-white text-sm"></i>
            </div>
            <div>
                <h3 class="text-lg font-semibold text-gray-900">Deposit Requests</h3>
                <p class="text-sm text-gray-600"><?php echo ucfirst($status_filter); ?> deposit requests from users</p>
            </div>
        </div>
    </div>

    <div class="overflow-x-auto" style="border-radius: 0 0 0.75rem 0.75rem;">
        <table class="admin-table">
            <thead>
                <tr>
                    <th>Date</th>
                    <th>User</th>
                    <th>Amount</th>
                    <th>Receipt</th>
                    <?php if ($status_filter === 'pending'): ?>
                        <th>Actions</th>
                    <?php endif; ?>
                </tr>
            </thead>
            <tbody>
                <?php if ($requests_result->num_rows > 0): ?>
                    <?php while ($request = $requests_result->fetch_assoc()): ?>
                        <tr id="request-row-<?php echo $request['id']; ?>">
                            <td>
                                <div class="flex flex-col items-start">
                                    <div class="text-sm font-medium text-gray-900 mb-1">
                                        <?php echo date('M j, Y', strtotime($request['requested_at'])); ?>
                                    </div>
                                    <div class="text-xs text-gray-500">
                                        <?php echo date('g:i A', strtotime($request['requested_at'])); ?>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div class="flex items-center justify-center space-x-3">
                                    <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                        <i class="fas fa-user text-blue-600 text-xs"></i>
                                    </div>
                                    <div>
                                        <div class="text-sm font-medium text-gray-900">
                                            <?php echo htmlspecialchars($request['user_name']); ?>
                                        </div>
                                        <div class="text-xs text-gray-500">
                                            ID: <?php echo htmlspecialchars($request['user_identifier']); ?>
                                        </div>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <span class="text-lg font-bold text-green-600">
                                    $<?php echo number_format($request['amount'], 2); ?>
                                </span>
                            </td>
                            <td>
                                <div class="flex items-center justify-center space-x-2">
                                    <button class="action-btn action-btn-view"
                                        onclick="window.open('/netvis/<?php echo htmlspecialchars($request['receipt_path']); ?>', '_blank')"
                                        title="View Receipt">
                                        <i class="fas fa-eye text-sm"></i>
                                    </button>
                                </div>
                            </td>
                            <?php if ($status_filter === 'pending'): ?>
                                <td>
                                    <div class="flex items-center justify-center space-x-2">
                                        <button class="action-btn action-btn-approve"
                                            onclick="handleRequest(<?php echo $request['id']; ?>, 'approve')"
                                            title="Approve Request">
                                            <i class="fas fa-check text-sm"></i>
                                        </button>
                                        <button class="action-btn action-btn-reject"
                                            onclick="openRejectModal(<?php echo $request['id']; ?>)"
                                            title="Reject Request">
                                            <i class="fas fa-times text-sm"></i>
                                        </button>
                                    </div>
                                </td>
                            <?php endif; ?>
                        </tr>
                    <?php endwhile; ?>
                <?php else: ?>
                    <tr>
                        <td colspan="<?php echo $status_filter === 'pending' ? '5' : '4'; ?>" style="text-align: center; padding: 3rem;">
                            <div class="flex flex-col items-center">
                                <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                                    <i class="fas fa-credit-card text-gray-400 text-2xl"></i>
                                </div>
                                <h3 class="text-lg font-medium text-gray-900 mb-2">No <?php echo ucfirst($status_filter); ?> Requests</h3>
                                <p class="text-sm text-gray-500 mb-4">No deposit requests found for the selected status.</p>
                            </div>
                        </td>
                    </tr>
                <?php endif; ?>
            </tbody>
        </table>
    </div>
</div>

<!-- Reject Modal -->
<div id="reject-modal" class="hidden fixed z-50 inset-0 overflow-y-auto">
    <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 transition-opacity" aria-hidden="true">
            <div class="absolute inset-0 bg-gray-900 opacity-75"></div>
        </div>
        <div class="inline-block align-bottom bg-white rounded-xl text-left overflow-hidden shadow-2xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
            <div class="bg-white px-6 pt-6 pb-4">
                <div class="flex items-center space-x-3 mb-4">
                    <div class="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center">
                        <i class="fas fa-times text-red-600 text-xl"></i>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900">Reject Deposit Request</h3>
                        <p class="text-sm text-gray-500">This action cannot be undone</p>
                    </div>
                </div>
                <div class="mt-4">
                    <label for="rejection-notes" class="block text-sm font-medium text-gray-700 mb-2">
                        Rejection Reason (Optional)
                    </label>
                    <textarea id="rejection-notes" rows="4" class="form-input w-full" placeholder="Enter reason for rejection..."></textarea>
                </div>
            </div>
            <div class="bg-gray-50 px-6 py-4 flex flex-col sm:flex-row-reverse space-y-2 sm:space-y-0 sm:space-x-3 sm:space-x-reverse">
                <button type="button" id="confirm-reject-btn" class="btn-primary bg-red-600 hover:bg-red-700">
                    <i class="fas fa-times mr-2"></i>Confirm Rejection
                </button>
                <button type="button" onclick="closeRejectModal()" class="btn-secondary">
                    <i class="fas fa-arrow-left mr-2"></i>Cancel
                </button>
            </div>
        </div>
    </div>
</div>

<?php
// Get the page content
$page_content = ob_get_clean();

// Additional JavaScript for enhanced functionality
$additional_js = <<<'EOD'
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>
    let currentRequestId = null;

    function openRejectModal(requestId) {
        currentRequestId = requestId;
        document.getElementById('reject-modal').classList.remove('hidden');
    }

    function closeRejectModal() {
        document.getElementById('reject-modal').classList.add('hidden');
        document.getElementById('rejection-notes').value = '';
        currentRequestId = null;
    }

    document.getElementById('confirm-reject-btn').addEventListener('click', function() {
        const notes = document.getElementById('rejection-notes').value;
        handleRequest(currentRequestId, 'reject', notes);
        closeRejectModal();
    });

    function handleRequest(requestId, action, notes = '') {
        // Show loading state
        const actionButtons = document.querySelectorAll(`#request-row-${requestId} button`);
        actionButtons.forEach(btn => {
            btn.disabled = true;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
        });

        fetch('/netvis/ui/api/review_deposit_request.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                request_id: requestId,
                action: action,
                notes: notes
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification(`Request successfully ${action}ed.`, 'success');
                // Fade out and remove the row
                const row = document.getElementById(`request-row-${requestId}`);
                row.style.transition = 'all 0.5s ease';
                row.style.opacity = '0';
                row.style.transform = 'translateX(-100%)';
                setTimeout(() => {
                    row.remove();
                    // Check if table is empty and show empty state
                    const tbody = document.querySelector('.admin-table tbody');
                    if (tbody.children.length === 0) {
                        location.reload(); // Reload to show empty state
                    }
                }, 500);
            } else {
                showNotification(data.message || 'An error occurred.', 'error');
                resetActionButtons(requestId);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('A network error occurred. Please try again.', 'error');
            resetActionButtons(requestId);
        });
    }

    function resetActionButtons(requestId) {
        const approveBtn = document.querySelector(`#request-row-${requestId} .action-btn-approve`);
        const rejectBtn = document.querySelector(`#request-row-${requestId} .action-btn-reject`);
        if (approveBtn) {
            approveBtn.disabled = false;
            approveBtn.innerHTML = '<i class="fas fa-check text-sm"></i>';
        }
        if (rejectBtn) {
            rejectBtn.disabled = false;
            rejectBtn.innerHTML = '<i class="fas fa-times text-sm"></i>';
        }
    }

    // Add fade-in animation to cards
    document.addEventListener("DOMContentLoaded", function() {
        const cards = document.querySelectorAll(".admin-card");
        cards.forEach((card, index) => {
            card.style.opacity = "0";
            card.style.transform = "translateY(20px)";
            setTimeout(() => {
                card.style.transition = "all 0.5s ease";
                card.style.opacity = "1";
                card.style.transform = "translateY(0)";
            }, index * 100);
        });
    });
</script>
EOD;

// Include the layout
include 'admin/includes/layout.php';
?>