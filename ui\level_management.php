<?php
session_start();
if (!isset($_SESSION['user_id']) || !$_SESSION['is_admin']) {
    header('Location: login.php');
    exit();
}

// Page configuration
$page_title = "Level Distribution Management";
$additional_css = "
<style>
    /* Enhanced form styling */
    .level-form {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.98) 100%);
        backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 1.5rem;
        padding: 2.5rem;
        margin-bottom: 2rem;
        color: #1f2937;
        box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.1);
        animation: slideInUp 0.6s ease-out;
    }

    .form-input {
        background: rgba(255, 255, 255, 0.8);
        border: 2px solid rgba(59, 130, 246, 0.2);
        color: #1f2937;
        border-radius: 0.75rem;
        padding: 0.875rem 1rem;
        width: 100%;
        transition: all 0.3s ease;
        font-size: 0.95rem;
    }

    .form-input::placeholder {
        color: rgba(107, 114, 128, 0.7);
    }

    .form-input:focus {
        outline: none;
        border-color: rgba(59, 130, 246, 0.6);
        background: rgba(255, 255, 255, 0.95);
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        transform: translateY(-1px);
    }

    .form-label {
        color: #374151;
        font-weight: 600;
        margin-bottom: 0.5rem;
        display: block;
        font-size: 0.9rem;
    }

    /* Enhanced submit button */
    .submit-btn {
        background: linear-gradient(135deg, #3b82f6 0%, #6366f1 50%, #8b5cf6 100%);
        color: white;
        border: none;
        padding: 1rem 2.5rem;
        border-radius: 1rem;
        font-weight: 700;
        font-size: 1rem;
        letter-spacing: 0.025em;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        cursor: pointer;
        position: relative;
        overflow: hidden;
        box-shadow: 0 10px 25px -5px rgba(59, 130, 246, 0.4), 0 4px 6px -2px rgba(59, 130, 246, 0.05);
        transform: perspective(1000px) rotateX(0deg);
        width: 100%;
    }

    .submit-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
        transition: left 0.6s ease;
    }

    .submit-btn:hover::before {
        left: 100%;
    }

    .submit-btn:hover {
        background: linear-gradient(135deg, #2563eb 0%, #4f46e5 50%, #7c3aed 100%);
        transform: perspective(1000px) rotateX(-5deg) translateY(-3px);
        box-shadow: 0 20px 40px -10px rgba(59, 130, 246, 0.6), 0 10px 20px -5px rgba(59, 130, 246, 0.2);
    }

    .submit-btn:active {
        transform: perspective(1000px) rotateX(0deg) translateY(-1px);
        box-shadow: 0 5px 15px -3px rgba(59, 130, 246, 0.4);
        transition: all 0.1s ease;
    }

    .submit-btn span {
        position: relative;
        z-index: 1;
    }

    /* Enhanced animations */
    @keyframes slideInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* Level cards */
    .level-card {
        background: rgba(255, 255, 255, 0.9);
        border: 2px solid rgba(59, 130, 246, 0.2);
        border-radius: 1rem;
        padding: 1.5rem;
        margin-bottom: 1rem;
        transition: all 0.3s ease;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    }

    .level-card:hover {
        background: rgba(255, 255, 255, 0.95);
        border-color: rgba(59, 130, 246, 0.4);
        transform: translateY(-2px);
        box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
    }

    /* Toast styling */
    .toast-success {
        background: linear-gradient(135deg, #10b981, #059669) !important;
        color: white !important;
        padding: 1rem 1.5rem !important;
        border-radius: 1rem !important;
        box-shadow: 0 10px 25px -5px rgba(16, 185, 129, 0.4) !important;
        position: fixed !important;
        top: 1.25rem !important;
        right: 1.25rem !important;
        z-index: 9999 !important;
        display: flex !important;
        align-items: center !important;
        min-width: 300px !important;
        max-width: 500px !important;
        font-weight: 500 !important;
        opacity: 1 !important;
        visibility: visible !important;
        pointer-events: auto !important;
    }

    .toast-success .flex {
        gap: 0.5rem !important;
    }

    .toast-error {
        background: linear-gradient(135deg, #ef4444, #dc2626) !important;
        color: white !important;
        padding: 1rem 1.5rem !important;
        border-radius: 1rem !important;
        box-shadow: 0 10px 25px -5px rgba(239, 68, 68, 0.4) !important;
        position: fixed !important;
        top: 1.25rem !important;
        right: 1.25rem !important;
        z-index: 9999 !important;
        display: flex !important;
        align-items: center !important;
        min-width: 300px !important;
        max-width: 500px !important;
        font-weight: 500 !important;
        opacity: 1 !important;
        visibility: visible !important;
        pointer-events: auto !important;
    }

    .toast-error .flex {
        gap: 0.5rem !important;
    }

    /* Ensure hidden class works */
    .hidden {
        display: none !important;
    }

    /* Enhanced level card styling */
    .level-card .form-label {
        margin-bottom: 0;
    }

    .level-card .form-input {
        margin-top: 0;
    }
</style>
";

// Start output buffering to capture the page content
ob_start();
?>

<!-- Single Unified Header -->
<div class="relative overflow-hidden bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 rounded-2xl p-8 mb-8 border border-blue-100">
    <div class="absolute inset-0 bg-gradient-to-br from-blue-500/5 via-indigo-500/5 to-purple-500/5"></div>

    <!-- Subtle decorative elements -->
    <div class="absolute top-0 right-0 -mt-8 -mr-8 w-32 h-32 bg-gradient-to-br from-blue-200/20 to-indigo-200/20 rounded-full blur-3xl"></div>
    <div class="absolute bottom-0 left-0 -mb-8 -ml-8 w-40 h-40 bg-gradient-to-br from-indigo-200/20 to-purple-200/20 rounded-full blur-3xl"></div>

    <div class="relative flex flex-col lg:flex-row justify-between items-start lg:items-center space-y-6 lg:space-y-0">
        <div class="flex-1">
            <div class="flex items-center space-x-4 mb-4">
                <div class="w-14 h-14 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg">
                    <i class="fas fa-layer-group text-white text-xl"></i>
                </div>
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">Level Manager</h1>
                    <p class="text-gray-600 mt-1">Configure commission percentages for network distribution levels</p>
                </div>
            </div>

            <!-- Status indicators and information combined -->
            <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
                <div class="flex items-center space-x-6 text-sm text-gray-600">
                    <div class="flex items-center space-x-2">
                        <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                        <span>Commission Levels</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
                        <span>Network Distribution</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <div class="w-2 h-2 bg-purple-500 rounded-full"></div>
                        <span>Admin Control</span>
                    </div>
                </div>

                <!-- Information box integrated -->
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-3 max-w-md">
                    <div class="flex items-center space-x-2 mb-1">
                        <i class="fas fa-info-circle text-blue-600 text-sm"></i>
                        <span class="text-xs font-medium text-blue-800">Information</span>
                    </div>
                    <p class="text-xs text-blue-700">
                        Set percentages for three distribution levels. Total should not exceed 100%.
                    </p>
                </div>
            </div>
        </div>

        <div class="flex items-center space-x-3 lg:ml-6">
            <a href="bonus_management.php" class="inline-flex items-center px-4 py-2 bg-white border border-gray-200 text-gray-700 rounded-lg hover:bg-gray-50 transition-all duration-200 shadow-sm">
                <i class="fas fa-gift mr-2 text-purple-600"></i>
                Bonus Management
            </a>
            <div class="flex items-center space-x-2 bg-blue-50 px-4 py-2 rounded-lg border border-blue-200">
                <i class="fas fa-layer-group text-blue-600"></i>
                <span class="text-sm font-medium text-blue-700">Level Management</span>
            </div>
        </div>
    </div>
</div>

<!-- Level Management Form -->
<div class="level-form">

    <form id="levels-form">
        <div id="levels-container" class="space-y-4">
            <!-- JavaScript will populate this section -->
            <div class="text-center text-gray-600 py-8">
                <i class="fas fa-spinner fa-spin text-2xl mb-2"></i>
                <div>Loading level settings...</div>
            </div>
        </div>

        <div class="mt-6 pt-6 border-t border-gray-200">
            <button type="submit" class="submit-btn">
                <span>
                    <i class="fas fa-save mr-3"></i>
                    Save Changes
                </span>
            </button>
            <p class="text-center text-xs text-gray-500 mt-3">
                <i class="fas fa-info-circle mr-1"></i>
                Changes will be applied to new investments immediately
            </p>
        </div>
    </form>
</div>

<div id="toast-message" class="hidden"></div>

<?php
// Capture the page content
$page_content = ob_get_clean();

// Include the admin layout
include 'admin/includes/layout.php';
?>

<script src="/netvis/ui/js/level_management.js"></script>