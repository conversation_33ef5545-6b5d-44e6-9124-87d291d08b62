<?php
header('Content-Type: application/json');
include 'dbcon.php';

$response = ['success' => false, 'message' => ''];

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $name = $_POST['name'] ?? '';
    $email = $_POST['email'] ?? '';
    $phone = $_POST['phone'] ?? '';
    $password = $_POST['password'] ?? '';
    $binanace_address = $_POST['binanace_address'] ?? '';
    $sponser_user_code = $_POST['sponser_id'] ?? '';

    // Basic validation
    if (empty($name) || empty($email) || empty($password) || empty($sponser_user_code)) {
        $response['message'] = 'All required fields must be filled.';
        echo json_encode($response);
        exit;
    }

    // Validate email format
    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $response['message'] = 'Please enter a valid email address.';
        echo json_encode($response);
        exit;
    }

    // Check if email already exists
    $stmt = $conn->prepare('SELECT id FROM users WHERE email = ?');
    $stmt->bind_param('s', $email);
    $stmt->execute();
    $result = $stmt->get_result();
    if ($result->num_rows > 0) {
        $response['message'] = 'Email address is already registered.';
        echo json_encode($response);
        $stmt->close();
        exit;
    }
    $stmt->close();

    // --- Validate and find sponsor's internal ID from their user_id ---
    $sponser_id = null;
    $stmt = $conn->prepare('SELECT id FROM users WHERE user_id = ?');
    $stmt->bind_param('s', $sponser_user_code);
    $stmt->execute();
    $result = $stmt->get_result();
    if ($result->num_rows > 0) {
        $sponser_id = $result->fetch_assoc()['id'];
    } else {
        $response['message'] = 'Invalid sponsor code. Please check and try again.';
        echo json_encode($response);
        $stmt->close();
        exit;
    }
    $stmt->close();

    // --- Generate a unique user_id ---
    $initials = strtoupper(substr(preg_replace('/\s+/', '', $name), 0, 2));
    $is_unique = false;
    $user_id_code = '';
    while (!$is_unique) {
        $timestamp_part = substr(time(), -6);
        $user_id_code = $initials . $timestamp_part;
        $check_stmt = $conn->prepare('SELECT id FROM users WHERE user_id = ?');
        $check_stmt->bind_param('s', $user_id_code);
        $check_stmt->execute();
        if ($check_stmt->get_result()->num_rows == 0) {
            $is_unique = true;
        } else {
            // Add a random element to ensure uniqueness in case of rapid sign-ups
            $user_id_code = $initials . substr(time(), -5) . rand(0, 9);
        }
        $check_stmt->close();
    }

    // Hash password
    $password_hash = password_hash($password, PASSWORD_DEFAULT);

    // --- Insert the new user ---
    $stmt = $conn->prepare('INSERT INTO users (name, email, phone, password, sponser_id, binanace_address, user_id, is_active) VALUES (?, ?, ?, ?, ?, ?, ?, 1)');
    $stmt->bind_param('ssssiss', $name, $email, $phone, $password_hash, $sponser_id, $binanace_address, $user_id_code);

    if ($stmt->execute()) {
        $response['success'] = true;
        $response['message'] = 'Registration successful! You can now login with your credentials.';
        $response['redirect'] = 'login.php?registration=success';
    } else {
        $response['message'] = 'Registration failed. Please try again.';
    }
    $stmt->close();
} else {
    $response['message'] = 'Invalid request method.';
}

$conn->close();
echo json_encode($response);
?>
