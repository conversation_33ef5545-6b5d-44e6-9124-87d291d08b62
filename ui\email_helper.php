<?php
// <PERSON><PERSON><PERSON>ailer email helper for CryptoApp

// Include Composer autoloader
require_once 'vendor/autoload.php';

use <PERSON><PERSON><PERSON><PERSON><PERSON>\PHPMailer\PHPMailer;
use P<PERSON><PERSON>ailer\PHPMailer\SMTP;
use <PERSON><PERSON><PERSON>ailer\PHPMailer\Exception;

function sendCryptoAppEmail($to_email, $to_name, $subject, $html_message, $email_type = 'notification')
{
    $config = include 'email_config.php';

    // Create a new PHPMailer instance
    $mail = new PHPMailer(true);

    try {
        // Server settings
        $mail->isSMTP();
        $mail->Host = $config['smtp_host'];
        $mail->SMTPAuth = true;
        $mail->Username = $config['smtp_username'];
        $mail->Password = $config['smtp_password'];
        $mail->SMTPSecure = PHPMailer::ENCRYPTION_SMTPS; // SSL encryption
        $mail->Port = $config['smtp_port'];

        // Recipients
        $mail->setFrom($config['from_email'], $config['from_name']);
        $mail->addAddress($to_email, $to_name);
        $mail->addReplyTo($config['from_email'], $config['from_name']);

        // Content
        $mail->isHTML(true);
        $mail->Subject = $subject;
        $mail->Body = $html_message;
        $mail->CharSet = 'UTF-8';

        // Add plain text version for better deliverability
        $plain_text = createPlainTextVersion($html_message, $email_type);
        $mail->AltBody = $plain_text;

        // Anti-spam headers to improve deliverability
        $mail->Priority = 3; // Normal priority (1=high can trigger spam filters)
        $mail->addCustomHeader('X-Mailer', 'CryptoApp Security System');
        $mail->addCustomHeader('X-Auto-Response-Suppress', 'All');
        $mail->addCustomHeader('List-Unsubscribe', '<mailto:unsubscribe@' . parse_url($config['smtp_host'], PHP_URL_HOST) . '>');
        $mail->addCustomHeader('X-Entity-ID', 'CryptoApp-' . time());

        // SPF and DKIM friendly headers
        $mail->addCustomHeader('Return-Path', $config['from_email']);
        $mail->addCustomHeader('Sender', $config['from_email']);

        // PHPMailer handles MIME headers automatically, so we don't add them manually

        // Authentication headers
        if ($email_type === 'password_reset') {
            $mail->addCustomHeader('X-Priority', '2'); // High priority for security emails
            $mail->addCustomHeader('Importance', 'High');
            $mail->addCustomHeader('X-MSMail-Priority', 'High');
        }

        // Send the email
        $result = $mail->send();

        // Log success
        logEmailAttempt($to_email, $subject, $email_type, 'sent');
        error_log("Email sent successfully to: $to_email - Subject: $subject");

        return true;
    } catch (Exception $e) {
        // Log failure
        logEmailAttempt($to_email, $subject, $email_type, 'failed');
        error_log("Failed to send email to: $to_email - Error: {$mail->ErrorInfo}");

        return false;
    }
}

function logEmailAttempt($recipient_email, $subject, $email_type, $status)
{
    try {
        include 'dbcon.php';
        $stmt = $conn->prepare('INSERT INTO email_logs (recipient_email, subject, email_type, status) VALUES (?, ?, ?, ?)');
        $stmt->bind_param('ssss', $recipient_email, $subject, $email_type, $status);
        $stmt->execute();
        $stmt->close();
        $conn->close();
    } catch (Exception $e) {
        error_log("Failed to log email attempt: " . $e->getMessage());
    }
}

function createPasswordResetLinkEmail($user_name, $reset_url)
{
    return '
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Password Reset Link - CryptoApp</title>
        <style>
            body {
                font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
                line-height: 1.6;
                color: #1e293b !important;
                margin: 0;
                padding: 0;
                background-color: #f8fafc;
            }
            /* Dark mode compatibility */
            @media (prefers-color-scheme: dark) {
                body {
                    background-color: #f8fafc !important;
                }
                .container {
                    background: white !important;
                }
                /* iOS Mail specific fixes */
                .instructions li {
                    color: #000000 !important;
                    -webkit-text-fill-color: #000000 !important;
                }
                .content p {
                    color: #000000 !important;
                    -webkit-text-fill-color: #000000 !important;
                }
                .content h2 {
                    color: #000000 !important;
                    -webkit-text-fill-color: #000000 !important;
                }
            }
            .container {
                max-width: 600px;
                margin: 30px auto;
                background: white;
                border-radius: 16px;
                overflow: hidden;
                box-shadow: 0 20px 50px rgba(0,0,0,0.15);
                border: 1px solid #e2e8f0;
            }
            .header {
                background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
                color: white;
                padding: 40px 30px;
                text-align: center;
                position: relative;
            }

            .header h1 {
                margin: 0;
                font-size: 32px;
                font-weight: 700;
                position: relative;
                z-index: 1;
            }
            .header p {
                margin: 8px 0 0 0;
                opacity: 0.95;
                font-size: 16px;
                position: relative;
                z-index: 1;
            }
            .content {
                padding: 50px 40px;
                background: linear-gradient(180deg, #ffffff 0%, #f8fafc 100%);
            }
            .content h2 {
                color: #000000 !important;
                margin-top: 0;
                margin-bottom: 20px;
                font-size: 26px;
                font-weight: 700;
            }
            .content p {
                color: #000000 !important;
                font-size: 16px;
                line-height: 1.7;
                margin: 16px 0;
                font-weight: 500;
            }
            .reset-button {
                display: inline-block;
                background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
                color: white !important;
                padding: 20px 40px;
                text-decoration: none;
                border-radius: 12px;
                margin: 30px 0;
                font-weight: 700;
                font-size: 18px;
                transition: all 0.3s ease;
                box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
                border: none;
                cursor: pointer;
                text-align: center;
                min-width: 200px;
            }
            .reset-button:hover {
                transform: translateY(-3px);
                box-shadow: 0 8px 25px rgba(59, 130, 246, 0.5);
                background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
            }
            .link-box {
                background: #f1f5f9;
                border: 2px dashed #3b82f6;
                border-radius: 8px;
                padding: 15px;
                margin: 15px 0;
                word-break: break-all;
                font-family: monospace;
                font-size: 14px;
                color: #1e40af !important;
                text-align: center;
            }
            .instructions {
                background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
                border: 2px solid #0ea5e9;
                border-radius: 12px;
                padding: 25px;
                margin: 30px 0;
                box-shadow: 0 4px 15px rgba(14, 165, 233, 0.1);
            }
            .instructions h3 {
                color: #1e40af !important;
                margin-top: 0;
                margin-bottom: 15px;
                font-size: 18px;
                font-weight: 700;
            }
            .instructions ul {
                margin: 15px 0;
                padding-left: 25px;
            }
            .instructions li {
                margin: 12px 0;
                color: #000000 !important;
                font-size: 15px;
                line-height: 1.6;
                font-weight: 500;
            }
            .warning {
                background: linear-gradient(135deg, #fef2f2 0%, #fecaca 20%);
                border: 2px solid #f87171;
                border-radius: 12px;
                padding: 25px;
                margin: 30px 0;
                box-shadow: 0 4px 15px rgba(248, 113, 113, 0.1);
            }
            .warning h3 {
                color: #dc2626 !important;
                margin-top: 0;
                margin-bottom: 15px;
                font-size: 18px;
                font-weight: 600;
            }
            .warning p {
                color: #7f1d1d !important;
                margin: 0;
                font-size: 15px;
                line-height: 1.6;
                font-weight: 500;
            }
            .footer {
                background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
                text-align: center;
                padding: 40px 30px;
                color: #1f2937 !important;
                font-size: 14px;
                border-top: 2px solid #e2e8f0;
            }
            .footer p {
                margin: 8px 0;
                line-height: 1.5;
                color: #1f2937 !important;
                font-weight: 500;
            }
            .footer strong {
                color: #000000 !important;
                font-weight: 700;
            }
            .link-box {
                background: #f1f5f9;
                border: 2px dashed #3b82f6;
                border-radius: 12px;
                padding: 20px;
                margin: 20px 0;
                word-break: break-all;
                font-family: monospace;
                font-size: 14px;
                color: #1e40af;
                text-align: center;
                box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);
            }
            @media (max-width: 600px) {
                .container {
                    margin: 15px;
                    border-radius: 12px;
                }
                .header {
                    padding: 30px 20px;
                }
                .header h1 {
                    font-size: 26px;
                }
                .content {
                    padding: 35px 25px;
                }
                .content h2 {
                    font-size: 22px;
                }
                .reset-button {
                    padding: 18px 30px;
                    font-size: 16px;
                    min-width: 180px;
                }
                .instructions, .warning {
                    padding: 20px;
                    margin: 25px 0;
                }
                .link-box {
                    padding: 15px;
                    font-size: 12px;
                }
                .footer {
                    padding: 30px 20px;
                }
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🔐 Password Reset</h1>
                <p>CryptoApp Security Team</p>
            </div>
            <div class="content">
                <h2 style="color: #000000 !important; -webkit-text-fill-color: #000000 !important; font-weight: 700;">Hello ' . htmlspecialchars($user_name) . ',</h2>
                <p style="color: #000000 !important; font-weight: 500; font-size: 16px;">We received a request to reset your password for your CryptoApp account. Click the button below to create a new password:</p>

                <div style="text-align: center; margin: 30px 0;">
                    <a href="' . htmlspecialchars($reset_url) . '" class="reset-button">🔑 Reset My Password</a>
                </div>

                <div class="instructions">
                    <h3 style="color: #1e40af !important; -webkit-text-fill-color: #1e40af !important; font-weight: 700;">📋 Security Instructions</h3>
                    <ul>
                        <li style="color: #000000 !important; -webkit-text-fill-color: #000000 !important; font-weight: 500;"><strong>Click the button above</strong> to securely reset your password</li>
                        <li style="color: #000000 !important; -webkit-text-fill-color: #000000 !important; font-weight: 500;"><strong>This link expires in 1 hour</strong> for your security</li>
                        <li style="color: #000000 !important; -webkit-text-fill-color: #000000 !important; font-weight: 500;"><strong>Choose a strong password</strong> with letters, numbers, and symbols</li>
                        <li style="color: #000000 !important; -webkit-text-fill-color: #000000 !important; font-weight: 500;"><strong>Never share</strong> your password or reset links with anyone</li>
                    </ul>
                </div>

                <p style="color: #000000 !important; font-weight: 600;"><strong>If the button doesn\'t work, copy and paste this link into your browser:</strong></p>
                <div class="link-box">' . htmlspecialchars($reset_url) . '</div>

                <div class="warning">
                    <h3 style="color: #dc2626 !important; -webkit-text-fill-color: #dc2626 !important; font-weight: 700;">⚠️ Important Security Notice</h3>
                    <p style="color: #7f1d1d !important; -webkit-text-fill-color: #7f1d1d !important; font-weight: 500;">If you did not request this password reset, please ignore this email. Your account remains secure and no changes will be made.</p>
                </div>

                <p style="margin-top: 30px; color: #1f2937 !important; font-size: 14px; font-weight: 500;">
                    This reset link will expire in 1 hour for your security.
                </p>
            </div>
            <div class="footer">
                <p><strong>CryptoApp Security System</strong></p>
                <p>This is an automated security message</p>
                <p>© 2024 CryptoApp. All rights reserved.</p>
            </div>
        </div>
    </body>
    </html>';
}

function createPlainTextVersion($html_message, $email_type = 'notification')
{
    // Extract key information from HTML for plain text version
    if ($email_type === 'password_reset') {
        // Extract reset URL from HTML
        preg_match('/href="([^"]*reset_password\.php[^"]*)"/', $html_message, $matches);
        $reset_url = isset($matches[1]) ? $matches[1] : '';

        return "
CRYPTOAPP - PASSWORD RESET REQUEST

Hello,

We received a request to reset your password for your CryptoApp account.

To reset your password, please click the link below or copy and paste it into your browser:
$reset_url

SECURITY INSTRUCTIONS:
- This link expires in 1 hour for your security
- Choose a strong password with letters, numbers, and symbols
- Never share your password or reset links with anyone

If you did not request this password reset, please ignore this email. Your account remains secure.

---
CryptoApp Security Team
This is an automated security message
";
    }

    // Default plain text for other email types
    $plain_text = strip_tags($html_message);
    $plain_text = html_entity_decode($plain_text, ENT_QUOTES, 'UTF-8');
    $plain_text = preg_replace('/\s+/', ' ', $plain_text);
    $plain_text = trim($plain_text);

    return $plain_text;
}

// Old temporary password function removed - use createPasswordResetLinkEmail instead
