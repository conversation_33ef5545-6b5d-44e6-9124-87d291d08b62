<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Team - Crypto Wallet</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <style>
        body {
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
            min-height: 100vh;
        }

        .stats-card {
            background: rgba(30, 41, 59, 0.4);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(59, 130, 246, 0.2);
            border-radius: 0.75rem;
            padding: 1rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }

        .level-card {
            background: rgba(15, 23, 42, 0.8);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(59, 130, 246, 0.3);
            border-radius: 0.75rem;
            padding: 1rem;
            transition: all 0.3s ease;
        }

        .level-card:hover {
            border-color: rgba(6, 182, 212, 0.6);
            background: rgba(15, 23, 42, 0.9);
            transform: translateY(-2px);
        }

        .member-card {
            background: rgba(30, 41, 59, 0.4);
            border: 1px solid rgba(59, 130, 246, 0.15);
            border-radius: 0.75rem;
            padding: 1rem;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .member-card:hover {
            border-color: rgba(6, 182, 212, 0.3);
            background: rgba(30, 41, 59, 0.6);
            transform: translateY(-1px);
        }

        .copy-btn {
            background: linear-gradient(135deg, #3b82f6, #06b6d4);
            border: none;
            border-radius: 0.5rem;
            padding: 0.5rem 1rem;
            color: white;
            font-weight: 600;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .copy-btn:hover {
            background: linear-gradient(135deg, #2563eb, #0891b2);
            transform: translateY(-1px);
        }

        /* Bottom Navigation Styles */
        .accent {
            color: #06b6d4 !important;
        }

        .hover\:accent:hover {
            color: #06b6d4 !important;
        }

        .stats-card:hover {
            transform: translateY(-5px);
            border-color: rgba(59, 130, 246, 0.4);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.4);
        }

        /* Team Member Card */
        .team-member-card {
            background: linear-gradient(135deg, rgba(30, 41, 59, 0.9) 0%, rgba(15, 23, 42, 0.9) 100%);
            backdrop-filter: blur(15px);
            border: 1px solid rgba(59, 130, 246, 0.2);
            border-radius: 1rem;
            padding: 1.5rem;
            transition: all 0.4s ease;
            position: relative;
            overflow: hidden;
        }

        .team-member-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #3b82f6, #06b6d4);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .team-member-card:hover::before {
            opacity: 1;
        }

        .team-member-card:hover {
            transform: translateY(-8px);
            border-color: rgba(59, 130, 246, 0.4);
            box-shadow: 0 20px 50px rgba(0, 0, 0, 0.5);
        }

        /* Level Badge */
        .level-badge {
            background: linear-gradient(135deg, #3b82f6, #06b6d4);
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        /* Shimmer Animation */
        @keyframes shimmer {
            0% {
                transform: translateX(-100%);
            }

            100% {
                transform: translateX(100%);
            }
        }

        .animate-shimmer {
            animation: shimmer 2s infinite;
        }
    </style>
</head>

<body class="text-white">
    <div class="min-h-screen flex flex-col">

        <!-- Sleek Top Navigation Bar -->
        <!-- Header -->
        <div class="sticky top-0 z-50 bg-slate-800/95 backdrop-blur-sm border-b border-slate-700">
            <div class="flex items-center justify-between px-6 py-4">
                <div class="flex items-center gap-4">
                    <button onclick="goBack()"
                        class="w-10 h-10 rounded-full bg-slate-700 hover:bg-slate-600 flex items-center justify-center transition-colors">
                        <i class="fas fa-arrow-left text-slate-300"></i>
                    </button>
                    <div>
                        <h1 class="text-xl font-bold text-white">My Team</h1>
                        <p class="text-sm text-slate-400">3-Level Referral System</p>
                    </div>
                </div>
                <div class="flex items-center gap-2">
                    <div class="text-right">
                        <p class="text-xs text-slate-400">Total Earnings</p>
                        <p class="text-lg font-bold text-cyan-400">$1,247.50</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-grow overflow-y-auto px-4 sm:px-6 py-4">

            <!-- Referral Link -->
            <div class="stats-card mb-4">
                <div class="flex items-center gap-2 mb-4">
                    <div
                        class="w-10 h-10 rounded-lg bg-gradient-to-r from-blue-600 to-cyan-600 flex items-center justify-center">
                        <i class="fas fa-link text-white"></i>
                    </div>
                    <div>
                        <h2 class="text-lg font-bold text-white">Referral Link</h2>
                        <p class="text-xs text-slate-400">Share and earn commissions</p>
                    </div>
                </div>
                <div
                    class="bg-gradient-to-r from-slate-800/50 to-slate-700/50 rounded-lg p-3 border border-cyan-500/20">
                    <div class="flex items-center justify-between">
                        <div class="flex-1">
                            <p class="text-xs text-slate-400 mb-1">Your Referral Link:</p>
                            <p class="text-sm font-mono text-white break-all" id="referralLink">
                                https://cryptowallet.com/ref/CW123456</p>
                        </div>
                        <button onclick="copyReferralLink()" class="copy-btn ml-3">
                            <i class="fas fa-copy"></i>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Team Overview Stats -->
            <div class="grid grid-cols-2 gap-3 mb-4">
                <!-- Total Team Members -->
                <div class="stats-card text-center">
                    <div
                        class="w-10 h-10 mx-auto mb-3 rounded-lg bg-gradient-to-r from-blue-600 to-cyan-600 flex items-center justify-center">
                        <i class="fas fa-users text-white"></i>
                    </div>
                    <p class="text-xs text-slate-400 mb-1">Total Members</p>
                    <p class="text-xl font-bold text-white mb-1">24</p>
                    <div class="flex items-center justify-center gap-1">
                        <span class="text-xs text-cyan-400 font-semibold">+3</span>
                        <span class="text-xs text-slate-400">this month</span>
                    </div>
                </div>

                <!-- Team Earnings -->
                <div class="stats-card text-center">
                    <div
                        class="w-10 h-10 mx-auto mb-3 rounded-lg bg-gradient-to-r from-cyan-600 to-blue-600 flex items-center justify-center">
                        <i class="fas fa-dollar-sign text-white"></i>
                    </div>
                    <p class="text-xs text-slate-400 mb-1">Team Earnings</p>
                    <p class="text-xl font-bold text-white mb-1">$1,247</p>
                    <div class="flex items-center justify-center gap-1">
                        <span class="text-xs text-cyan-400 font-semibold">+$124</span>
                        <span class="text-xs text-slate-400">this month</span>
                    </div>
                </div>
            </div>

            <!-- Direct Referrals (Level 1) -->
            <div class="stats-card mb-4">
                <div class="flex items-center justify-between mb-4">
                    <h2 class="text-lg font-bold text-white">My Team</h2>
                    <span class="text-sm text-slate-400">5 members</span>
                </div>

                <div class="space-y-3">
                    <!-- Member 1 -->
                    <div class="member-card" onclick="viewUserDownline('john-doe', 'level-2')">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center gap-3">
                                <div
                                    class="w-10 h-10 rounded-full bg-gradient-to-r from-blue-600 to-cyan-600 flex items-center justify-center text-white font-bold text-sm">
                                    JD
                                </div>
                                <div>
                                    <p class="text-base font-semibold text-white">John Doe</p>
                                    <p class="text-sm text-slate-400">Standard • Active</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <p class="text-lg font-bold text-cyan-400">$485</p>
                                <p class="text-xs text-slate-500">4 referrals</p>
                            </div>
                        </div>
                    </div>

                    <!-- Member 2 -->
                    <div class="member-card" onclick="viewUserDownline('alice-smith', 'level-2')">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center gap-3">
                                <div
                                    class="w-10 h-10 rounded-full bg-gradient-to-r from-cyan-600 to-blue-600 flex items-center justify-center text-white font-bold text-sm">
                                    AS
                                </div>
                                <div>
                                    <p class="text-base font-semibold text-white">Alice Smith</p>
                                    <p class="text-sm text-slate-400">Premium • Active</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <p class="text-lg font-bold text-cyan-400">$158</p>
                                <p class="text-xs text-slate-500">3 referrals</p>
                            </div>
                        </div>
                    </div>

                    <!-- Member 3 -->
                    <div class="member-card" onclick="viewUserDownline('mike-brown', 'level-2')">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center gap-3">
                                <div
                                    class="w-10 h-10 rounded-full bg-gradient-to-r from-blue-600 to-cyan-600 flex items-center justify-center text-white font-bold text-sm">
                                    MB
                                </div>
                                <div>
                                    <p class="text-base font-semibold text-white">Mike Brown</p>
                                    <p class="text-sm text-slate-400">Large • Active</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <p class="text-lg font-bold text-cyan-400">$192</p>
                                <p class="text-xs text-slate-500">4 referrals</p>
                            </div>
                        </div>
                    </div>

                    <!-- Member 4 -->
                    <div class="member-card" onclick="viewUserDownline('sarah-johnson', 'level-2')">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center gap-3">
                                <div
                                    class="w-10 h-10 rounded-full bg-gradient-to-r from-cyan-600 to-blue-600 flex items-center justify-center text-white font-bold text-sm">
                                    SJ
                                </div>
                                <div>
                                    <p class="text-base font-semibold text-white">Sarah Johnson</p>
                                    <p class="text-sm text-slate-400">Standard • Active</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <p class="text-lg font-bold text-cyan-400">$65</p>
                                <p class="text-xs text-slate-500">2 referrals</p>
                            </div>
                        </div>
                    </div>

                    <!-- Member 5 -->
                    <div class="member-card" onclick="viewUserDownline('robert-davis', 'level-2')">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center gap-3">
                                <div
                                    class="w-10 h-10 rounded-full bg-gradient-to-r from-blue-600 to-cyan-600 flex items-center justify-center text-white font-bold text-sm">
                                    RD
                                </div>
                                <div>
                                    <p class="text-base font-semibold text-white">Robert Davis</p>
                                    <p class="text-sm text-slate-400">Premium • Active</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <p class="text-lg font-bold text-cyan-400">$225</p>
                                <p class="text-xs text-slate-500">3 referrals</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Team Members -->
            <div class="stats-card mb-4">
                <div class="flex items-center gap-2 mb-4">
                    <div
                        class="w-10 h-10 rounded-lg bg-gradient-to-r from-blue-600 to-cyan-600 flex items-center justify-center">
                        <i class="fas fa-user-plus text-white"></i>
                    </div>
                    <div>
                        <h2 class="text-lg font-bold text-white">Recent Joins</h2>
                        <p class="text-xs text-slate-400">New team members this month</p>
                    </div>
                </div>

                <div class="space-y-3">
                    <!-- Team Member 1 -->
                    <div class="member-card">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center gap-3">
                                <div
                                    class="w-10 h-10 rounded-full bg-gradient-to-r from-blue-600 to-cyan-600 flex items-center justify-center text-white font-bold text-sm">
                                    JD
                                </div>
                                <div>
                                    <p class="text-sm font-semibold text-white">John Doe</p>
                                    <p class="text-xs text-slate-400">Level 1 • 2 days ago</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <p class="text-sm font-bold text-cyan-400">$45</p>
                                <p class="text-xs text-slate-400">Earned</p>
                            </div>
                        </div>
                    </div>

                    <!-- Team Member 2 -->
                    <div class="member-card">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center gap-3">
                                <div
                                    class="w-10 h-10 rounded-full bg-gradient-to-r from-cyan-600 to-blue-600 flex items-center justify-center text-white font-bold text-sm">
                                    AS
                                </div>
                                <div>
                                    <p class="text-sm font-semibold text-white">Alice Smith</p>
                                    <p class="text-xs text-slate-400">Level 1 • 5 days ago</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <p class="text-sm font-bold text-cyan-400">$78</p>
                                <p class="text-xs text-slate-400">Earned</p>
                            </div>
                        </div>
                    </div>

                    <!-- Team Member 3 -->
                    <div class="member-card">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center gap-3">
                                <div
                                    class="w-10 h-10 rounded-full bg-gradient-to-r from-cyan-600 to-blue-600 flex items-center justify-center text-white font-bold text-sm">
                                    MB
                                </div>
                                <div>
                                    <p class="text-sm font-semibold text-white">Mike Brown</p>
                                    <p class="text-xs text-slate-400">Level 2 • 1 week ago</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <p class="text-sm font-bold text-cyan-400">$32</p>
                                <p class="text-xs text-slate-400">Earned</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sticky Bottom Navigation Bar -->
        <div class="sticky bottom-0 z-40 flex-shrink-0">
            <div class="bg-slate-800/95 backdrop-blur-sm mx-4 mb-4 rounded-2xl border border-slate-700">
                <div class="flex justify-around items-center h-16">
                    <a href="1.html" class="flex flex-col items-center gap-1 text-slate-400 hover:accent">
                        <i class="fas fa-home"></i>
                        <span class="text-xs">Home</span>
                    </a>
                    <a href="wallet.html" class="flex flex-col items-center gap-1 text-slate-400 hover:accent">
                        <i class="fas fa-wallet"></i>
                        <span class="text-xs">Wallet</span>
                    </a>
                    <a href="team.html" class="flex flex-col items-center gap-1 accent">
                        <i class="fas fa-users"></i>
                        <span class="text-xs">Team</span>
                    </a>
                    <a href="profile.html" class="flex flex-col items-center gap-1 text-slate-400 hover:accent">
                        <i class="fas fa-user"></i>
                        <span class="text-xs">Profile</span>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script>
        function goBack() {
            window.history.back();
        }

        function copyReferralLink() {
            const link = document.getElementById('referralLink').textContent;
            navigator.clipboard.writeText(link).then(() => {
                Swal.fire({
                    icon: 'success',
                    title: 'Link Copied!',
                    text: 'Referral link has been copied to clipboard',
                    background: '#1e293b',
                    color: '#ffffff',
                    confirmButtonColor: '#06b6d4',
                    iconColor: '#06b6d4',
                    timer: 2000,
                    showConfirmButton: false
                });
            });
        }

        function viewUserDownline(userId, targetLevel) {
            // Navigate to user downline page showing their referrals
            window.location.href = `user-downline.html?user=${userId}&target=${targetLevel}&from=team`;
        }
    </script>

</body>

</html>