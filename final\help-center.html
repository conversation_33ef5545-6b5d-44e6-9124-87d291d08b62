<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Help Center - Crypto Wallet</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <style>
        body {
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
            min-height: 100vh;
        }

        .stats-card {
            background: rgba(30, 41, 59, 0.4);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(59, 130, 246, 0.2);
            border-radius: 0.75rem;
            padding: 1rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }

        .help-card {
            background: rgba(15, 23, 42, 0.6);
            backdrop-filter: blur(15px);
            border: 1px solid rgba(59, 130, 246, 0.2);
            border-radius: 1rem;
            padding: 1.25rem;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .help-card:hover {
            border-color: rgba(6, 182, 212, 0.5);
            background: rgba(15, 23, 42, 0.8);
            transform: translateY(-3px) scale(1.02);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
        }

        .ticket-card {
            background: rgba(30, 41, 59, 0.3);
            border: 1px solid rgba(71, 85, 105, 0.3);
            border-radius: 0.75rem;
            padding: 1rem;
            transition: all 0.2s ease;
            cursor: pointer;
        }

        .ticket-card:hover {
            background: rgba(30, 41, 59, 0.5);
            border-color: rgba(6, 182, 212, 0.4);
        }

        .input-field {
            background: rgba(15, 23, 42, 0.8);
            border: 1px solid rgba(59, 130, 246, 0.3);
            border-radius: 0.5rem;
            padding: 0.75rem;
            color: white;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .input-field:focus {
            outline: none;
            border-color: rgba(6, 182, 212, 0.6);
            box-shadow: 0 0 0 3px rgba(6, 182, 212, 0.15);
            background: rgba(15, 23, 42, 0.9);
        }

        .input-field::placeholder {
            color: rgba(148, 163, 184, 0.5);
        }

        .btn-primary {
            background: linear-gradient(135deg, #3b82f6, #06b6d4);
            border: none;
            border-radius: 0.5rem;
            padding: 0.75rem 1.5rem;
            color: white;
            font-weight: 600;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #2563eb, #0891b2);
            transform: translateY(-1px);
        }

        .status-open {
            background: rgba(34, 197, 94, 0.2);
            color: #22c55e;
            border: 1px solid rgba(34, 197, 94, 0.3);
        }

        .status-pending {
            background: rgba(251, 191, 36, 0.2);
            color: #fbbf24;
            border: 1px solid rgba(251, 191, 36, 0.3);
        }

        .status-closed {
            background: rgba(107, 114, 128, 0.2);
            color: #9ca3af;
            border: 1px solid rgba(107, 114, 128, 0.3);
        }

        /* Bottom Navigation Styles */
        .accent {
            color: #06b6d4 !important;
        }

        .hover\:accent:hover {
            color: #06b6d4 !important;
        }
    </style>
</head>

<body class="text-white">
    <div class="min-h-screen flex flex-col">
        <!-- Header -->
        <div class="sticky top-0 z-50 bg-slate-800/95 backdrop-blur-sm border-b border-slate-700">
            <div class="flex items-center justify-between px-6 py-4">
                <div class="flex items-center gap-4">
                    <button onclick="goBack()"
                        class="w-10 h-10 rounded-full bg-slate-700 hover:bg-slate-600 flex items-center justify-center transition-colors">
                        <i class="fas fa-arrow-left text-slate-300"></i>
                    </button>
                    <div>
                        <h1 class="text-xl font-bold text-white">Help Center</h1>
                        <p class="text-sm text-slate-400">Get help and manage support tickets</p>
                    </div>
                </div>
                <button onclick="createTicket()"
                    class="w-10 h-10 rounded-full bg-blue-600 hover:bg-blue-700 flex items-center justify-center transition-colors">
                    <i class="fas fa-plus text-white"></i>
                </button>
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-grow overflow-y-auto px-4 sm:px-6 py-4">
            <!-- Quick Help -->
            <div class="stats-card mb-4">
                <div class="mb-4">
                    <h2 class="text-lg font-semibold text-white">Quick Help</h2>
                    <p class="text-sm text-slate-500">Common questions and guides</p>
                </div>

                <div class="grid grid-cols-1 sm:grid-cols-2 gap-3">
                    <div class="help-card" onclick="showFAQ('wallet')">
                        <div class="flex items-center gap-3">
                            <i class="fas fa-wallet text-blue-400"></i>
                            <div>
                                <p class="text-sm font-semibold text-white">Wallet & Deposits</p>
                                <p class="text-xs text-slate-400">How to add funds and manage wallet</p>
                            </div>
                        </div>
                    </div>

                    <div class="help-card" onclick="showFAQ('withdrawal')">
                        <div class="flex items-center gap-3">
                            <i class="fas fa-money-bill-wave text-cyan-400"></i>
                            <div>
                                <p class="text-sm font-semibold text-white">Withdrawals</p>
                                <p class="text-xs text-slate-400">How to withdraw your earnings</p>
                            </div>
                        </div>
                    </div>

                    <div class="help-card" onclick="showFAQ('referral')">
                        <div class="flex items-center gap-3">
                            <i class="fas fa-users text-blue-400"></i>
                            <div>
                                <p class="text-sm font-semibold text-white">Referral System</p>
                                <p class="text-xs text-slate-400">How to earn from referrals</p>
                            </div>
                        </div>
                    </div>

                    <div class="help-card" onclick="showFAQ('security')">
                        <div class="flex items-center gap-3">
                            <i class="fas fa-shield-alt text-cyan-400"></i>
                            <div>
                                <p class="text-sm font-semibold text-white">Security</p>
                                <p class="text-xs text-slate-400">Account security and safety</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- My Tickets -->
            <div class="stats-card mb-4">
                <div class="flex items-center justify-between mb-4">
                    <div>
                        <h2 class="text-lg font-semibold text-white">My Support Tickets</h2>
                        <p class="text-sm text-slate-500">Track your support requests</p>
                    </div>
                    <button onclick="createTicket()"
                        class="bg-blue-600 hover:bg-blue-700 text-white text-sm py-2 px-4 rounded-lg">
                        <i class="fas fa-plus mr-1"></i>
                        New Ticket
                    </button>
                </div>

                <div class="space-y-3" id="ticketsList">
                    <!-- Ticket 1 -->
                    <div class="ticket-card" onclick="viewTicket('TK001')">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center gap-3">
                                <div class="w-8 h-8 rounded-full bg-blue-600/20 flex items-center justify-center">
                                    <i class="fas fa-wallet text-blue-400 text-sm"></i>
                                </div>
                                <div>
                                    <p class="text-sm font-semibold text-white">Withdrawal Issue</p>
                                    <p class="text-xs text-slate-400">Ticket #TK001 • 2 hours ago</p>
                                </div>
                            </div>
                            <div class="flex items-center gap-2">
                                <span class="status-open text-xs px-2 py-1 rounded-full">Open</span>
                                <i class="fas fa-chevron-right text-slate-400 text-sm"></i>
                            </div>
                        </div>
                    </div>

                    <!-- Ticket 2 -->
                    <div class="ticket-card" onclick="viewTicket('TK002')">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center gap-3">
                                <div class="w-8 h-8 rounded-full bg-blue-600/20 flex items-center justify-center">
                                    <i class="fas fa-users text-blue-400 text-sm"></i>
                                </div>
                                <div>
                                    <p class="text-sm font-semibold text-white">Referral Commission</p>
                                    <p class="text-xs text-slate-400">Ticket #TK002 • 1 day ago</p>
                                </div>
                            </div>
                            <div class="flex items-center gap-2">
                                <span class="status-pending text-xs px-2 py-1 rounded-full">Pending</span>
                                <i class="fas fa-chevron-right text-slate-400 text-sm"></i>
                            </div>
                        </div>
                    </div>

                    <!-- Ticket 3 -->
                    <div class="ticket-card" onclick="viewTicket('TK003')">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center gap-3">
                                <div class="w-8 h-8 rounded-full bg-cyan-600/20 flex items-center justify-center">
                                    <i class="fas fa-check text-cyan-400 text-sm"></i>
                                </div>
                                <div>
                                    <p class="text-sm font-semibold text-white">Account Verification</p>
                                    <p class="text-xs text-slate-400">Ticket #TK003 • 3 days ago</p>
                                </div>
                            </div>
                            <div class="flex items-center gap-2">
                                <span class="status-closed text-xs px-2 py-1 rounded-full">Closed</span>
                                <i class="fas fa-chevron-right text-slate-400 text-sm"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Contact Options -->
            <div class="stats-card mb-4">
                <div class="mb-4">
                    <h2 class="text-lg font-semibold text-white">Contact Support</h2>
                    <p class="text-sm text-slate-500">Get in touch with our team</p>
                </div>

                <div class="space-y-3">
                    <div class="help-card" onclick="window.location.href='contact-support.html'">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center gap-3">
                                <i class="fas fa-envelope text-blue-400"></i>
                                <div>
                                    <p class="text-sm font-semibold text-white">Email Support</p>
                                    <p class="text-xs text-slate-400">Get help via email</p>
                                </div>
                            </div>
                            <i class="fas fa-chevron-right text-slate-400 text-sm"></i>
                        </div>
                    </div>

                    <div class="help-card" onclick="startLiveChat()">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center gap-3">
                                <i class="fas fa-comments text-green-400"></i>
                                <div>
                                    <p class="text-sm font-semibold text-white">Live Chat</p>
                                    <p class="text-xs text-slate-400">Chat with support agent</p>
                                </div>
                            </div>
                            <span class="text-xs text-green-400 bg-green-500/20 px-2 py-1 rounded-full">Online</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sticky Bottom Navigation Bar -->
        <div class="sticky bottom-0 z-40 flex-shrink-0">
            <div class="bg-slate-800/95 backdrop-blur-sm mx-4 mb-4 rounded-2xl border border-slate-700">
                <div class="flex justify-around items-center h-16">
                    <a href="1.html" class="flex flex-col items-center gap-1 text-slate-400 hover:accent">
                        <i class="fas fa-home"></i>
                        <span class="text-xs">Home</span>
                    </a>
                    <a href="wallet.html" class="flex flex-col items-center gap-1 text-slate-400 hover:accent">
                        <i class="fas fa-wallet"></i>
                        <span class="text-xs">Wallet</span>
                    </a>
                    <a href="team.html" class="flex flex-col items-center gap-1 text-slate-400 hover:accent">
                        <i class="fas fa-users"></i>
                        <span class="text-xs">Team</span>
                    </a>
                    <a href="profile.html" class="flex flex-col items-center gap-1 accent">
                        <i class="fas fa-user"></i>
                        <span class="text-xs">Profile</span>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script>
        function goBack() {
            window.history.back();
        }

        function showFAQ(category) {
            let title, content;

            switch (category) {
                case 'wallet':
                    title = 'Wallet & Deposits';
                    content = `
                        <div class="text-left space-y-3">
                            <div class="border-b border-slate-600 pb-2">
                                <p class="font-semibold text-cyan-400">How to add funds?</p>
                                <p class="text-sm text-slate-300">Go to Wallet → Add Funds → Choose amount → Pay with crypto</p>
                            </div>
                            <div class="border-b border-slate-600 pb-2">
                                <p class="font-semibold text-cyan-400">Processing time?</p>
                                <p class="text-sm text-slate-300">Crypto deposits are processed within 24 hours</p>
                            </div>
                            <div>
                                <p class="font-semibold text-cyan-400">Minimum deposit?</p>
                                <p class="text-sm text-slate-300">Minimum deposit is $10</p>
                            </div>
                        </div>
                    `;
                    break;
                case 'withdrawal':
                    title = 'Withdrawals';
                    content = `
                        <div class="text-left space-y-3">
                            <div class="border-b border-slate-600 pb-2">
                                <p class="font-semibold text-cyan-400">How to withdraw?</p>
                                <p class="text-sm text-slate-300">Go to Wallet → Withdraw → Enter amount → Confirm</p>
                            </div>
                            <div class="border-b border-slate-600 pb-2">
                                <p class="font-semibold text-cyan-400">Withdrawal fees?</p>
                                <p class="text-sm text-slate-300">10% GAS fee is deducted from withdrawal amount</p>
                            </div>
                            <div>
                                <p class="font-semibold text-cyan-400">Processing time?</p>
                                <p class="text-sm text-slate-300">Withdrawals are processed within 24 hours</p>
                            </div>
                        </div>
                    `;
                    break;
                case 'referral':
                    title = 'Referral System';
                    content = `
                        <div class="text-left space-y-3">
                            <div class="border-b border-slate-600 pb-2">
                                <p class="font-semibold text-cyan-400">Commission rates?</p>
                                <p class="text-sm text-slate-300">Level 1: 10%, Level 2: 5%, Level 3: 2%</p>
                            </div>
                            <div class="border-b border-slate-600 pb-2">
                                <p class="font-semibold text-cyan-400">How to refer?</p>
                                <p class="text-sm text-slate-300">Share your referral link from Team page</p>
                            </div>
                            <div>
                                <p class="font-semibold text-cyan-400">When do I earn?</p>
                                <p class="text-sm text-slate-300">You earn when your referrals make deposits</p>
                            </div>
                        </div>
                    `;
                    break;
                case 'security':
                    title = 'Security';
                    content = `
                        <div class="text-left space-y-3">
                            <div class="border-b border-slate-600 pb-2">
                                <p class="font-semibold text-cyan-400">Is my account secure?</p>
                                <p class="text-sm text-slate-300">Yes, we use advanced encryption and 2FA</p>
                            </div>
                            <div class="border-b border-slate-600 pb-2">
                                <p class="font-semibold text-cyan-400">Can I change wallet address?</p>
                                <p class="text-sm text-slate-300">No, wallet addresses are locked for security</p>
                            </div>
                            <div>
                                <p class="font-semibold text-cyan-400">Forgot password?</p>
                                <p class="text-sm text-slate-300">Use password reset or contact support</p>
                            </div>
                        </div>
                    `;
                    break;
            }

            Swal.fire({
                title: title,
                html: content,
                background: '#1e293b',
                color: '#ffffff',
                confirmButtonColor: '#06b6d4',
                confirmButtonText: 'Got it',
                iconColor: '#06b6d4',
                width: '90%',
                maxWidth: '500px',
                customClass: {
                    popup: 'rounded-2xl border border-slate-600',
                    title: 'text-lg font-semibold mb-4',
                    confirmButton: 'bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg'
                },
                buttonsStyling: false
            });
        }

        function createTicket() {
            Swal.fire({
                title: 'Create Support Ticket',
                html: `
                    <div class="space-y-4">
                        <div class="text-left">
                            <label class="block text-sm font-medium text-slate-300 mb-2">Subject</label>
                            <input type="text" id="ticketSubject" class="w-full p-3 bg-slate-700 border border-slate-600 rounded text-white" placeholder="Brief description of your issue">
                        </div>
                        <div class="text-left">
                            <label class="block text-sm font-medium text-slate-300 mb-2">Category</label>
                            <select id="ticketCategory" class="w-full p-3 bg-slate-700 border border-slate-600 rounded text-white">
                                <option value="wallet">Wallet & Deposits</option>
                                <option value="withdrawal">Withdrawals</option>
                                <option value="referral">Referral System</option>
                                <option value="security">Security</option>
                                <option value="other">Other</option>
                            </select>
                        </div>
                        <div class="text-left">
                            <label class="block text-sm font-medium text-slate-300 mb-2">Description</label>
                            <textarea id="ticketDescription" rows="4" class="w-full p-3 bg-slate-700 border border-slate-600 rounded text-white" placeholder="Please describe your issue in detail..."></textarea>
                        </div>
                    </div>
                `,
                background: '#1e293b',
                color: '#ffffff',
                confirmButtonColor: '#06b6d4',
                cancelButtonColor: '#64748b',
                confirmButtonText: 'Create Ticket',
                cancelButtonText: 'Cancel',
                showCancelButton: true,
                customClass: {
                    popup: 'rounded-2xl border border-slate-600',
                    title: 'text-lg font-semibold mb-4',
                    confirmButton: 'bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg mr-3',
                    cancelButton: 'bg-slate-600 hover:bg-slate-500 text-white font-medium py-2 px-4 rounded-lg'
                },
                buttonsStyling: false,
                preConfirm: () => {
                    const subject = document.getElementById('ticketSubject').value;
                    const category = document.getElementById('ticketCategory').value;
                    const description = document.getElementById('ticketDescription').value;

                    if (!subject || !description) {
                        Swal.showValidationMessage('Please fill in all required fields');
                        return false;
                    }

                    return { subject, category, description };
                }
            }).then((result) => {
                if (result.isConfirmed) {
                    Swal.fire({
                        icon: 'success',
                        title: 'Ticket Created!',
                        text: 'Your support ticket has been created. We will respond within 24 hours.',
                        background: '#1e293b',
                        color: '#ffffff',
                        confirmButtonColor: '#06b6d4',
                        iconColor: '#10b981',
                        customClass: {
                            popup: 'rounded-2xl border border-slate-600',
                            confirmButton: 'bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg'
                        },
                        buttonsStyling: false
                    });
                }
            });
        }

        function viewTicket(ticketId) {
            let ticketData = {
                'TK001': {
                    subject: 'Withdrawal Issue',
                    status: 'Open',
                    created: '2 hours ago',
                    description: 'My withdrawal has been pending for 2 days. Please help.',
                    response: 'We are investigating your withdrawal. Please allow 24-48 hours for processing.'
                },
                'TK002': {
                    subject: 'Referral Commission',
                    status: 'Pending',
                    created: '1 day ago',
                    description: 'I referred someone but did not receive commission.',
                    response: 'We are reviewing your referral activity. Update coming soon.'
                },
                'TK003': {
                    subject: 'Account Verification',
                    status: 'Closed',
                    created: '3 days ago',
                    description: 'Need help with KYC verification process.',
                    response: 'Your account has been successfully verified. Thank you!'
                }
            };

            const ticket = ticketData[ticketId];

            Swal.fire({
                title: `Ticket #${ticketId}`,
                html: `
                    <div class="text-left space-y-4">
                        <div class="bg-slate-800 p-3 rounded">
                            <p class="text-sm text-slate-400">Subject: <span class="text-white">${ticket.subject}</span></p>
                            <p class="text-sm text-slate-400">Status: <span class="text-cyan-400">${ticket.status}</span></p>
                            <p class="text-sm text-slate-400">Created: <span class="text-white">${ticket.created}</span></p>
                        </div>
                        <div>
                            <p class="text-sm font-semibold text-slate-300 mb-2">Your Message:</p>
                            <p class="text-sm text-slate-300 bg-slate-800 p-3 rounded">${ticket.description}</p>
                        </div>
                        <div>
                            <p class="text-sm font-semibold text-slate-300 mb-2">Support Response:</p>
                            <p class="text-sm text-slate-300 bg-blue-900/30 p-3 rounded border-l-4 border-blue-400">${ticket.response}</p>
                        </div>
                    </div>
                `,
                background: '#1e293b',
                color: '#ffffff',
                confirmButtonColor: '#06b6d4',
                confirmButtonText: 'Close',
                width: '90%',
                maxWidth: '600px',
                customClass: {
                    popup: 'rounded-2xl border border-slate-600',
                    title: 'text-lg font-semibold mb-4',
                    confirmButton: 'bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg'
                },
                buttonsStyling: false
            });
        }

        function startLiveChat() {
            Swal.fire({
                title: 'Live Chat',
                text: 'Live chat feature will be available soon. Please create a support ticket for now.',
                icon: 'info',
                background: '#1e293b',
                color: '#ffffff',
                confirmButtonColor: '#06b6d4',
                iconColor: '#06b6d4',
                customClass: {
                    popup: 'rounded-2xl border border-slate-600',
                    title: 'text-lg font-semibold mb-4',
                    confirmButton: 'bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg'
                },
                buttonsStyling: false
            });
        }
    </script>
</body>

</html>