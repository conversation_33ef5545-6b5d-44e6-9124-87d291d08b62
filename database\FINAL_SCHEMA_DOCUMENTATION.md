# NetVis Crypto Platform - Final Database Schema Documentation

## Overview
This document describes the complete database schema for the NetVis Crypto Platform, including all tables, relationships, and initial data required for the system to function properly.

## Database File
**Location**: `database/schema.sql`
**Purpose**: Complete database structure with all features implemented

## Tables Overview

### 1. Core Tables

#### `users` - User Management
- **Purpose**: Stores all user information including admins and regular users
- **Key Features**:
  - 4 decimal precision for wallet balances
  - Plan status tracking (active/expired)
  - Sponsor relationship tracking
  - Admin privilege management
- **Important Fields**:
  - `deposit_wallet_balance` - decimal(20,4)
  - `withdrawal_wallet_balance` - decimal(20,4)
  - `total_earnings_received` - decimal(20,4)
  - `plan_status` - enum('active','expired')

#### `packages` - Investment Packages
- **Purpose**: Defines available investment packages
- **Key Features**:
  - 4 decimal precision for amounts and percentages
  - Active/inactive status management
  - Audit trail with created_by/updated_by
- **Important Fields**:
  - `package_amount` - decimal(10,4)
  - `profit_percentage` - decimal(5,4)

### 2. Network & Commission Tables

#### `levels` - Network Level Management
- **Purpose**: Defines commission levels and qualification requirements
- **Key Features**:
  - Qualification system with minimum direct referrals
  - Percentage-based commission structure
- **Important Fields**:
  - `level_number` - int(2) UNSIGNED
  - `distribution_percentage` - decimal(5,2)
  - `min_direct_referrals` - int(3) UNSIGNED (NEW FEATURE)

#### `bonuses` - Bonus Management
- **Purpose**: Manages various bonus types and criteria
- **Key Features**:
  - Multiple bonus types (team, leadership, performance)
  - Flexible eligibility criteria
  - 4 decimal precision for amounts
- **Important Fields**:
  - `bonus_type` - enum('team_bonus','leadership_bonus','performance_bonus')
  - `eligibility_criteria` - text (JSON or description)

### 3. Transaction & History Tables

#### `wallet_history` - Transaction History
- **Purpose**: Complete audit trail of all wallet transactions
- **Key Features**:
  - 4 decimal precision for amounts
  - Multiple transaction types
  - Related user tracking for commissions
- **Important Fields**:
  - `amount` - decimal(20,4)
  - `type` - varchar(50) (daily_profit, network_commission, etc.)
  - `related_user_id` - int(11) (for commission tracking)

#### `package_history` - Package Purchase History
- **Purpose**: Tracks all package purchases and their lifecycle
- **Key Features**:
  - Real-time earnings tracking
  - Expiration management
  - Duration calculation
- **Important Fields**:
  - `total_earnings_received` - decimal(20,4)
  - `earnings_limit` - decimal(20,4) (2x package amount)
  - `expiry_reason` - varchar(100)

### 4. Staking System Tables

#### `staking_records` - Staking Management
- **Purpose**: Manages coin staking with freeze periods
- **Key Features**:
  - 4 decimal precision for USD amounts
  - Claim request system
  - Admin approval workflow
- **Important Fields**:
  - `amount_usd` - decimal(20,4)
  - `status` - enum('active','completed','cancelled','claim_pending')
  - `claim_requested_at` - timestamp

### 5. Request Management Tables

#### `deposit_requests` - Deposit Management
- **Purpose**: Handles deposit requests with admin approval
- **Key Features**:
  - Receipt upload system
  - Admin review workflow
  - 4 decimal precision

#### `withdrawal_requests` - Withdrawal Management
- **Purpose**: Manages withdrawal requests and processing
- **Key Features**:
  - Binance address validation
  - Transaction hash tracking
  - Admin approval system

### 6. Dynamic Configuration Tables

#### `system_settings` - Dynamic Settings
- **Purpose**: Stores configurable system parameters
- **Key Features**:
  - Type-safe value storage (string, number, boolean, json)
  - Change tracking with user attribution
  - Real-time configuration updates
- **Default Settings**:
  - `staking_coin_price` - 0.4500
  - `staking_min_coins` - 10
  - `staking_freeze_months` - 6
  - `staking_min_usd_amount` - 10

#### `coin_price_history` - Price Tracking
- **Purpose**: Historical tracking of coin price changes for graphing
- **Key Features**:
  - New row for each price change (not updates)
  - Automatic change calculation
  - Reason tracking for price changes
- **Important Fields**:
  - `price` - decimal(10,4)
  - `change_amount` - decimal(10,4)
  - `change_percentage` - decimal(8,4)

## Key Features Implemented

### 1. 4 Decimal Precision
- All monetary fields use decimal(20,4) or decimal(10,4)
- Supports micro-pricing (e.g., $0.4567)
- Consistent across all tables

### 2. Network Qualification System
- Level 1: No qualification needed (always receive 10%)
- Level 2: Need 3+ direct referrals (receive 5%)
- Level 3: Need 5+ direct referrals (receive 3%)

### 3. Dynamic Price Management
- Coin prices stored in database (not hardcoded)
- Historical tracking for graphing
- Admin interface for price updates

### 4. Package Lifecycle Management
- Real-time earnings tracking
- Automatic expiration at 2x limit
- Complete purchase history

### 5. Comprehensive Audit Trail
- All transactions logged in wallet_history
- User attribution for all changes
- Timestamp tracking throughout

## Initial Data Included

### Default Admin User
- **Username**: ADMIN001
- **Email**: <EMAIL>
- **Password**: admin123 (hashed)

### Default Packages
1. Starter Package - $100 (1% daily)
2. Basic Package - $500 (1.2% daily)
3. Premium Package - $1,000 (1.5% daily)
4. VIP Package - $5,000 (2% daily)
5. Elite Package - $10,000 (2.5% daily)

### Default Levels
1. Level 1 - 10% commission (no qualification)
2. Level 2 - 5% commission (3+ direct referrals)
3. Level 3 - 3% commission (5+ direct referrals)

### Default Bonuses
1. Team Building Bonus - $1,000 fund
2. Leadership Bonus - $2,500 fund
3. Performance Bonus - $5,000 fund

## Foreign Key Relationships

### User Dependencies
- `packages.created_by` → `users.id`
- `packages.updated_by` → `users.id`
- `deposit_requests.user_id` → `users.id`
- `deposit_requests.reviewed_by` → `users.id`
- `staking_records.user_id` → `users.id`
- `staking_records.created_by_admin` → `users.id`
- `package_history.user_id` → `users.id`
- `system_settings.updated_by` → `users.id`
- `coin_price_history.updated_by` → `users.id`

## Installation Instructions

1. **Create Database**: Create a MySQL database named `crypto`
2. **Import Schema**: Run the `database/schema.sql` file
3. **Verify Installation**: Check that all 11 tables are created
4. **Test Login**: Use admin credentials (ADMIN001 / admin123)
5. **Configure Settings**: Update system settings as needed

## Security Features

- Password hashing using PHP's password_hash()
- Foreign key constraints prevent orphaned records
- Soft deletes with deleted_at timestamps
- Admin privilege separation
- Transaction integrity with proper indexing

## Performance Optimizations

- Strategic indexing on frequently queried columns
- Proper data types for optimal storage
- Efficient foreign key relationships
- Timestamp indexing for historical queries

This schema provides a complete foundation for the NetVis Crypto Platform with all features implemented and ready for production use.
