<?php
// Test email functionality
include 'email_helper.php';
include 'dbcon.php';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $test_email = $_POST['test_email'] ?? '';

    if (!empty($test_email) && filter_var($test_email, FILTER_VALIDATE_EMAIL)) {
        // Generate a real reset token and store it in database
        $reset_token = bin2hex(random_bytes(32)); // 64 character secure token
        $expires_at = date('Y-m-d H:i:s', strtotime('+1 hour')); // Token expires in 1 hour

        // Store reset token in database
        $stmt = $conn->prepare('INSERT INTO password_resets (email, token, expires_at) VALUES (?, ?, ?) ON DUPLICATE KEY UPDATE token = VALUES(token), expires_at = VALUES(expires_at), created_at = NOW()');
        $stmt->bind_param('sss', $test_email, $reset_token, $expires_at);

        if ($stmt->execute()) {
            // Create real reset URL with valid token
            $reset_url = "http://" . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . "/reset_password.php?token=" . $reset_token;
            $subject = 'Password Reset Link - CryptoApp (Test Email)';
            $message = createPasswordResetLinkEmail('Test User', $reset_url);

            if (sendCryptoAppEmail($test_email, 'Test User', $subject, $message, 'password_reset')) {
                $result = "✅ Email sent successfully to: $test_email<br>";
                $result .= "🔗 Reset URL: <a href='" . htmlspecialchars($reset_url) . "' target='_blank'>Click here to test reset</a><br>";
                $result .= "⏰ Token expires at: " . $expires_at . " (IST)";
            } else {
                $result = "❌ Failed to send email to: $test_email";
            }
        } else {
            $result = "❌ Failed to create reset token in database: " . $conn->error;
        }
        $stmt->close();
    } else {
        $result = "❌ Please enter a valid email address";
    }
}
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Email - CryptoApp</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>

<body class="bg-gray-100 min-h-screen flex items-center justify-center">
    <div class="bg-white p-8 rounded-lg shadow-md w-full max-w-md">
        <h1 class="text-2xl font-bold mb-6 text-center">Test Email System</h1>

        <?php if (isset($result)): ?>
            <div class="mb-4 p-4 rounded-lg <?php echo strpos($result, '✅') !== false ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'; ?>">
                <?php echo $result; // Don't escape HTML since we want the link to work 
                ?>
            </div>
        <?php endif; ?>

        <form method="POST" class="space-y-4">
            <div>
                <label for="test_email" class="block text-sm font-medium text-gray-700 mb-2">
                    Test Email Address
                </label>
                <input
                    type="email"
                    id="test_email"
                    name="test_email"
                    required
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Enter email to test"
                    value="<?php echo htmlspecialchars($_POST['test_email'] ?? ''); ?>">
            </div>

            <button
                type="submit"
                class="w-full bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600 transition-colors">
                Send Test Email
            </button>
        </form>

        <div class="mt-6 text-sm text-gray-600">
            <h3 class="font-semibold mb-2">📧 Test Email Information:</h3>
            <div class="bg-blue-50 p-3 rounded-lg mb-4">
                <p class="text-blue-800"><strong>✅ This test now creates REAL reset tokens!</strong></p>
                <p class="text-blue-700">• Generates secure 64-character token</p>
                <p class="text-blue-700">• Stores token in database with 1-hour expiration</p>
                <p class="text-blue-700">• Sends working reset link via email</p>
                <p class="text-blue-700">• Uses Asia/Kolkata timezone</p>
            </div>

            <h3 class="font-semibold mb-2">Email Configuration:</h3>
            <?php
            $config = include 'email_config.php';
            echo "<p><strong>SMTP Host:</strong> " . htmlspecialchars($config['smtp_host']) . "</p>";
            echo "<p><strong>SMTP Port:</strong> " . htmlspecialchars($config['smtp_port']) . "</p>";
            echo "<p><strong>From Email:</strong> " . htmlspecialchars($config['from_email']) . "</p>";
            echo "<p><strong>Username:</strong> " . htmlspecialchars($config['smtp_username']) . "</p>";
            ?>

            <h3 class="font-semibold mb-2 mt-4">PHPMailer Status:</h3>
            <?php
            if (file_exists('vendor/autoload.php') && file_exists('vendor/phpmailer/phpmailer/src/PHPMailer.php')) {
                echo "<p class='text-green-600'>✅ PHPMailer installed via Composer</p>";
            } else {
                echo "<p class='text-red-600'>❌ PHPMailer not found via Composer</p>";
                echo "<p class='text-sm text-gray-500'>Run: composer require phpmailer/phpmailer</p>";
            }
            ?>
        </div>

        <div class="mt-4 text-center">
            <a href="login.php" class="text-blue-500 hover:underline">← Back to Login</a>
        </div>
    </div>
</body>

</html>