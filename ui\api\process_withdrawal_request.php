<?php
// Prevent any output before JSON
ini_set('display_errors', 0);
error_reporting(0);

session_start();
header('Content-Type: application/json');

// Admin authentication check
if (!isset($_SESSION['user_id']) || !$_SESSION['is_admin']) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Unauthorized access.']);
    exit();
}

include '../dbcon.php';

// Check database connection
if ($conn->connect_error) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Database connection failed.']);
    exit();
}

$admin_id = $_SESSION['user_id'];
$data = json_decode(file_get_contents('php://input'), true);

$request_id = $data['request_id'] ?? null;
$action = $data['action'] ?? null; // 'approve' or 'reject'
$notes = $data['notes'] ?? '';
$transaction_hash = $data['transaction_hash'] ?? '';

if (!$request_id || !$action || !in_array($action, ['approve', 'reject'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Invalid request data.']);
    exit();
}

// For rejection, notes are required
if ($action === 'reject' && empty(trim($notes))) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Rejection reason is required.']);
    exit();
}

try {
    // Start transaction
    $conn->begin_transaction();

    // 1. Get withdrawal request details
    $sql_get_request = "SELECT wr.*, u.name as user_name
                        FROM withdrawal_requests wr
                        JOIN users u ON wr.user_id = u.id
                        WHERE wr.id = ? AND wr.status = 'pending'";
    $stmt_get_request = $conn->prepare($sql_get_request);

    if (!$stmt_get_request) {
        throw new Exception('Failed to prepare statement: ' . $conn->error);
    }

    $stmt_get_request->bind_param("i", $request_id);

    if (!$stmt_get_request->execute()) {
        throw new Exception('Failed to execute query: ' . $stmt_get_request->error);
    }

    $request_result = $stmt_get_request->get_result();

    if ($request_result->num_rows === 0) {
        throw new Exception('Withdrawal request not found or already processed.');
    }

    $withdrawal_request = $request_result->fetch_assoc();
    $stmt_get_request->close();

    if ($action === 'approve') {
        // 2a. For approval - withdrawal amount was already deducted when request was created
        // Just update the request status
        $new_status = 'approved';
        $description = "Withdrawal approved - sent to Binance address";

        // Log the approval in wallet history
        $log_sql = "INSERT INTO wallet_history (user_id, amount, type, wallet_type, description) VALUES (?, 0, 'withdrawal_approved', 'withdrawal', ?)";
        $stmt_log = $conn->prepare($log_sql);

        if (!$stmt_log) {
            throw new Exception('Failed to prepare log statement: ' . $conn->error);
        }

        $stmt_log->bind_param("is", $withdrawal_request['user_id'], $description);

        if (!$stmt_log->execute()) {
            throw new Exception('Failed to log approval: ' . $stmt_log->error);
        }

        $stmt_log->close();
    } else { // reject
        // 2b. For rejection - refund the amount back to user's withdrawal wallet
        $new_status = 'rejected';

        // Refund amount to user's withdrawal wallet
        $refund_sql = "UPDATE users SET withdrawal_wallet_balance = withdrawal_wallet_balance + ? WHERE id = ?";
        $stmt_refund = $conn->prepare($refund_sql);

        if (!$stmt_refund) {
            throw new Exception('Failed to prepare refund statement: ' . $conn->error);
        }

        $stmt_refund->bind_param("di", $withdrawal_request['amount'], $withdrawal_request['user_id']);

        if (!$stmt_refund->execute()) {
            throw new Exception('Failed to process refund: ' . $stmt_refund->error);
        }

        $stmt_refund->close();

        // Log the refund in wallet history
        $description = "Withdrawal rejected - amount refunded to withdrawal wallet. Reason: " . $notes;
        $log_sql = "INSERT INTO wallet_history (user_id, amount, type, wallet_type, description) VALUES (?, ?, 'withdrawal_refund', 'withdrawal', ?)";
        $stmt_log = $conn->prepare($log_sql);

        if (!$stmt_log) {
            throw new Exception('Failed to prepare refund log statement: ' . $conn->error);
        }

        $stmt_log->bind_param("ids", $withdrawal_request['user_id'], $withdrawal_request['amount'], $description);

        if (!$stmt_log->execute()) {
            throw new Exception('Failed to log refund: ' . $stmt_log->error);
        }

        $stmt_log->close();
    }

    // 3. Update the withdrawal request status
    $sql_update_request = "UPDATE withdrawal_requests
                          SET status = ?, reviewed_by = ?, reviewed_at = NOW(), admin_notes = ?, transaction_hash = ?
                          WHERE id = ?";
    $stmt_update_request = $conn->prepare($sql_update_request);

    if (!$stmt_update_request) {
        throw new Exception('Failed to prepare update statement: ' . $conn->error);
    }

    $stmt_update_request->bind_param("sissi", $new_status, $admin_id, $notes, $transaction_hash, $request_id);

    if (!$stmt_update_request->execute()) {
        throw new Exception('Failed to update withdrawal request status: ' . $stmt_update_request->error);
    }

    $stmt_update_request->close();

    // Commit transaction
    $conn->commit();

    echo json_encode([
        'success' => true,
        'message' => "Withdrawal request {$action}ed successfully."
    ]);
} catch (Exception $e) {
    $conn->rollback();
    error_log('Process Withdrawal Request Error: ' . $e->getMessage());
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => $e->getMessage()]);
} finally {
    // Close statements safely
    if (isset($stmt_get_request) && $stmt_get_request !== false) {
        $stmt_get_request->close();
    }
    if (isset($stmt_refund) && $stmt_refund !== false) {
        $stmt_refund->close();
    }
    if (isset($stmt_log) && $stmt_log !== false) {
        $stmt_log->close();
    }
    if (isset($stmt_update_request) && $stmt_update_request !== false) {
        $stmt_update_request->close();
    }
    if (isset($conn) && $conn !== false) {
        $conn->close();
    }
}
