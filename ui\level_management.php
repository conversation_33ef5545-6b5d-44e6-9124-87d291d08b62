<?php
session_start();
if (!isset($_SESSION['user_id']) || !$_SESSION['is_admin']) {
    header('Location: login.php');
    exit();
}

// Page configuration
$page_title = "Level Distribution Management";
$additional_css = '
<style>
    /* Level form styling */
    .level-form {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 1rem;
        padding: 2rem;
        margin-bottom: 2rem;
        color: white;
    }

    .form-input {
        background: rgba(255, 255, 255, 0.1);
        border: 2px solid rgba(255, 255, 255, 0.2);
        color: white;
        border-radius: 0.5rem;
        padding: 0.75rem 1rem;
        width: 100%;
        transition: all 0.3s ease;
    }

    .form-input::placeholder {
        color: rgba(255, 255, 255, 0.7);
    }

    .form-input:focus {
        outline: none;
        border-color: rgba(255, 255, 255, 0.5);
        background: rgba(255, 255, 255, 0.15);
        box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.1);
    }

    .form-label {
        color: rgba(255, 255, 255, 0.9);
        font-weight: 600;
        margin-bottom: 0.5rem;
        display: block;
    }

    .submit-btn {
        background: rgba(255, 255, 255, 0.2);
        color: white;
        border: 2px solid rgba(255, 255, 255, 0.3);
        padding: 0.75rem 2rem;
        border-radius: 0.5rem;
        font-weight: 600;
        transition: all 0.3s ease;
        cursor: pointer;
        width: 100%;
    }

    .submit-btn:hover {
        background: rgba(255, 255, 255, 0.3);
        border-color: rgba(255, 255, 255, 0.5);
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    }

    /* Level cards */
    .level-card {
        background: rgba(255, 255, 255, 0.1);
        border: 2px solid rgba(255, 255, 255, 0.2);
        border-radius: 0.75rem;
        padding: 1.5rem;
        margin-bottom: 1rem;
        transition: all 0.3s ease;
    }

    .level-card:hover {
        background: rgba(255, 255, 255, 0.15);
        border-color: rgba(255, 255, 255, 0.3);
        transform: translateY(-2px);
    }

    /* Toast styling */
    .toast-success {
        background: linear-gradient(135deg, #10b981, #059669);
        color: white;
        padding: 1rem 1.5rem;
        border-radius: 0.5rem;
        box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
        position: fixed;
        top: 1.25rem;
        right: 1.25rem;
        z-index: 1000;
        display: flex;
        align-items: center;
        space-x: 0.5rem;
    }

    .toast-error {
        background: linear-gradient(135deg, #ef4444, #dc2626);
        color: white;
        padding: 1rem 1.5rem;
        border-radius: 0.5rem;
        box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
        position: fixed;
        top: 1.25rem;
        right: 1.25rem;
        z-index: 1000;
        display: flex;
        align-items: center;
        space-x: 0.5rem;
    }
</style>
';

// Start output buffering to capture the page content
ob_start();
?>

<!-- Page Header -->
<div class="flex flex-col lg:flex-row justify-between items-start lg:items-center space-y-4 lg:space-y-0 mb-6">
    <div>
        <h1 class="text-3xl font-bold text-gray-900">Level Distribution Management</h1>
        <p class="text-gray-600 mt-1">Configure commission percentages for network distribution levels</p>
    </div>
    <div class="flex items-center space-x-2 bg-blue-50 px-4 py-2 rounded-lg border border-blue-200">
        <i class="fas fa-layer-group text-blue-600"></i>
        <span class="text-sm font-medium text-blue-700">Level Management</span>
    </div>
</div>

<!-- Level Management Form -->
<div class="level-form">
    <div class="flex items-center space-x-3 mb-6">
        <div class="w-10 h-10 bg-white bg-opacity-20 rounded-xl flex items-center justify-center">
            <i class="fas fa-cogs text-white text-lg"></i>
        </div>
        <div>
            <h2 class="text-2xl font-bold text-white">Level Distribution Settings</h2>
            <p class="text-blue-100">Set commission percentages for the three distribution levels</p>
        </div>
    </div>

    <div class="bg-white bg-opacity-10 rounded-lg p-4 mb-6">
        <div class="flex items-center space-x-2 mb-2">
            <i class="fas fa-info-circle text-blue-200"></i>
            <span class="text-sm font-medium text-blue-200">Information</span>
        </div>
        <p class="text-sm text-blue-100">
            These percentages will be applied when new user investments are made.
            The total of all levels should not exceed 100%.
        </p>
    </div>

    <form id="levels-form">
        <div id="levels-container" class="space-y-4">
            <!-- JavaScript will populate this section -->
            <div class="text-center text-blue-100 py-8">
                <i class="fas fa-spinner fa-spin text-2xl mb-2"></i>
                <div>Loading level settings...</div>
            </div>
        </div>

        <div class="mt-6 pt-6 border-t border-white border-opacity-20">
            <button type="submit" class="submit-btn">
                <i class="fas fa-save mr-2"></i>
                Save Changes
            </button>
        </div>
    </form>
</div>

<div id="toast-message" class="hidden"></div>

<?php
// Capture the page content
$page_content = ob_get_clean();

// Include the admin layout
include 'admin/includes/layout.php';
?>

<script src="/netvis/ui/js/level_management.js"></script>