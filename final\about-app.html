<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>About Crypto App - Crypto Wallet</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
            min-height: 100vh;
        }

        .stats-card {
            background: rgba(30, 41, 59, 0.4);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(59, 130, 246, 0.2);
            border-radius: 0.75rem;
            padding: 1rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }

        .feature-card {
            background: rgba(15, 23, 42, 0.8);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(59, 130, 246, 0.3);
            border-radius: 0.75rem;
            padding: 1rem;
            transition: all 0.3s ease;
        }

        .feature-card:hover {
            border-color: rgba(6, 182, 212, 0.6);
            background: rgba(15, 23, 42, 0.9);
            transform: translateY(-2px);
        }

        .accent {
            color: #06b6d4 !important;
        }

        .hover\:accent:hover {
            color: #06b6d4 !important;
        }
    </style>
</head>

<body class="text-white">
    <div class="min-h-screen flex flex-col">
        <!-- Header -->
        <div class="sticky top-0 z-50 bg-slate-800/95 backdrop-blur-sm border-b border-slate-700">
            <div class="flex items-center justify-between px-6 py-4">
                <div class="flex items-center gap-4">
                    <button onclick="goBack()"
                        class="w-10 h-10 rounded-full bg-slate-700 hover:bg-slate-600 flex items-center justify-center transition-colors">
                        <i class="fas fa-arrow-left text-slate-300"></i>
                    </button>
                    <div>
                        <h1 class="text-xl font-bold text-white">About Crypto App</h1>
                        <p class="text-sm text-slate-400">Learn about our platform</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-grow overflow-y-auto px-4 sm:px-6 py-4">
            <!-- App Info -->
            <div class="stats-card mb-4">
                <div class="flex items-center gap-2 mb-4">
                    <div
                        class="w-10 h-10 rounded-lg bg-gradient-to-r from-blue-600 to-cyan-600 flex items-center justify-center">
                        <i class="fas fa-info-circle text-white"></i>
                    </div>
                    <div>
                        <h2 class="text-lg font-bold text-white">App Information</h2>
                        <p class="text-xs text-slate-400">About Crypto Wallet platform</p>
                    </div>
                </div>

                <div class="text-center mb-4">
                    <div
                        class="w-16 h-16 mx-auto mb-3 rounded-xl bg-gradient-to-r from-blue-600 to-cyan-600 flex items-center justify-center">
                        <i class="fas fa-wallet text-white text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-bold text-white mb-2">Crypto Wallet</h3>
                    <p class="text-sm text-slate-400 mb-3">Your Gateway to Digital Finance</p>
                    <div class="flex items-center justify-center gap-3 text-xs text-slate-500">
                        <span>Version 2.1.0</span>
                        <span>•</span>
                        <span>Build 2024.01.15</span>
                    </div>
                </div>

                <div
                    class="bg-gradient-to-r from-slate-800/50 to-slate-700/50 rounded-lg p-3 border border-blue-500/20">
                    <p class="text-sm text-slate-300 text-center">
                        A comprehensive digital finance platform designed to make cryptocurrency investment accessible
                        to everyone.
                    </p>
                </div>
            </div>

            <!-- Key Features -->
            <div class="stats-card mb-4">
                <div class="flex items-center gap-2 mb-4">
                    <div
                        class="w-10 h-10 rounded-lg bg-gradient-to-r from-blue-600 to-cyan-600 flex items-center justify-center">
                        <i class="fas fa-star text-white"></i>
                    </div>
                    <div>
                        <h2 class="text-lg font-bold text-white">Key Features</h2>
                        <p class="text-xs text-slate-400">What makes us special</p>
                    </div>
                </div>

                <div class="space-y-3">
                    <div class="feature-card">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center gap-3">
                                <i class="fas fa-shield-alt text-cyan-400"></i>
                                <div>
                                    <p class="text-sm font-semibold text-white">Secure Platform</p>
                                    <p class="text-xs text-slate-400">Bank-grade security with advanced encryption</p>
                                </div>
                            </div>
                            <i class="fas fa-check-circle text-cyan-400"></i>
                        </div>
                    </div>

                    <div class="feature-card">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center gap-3">
                                <i class="fas fa-users text-blue-400"></i>
                                <div>
                                    <p class="text-sm font-semibold text-white">3-Level Referral System</p>
                                    <p class="text-xs text-slate-400">Earn commissions: 10%, 5%, 2%</p>
                                </div>
                            </div>
                            <i class="fas fa-check-circle text-cyan-400"></i>
                        </div>
                    </div>

                    <div class="feature-card">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center gap-3">
                                <i class="fas fa-bolt text-cyan-400"></i>
                                <div>
                                    <p class="text-sm font-semibold text-white">Fast Transactions</p>
                                    <p class="text-xs text-slate-400">Processing within 24 hours</p>
                                </div>
                            </div>
                            <i class="fas fa-check-circle text-cyan-400"></i>
                        </div>
                    </div>

                    <div class="feature-card">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center gap-3">
                                <i class="fas fa-headset text-blue-400"></i>
                                <div>
                                    <p class="text-sm font-semibold text-white">24/7 Support</p>
                                    <p class="text-xs text-slate-400">Round-the-clock customer support</p>
                                </div>
                            </div>
                            <i class="fas fa-check-circle text-cyan-400"></i>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Platform Statistics -->
            <div class="stats-card mb-4">
                <div class="flex items-center gap-2 mb-4">
                    <div
                        class="w-10 h-10 rounded-lg bg-gradient-to-r from-blue-600 to-cyan-600 flex items-center justify-center">
                        <i class="fas fa-chart-bar text-white"></i>
                    </div>
                    <div>
                        <h2 class="text-lg font-bold text-white">Platform Statistics</h2>
                        <p class="text-xs text-slate-400">Our achievements in numbers</p>
                    </div>
                </div>

                <div class="grid grid-cols-2 gap-3">
                    <div
                        class="bg-gradient-to-r from-slate-800/50 to-slate-700/50 rounded-lg p-3 border border-blue-500/20 text-center">
                        <div
                            class="w-10 h-10 mx-auto mb-2 rounded-lg bg-gradient-to-r from-blue-600 to-cyan-600 flex items-center justify-center">
                            <i class="fas fa-users text-white text-sm"></i>
                        </div>
                        <p class="text-lg font-bold text-blue-400">50K+</p>
                        <p class="text-xs text-slate-400">Active Users</p>
                    </div>

                    <div
                        class="bg-gradient-to-r from-slate-800/50 to-slate-700/50 rounded-lg p-3 border border-cyan-500/20 text-center">
                        <div
                            class="w-10 h-10 mx-auto mb-2 rounded-lg bg-gradient-to-r from-blue-600 to-cyan-600 flex items-center justify-center">
                            <i class="fas fa-dollar-sign text-white text-sm"></i>
                        </div>
                        <p class="text-lg font-bold text-cyan-400">$10M+</p>
                        <p class="text-xs text-slate-400">Total Volume</p>
                    </div>

                    <div
                        class="bg-gradient-to-r from-slate-800/50 to-slate-700/50 rounded-lg p-3 border border-blue-500/20 text-center">
                        <div
                            class="w-10 h-10 mx-auto mb-2 rounded-lg bg-gradient-to-r from-blue-600 to-cyan-600 flex items-center justify-center">
                            <i class="fas fa-globe text-white text-sm"></i>
                        </div>
                        <p class="text-lg font-bold text-blue-400">150+</p>
                        <p class="text-xs text-slate-400">Countries</p>
                    </div>

                    <div
                        class="bg-gradient-to-r from-slate-800/50 to-slate-700/50 rounded-lg p-3 border border-cyan-500/20 text-center">
                        <div
                            class="w-10 h-10 mx-auto mb-2 rounded-lg bg-gradient-to-r from-blue-600 to-cyan-600 flex items-center justify-center">
                            <i class="fas fa-award text-white text-sm"></i>
                        </div>
                        <p class="text-lg font-bold text-cyan-400">99.9%</p>
                        <p class="text-xs text-slate-400">Uptime</p>
                    </div>
                </div>
            </div>

            <!-- Contact Information -->
            <div class="stats-card mb-4">
                <div class="flex items-center gap-2 mb-4">
                    <div
                        class="w-10 h-10 rounded-lg bg-gradient-to-r from-cyan-600 to-blue-600 flex items-center justify-center">
                        <i class="fas fa-envelope text-white"></i>
                    </div>
                    <div>
                        <h2 class="text-lg font-bold text-white">Get in Touch</h2>
                        <p class="text-xs text-slate-400">Connect with our team</p>
                    </div>
                </div>

                <div class="space-y-3">
                    <div
                        class="bg-gradient-to-r from-slate-800/50 to-slate-700/50 rounded-lg p-3 border border-blue-500/20">
                        <div class="flex items-center gap-3">
                            <i class="fas fa-envelope text-blue-400"></i>
                            <div>
                                <p class="text-sm font-semibold text-white">Email</p>
                                <p class="text-xs text-slate-400"><EMAIL></p>
                            </div>
                        </div>
                    </div>

                    <div
                        class="bg-gradient-to-r from-slate-800/50 to-slate-700/50 rounded-lg p-3 border border-cyan-500/20">
                        <div class="flex items-center gap-3">
                            <i class="fab fa-telegram text-cyan-400"></i>
                            <div>
                                <p class="text-sm font-semibold text-white">Telegram</p>
                                <p class="text-xs text-slate-400">@CryptoWalletOfficial</p>
                            </div>
                        </div>
                    </div>

                    <div
                        class="bg-gradient-to-r from-slate-800/50 to-slate-700/50 rounded-lg p-3 border border-blue-500/20">
                        <div class="flex items-center gap-3">
                            <i class="fas fa-globe text-blue-400"></i>
                            <div>
                                <p class="text-sm font-semibold text-white">Website</p>
                                <p class="text-xs text-slate-400">www.cryptowallet.com</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sticky Bottom Navigation Bar -->
        <div class="sticky bottom-0 z-40 flex-shrink-0">
            <div class="bg-slate-800/95 backdrop-blur-sm mx-4 mb-4 rounded-2xl border border-slate-700">
                <div class="flex justify-around items-center h-16">
                    <a href="1.html" class="flex flex-col items-center gap-1 text-slate-400 hover:accent">
                        <i class="fas fa-home"></i>
                        <span class="text-xs">Home</span>
                    </a>
                    <a href="wallet.html" class="flex flex-col items-center gap-1 text-slate-400 hover:accent">
                        <i class="fas fa-wallet"></i>
                        <span class="text-xs">Wallet</span>
                    </a>
                    <a href="team.html" class="flex flex-col items-center gap-1 text-slate-400 hover:accent">
                        <i class="fas fa-users"></i>
                        <span class="text-xs">Team</span>
                    </a>
                    <a href="profile.html" class="flex flex-col items-center gap-1 accent">
                        <i class="fas fa-user"></i>
                        <span class="text-xs">Profile</span>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script>
        function goBack() {
            window.history.back();
        }
    </script>
</body>

</html>