    <!-- Mobile Sidebar Overlay -->
    <div id="sidebarOverlay" class="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden hidden"></div>

    <!-- Sidebar -->
    <aside id="sidebar" class="fixed left-0 top-0 h-full bg-gradient-to-b from-white via-gray-50 to-white shadow-2xl border-r border-gray-200 z-50 transform -translate-x-full lg:translate-x-0 transition-all duration-300 ease-in-out w-72 sidebar-expanded">
        <!-- Sidebar Header -->
        <div class="flex items-center justify-between p-4 border-b border-gray-200 h-16 sidebar-header">
            <div class="flex items-center space-x-3 overflow-hidden">
                <div class="w-10 h-10 bg-gradient-to-br from-blue-500 via-blue-600 to-indigo-600 rounded-xl flex items-center justify-center shadow-lg flex-shrink-0 relative overflow-hidden">
                    <div class="absolute inset-0 bg-gradient-to-br from-white/20 to-transparent rounded-xl"></div>
                    <i class="fas fa-chart-line text-white text-lg relative z-10"></i>
                </div>
                <div class="sidebar-text">
                    <span class="font-bold text-gray-800 text-lg tracking-tight">CryptoApp</span>
                    <p class="text-xs text-gray-500 font-medium">Admin Panel</p>
                </div>
            </div>
            <button id="sidebarToggleDesktop" class="hidden lg:flex p-2.5 rounded-xl hover:bg-gray-100 transition-all duration-200 group">
                <i class="fas fa-chevron-left text-gray-500 text-sm group-hover:text-gray-700 transition-colors"></i>
            </button>
            <button id="sidebarClose" class="lg:hidden p-2.5 rounded-xl hover:bg-gray-100 transition-all duration-200">
                <i class="fas fa-times text-gray-500"></i>
            </button>
        </div>

        <!-- Navigation Menu -->
        <nav class="flex-1 overflow-y-auto py-4 scrollbar-hide">
            <div class="px-3 space-y-1">
                <!-- Dashboard -->
                <a href="admin.php" class="sidebar-link active" data-tooltip="Dashboard">
                    <div class="sidebar-icon">
                        <i class="fas fa-home"></i>
                    </div>
                    <span class="sidebar-text">Dashboard</span>
                    <div class="sidebar-indicator"></div>
                </a>

                <!-- Fund Management -->
                <div class="sidebar-group">
                    <button class="sidebar-group-toggle" onclick="toggleSidebarGroup('funds')" data-tooltip="Fund Management">
                        <div class="flex items-center">
                            <div class="sidebar-icon">
                                <i class="fas fa-money-bill-wave"></i>
                            </div>
                            <span class="sidebar-text">Fund Management</span>
                        </div>
                        <i class="fas fa-chevron-down transform transition-transform sidebar-text" id="funds-chevron"></i>
                    </button>
                    <div id="funds-group" class="sidebar-submenu">
                        <a href="admin_add_fund.php" class="sidebar-sublink">
                            <div class="sidebar-icon">
                                <i class="fas fa-plus-circle"></i>
                            </div>
                            <span class="sidebar-text">Add Fund</span>
                        </a>
                        <a href="admin_manual_transfer.php" class="sidebar-sublink">
                            <div class="sidebar-icon">
                                <i class="fas fa-hand-holding-usd"></i>
                            </div>
                            <span class="sidebar-text">Manual Transfer</span>
                        </a>

                        <a href="admin_fund_transfer.php" class="sidebar-sublink">
                            <div class="sidebar-icon">
                                <i class="fas fa-exchange-alt"></i>
                            </div>
                            <span class="sidebar-text">Fund Transfer Log </span>
                        </a>
                    </div>
                </div>

                <!-- Transactions -->
                <div class="sidebar-group">
                    <button class="sidebar-group-toggle" onclick="toggleSidebarGroup('transactions')" data-tooltip="Transactions">
                        <div class="flex items-center">
                            <div class="sidebar-icon">
                                <i class="fas fa-receipt"></i>
                            </div>
                            <span class="sidebar-text">Transactions</span>
                        </div>
                        <i class="fas fa-chevron-down transform transition-transform sidebar-text" id="transactions-chevron"></i>
                    </button>
                    <div id="transactions-group" class="sidebar-submenu">
                        <a href="admin_deposit_requests.php" class="sidebar-sublink">
                            <div class="sidebar-icon">
                                <i class="fas fa-arrow-down text-green-500"></i>
                            </div>
                            <span class="sidebar-text">Deposit Requests</span>
                        </a>
                        <a href="admin_withdrawal_requests.php" class="sidebar-sublink">
                            <div class="sidebar-icon">
                                <i class="fas fa-arrow-up text-red-500"></i>
                            </div>
                            <span class="sidebar-text">Withdrawal Requests</span>
                        </a>
                    </div>
                </div>

                <!-- Network -->
                <a href="network.php" class="sidebar-link" data-tooltip="Network">
                    <div class="sidebar-icon">
                        <i class="fas fa-network-wired"></i>
                    </div>
                    <span class="sidebar-text">Network</span>
                </a>

                <!-- Package Management -->
                <div class="sidebar-group">
                    <button class="sidebar-group-toggle" onclick="toggleSidebarGroup('packages')" data-tooltip="Package Management">
                        <div class="flex items-center">
                            <div class="sidebar-icon">
                                <i class="fas fa-box"></i>
                            </div>
                            <span class="sidebar-text">Package Management</span>
                        </div>
                        <i class="fas fa-chevron-down transform transition-transform sidebar-text" id="packages-chevron"></i>
                    </button>
                    <div id="packages-group" class="sidebar-submenu">
                        <a href="package_manager.php" class="sidebar-sublink">
                            <div class="sidebar-icon">
                                <i class="fas fa-boxes"></i>
                            </div>
                            <span class="sidebar-text">Package Master</span>
                        </a>
                        <a href="manage_investments.php" class="sidebar-sublink">
                            <div class="sidebar-icon">
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <span class="sidebar-text">Manage Investments</span>
                        </a>
                        <a href="admin_package_history.php" class="sidebar-sublink">
                            <div class="sidebar-icon">
                                <i class="fas fa-history"></i>
                            </div>
                            <span class="sidebar-text">Package History</span>
                        </a>
                    </div>
                </div>

                <!-- Bonus Management -->
                <div class="sidebar-group">
                    <button class="sidebar-group-toggle" onclick="toggleSidebarGroup('bonus')" data-tooltip="Bonus Management">
                        <div class="flex items-center">
                            <div class="sidebar-icon">
                                <i class="fas fa-gift"></i>
                            </div>
                            <span class="sidebar-text">Bonus Management</span>
                        </div>
                        <i class="fas fa-chevron-down transform transition-transform sidebar-text" id="bonus-chevron"></i>
                    </button>
                    <div id="bonus-group" class="sidebar-submenu">
                        <a href="bonus_management.php" class="sidebar-sublink">
                            <div class="sidebar-icon">
                                <i class="fas fa-cogs"></i>
                            </div>
                            <span class="sidebar-text">Bonus Management</span>
                        </a>
                        <a href="bonus_claims.php" class="sidebar-sublink">
                            <div class="sidebar-icon">
                                <i class="fas fa-hand-holding-usd"></i>
                            </div>
                            <span class="sidebar-text">Bonus Claims</span>
                        </a>
                        <a href="level_management.php" class="sidebar-sublink">
                            <div class="sidebar-icon">
                                <i class="fas fa-layer-group"></i>
                            </div>
                            <span class="sidebar-text">Level Management</span>
                        </a>
                    </div>
                </div>

                <!-- Staking Management -->
                <div class="sidebar-group">
                    <button class="sidebar-group-toggle" onclick="toggleSidebarGroup('staking')" data-tooltip="Staking Management">
                        <div class="flex items-center">
                            <div class="sidebar-icon">
                                <i class="fas fa-coins"></i>
                            </div>
                            <span class="sidebar-text">Staking Management</span>
                        </div>
                        <i class="fas fa-chevron-down transform transition-transform sidebar-text" id="staking-chevron"></i>
                    </button>
                    <div id="staking-group" class="sidebar-submenu">
                        <a href="admin_staking.php" class="sidebar-sublink">
                            <div class="sidebar-icon">
                                <i class="fas fa-coins"></i>
                            </div>
                            <span class="sidebar-text">Staking</span>
                        </a>
                        <a href="admin_staking_claims.php" class="sidebar-sublink">
                            <div class="sidebar-icon">
                                <i class="fas fa-hand-holding"></i>
                            </div>
                            <span class="sidebar-text">Staking Claims</span>
                        </a>
                    </div>
                </div>

                <!-- Coin Management -->
                <div class="sidebar-group">
                    <button class="sidebar-group-toggle" onclick="toggleSidebarGroup('coin')" data-tooltip="Coin Management">
                        <div class="flex items-center">
                            <div class="sidebar-icon">
                                <i class="fas fa-bitcoin"></i>
                            </div>
                            <span class="sidebar-text">Coin Management</span>
                        </div>
                        <i class="fas fa-chevron-down transform transition-transform sidebar-text" id="coin-chevron"></i>
                    </button>
                    <div id="coin-group" class="sidebar-submenu">
                        <a href="admin_coin_price.php" class="sidebar-sublink">
                            <div class="sidebar-icon">
                                <i class="fas fa-dollar-sign"></i>
                            </div>
                            <span class="sidebar-text">Coin Price</span>
                        </a>
                        <a href="coin_price_graph.php" class="sidebar-sublink">
                            <div class="sidebar-icon">
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <span class="sidebar-text">Price Graph</span>
                        </a>
                    </div>
                </div>

                <!-- Account Management -->
                <a href="admin_change_password.php" class="sidebar-link" data-tooltip="Password Manager">
                    <div class="sidebar-icon">
                        <i class="fas fa-key"></i>
                    </div>
                    <span class="sidebar-text">Password Manager</span>
                </a>

                <!-- Logout -->
                <a href="logout.php" class="sidebar-link" data-tooltip="Logout">
                    <div class="sidebar-icon">
                        <i class="fas fa-sign-out-alt"></i>
                    </div>
                    <span class="sidebar-text">Logout</span>
                </a>
            </div>
        </nav>

        <!-- Sidebar Footer -->
        <div class="p-4 border-t border-gray-200">
            <div class="bg-gradient-to-r from-emerald-50 to-blue-50 rounded-xl p-4 border border-emerald-200">
                <div class="flex items-center space-x-3">
                    <div class="w-3 h-3 bg-emerald-500 rounded-full animate-pulse shadow-lg shadow-emerald-500/30"></div>
                    <div class="sidebar-text">
                        <span class="text-sm font-semibold text-gray-800">System Status</span>
                        <p class="text-xs text-gray-600 mt-0.5">All systems operational</p>
                    </div>
                </div>
            </div>
        </div>
    </aside>

    <style id="sidebar-styles-v2">
        /* Modern Light Sidebar Styles - Force Refresh */
        #sidebar {
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border-right: 2px solid rgba(229, 231, 235, 0.8);
        }

        /* Sidebar States with Smooth Transitions */
        .sidebar-expanded {
            width: 18rem;
            transition: width 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .sidebar-collapsed {
            width: 4rem;
            transition: width 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        /* REDESIGNED COLLAPSED SIDEBAR */
        #sidebar.sidebar-collapsed {
            background: linear-gradient(180deg, #ffffff 0%, #f8fafc 100%) !important;
            border-right: 1px solid #e2e8f0 !important;
            box-shadow: 2px 0 10px rgba(0, 0, 0, 0.05) !important;
        }

        /* Collapsed Navigation Container */
        #sidebar.sidebar-collapsed nav {
            padding: 1rem 0.5rem !important;
        }

        #sidebar.sidebar-collapsed nav>div {
            padding: 0 !important;
            gap: 0.5rem !important;
        }

        /* Collapsed Menu Items - Compact & Sleek */
        #sidebar.sidebar-collapsed .sidebar-link,
        #sidebar.sidebar-collapsed .sidebar-group-toggle {
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            width: 2.5rem !important;
            height: 2.5rem !important;
            padding: 0 !important;
            margin: 0 auto 0.5rem auto !important;
            border-radius: 0.625rem !important;
            background: rgba(248, 250, 252, 0.7) !important;
            border: 1px solid rgba(226, 232, 240, 0.5) !important;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
            position: relative !important;
            overflow: hidden !important;
            backdrop-filter: blur(8px) !important;
            -webkit-backdrop-filter: blur(8px) !important;
        }

        /* Collapsed Icons - Reduced Size */
        #sidebar.sidebar-collapsed .sidebar-icon {
            width: 1rem !important;
            height: 1rem !important;
            margin: 0 !important;
            padding: 0 !important;
            background: transparent !important;
            border-radius: 0 !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            color: #64748b !important;
            font-size: 0.875rem !important;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
            z-index: 2 !important;
            position: relative !important;
        }

        /* Collapsed Hover Effects */
        #sidebar.sidebar-collapsed .sidebar-link:hover,
        #sidebar.sidebar-collapsed .sidebar-group-toggle:hover {
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(99, 102, 241, 0.08)) !important;
            border-color: rgba(59, 130, 246, 0.3) !important;
            transform: translateY(-2px) scale(1.05) !important;
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15) !important;
        }

        #sidebar.sidebar-collapsed .sidebar-link:hover .sidebar-icon,
        #sidebar.sidebar-collapsed .sidebar-group-toggle:hover .sidebar-icon {
            color: #3b82f6 !important;
            transform: scale(1.1) !important;
        }

        /* Collapsed Active State */
        #sidebar.sidebar-collapsed .sidebar-link.active {
            background: linear-gradient(135deg, #3b82f6, #6366f1) !important;
            border-color: #3b82f6 !important;
            box-shadow: 0 4px 15px rgba(59, 130, 246, 0.4) !important;
        }

        #sidebar.sidebar-collapsed .sidebar-link.active .sidebar-icon {
            color: white !important;
        }

        /* Hide elements in collapsed state */
        #sidebar.sidebar-collapsed .sidebar-text {
            display: none !important;
        }

        #sidebar.sidebar-collapsed .sidebar-link::before,
        #sidebar.sidebar-collapsed .sidebar-indicator {
            display: none !important;
        }

        #sidebar.sidebar-collapsed .sidebar-submenu {
            display: none !important;
        }

        /* Collapsed Header Design */
        #sidebar.sidebar-collapsed .sidebar-header {
            padding: 1rem 0.5rem !important;
            justify-content: center !important;
            border-bottom: 1px solid rgba(226, 232, 240, 0.8) !important;
        }

        #sidebar.sidebar-collapsed .sidebar-header .flex.items-center {
            justify-content: center !important;
        }

        #sidebar.sidebar-collapsed .sidebar-header .w-10 {
            width: 2.5rem !important;
            height: 2.5rem !important;
            border-radius: 0.75rem !important;
        }

        /* Collapsed Toggle Button */
        #sidebar.sidebar-collapsed #sidebarToggleDesktop {
            position: absolute !important;
            right: -0.75rem !important;
            top: 50% !important;
            transform: translateY(-50%) !important;
            width: 1.5rem !important;
            height: 1.5rem !important;
            border-radius: 50% !important;
            background: white !important;
            border: 1px solid #e2e8f0 !important;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
            padding: 0 !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
        }

        .sidebar-collapsed .sidebar-link.active .sidebar-icon {
            background: linear-gradient(135deg, #3b82f6, #6366f1);
            color: white;
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        }

        /* Hide chevrons in collapsed state */
        .sidebar-collapsed .sidebar-group-toggle i[id$="-chevron"] {
            display: none !important;
        }

        /* Collapsed header adjustments */
        .sidebar-collapsed .sidebar-text {
            opacity: 0;
            width: 0;
            margin: 0;
            padding: 0;
            overflow: hidden;
        }

        /* Center logo in collapsed state */
        .sidebar-collapsed .flex.items-center.space-x-3 {
            justify-content: center;
            gap: 0;
        }

        .sidebar-collapsed .sidebar-header {
            padding-left: 0.75rem;
            padding-right: 0.75rem;
        }

        /* Collapsed state improvements */
        .sidebar-collapsed .sidebar-link,
        .sidebar-collapsed .sidebar-group-toggle {
            justify-content: center;
            padding: 1rem 0.75rem;
            margin-bottom: 0.75rem;
        }

        .sidebar-collapsed .sidebar-icon {
            margin: 0 auto;
            width: 3rem;
            height: 3rem;
        }

        .sidebar-collapsed .sidebar-link::before {
            display: none;
        }

        .sidebar-collapsed .sidebar-indicator {
            display: none;
        }

        /* Improve spacing in collapsed state */
        .sidebar-collapsed nav {
            padding-top: 2rem;
        }

        .sidebar-collapsed .sidebar-link:hover,
        .sidebar-collapsed .sidebar-group-toggle:hover {
            transform: scale(1.05);
        }

        /* Hide chevrons in collapsed state */
        .sidebar-collapsed .sidebar-group-toggle i[id$="-chevron"] {
            display: none !important;
        }

        /* Hide System Status in Collapsed State */
        #sidebar.sidebar-collapsed .p-4.border-t,
        #sidebar.sidebar-collapsed>div:last-child {
            display: none !important;
        }

        /* Sidebar Text Elements - Enhanced Animations */
        .sidebar-text {
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            white-space: nowrap;
            overflow: hidden;
            transform: translateX(0);
        }

        /* Smooth text fade out when collapsing */
        .sidebar-collapsed .sidebar-text {
            opacity: 0;
            width: 0;
            margin: 0;
            padding: 0;
            transform: translateX(-10px);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        /* Smooth text fade in when expanding */
        .sidebar-expanded .sidebar-text {
            opacity: 1;
            transform: translateX(0);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) 0.1s;
        }

        /* Sidebar Icons */
        .sidebar-icon {
            width: 2.25rem;
            height: 2.25rem;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 0.75rem;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            flex-shrink: 0;
            position: relative;
            background: rgba(229, 231, 235, 0.5);
            color: #6b7280;
        }

        .sidebar-icon::before {
            content: '';
            position: absolute;
            inset: 0;
            border-radius: 1rem;
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(99, 102, 241, 0.1));
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        /* Sidebar Links */
        .sidebar-link {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.75rem;
            border-radius: 1rem;
            color: #4b5563;
            text-decoration: none;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            margin-bottom: 0.25rem;
            font-weight: 500;
            font-size: 0.875rem;
            overflow: hidden;
        }

        .sidebar-link::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 4px;
            background: linear-gradient(to bottom, #3b82f6, #6366f1);
            transform: scaleY(0);
            transition: transform 0.3s ease;
            border-radius: 0 2px 2px 0;
        }

        .sidebar-indicator {
            position: absolute;
            right: 1rem;
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background: #3b82f6;
            opacity: 0;
            transition: all 0.3s ease;
        }

        .sidebar-link:hover {
            background: rgba(59, 130, 246, 0.08);
            color: #1f2937;
            transform: translateX(6px);
        }

        .sidebar-link:hover::before {
            transform: scaleY(1);
        }

        .sidebar-link:hover .sidebar-icon {
            background: rgba(59, 130, 246, 0.15);
            color: #3b82f6;
            transform: scale(1.1) rotate(5deg);
        }

        .sidebar-link:hover .sidebar-icon::before {
            opacity: 1;
        }

        .sidebar-link.active {
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.12), rgba(99, 102, 241, 0.08));
            color: #1f2937;
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15);
        }

        .sidebar-link.active::before {
            transform: scaleY(1);
        }

        .sidebar-link.active .sidebar-icon {
            background: linear-gradient(135deg, #3b82f6, #6366f1);
            color: white;
            box-shadow: 0 4px 15px rgba(59, 130, 246, 0.4);
        }

        .sidebar-link.active .sidebar-indicator {
            opacity: 1;
        }

        /* Group Toggle */
        .sidebar-group-toggle {
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;
            gap: 0.75rem;
            padding: 0.75rem;
            border-radius: 1rem;
            color: #4b5563;
            background: none;
            border: none;
            text-align: left;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            margin-bottom: 0.25rem;
            position: relative;
            font-weight: 500;
            font-size: 0.875rem;
        }

        /* Chevron rotation animation */
        .sidebar-group-toggle i[id$="-chevron"] {
            transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .sidebar-group-toggle i[id$="-chevron"].rotate-180 {
            transform: rotate(180deg);
        }

        .sidebar-group-toggle:hover {
            background: rgba(107, 114, 128, 0.08);
            color: #1f2937;
            transform: translateX(4px);
        }

        .sidebar-group-toggle:hover .sidebar-icon {
            background: rgba(107, 114, 128, 0.15);
            color: #374151;
            transform: scale(1.05);
        }

        /* Submenu */
        .sidebar-submenu {
            margin-left: 1rem;
            margin-top: 0.25rem;
            margin-bottom: 0.5rem;
            max-height: 0;
            overflow: hidden;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            border-left: 2px solid rgba(229, 231, 235, 0.6);
            padding-left: 0.75rem;
        }

        .sidebar-submenu.open {
            max-height: 24rem;
        }

        .sidebar-collapsed .sidebar-submenu {
            display: none;
        }

        .sidebar-sublink {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 0.75rem;
            border-radius: 0.75rem;
            color: #6b7280;
            text-decoration: none;
            font-size: 0.8125rem;
            font-weight: 500;
            transition: all 0.3s ease;
            margin-bottom: 0.125rem;
            position: relative;
        }

        .sidebar-sublink:hover {
            background: rgba(59, 130, 246, 0.06);
            color: #374151;
            transform: translateX(6px);
        }

        .sidebar-sublink:hover .sidebar-icon {
            background: rgba(59, 130, 246, 0.12);
            color: #3b82f6;
            transform: scale(1.05);
        }

        .sidebar-sublink .sidebar-icon {
            width: 1.75rem;
            height: 1.75rem;
            background: rgba(229, 231, 235, 0.4);
            border-radius: 0.5rem;
        }

        /* Tooltip for collapsed state */
        .sidebar-tooltip {
            position: absolute;
            left: 100%;
            top: 50%;
            transform: translateY(-50%);
            margin-left: 1rem;
            padding: 0.75rem 1rem;
            background: linear-gradient(135deg, #374151, #4b5563);
            color: white;
            border-radius: 1rem;
            font-size: 0.875rem;
            font-weight: 500;
            white-space: nowrap;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            z-index: 1000;
            pointer-events: none;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
            border: 1px solid rgba(229, 231, 235, 0.3);
        }

        .sidebar-tooltip::before {
            content: '';
            position: absolute;
            right: 100%;
            top: 50%;
            transform: translateY(-50%);
            border: 8px solid transparent;
            border-right-color: #374151;
        }

        .sidebar-collapsed .sidebar-link:hover .sidebar-tooltip,
        .sidebar-collapsed .sidebar-group-toggle:hover .sidebar-tooltip {
            opacity: 1;
            visibility: visible;
            transform: translateY(-50%) translateX(4px);
        }

        /* Hide Scrollbar */
        .scrollbar-hide {
            -ms-overflow-style: none;
            scrollbar-width: none;
        }

        .scrollbar-hide::-webkit-scrollbar {
            display: none;
        }

        #sidebar::-webkit-scrollbar {
            display: none;
        }

        /* Responsive adjustments */
        @media (max-width: 1024px) {
            .sidebar-collapsed {
                width: 18rem;
            }

            .sidebar-collapsed .sidebar-text {
                opacity: 1;
                width: auto;
            }

            .sidebar-tooltip {
                display: none;
            }
        }

        /* Animation for active state indicator */
        @keyframes pulse-glow {

            0%,
            100% {
                box-shadow: 0 0 5px rgba(59, 130, 246, 0.5);
            }

            50% {
                box-shadow: 0 0 20px rgba(59, 130, 246, 0.8);
            }
        }

        .sidebar-link.active .sidebar-icon {
            animation: pulse-glow 2s ease-in-out infinite;
        }

        /* Enhanced transitions for smooth animations */
        .sidebar-icon {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
        }

        .sidebar-link,
        .sidebar-group-toggle {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
        }

        /* Prevent layout shift during transitions */
        #sidebar {
            will-change: width;
        }

        .sidebar-text {
            will-change: opacity, transform;
        }

        /* Enhanced focus states for accessibility */
        #sidebar.sidebar-collapsed .sidebar-link:focus,
        #sidebar.sidebar-collapsed .sidebar-group-toggle:focus {
            outline: 2px solid rgba(59, 130, 246, 0.5);
            outline-offset: 2px;
            box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
        }
    </style>

    <script>
        // Sidebar functionality
        let sidebarCollapsed = false;

        // Sidebar group toggle functionality
        function toggleSidebarGroup(groupName) {
            if (sidebarCollapsed) return; // Don't open groups when collapsed

            const group = document.getElementById(groupName + '-group');
            const chevron = document.getElementById(groupName + '-chevron');

            // Close all other open groups first
            const allGroups = document.querySelectorAll('.sidebar-submenu');
            const allChevrons = document.querySelectorAll('[id$="-chevron"]');

            allGroups.forEach(otherGroup => {
                if (otherGroup.id !== groupName + '-group') {
                    otherGroup.classList.remove('open');
                }
            });

            allChevrons.forEach(otherChevron => {
                if (otherChevron.id !== groupName + '-chevron') {
                    otherChevron.classList.remove('rotate-180');
                }
            });

            // Toggle the clicked group
            group.classList.toggle('open');
            chevron.classList.toggle('rotate-180');
        }

        // Desktop sidebar collapse toggle - Smooth Animations
        document.getElementById('sidebarToggleDesktop').addEventListener('click', function() {
            const sidebar = document.getElementById('sidebar');
            const chevron = this.querySelector('i');
            const mainContent = document.getElementById('mainContent');

            sidebarCollapsed = !sidebarCollapsed;

            if (sidebarCollapsed) {
                // Start collapsing sequence
                sidebar.style.transition = 'width 0.4s cubic-bezier(0.4, 0, 0.2, 1)';

                // Close all open groups immediately
                document.querySelectorAll('.sidebar-submenu.open').forEach(submenu => {
                    submenu.classList.remove('open');
                });
                document.querySelectorAll('.sidebar-group-toggle i[id$="-chevron"]').forEach(chevron => {
                    chevron.classList.remove('rotate-180');
                });

                // Hide text elements first for smooth animation
                document.querySelectorAll('.sidebar-text').forEach(text => {
                    text.style.transition = 'all 0.2s cubic-bezier(0.4, 0, 0.2, 1)';
                    text.style.opacity = '0';
                    text.style.transform = 'translateX(-10px)';
                });

                // After text fade, apply collapsed state
                setTimeout(() => {
                    sidebar.classList.remove('sidebar-expanded');
                    sidebar.classList.add('sidebar-collapsed');
                    chevron.classList.remove('fa-chevron-left');
                    chevron.classList.add('fa-chevron-right');

                    // Hide chevrons
                    document.querySelectorAll('.sidebar-group-toggle i[id$="-chevron"]').forEach(chevron => {
                        chevron.style.display = 'none';
                    });

                    // Smooth main content transition
                    if (mainContent) {
                        mainContent.style.transition = 'margin-left 0.4s cubic-bezier(0.4, 0, 0.2, 1)';
                        mainContent.style.marginLeft = '4rem';
                    }
                }, 150);

            } else {
                // Start expanding sequence
                sidebar.style.transition = 'width 0.4s cubic-bezier(0.4, 0, 0.2, 1)';

                sidebar.classList.remove('sidebar-collapsed');
                sidebar.classList.add('sidebar-expanded');
                chevron.classList.remove('fa-chevron-right');
                chevron.classList.add('fa-chevron-left');

                // Show chevrons immediately
                document.querySelectorAll('.sidebar-group-toggle i[id$="-chevron"]').forEach(chevron => {
                    chevron.style.display = 'block';
                });

                // Smooth main content transition
                if (mainContent) {
                    mainContent.style.transition = 'margin-left 0.4s cubic-bezier(0.4, 0, 0.2, 1)';
                    mainContent.style.marginLeft = '18rem';
                }

                // After width expansion, show text elements
                setTimeout(() => {
                    document.querySelectorAll('.sidebar-text').forEach(text => {
                        text.style.transition = 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1) 0.1s';
                        text.style.opacity = '1';
                        text.style.transform = 'translateX(0)';
                    });
                }, 200);
            }
        });

        // Close sidebar on mobile
        document.getElementById('sidebarClose').addEventListener('click', function() {
            const sidebar = document.getElementById('sidebar');
            const overlay = document.getElementById('sidebarOverlay');

            sidebar.classList.add('-translate-x-full');
            overlay.classList.add('hidden');
        });

        // Close sidebar when clicking overlay
        document.getElementById('sidebarOverlay').addEventListener('click', function() {
            const sidebar = document.getElementById('sidebar');
            const overlay = document.getElementById('sidebarOverlay');

            sidebar.classList.add('-translate-x-full');
            overlay.classList.add('hidden');
        });

        // Add tooltips to collapsed sidebar items
        function addTooltips() {
            const sidebarLinks = document.querySelectorAll('.sidebar-link, .sidebar-group-toggle');

            sidebarLinks.forEach(link => {
                const tooltip = document.createElement('div');
                tooltip.className = 'sidebar-tooltip';
                tooltip.textContent = link.getAttribute('data-tooltip');
                link.appendChild(tooltip);
            });
        }

        // Initialize tooltips when page loads
        document.addEventListener('DOMContentLoaded', addTooltips);
    </script>