<?php
// Test the improved email template
include 'email_helper.php';
include 'dbcon.php';

echo "<h2>🎨 Testing Improved Email Template</h2>";

// Generate a test token
$test_email = '<EMAIL>';
$reset_token = bin2hex(random_bytes(32));
$expires_at = date('Y-m-d H:i:s', strtotime('+1 hour'));

// Store token in database
$stmt = $conn->prepare('INSERT INTO password_resets (email, token, expires_at) VALUES (?, ?, ?) ON DUPLICATE KEY UPDATE token = VALUES(token), expires_at = VALUES(expires_at), created_at = NOW()');
$stmt->bind_param('sss', $test_email, $reset_token, $expires_at);
$stmt->execute();

// Create reset URL
$reset_url = "http://localhost/netvis/ui/reset_password.php?token=" . $reset_token;

// Create email content
$subject = 'Password Reset Link - CryptoApp';
$html_message = createPasswordResetLinkEmail('Test User', $reset_url);

echo "<h3>📧 Email Preview:</h3>";
echo "<div style='border: 2px solid #e2e8f0; border-radius: 12px; padding: 20px; margin: 20px 0; background: #f8fafc;'>";
echo "<p><strong>Subject:</strong> " . htmlspecialchars($subject) . "</p>";
echo "<p><strong>To:</strong> " . htmlspecialchars($test_email) . "</p>";
echo "<p><strong>Reset URL:</strong> <a href='" . htmlspecialchars($reset_url) . "' target='_blank'>Click to test</a></p>";
echo "</div>";

echo "<h3>🎨 Improved Email Template:</h3>";
echo "<div style='border: 3px solid #3b82f6; border-radius: 16px; padding: 10px; margin: 20px 0; background: white; box-shadow: 0 10px 30px rgba(0,0,0,0.1);'>";
echo $html_message;
echo "</div>";

echo "<h3>✨ Improvements Made:</h3>";
echo "<ul style='background: #f0f9ff; padding: 20px; border-radius: 12px; border: 2px solid #0ea5e9;'>";
echo "<li><strong>🎯 Enhanced Reset Button:</strong> Larger, more prominent with better hover effects</li>";
echo "<li><strong>🎨 Modern Card Design:</strong> Improved gradients, shadows, and rounded corners</li>";
echo "<li><strong>📱 Better Mobile Support:</strong> Responsive design for all screen sizes</li>";
echo "<li><strong>🔗 Styled Link Box:</strong> Better formatting for backup URL</li>";
echo "<li><strong>🛡️ Clean Headers:</strong> Removed duplicate MIME headers to prevent display issues</li>";
echo "<li><strong>🌈 Enhanced Colors:</strong> Better contrast and visual hierarchy</li>";
echo "<li><strong>📐 Improved Spacing:</strong> Better padding and margins throughout</li>";
echo "</ul>";

echo "<h3>🧪 Technical Improvements:</h3>";
echo "<ul style='background: #f0fdf4; padding: 20px; border-radius: 12px; border: 2px solid #22c55e;'>";
echo "<li><strong>✅ MIME Headers Fixed:</strong> No more raw headers in email body</li>";
echo "<li><strong>✅ Button Visibility:</strong> Reset button now stands out prominently</li>";
echo "<li><strong>✅ Professional Styling:</strong> Modern, clean design</li>";
echo "<li><strong>✅ Cross-Client Compatibility:</strong> Works in Gmail, Outlook, Apple Mail</li>";
echo "<li><strong>✅ Accessibility:</strong> Better color contrast and readable fonts</li>";
echo "</ul>";

$conn->close();
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; background: #f8fafc; }
h2, h3 { color: #333; }
p { margin: 10px 0; }
ul { margin-left: 20px; }
li { margin: 8px 0; line-height: 1.6; }
a { color: #3b82f6; }
</style>
