<?php
session_start();

if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit();
}

// Include database connection
include 'dbcon.php';

$user_id = $_SESSION['user_id'];

// Fetch user and package details
$sql = 'SELECT u.name, u.user_id, p.package_name, p.package_amount, p.profit_percentage 
        FROM users u 
        JOIN packages p ON u.package = p.package_name 
        WHERE u.id = ?';
$stmt = $conn->prepare($sql);
$stmt->bind_param('i', $user_id);
$stmt->execute();
$result = $stmt->get_result();
$user_data = $result->fetch_assoc();
$stmt->close();

// Fetch today's earnings
$today_sql = 'SELECT SUM(amount) AS todays_earnings FROM wallet WHERE user_id = ? AND DATE(created_at) = CURDATE()';
$today_stmt = $conn->prepare($today_sql);
$today_stmt->bind_param('i', $user_id);
$today_stmt->execute();
$today_result = $today_stmt->get_result();
$today_earnings_row = $today_result->fetch_assoc();
$todays_earnings = $today_earnings_row['todays_earnings'] ?? 0;
$today_stmt->close();

// Fetch total network commission
$commission_sql = "SELECT SUM(amount) AS total_commission FROM wallet WHERE user_id = ? AND type = 'network_commission'";
$commission_stmt = $conn->prepare($commission_sql);
$commission_stmt->bind_param('i', $user_id);
$commission_stmt->execute();
$commission_result = $commission_stmt->get_result();
$commission_row = $commission_result->fetch_assoc();
$total_commission = $commission_row['total_commission'] ?? 0;
$commission_stmt->close();

$conn->close();

// Assign variables for the view
$user_name = $user_data['name'] ?? 'User';
$_SESSION['user_name'] = $user_name;  // Ensure session is updated
$package_name = $user_data['package_name'] ?? 'N/A';
$package_amount = $user_data['package_amount'] ?? 0;
$profit_percentage = $user_data['profit_percentage'] ?? 0;

?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Account</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>

<body class="bg-gray-100">
    <?php include 'user_nav.php'; ?>

    <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div class="px-4 py-6 sm:px-0">
            <div class="bg-white shadow-md rounded-lg p-8">
                <h2 class="text-2xl font-bold mb-6">Welcome, <?php echo htmlspecialchars($user_name); ?>!</h2>
                <div class="bg-white shadow-md rounded-lg p-6 mt-6">
                    <h3 class="text-lg font-semibold text-gray-800">Your Referral Link</h3>
                    <div class="flex items-center mt-2 space-x-4">
                        <input type="text" id="referral-link" value="<?php echo 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['PHP_SELF']) . '/register.php?sponser_id=' . urlencode($user_data['user_id'] ?? ''); ?>" class="w-full p-2 border border-gray-300 rounded-md bg-gray-50" readonly>
                        <button id="copy-button" class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 flex-shrink-0">Copy</button>
                    </div>
                    <p id="copy-feedback" class="text-sm text-green-600 mt-2 h-4"></p>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mt-6">
                    <div class="bg-blue-100 p-4 rounded-lg shadow">
                        <h3 class="text-lg font-semibold text-blue-800">Your Investment</h3>
                        <p class="text-3xl font-bold text-blue-600 mt-2">$<?php echo number_format($package_amount, 2); ?></p>
                        <p class="text-sm text-gray-600 mt-1">Package: <?php echo htmlspecialchars($package_name); ?></p>
                    </div>
                    <div class="bg-yellow-100 p-4 rounded-lg shadow">
                        <h3 class="text-lg font-semibold text-yellow-800">Daily Profit Rate</h3>
                        <p class="text-3xl font-bold text-yellow-600 mt-2"><?php echo htmlspecialchars($profit_percentage); ?>%</p>
                    </div>
                    <div class="bg-green-100 p-4 rounded-lg shadow">
                        <h3 class="text-lg font-semibold text-green-800">Today's Earnings</h3>
                        <p class="text-3xl font-bold text-green-600 mt-2">$<?php echo number_format($todays_earnings, 4); ?></p>
                    </div>
                    <div class="bg-purple-100 p-4 rounded-lg shadow">
                        <h3 class="text-lg font-semibold text-purple-800">Network Commission</h3>
                        <p class="text-3xl font-bold text-purple-600 mt-2">$<?php echo number_format($total_commission, 4); ?></p>
                    </div>
                </div>

                <!-- Bonus Achievement Section -->
                <div class="bg-white shadow-md rounded-lg p-8 mt-6">
                    <h2 class="text-2xl font-bold mb-6">Bonus Achievements</h2>
                    <div id="bonus-achievement-section">
                        <p>Loading bonus achievements...</p>
                    </div>
                </div>

            </div>
        </div>
    </div>
    <script>
        document.getElementById('copy-button').addEventListener('click', function() {
            const referralLinkInput = document.getElementById('referral-link');
            referralLinkInput.select();
            referralLinkInput.setSelectionRange(0, 99999); // For mobile devices

            try {
                document.execCommand('copy');
                const feedback = document.getElementById('copy-feedback');
                feedback.innerText = 'Copied to clipboard!';
                setTimeout(() => {
                    feedback.innerText = '';
                }, 2000);
            } catch (err) {
                console.error('Failed to copy: ', err);
                alert('Failed to copy the link.');
            }
        });

        document.addEventListener('DOMContentLoaded', function() {
            fetch('get_team_investment.php')
                .then(response => response.json())
                .then(data => {
                    const bonusSection = document.getElementById('bonus-achievement-section');
                    if (data.error) {
                        bonusSection.innerHTML = `<p class="text-red-500">${data.error}</p>`;
                        return;
                    }

                    let content = `<p class="text-lg font-semibold">Total Team Investment: <span class="text-blue-600">$${(data.team_investment || 0).toFixed(2)}</span></p>`;

                    if (data.bonuses && data.bonuses.length > 0) {
                        content += '<div class="mt-4 space-y-4">';
                        data.bonuses.forEach(bonus => {
                            let statusHtml = '';
                            if (bonus.is_achieved) {
                                if (bonus.claim_status === 'approved') {
                                    statusHtml = `<span class="font-semibold text-green-600">Approved</span>`;
                                } else if (bonus.claim_status === 'pending') {
                                    statusHtml = `<span class="font-semibold text-yellow-600">Pending Approval</span>`;
                                } else if (bonus.claim_status === 'rejected') {
                                    statusHtml = `<span class="font-semibold text-red-600">Rejected</span>`;
                                } else {
                                    statusHtml = `<button onclick="claimBonus(${bonus.id})" class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 text-sm font-semibold">Claim</button>`;
                                }
                            } else {
                                statusHtml = `<span class="font-semibold text-gray-500">Locked</span>`;
                            }

                            const icon = bonus.is_achieved ?
                                `<svg class="w-6 h-6 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>` :
                                `<svg class="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path></svg>`;

                            content += `
                        <div class="flex items-center justify-between p-4 rounded-lg ${bonus.is_achieved ? 'bg-green-50' : 'bg-gray-50'} shadow-sm">
                            <div class="flex items-center space-x-4">
                                ${icon}
                                <div>
                                    <p class="font-bold text-gray-800">${bonus.bonus_name}</p>
                                    <p class="text-sm text-gray-600">Requires: $${parseFloat(bonus.bonus_fund).toFixed(2)}</p>
                                </div>
                            </div>
                            <div class="text-right">
                                ${statusHtml}
                            </div>
                        </div>
                    `;
                        });
                        content += '</div>';
                    } else {
                        content += '<p class="mt-4">No bonuses are currently available.</p>';
                    }

                    bonusSection.innerHTML = content;
                })
                .catch(error => {
                    console.error('Error fetching bonus data:', error);
                    document.getElementById('bonus-achievement-section').innerHTML = '<p class="text-red-500">Could not load bonus achievements.</p>';
                });
        });

        function claimBonus(bonusId) {
            if (!confirm('Are you sure you want to claim this bonus?')) return;

            fetch('claim_bonus.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `bonus_id=${bonusId}`
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('Bonus claimed successfully! It is now pending approval.');
                        location.reload();
                    } else {
                        alert('Error: ' + (data.error || 'An unknown error occurred.'));
                    }
                })
                .catch(error => {
                    console.error('Error claiming bonus:', error);
                    alert('An error occurred while claiming the bonus.');
                });
        }
    </script>
</body>

</html>