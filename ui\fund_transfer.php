<?php
session_start();

if (!isset($_SESSION['user_id'])) {
    header("Location: login.php");
    exit();
}

include 'dbcon.php';

$user_id = $_SESSION['user_id'];

// Fetch user's wallet balances
$balance_sql = "SELECT deposit_wallet_balance, withdrawal_wallet_balance FROM users WHERE id = ?";
$stmt = $conn->prepare($balance_sql);
$stmt->bind_param("i", $user_id);
$stmt->execute();
$stmt->store_result();
$stmt->bind_result($deposit_balance, $withdrawal_balance);
$stmt->fetch();
$deposit_balance = $deposit_balance ?? 0;
$withdrawal_balance = $withdrawal_balance ?? 0;

$stmt->close();
$conn->close();
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fund Transfer</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>

<body class="bg-gray-100 font-sans">
    <?php include 'user_nav.php'; ?>

    <main class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div class="bg-white shadow-lg rounded-lg p-8">
            <h2 class="text-3xl font-bold text-gray-800 mb-4">Internal Fund Transfer</h2>

            <div class="mb-8 bg-gray-50 p-6 rounded-lg">
                <h3 class="text-lg font-semibold text-gray-700 mb-4">Your Wallet Balances</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="bg-white p-4 rounded-lg border border-gray-200">
                        <h4 class="text-sm font-medium text-gray-600">Deposit Wallet</h4>
                        <p class="text-xl font-bold text-green-600">$<?php echo number_format($deposit_balance, 4); ?></p>
                    </div>
                    <div class="bg-white p-4 rounded-lg border border-gray-200">
                        <h4 class="text-sm font-medium text-gray-600">Withdrawal Wallet</h4>
                        <p class="text-xl font-bold text-blue-600">$<?php echo number_format($withdrawal_balance, 4); ?></p>
                    </div>
                </div>
            </div>

            <form id="transfer-form" class="space-y-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-3">Transfer From</label>
                    <div class="space-y-3">
                        <!-- Deposit Wallet Option -->
                        <label class="flex items-center p-3 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50">
                            <input type="radio" name="wallet_type" value="deposit" class="mr-3" required>
                            <div class="flex-1">
                                <div class="flex justify-between items-center">
                                    <div>
                                        <div class="font-medium text-gray-900">Deposit Wallet</div>
                                        <div class="text-sm text-gray-500">Transfer from deposit wallet</div>
                                    </div>
                                    <div class="text-right">
                                        <div class="text-lg font-bold text-green-600">$<?php echo number_format($deposit_balance, 4); ?></div>
                                        <div class="text-xs text-gray-500">Available</div>
                                    </div>
                                </div>
                            </div>
                        </label>

                        <!-- Withdrawal Wallet Option -->
                        <label class="flex items-center p-3 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50">
                            <input type="radio" name="wallet_type" value="withdrawal" class="mr-3" required>
                            <div class="flex-1">
                                <div class="flex justify-between items-center">
                                    <div>
                                        <div class="font-medium text-gray-900">Withdrawal Wallet</div>
                                        <div class="text-sm text-gray-500">Transfer from withdrawal wallet</div>
                                    </div>
                                    <div class="text-right">
                                        <div class="text-lg font-bold text-blue-600">$<?php echo number_format($withdrawal_balance, 4); ?></div>
                                        <div class="text-xs text-gray-500">Available</div>
                                    </div>
                                </div>
                            </div>
                        </label>
                    </div>
                </div>

                <div>
                    <label for="recipient_user_id" class="block text-sm font-medium text-gray-700">Recipient's User ID</label>
                    <input type="text" name="recipient_user_id" id="recipient_user_id" class="mt-1 block w-full px-3 py-2 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" required autocomplete="off">
                    <div id="recipient-validation" class="mt-2">
                        <div id="recipient-name-display" class="text-sm font-medium hidden"></div>
                        <div id="recipient-relationship" class="text-xs mt-1 hidden"></div>
                        <div id="recipient-error" class="text-sm text-red-600 hidden"></div>
                        <div id="recipient-loading" class="text-sm text-gray-500 hidden">
                            <span class="inline-flex items-center">
                                <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                                Validating user...
                            </span>
                        </div>
                    </div>
                </div>

                <div>
                    <label for="amount" class="block text-sm font-medium text-gray-700">Amount to Transfer (USD)</label>
                    <div class="mt-1 relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <span class="text-gray-500 sm:text-sm">$</span>
                        </div>
                        <input type="number" name="amount" id="amount" step="10" min="10" class="block w-full pl-7 pr-3 py-2 bg-white border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" placeholder="Enter amount (multiple of 10)" required>
                    </div>
                    <p class="mt-1 text-sm text-gray-500">
                        <span class="font-medium">Minimum:</span> $10 |
                        <span class="font-medium">Must be multiple of:</span> $10
                        <span class="text-indigo-600">(e.g., $10, $20, $30, $100)</span>
                    </p>
                </div>

                <div class="hidden">
                    <input type="text" name="transfer_type" id="transfer_type" value="">
                </div>

                <div>
                    <label for="password" class="block text-sm font-medium text-gray-700">Your Password (for verification)</label>
                    <input type="password" name="password" id="password" class="mt-1 block w-full px-3 py-2 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" required autocomplete="current-password">
                </div>

                <div>
                    <button type="button" id="confirm-transfer-btn" class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">Transfer Funds</button>
                </div>
            </form>
        </div>

        <div id="transfer-history" class="mt-8 bg-white shadow-lg rounded-lg p-8">
            <h3 class="text-2xl font-bold text-gray-800 mb-4">Transfer History</h3>
            <div class="overflow-x-auto border border-gray-200 rounded-lg">
                <table class="min-w-full bg-white">
                    <thead class="bg-gray-100">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">Date</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">Recipient</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-600 uppercase tracking-wider">Amount</th>
                        </tr>
                    </thead>
                    <tbody id="history-body">
                        <!-- History will be loaded here -->
                    </tbody>
                </table>
            </div>
        </div>
    </main>

    <!-- Confirmation Modal -->
    <div id="confirmation-modal" class="fixed z-10 inset-0 overflow-y-auto hidden" aria-labelledby="modal-title" role="dialog" aria-modal="true">
        <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>
            <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
            <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
                <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                    <div class="sm:flex sm:items-start">
                        <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-yellow-100 sm:mx-0 sm:h-10 sm:w-10">
                            <svg class="h-6 w-6 text-yellow-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                            </svg>
                        </div>
                        <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                            <h3 class="text-lg leading-6 font-medium text-gray-900" id="modal-title">Confirm Transfer</h3>
                            <div class="mt-2">
                                <p class="text-sm text-gray-500">Are you sure you want to transfer <strong id="confirm-amount"></strong> to <strong id="confirm-recipient"></strong>?</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                    <button type="button" id="execute-transfer-btn" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-indigo-600 text-base font-medium text-white hover:bg-indigo-700 focus:outline-none sm:ml-3 sm:w-auto sm:text-sm">Confirm</button>
                    <button type="button" id="cancel-transfer-btn" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none sm:mt-0 sm:w-auto sm:text-sm">Cancel</button>
                </div>
            </div>
        </div>
    </div>


    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const confirmBtn = document.getElementById('confirm-transfer-btn');
            const modal = document.getElementById('confirmation-modal');
            const executeBtn = document.getElementById('execute-transfer-btn');
            const cancelBtn = document.getElementById('cancel-transfer-btn');
            const amountInput = document.getElementById('amount');
            const recipientInput = document.getElementById('recipient_user_id');

            // Wallet balances from PHP
            const depositBalance = <?php echo $deposit_balance; ?>;
            const withdrawalBalance = <?php echo $withdrawal_balance; ?>;

            let validationTimeout;
            let isValidRecipient = false;

            // Real-time recipient validation
            recipientInput.addEventListener('input', function() {
                const userId = this.value.trim();

                // Clear previous timeout
                if (validationTimeout) {
                    clearTimeout(validationTimeout);
                }

                // Reset validation state
                isValidRecipient = false;
                hideAllValidationMessages();

                if (userId === '') {
                    return;
                }

                // Show loading state
                showValidationMessage('loading');

                // Debounce validation
                validationTimeout = setTimeout(() => {
                    validateRecipient(userId);
                }, 500);
            });

            confirmBtn.addEventListener('click', () => {
                const recipient = document.getElementById('recipient_user_id').value;
                const amount = parseFloat(document.getElementById('amount').value);
                const walletTypeRadio = document.querySelector('input[name="wallet_type"]:checked');

                if (!recipient || !amount || !walletTypeRadio) {
                    alert('Please fill in all fields including wallet selection.');
                    return;
                }

                if (!isValidRecipient) {
                    alert('Please enter a valid recipient User ID. You can only transfer to your direct sponsor or downline members.');
                    return;
                }

                // Validate amount is multiple of 10
                if (amount <= 0) {
                    alert('Amount must be greater than $0.');
                    return;
                }
                if (amount < 10) {
                    alert('Minimum transfer amount is $10.');
                    return;
                }
                if (amount % 10 !== 0) {
                    alert('Amount must be a multiple of $10 (e.g., $10, $20, $30, $100).');
                    return;
                }

                const walletType = walletTypeRadio.value;

                // Check if selected wallet has sufficient balance
                const selectedBalance = walletType === 'deposit' ? depositBalance : withdrawalBalance;
                if (amount > selectedBalance) {
                    alert(`Insufficient balance in ${walletType} wallet. Available: $${selectedBalance.toFixed(4)}`);
                    return;
                }

                document.getElementById('confirm-recipient').innerText = recipient;
                document.getElementById('confirm-amount').innerText = '$' + amount.toFixed(2);
                modal.classList.remove('hidden');
            });

            cancelBtn.addEventListener('click', () => {
                modal.classList.add('hidden');
            });

            executeBtn.addEventListener('click', () => {
                const form = document.getElementById('transfer-form');
                const formData = new FormData(form);

                const xhr = new XMLHttpRequest();
                xhr.open('POST', '/netvis/ui/api/execute_transfer.php', true);
                xhr.onload = function() {
                    modal.classList.add('hidden');
                    if (xhr.status >= 200 && xhr.status < 400) {
                        try {
                            const data = JSON.parse(xhr.responseText);
                            alert(data.message);
                            if (data.success) {
                                window.location.reload();
                            }
                        } catch (e) {
                            console.error('Error parsing JSON:', e, xhr.responseText);
                            alert('An error occurred: Invalid response from server.');
                        }
                    } else {
                        console.error('Server error:', xhr.status, xhr.responseText);
                        alert('An error occurred during the transfer.');
                    }
                };
                xhr.onerror = function() {
                    modal.classList.add('hidden');
                    console.error('Network error.');
                    alert('An error occurred during the transfer.');
                };
                xhr.send(formData);
            });

            // Real-time amount validation
            amountInput.addEventListener('input', function() {
                const amount = parseFloat(this.value);

                // Remove previous validation classes
                this.classList.remove('border-red-500', 'border-green-500');

                if (this.value === '') {
                    return; // Don't validate empty input
                }

                if (isNaN(amount) || amount <= 0) {
                    this.classList.add('border-red-500');
                    return;
                }

                if (amount < 10) {
                    this.classList.add('border-red-500');
                    return;
                }

                if (amount % 10 !== 0) {
                    this.classList.add('border-red-500');
                    return;
                }

                // Valid amount
                this.classList.add('border-green-500');
            });

            function validateRecipient(userId) {
                fetch('/netvis/ui/api/validate_transfer_recipient.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            recipient_user_id: userId
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        hideAllValidationMessages();

                        if (data.success) {
                            isValidRecipient = true;
                            showRecipientInfo(data.user.name, data.relationship);
                            recipientInput.classList.remove('border-red-500');
                            recipientInput.classList.add('border-green-500');
                        } else {
                            isValidRecipient = false;
                            showValidationError(data.message);
                            recipientInput.classList.remove('border-green-500');
                            recipientInput.classList.add('border-red-500');
                        }
                    })
                    .catch(error => {
                        console.error('Validation error:', error);
                        hideAllValidationMessages();
                        showValidationError('Error validating recipient. Please try again.');
                        isValidRecipient = false;
                        recipientInput.classList.remove('border-green-500');
                        recipientInput.classList.add('border-red-500');
                    });
            }

            function showRecipientInfo(name, relationship) {
                const nameDisplay = document.getElementById('recipient-name-display');
                const relationshipDisplay = document.getElementById('recipient-relationship');

                nameDisplay.textContent = `✓ ${name}`;
                nameDisplay.className = 'text-sm font-medium text-green-600';
                nameDisplay.classList.remove('hidden');

                let relationshipText = '';
                let relationshipClass = '';

                if (relationship === 'sponsor') {
                    relationshipText = '👆 Your Direct Sponsor';
                    relationshipClass = 'text-xs mt-1 text-blue-600';
                } else if (relationship === 'downline') {
                    relationshipText = '👇 In Your Downline';
                    relationshipClass = 'text-xs mt-1 text-purple-600';
                }

                relationshipDisplay.textContent = relationshipText;
                relationshipDisplay.className = relationshipClass;
                relationshipDisplay.classList.remove('hidden');
            }

            function showValidationError(message) {
                const errorDisplay = document.getElementById('recipient-error');
                errorDisplay.textContent = `✗ ${message}`;
                errorDisplay.classList.remove('hidden');
            }

            function showValidationMessage(type) {
                const loadingDisplay = document.getElementById('recipient-loading');
                if (type === 'loading') {
                    loadingDisplay.classList.remove('hidden');
                }
            }

            function hideAllValidationMessages() {
                document.getElementById('recipient-name-display').classList.add('hidden');
                document.getElementById('recipient-relationship').classList.add('hidden');
                document.getElementById('recipient-error').classList.add('hidden');
                document.getElementById('recipient-loading').classList.add('hidden');
                recipientInput.classList.remove('border-red-500', 'border-green-500');
            }

            function loadTransferHistory() {
                const xhr = new XMLHttpRequest();
                xhr.open('GET', '/netvis/ui/api/get_transfer_history.php', true);
                xhr.onload = function() {
                    if (xhr.status >= 200 && xhr.status < 400) {
                        try {
                            const data = JSON.parse(xhr.responseText);
                            const historyBody = document.getElementById('history-body');
                            historyBody.innerHTML = '';
                            if (data.length === 0) {
                                historyBody.innerHTML = '<tr><td colspan="3" class="text-center p-4 text-gray-500">No transfer history.</td></tr>';
                            } else {
                                data.forEach(item => {
                                    const row = `<tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">${new Date(item.created_at).toLocaleDateString()}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-800">${item.recipient_name}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-right font-semibold text-red-600">$${parseFloat(item.amount).toFixed(4)}</td>
                            </tr>`;
                                    historyBody.innerHTML += row;
                                });
                            }
                        } catch (e) {
                            console.error('Error parsing JSON:', e, xhr.responseText);
                            const historyBody = document.getElementById('history-body');
                            historyBody.innerHTML = '<tr><td colspan="3" class="text-center p-4 text-red-500">Error loading history.</td></tr>';
                        }
                    } else {
                        console.error('Server error:', xhr.status, xhr.responseText);
                        const historyBody = document.getElementById('history-body');
                        historyBody.innerHTML = '<tr><td colspan="3" class="text-center p-4 text-red-500">Error loading history.</td></tr>';
                    }
                };
                xhr.onerror = function() {
                    console.error('Network error.');
                    const historyBody = document.getElementById('history-body');
                    historyBody.innerHTML = '<tr><td colspan="3" class="text-center p-4 text-red-500">Error loading history.</td></tr>';
                };
                xhr.send();
            }

            loadTransferHistory();

            const recipientUserInput = document.getElementById('recipient_user_id');
            const recipientNameDisplay = document.getElementById('recipient-name-display');

            let debounceTimer;
            recipientUserInput.addEventListener('input', () => {
                clearTimeout(debounceTimer);
                const userId = recipientUserInput.value;
                if (userId.length > 2) { // Start searching after 3 characters
                    debounceTimer = setTimeout(() => {
                        const xhr = new XMLHttpRequest();
                        xhr.open('GET', `/netvis/ui/api/get_user_name.php?user_id=${userId}`, true);
                        xhr.onload = function() {
                            if (xhr.status >= 200 && xhr.status < 400) {
                                try {
                                    const data = JSON.parse(xhr.responseText);
                                    if (data.success) {
                                        recipientNameDisplay.textContent = `Recipient: ${data.name}`;
                                        recipientNameDisplay.classList.remove('text-red-500');
                                        recipientNameDisplay.classList.add('text-green-600');
                                    } else {
                                        recipientNameDisplay.textContent = 'User not found.';
                                        recipientNameDisplay.classList.remove('text-green-600');
                                        recipientNameDisplay.classList.add('text-red-500');
                                    }
                                } catch (e) {
                                    console.error('Error parsing JSON:', e, xhr.responseText);
                                    recipientNameDisplay.textContent = 'Error fetching user name.';
                                    recipientNameDisplay.classList.remove('text-green-600');
                                    recipientNameDisplay.classList.add('text-red-500');
                                }
                            } else {
                                console.error('Server error:', xhr.status, xhr.responseText);
                                recipientNameDisplay.textContent = 'Error fetching user name.';
                                recipientNameDisplay.classList.remove('text-green-600');
                                recipientNameDisplay.classList.add('text-red-500');
                            }
                        };
                        xhr.onerror = function() {
                            console.error('Network error.');
                            recipientNameDisplay.textContent = 'Error fetching user name.';
                            recipientNameDisplay.classList.remove('text-green-600');
                            recipientNameDisplay.classList.add('text-red-500');
                        };
                        xhr.send();
                    }, 500); // 500ms debounce
                } else {
                    recipientNameDisplay.textContent = '';
                }
            });
        });
    </script>
</body>

</html>