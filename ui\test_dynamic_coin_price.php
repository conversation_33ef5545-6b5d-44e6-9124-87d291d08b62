<?php
session_start();

if (!isset($_SESSION['user_id'])) {
    header("Location: login.php");
    exit();
}

include 'dbcon.php';
include 'settings_helper.php';

echo "<h2>Dynamic Coin Price System Test</h2>";
echo "<p>This page tests the dynamic coin price system to ensure it's working correctly across all staking components.</p>";

// Test 1: Check if settings table exists
echo "<h3>1. Settings Table Check</h3>";
$table_check = $conn->query("SHOW TABLES LIKE 'system_settings'");
if ($table_check->num_rows > 0) {
    echo "<p>✅ Settings table exists</p>";

    // Check if staking settings exist
    $settings_check = $conn->query("SELECT COUNT(*) as count FROM system_settings WHERE setting_key LIKE 'staking_%'");
    $settings_count = $settings_check->fetch_assoc()['count'];
    echo "<p>✅ Found $settings_count staking-related settings</p>";
} else {
    echo "<p>❌ Settings table missing - <a href='create_settings_table.php' style='color: blue;'>Create it here</a></p>";
}

// Test 2: Get current settings using helper function
echo "<h3>2. Current Staking Settings</h3>";
try {
    $staking_settings = getStakingSettings($conn);
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr style='background: #f0f0f0;'><th>Setting</th><th>Value</th><th>Description</th></tr>";
    echo "<tr><td>Coin Price</td><td><strong>$" . number_format($staking_settings['coin_price'], 4) . "</strong></td><td>Price per staking coin in USD</td></tr>";
    echo "<tr><td>Min Coins</td><td><strong>{$staking_settings['min_coins']}</strong></td><td>Minimum coins required for staking</td></tr>";
    echo "<tr><td>Freeze Months</td><td><strong>{$staking_settings['freeze_months']}</strong></td><td>Number of months for freeze period</td></tr>";
    echo "<tr><td>Min USD Amount</td><td><strong>$" . number_format($staking_settings['min_usd_amount'], 0) . "</strong></td><td>Minimum USD amount (multiples required)</td></tr>";
    echo "</table>";
} catch (Exception $e) {
    echo "<p>❌ Error getting settings: " . $e->getMessage() . "</p>";
}

// Test 3: Test individual setting retrieval
echo "<h3>3. Individual Setting Tests</h3>";
$coin_price = getSetting($conn, 'staking_coin_price', 0.45);
$min_coins = getSetting($conn, 'staking_min_coins', 10);
$freeze_months = getSetting($conn, 'staking_freeze_months', 6);

echo "<ul>";
echo "<li>Coin Price: $" . number_format($coin_price, 4) . "</li>";
echo "<li>Min Coins: $min_coins</li>";
echo "<li>Freeze Months: $freeze_months</li>";
echo "</ul>";

// Test 4: Calculation examples
echo "<h3>4. Calculation Examples</h3>";
echo "<div style='background: #f0f8ff; padding: 15px; border: 1px solid #4169E1; margin: 10px 0;'>";
echo "<h4>With Current Coin Price ($" . number_format($coin_price, 4) . "):</h4>";
$examples = [10, 50, 100, 500];
echo "<table border='1' style='border-collapse: collapse;'>";
echo "<tr style='background: #f0f0f0;'><th>USD Amount</th><th>Coins Received</th><th>Exact Value</th></tr>";
foreach ($examples as $usd) {
    $coins = floor($usd / $coin_price);
    $exact_value = $coins * $coin_price;
    echo "<tr>";
    echo "<td>$" . number_format($usd, 2) . "</td>";
    echo "<td>$coins coins</td>";
    echo "<td>$" . number_format($exact_value, 2) . "</td>";
    echo "</tr>";
}
echo "</table>";
echo "</div>";

// Test 5: Test setting update (if admin)
if (isset($_SESSION['is_admin']) && $_SESSION['is_admin']) {
    echo "<h3>5. Admin Test - Update Coin Price</h3>";

    if (isset($_POST['test_update'])) {
        $test_price = floatval($_POST['test_price']);
        $admin_id = $_SESSION['user_id'];

        if (setSetting($conn, 'staking_coin_price', $test_price, 'number', $admin_id)) {
            echo "<p style='color: green;'>✅ Successfully updated coin price to $" . number_format($test_price, 4) . "</p>";
            echo "<p><a href='" . $_SERVER['PHP_SELF'] . "' style='color: blue;'>Refresh page to see changes</a></p>";
        } else {
            echo "<p style='color: red;'>❌ Failed to update coin price</p>";
        }
    }

    echo "<form method='POST'>";
    echo "<label>Test New Coin Price: $</label>";
    echo "<input type='number' name='test_price' step='0.0001' min='0.0001' value='" . ($coin_price + 0.01) . "' required>";
    echo "<button type='submit' name='test_update' style='background: #ff9800; color: white; padding: 5px 10px; border: none; cursor: pointer; margin-left: 10px;'>Test Update</button>";
    echo "</form>";
    echo "<p><small>This will actually update the coin price. Use with caution!</small></p>";
} else {
    echo "<h3>5. Admin Test</h3>";
    echo "<p>You need admin privileges to test price updates.</p>";
}

// Test 6: Check if files are using dynamic prices
echo "<h3>6. File Integration Check</h3>";
$files_to_check = [
    'staking.php' => 'User staking page',
    'admin_staking.php' => 'Admin staking page',
    'api/submit_staking_request.php' => 'Staking API'
];

echo "<table border='1' style='border-collapse: collapse;'>";
echo "<tr style='background: #f0f0f0;'><th>File</th><th>Description</th><th>Status</th></tr>";

foreach ($files_to_check as $file => $description) {
    if (file_exists($file)) {
        $content = file_get_contents($file);
        $has_settings_helper = strpos($content, 'settings_helper.php') !== false;
        $has_hardcoded = strpos($content, '$COIN_PRICE = 0.45') !== false;

        if ($has_settings_helper && !$has_hardcoded) {
            $status = "✅ Using dynamic settings";
            $color = "color: green;";
        } elseif ($has_settings_helper && $has_hardcoded) {
            $status = "⚠️ Partially updated";
            $color = "color: orange;";
        } else {
            $status = "❌ Still using hardcoded values";
            $color = "color: red;";
        }

        echo "<tr>";
        echo "<td>$file</td>";
        echo "<td>$description</td>";
        echo "<td style='$color'>$status</td>";
        echo "</tr>";
    } else {
        echo "<tr>";
        echo "<td>$file</td>";
        echo "<td>$description</td>";
        echo "<td style='color: gray;'>File not found</td>";
        echo "</tr>";
    }
}
echo "</table>";

// Test 7: Live price update test
echo "<h3>7. Live Price Update Test</h3>";
echo "<div id='live-test'>";
echo "<p>Current coin price: <span id='current-price'>$" . number_format($coin_price, 4) . "</span></p>";
echo "<button onclick='refreshPrice()' style='background: #2196F3; color: white; padding: 10px; border: none; cursor: pointer;'>Refresh Price</button>";
echo "<div id='refresh-result' style='margin-top: 10px;'></div>";
echo "</div>";

echo "<script>
function refreshPrice() {
    const resultDiv = document.getElementById('refresh-result');
    resultDiv.innerHTML = 'Refreshing...';
    
    fetch('api/get_coin_price.php')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                document.getElementById('current-price').textContent = '$' + parseFloat(data.coin_price).toFixed(4);
                resultDiv.innerHTML = '<span style=\"color: green;\">✅ Price refreshed successfully!</span>';
            } else {
                resultDiv.innerHTML = '<span style=\"color: red;\">❌ Failed to refresh price</span>';
            }
        })
        .catch(error => {
            resultDiv.innerHTML = '<span style=\"color: red;\">❌ Network error: ' + error.message + '</span>';
        });
}
</script>";

echo "<hr>";
echo "<h3>Summary</h3>";
echo "<div style='background: #e8f5e8; padding: 15px; border: 1px solid #4CAF50; margin: 10px 0;'>";
echo "<h4>✅ Dynamic Coin Price System Features:</h4>";
echo "<ul>";
echo "<li><strong>Database Storage</strong> - Coin price stored in system_settings table</li>";
echo "<li><strong>Helper Functions</strong> - Easy-to-use functions for getting/setting values</li>";
echo "<li><strong>Admin Interface</strong> - <a href='admin_coin_price.php' style='color: blue;'>admin_coin_price.php</a> for price management</li>";
echo "<li><strong>Real-time Updates</strong> - Changes reflect immediately across all pages</li>";
echo "<li><strong>Audit Trail</strong> - Track who changed prices and when</li>";
echo "<li><strong>Backward Compatibility</strong> - Fallback to default values if database unavailable</li>";
echo "</ul>";
echo "</div>";

echo "<h3>Quick Links</h3>";
echo "<ul>";
echo "<li><a href='admin_coin_price.php' style='color: blue;'>Admin Coin Price Manager</a></li>";
echo "<li><a href='staking.php' style='color: blue;'>User Staking Page</a></li>";
echo "<li><a href='admin_staking.php' style='color: blue;'>Admin Staking Page</a></li>";
echo "<li><a href='create_settings_table.php' style='color: blue;'>Create/Initialize Settings Table</a></li>";
echo "</ul>";

$conn->close();
