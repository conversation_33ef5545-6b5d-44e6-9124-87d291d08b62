<?php
session_start();

// Simple admin check - bypass for testing
$_SESSION['admin_logged_in'] = true;
$_SESSION['admin_id'] = 1;

// Include database connection
include 'dbcon.php';

echo "<h2>🔍 Admin Dashboard Debug</h2>";

// Test database connection
if ($conn->connect_error) {
    echo "<p style='color: red;'>❌ Database connection failed: " . $conn->connect_error . "</p>";
    exit();
} else {
    echo "<p style='color: green;'>✅ Database connected successfully</p>";
}

echo "<h3>🧪 Testing Dashboard Queries:</h3>";

// Test each query individually
$queries = [
    'Total Users' => "SELECT COUNT(*) as total FROM users WHERE is_admin = 0",
    'Active Users' => "SELECT COUNT(*) as active FROM users WHERE is_admin = 0 AND plan_status = 'active'",
    'Expired Users' => "SELECT COUNT(*) as expired FROM users WHERE is_admin = 0 AND plan_status = 'expired'",
    'New Users Today' => "SELECT COUNT(*) as new_today FROM users WHERE is_admin = 0 AND DATE(reg_date) = CURDATE()",
    'Total Deposits' => "SELECT COALESCE(SUM(amount), 0) as total FROM deposit_requests WHERE status = 'approved'",
    'Total Withdrawals' => "SELECT COALESCE(SUM(amount), 0) as total FROM withdrawal_requests WHERE status = 'approved'",
    'Pending Deposits' => "SELECT COUNT(*) as pending FROM deposit_requests WHERE status = 'pending'",
    'Pending Withdrawals' => "SELECT COUNT(*) as pending FROM withdrawal_requests WHERE status = 'pending'",
    'Total Packages' => "SELECT COUNT(*) as total FROM packages",
    'Total Investments' => "SELECT COALESCE(SUM(package_amount), 0) as total FROM package_history",
    'Active Staking Count' => "SELECT COUNT(*) as active FROM staking_records WHERE status = 'active'",
    'Active Staking Amount' => "SELECT COALESCE(SUM(amount_usd), 0) as total_amount FROM staking_records WHERE status = 'active'",
    'Deposit Wallet Balance' => "SELECT COALESCE(SUM(deposit_wallet_balance), 0) as total FROM users WHERE is_admin = 0",
    'Withdrawal Wallet Balance' => "SELECT COALESCE(SUM(withdrawal_wallet_balance), 0) as total FROM users WHERE is_admin = 0"
];

echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>";
echo "<tr style='background: #f3f4f6;'><th>Query</th><th>Result</th><th>Status</th></tr>";

$all_results = [];

foreach ($queries as $name => $query) {
    echo "<tr>";
    echo "<td><strong>$name</strong><br><small><code>$query</code></small></td>";

    $result = $conn->query($query);
    if ($result) {
        $data = $result->fetch_assoc();
        $value = $data[array_keys($data)[0]]; // Get first column value
        $all_results[$name] = $value;
        echo "<td style='color: green; font-weight: bold;'>" . number_format($value, 2) . "</td>";
        echo "<td style='color: green;'>✅ SUCCESS</td>";
    } else {
        $all_results[$name] = 0;
        echo "<td style='color: red;'>ERROR: " . $conn->error . "</td>";
        echo "<td style='color: red;'>❌ FAILED</td>";
    }
    echo "</tr>";
}

echo "</table>";

// Test recent activities queries
echo "<h3>📋 Recent Activities Queries:</h3>";

$activity_queries = [
    'Recent Users' => "SELECT id, name, user_id, package, reg_date FROM users WHERE is_admin = 0 ORDER BY reg_date DESC LIMIT 5",
    'Recent Deposits' => "SELECT dr.id, dr.user_id, dr.amount, dr.status, u.name FROM deposit_requests dr JOIN users u ON dr.user_id = u.id ORDER BY dr.id DESC LIMIT 5",
    'Recent Withdrawals' => "SELECT wr.id, wr.user_id, wr.amount, wr.status, u.name FROM withdrawal_requests wr JOIN users u ON wr.user_id = u.id ORDER BY wr.id DESC LIMIT 5"
];

foreach ($activity_queries as $name => $query) {
    echo "<h4>$name:</h4>";
    echo "<p><code>$query</code></p>";

    $result = $conn->query($query);
    if ($result) {
        echo "<p style='color: green;'>✅ SUCCESS! Rows: " . $result->num_rows . "</p>";
        if ($result->num_rows > 0) {
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; font-size: 0.9em;'>";

            // Get column names
            $fields = $result->fetch_fields();
            echo "<tr style='background: #f3f4f6;'>";
            foreach ($fields as $field) {
                echo "<th>" . $field->name . "</th>";
            }
            echo "</tr>";

            // Reset result pointer and show data
            $result->data_seek(0);
            while ($row = $result->fetch_assoc()) {
                echo "<tr>";
                foreach ($row as $value) {
                    echo "<td>" . htmlspecialchars($value) . "</td>";
                }
                echo "</tr>";
            }
            echo "</table>";
        }
    } else {
        echo "<p style='color: red;'>❌ FAILED: " . $conn->error . "</p>";
    }
}

// Show summary
echo "<div style='background: #e0f2fe; border: 2px solid #0ea5e9; border-radius: 12px; padding: 20px; margin: 20px 0;'>";
echo "<h3 style='color: #0c4a6e; margin-top: 0;'>📊 Dashboard Data Summary</h3>";

echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;'>";

$summary_items = [
    ['👥 Total Users', $all_results['Total Users'] ?? 0, 'blue'],
    ['✅ Active Users', $all_results['Active Users'] ?? 0, 'green'],
    ['❌ Expired Users', $all_results['Expired Users'] ?? 0, 'red'],
    ['💰 Total Deposits', '$' . number_format($all_results['Total Deposits'] ?? 0, 2), 'emerald'],
    ['💸 Total Withdrawals', '$' . number_format($all_results['Total Withdrawals'] ?? 0, 2), 'red'],
    ['🪙 Active Staking', '$' . number_format($all_results['Active Staking Amount'] ?? 0, 2), 'yellow']
];

foreach ($summary_items as $item) {
    $color = $item[2];
    echo "<div style='background: rgba(59, 130, 246, 0.1); border: 1px solid #3b82f6; border-radius: 8px; padding: 15px; text-align: center;'>";
    echo "<h5 style='color: #1e40af; margin: 0 0 8px 0;'>{$item[0]}</h5>";
    echo "<p style='margin: 0; color: #1f2937; font-weight: bold; font-size: 1.2em;'>{$item[1]}</p>";
    echo "</div>";
}

echo "</div>";

echo "<p style='color: #0369a1; text-align: center; margin-top: 20px;'>";
echo "<a href='admin_dashboard.php' style='background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%); color: white; padding: 15px 30px; text-decoration: none; border-radius: 12px; font-weight: 600; box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);'>🎯 Open Main Dashboard</a>";
echo "</p>";

echo "</div>";

$conn->close();
?>

<style>
    body {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        margin: 20px;
        background: #f8fafc;
        line-height: 1.6;
    }

    h2,
    h3,
    h4 {
        color: #1f2937;
    }

    table {
        font-size: 0.9em;
        max-width: 100%;
        overflow-x: auto;
    }

    th,
    td {
        padding: 8px 12px;
        text-align: left;
        border: 1px solid #e5e7eb;
    }

    th {
        background: #f9fafb;
        font-weight: 600;
    }

    code {
        background: #f1f5f9;
        padding: 2px 6px;
        border-radius: 4px;
        font-family: monospace;
        font-size: 0.8em;
    }
</style>