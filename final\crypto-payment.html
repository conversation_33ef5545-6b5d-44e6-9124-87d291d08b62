<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Crypto Payment - Add Funds</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <style>
        body {
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
            min-height: 100vh;
        }

        .stats-card {
            background: rgba(30, 41, 59, 0.4);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(59, 130, 246, 0.2);
            border-radius: 0.75rem;
            padding: 1rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }

        .qr-container {
            background: white;
            border-radius: 1rem;
            padding: 1rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .upload-area {
            background: rgba(15, 23, 42, 0.8);
            border: 2px dashed rgba(59, 130, 246, 0.3);
            border-radius: 0.75rem;
            padding: 2rem;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .upload-area:hover {
            border-color: rgba(6, 182, 212, 0.6);
            background: rgba(15, 23, 42, 0.9);
        }

        .upload-area.dragover {
            border-color: rgba(6, 182, 212, 0.8);
            background: rgba(6, 182, 212, 0.1);
        }

        .copy-btn {
            background: linear-gradient(135deg, #3b82f6, #06b6d4);
            border: none;
            border-radius: 0.5rem;
            padding: 0.5rem 1rem;
            color: white;
            font-weight: 600;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .copy-btn:hover {
            background: linear-gradient(135deg, #2563eb, #0891b2);
            transform: translateY(-1px);
        }

        .submit-btn {
            background: linear-gradient(135deg, #3b82f6, #06b6d4);
            border: none;
            border-radius: 0.75rem;
            padding: 1rem 2rem;
            color: white;
            font-weight: 600;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .submit-btn:hover {
            background: linear-gradient(135deg, #2563eb, #0891b2);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(6, 182, 212, 0.3);
        }

        .submit-btn:disabled {
            background: linear-gradient(135deg, #64748b, #475569);
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        /* Bottom Navigation Styles */
        .accent {
            color: #06b6d4 !important;
        }

        .hover\:accent:hover {
            color: #06b6d4 !important;
        }
    </style>
</head>

<body class="text-white">
    <div class="min-h-screen flex flex-col">
        <!-- Header -->
        <div class="sticky top-0 z-50 bg-slate-800/95 backdrop-blur-sm border-b border-slate-700">
            <div class="flex items-center justify-between px-6 py-4">
                <div class="flex items-center gap-4">
                    <button onclick="goBack()" class="w-10 h-10 rounded-full bg-slate-700 hover:bg-slate-600 flex items-center justify-center transition-colors">
                        <i class="fas fa-arrow-left text-slate-300"></i>
                    </button>
                    <div>
                        <h1 class="text-xl font-bold text-white">Crypto Payment</h1>
                        <p class="text-sm text-slate-400">Complete your payment</p>
                    </div>
                </div>
                <div class="flex items-center gap-2">
                    <div class="text-right">
                        <p class="text-xs text-slate-400">Amount</p>
                        <p class="text-lg font-bold text-cyan-400" id="paymentAmount">$0</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-grow overflow-y-auto px-4 sm:px-6 py-4">
            <!-- Payment Instructions -->
            <div class="stats-card mb-4">
                <div class="flex items-center gap-2 mb-4">
                    <div class="w-10 h-10 rounded-lg bg-gradient-to-r from-blue-600 to-cyan-600 flex items-center justify-center">
                        <i class="fas fa-info-circle text-white"></i>
                    </div>
                    <div>
                        <h2 class="text-lg font-bold text-white">Payment Instructions</h2>
                        <p class="text-xs text-slate-400">Follow these steps to complete payment</p>
                    </div>
                </div>
                <div class="space-y-3 text-sm">
                    <div class="flex items-start gap-3">
                        <div class="w-6 h-6 rounded-full bg-blue-600 flex items-center justify-center text-xs font-bold">1</div>
                        <span class="text-slate-300">Send <strong class="text-yellow-400" id="totalAmount">$0</strong> USDT to the BEP20 address below</span>
                    </div>
                    <div class="flex items-start gap-3">
                        <div class="w-6 h-6 rounded-full bg-blue-600 flex items-center justify-center text-xs font-bold">2</div>
                        <span class="text-slate-300">Take a screenshot of your transaction</span>
                    </div>
                    <div class="flex items-start gap-3">
                        <div class="w-6 h-6 rounded-full bg-blue-600 flex items-center justify-center text-xs font-bold">3</div>
                        <span class="text-slate-300">Upload the screenshot and submit for verification</span>
                    </div>
                </div>
            </div>

            <!-- BEP20 Address -->
            <div class="stats-card mb-4">
                <div class="flex items-center gap-2 mb-4">
                    <div class="w-10 h-10 rounded-lg bg-gradient-to-r from-yellow-600 to-orange-600 flex items-center justify-center">
                        <i class="fab fa-ethereum text-white"></i>
                    </div>
                    <div>
                        <h2 class="text-lg font-bold text-white">BEP20 Address</h2>
                        <p class="text-xs text-slate-400">Send USDT to this address</p>
                    </div>
                </div>
                
                <!-- QR Code -->
                <div class="flex justify-center mb-4">
                    <div class="qr-container">
                        <img src="https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=******************************************" alt="BEP20 QR Code" class="w-48 h-48">
                    </div>
                </div>

                <!-- Address -->
                <div class="bg-gradient-to-r from-slate-800/50 to-slate-700/50 rounded-lg p-4 border border-yellow-500/20">
                    <div class="flex items-center justify-between">
                        <div class="flex-1">
                            <p class="text-xs text-slate-400 mb-1">BEP20 Address:</p>
                            <p class="text-sm font-mono text-white break-all" id="bep20Address">******************************************</p>
                        </div>
                        <button onclick="copyAddress()" class="copy-btn ml-3">
                            <i class="fas fa-copy"></i>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Upload Screenshot -->
            <div class="stats-card mb-4">
                <div class="flex items-center gap-2 mb-4">
                    <div class="w-10 h-10 rounded-lg bg-gradient-to-r from-green-600 to-emerald-600 flex items-center justify-center">
                        <i class="fas fa-camera text-white"></i>
                    </div>
                    <div>
                        <h2 class="text-lg font-bold text-white">Upload Screenshot</h2>
                        <p class="text-xs text-slate-400">Upload your transaction screenshot</p>
                    </div>
                </div>

                <div class="upload-area" onclick="document.getElementById('fileInput').click()" ondrop="handleDrop(event)" ondragover="handleDragOver(event)" ondragleave="handleDragLeave(event)">
                    <input type="file" id="fileInput" accept="image/*" style="display: none;" onchange="handleFileSelect(event)">
                    <div id="uploadContent">
                        <i class="fas fa-cloud-upload-alt text-4xl text-cyan-400 mb-4"></i>
                        <p class="text-lg font-semibold text-white mb-2">Upload Transaction Screenshot</p>
                        <p class="text-sm text-slate-400 mb-4">Drag and drop or click to select</p>
                        <p class="text-xs text-slate-500">Supported: JPG, PNG, GIF (Max 5MB)</p>
                    </div>
                </div>

                <div id="uploadedFile" style="display: none;" class="mt-4 p-4 bg-slate-800/50 rounded-lg border border-green-500/20">
                    <div class="flex items-center gap-3">
                        <i class="fas fa-check-circle text-green-400"></i>
                        <div>
                            <p class="text-sm font-semibold text-white" id="fileName"></p>
                            <p class="text-xs text-slate-400" id="fileSize"></p>
                        </div>
                        <button onclick="removeFile()" class="ml-auto text-red-400 hover:text-red-300">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Submit Button -->
            <div class="stats-card">
                <button onclick="submitPayment()" id="submitBtn" disabled class="submit-btn w-full text-lg flex items-center justify-center gap-3">
                    <i class="fas fa-paper-plane"></i>
                    <span>Submit Payment Proof</span>
                </button>
                <p class="text-xs text-slate-500 text-center mt-3">🔒 Your payment will be verified within 24 hours</p>
            </div>
        </div>

        <!-- Sticky Bottom Navigation Bar -->
        <div class="sticky bottom-0 z-40 flex-shrink-0">
            <div class="bg-slate-800/95 backdrop-blur-sm mx-4 mb-4 rounded-2xl border border-slate-700">
                <div class="flex justify-around items-center h-16">
                    <a href="1.html" class="flex flex-col items-center gap-1 text-slate-400 hover:accent">
                        <i class="fas fa-home"></i>
                        <span class="text-xs">Home</span>
                    </a>
                    <a href="wallet.html" class="flex flex-col items-center gap-1 accent">
                        <i class="fas fa-wallet"></i>
                        <span class="text-xs">Wallet</span>
                    </a>
                    <a href="team.html" class="flex flex-col items-center gap-1 text-slate-400 hover:accent">
                        <i class="fas fa-users"></i>
                        <span class="text-xs">Team</span>
                    </a>
                    <a href="profile.html" class="flex flex-col items-center gap-1 text-slate-400 hover:accent">
                        <i class="fas fa-user"></i>
                        <span class="text-xs">Profile</span>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script>
        let selectedFile = null;
        let paymentAmount = 0;

        function goBack() {
            window.history.back();
        }

        function getUrlParameter(name) {
            name = name.replace(/[\[]/, '\\[').replace(/[\]]/, '\\]');
            var regex = new RegExp('[\\?&]' + name + '=([^&#]*)');
            var results = regex.exec(location.search);
            return results === null ? '' : decodeURIComponent(results[1].replace(/\+/g, ' '));
        }

        function copyAddress() {
            const address = document.getElementById('bep20Address').textContent;
            navigator.clipboard.writeText(address).then(() => {
                Swal.fire({
                    icon: 'success',
                    title: 'Address Copied!',
                    text: 'BEP20 address has been copied to clipboard',
                    background: '#1e293b',
                    color: '#ffffff',
                    confirmButtonColor: '#06b6d4',
                    iconColor: '#06b6d4',
                    timer: 2000,
                    showConfirmButton: false
                });
            });
        }

        function handleDragOver(e) {
            e.preventDefault();
            e.currentTarget.classList.add('dragover');
        }

        function handleDragLeave(e) {
            e.preventDefault();
            e.currentTarget.classList.remove('dragover');
        }

        function handleDrop(e) {
            e.preventDefault();
            e.currentTarget.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleFile(files[0]);
            }
        }

        function handleFileSelect(e) {
            const file = e.target.files[0];
            if (file) {
                handleFile(file);
            }
        }

        function handleFile(file) {
            // Validate file type
            if (!file.type.startsWith('image/')) {
                Swal.fire({
                    icon: 'error',
                    title: 'Invalid File Type',
                    text: 'Please upload an image file (JPG, PNG, GIF)',
                    background: '#1e293b',
                    color: '#ffffff',
                    confirmButtonColor: '#3b82f6',
                    iconColor: '#3b82f6'
                });
                return;
            }

            // Validate file size (5MB)
            if (file.size > 5 * 1024 * 1024) {
                Swal.fire({
                    icon: 'error',
                    title: 'File Too Large',
                    text: 'Please upload an image smaller than 5MB',
                    background: '#1e293b',
                    color: '#ffffff',
                    confirmButtonColor: '#3b82f6',
                    iconColor: '#3b82f6'
                });
                return;
            }

            selectedFile = file;
            
            // Show uploaded file info
            document.getElementById('uploadContent').style.display = 'none';
            document.getElementById('uploadedFile').style.display = 'block';
            document.getElementById('fileName').textContent = file.name;
            document.getElementById('fileSize').textContent = `${(file.size / 1024 / 1024).toFixed(2)} MB`;
            
            // Enable submit button
            document.getElementById('submitBtn').disabled = false;
        }

        function removeFile() {
            selectedFile = null;
            document.getElementById('uploadContent').style.display = 'block';
            document.getElementById('uploadedFile').style.display = 'none';
            document.getElementById('fileInput').value = '';
            document.getElementById('submitBtn').disabled = true;
        }

        function submitPayment() {
            if (!selectedFile) {
                Swal.fire({
                    icon: 'warning',
                    title: 'No Screenshot',
                    text: 'Please upload a transaction screenshot',
                    background: '#1e293b',
                    color: '#ffffff',
                    confirmButtonColor: '#06b6d4',
                    iconColor: '#06b6d4'
                });
                return;
            }

            Swal.fire({
                icon: 'success',
                title: 'Payment Submitted!',
                html: `
                    <div class="text-center space-y-2">
                        <p>Your payment proof has been submitted successfully.</p>
                        <p class="text-sm text-slate-400">Amount: <strong class="text-cyan-400">$${paymentAmount + 10}</strong></p>
                        <p class="text-sm text-slate-400">We will verify your payment within 24 hours.</p>
                        <p class="text-sm text-slate-400">You will receive a notification once approved.</p>
                    </div>
                `,
                background: '#1e293b',
                color: '#ffffff',
                confirmButtonColor: '#06b6d4',
                confirmButtonText: 'Continue',
                iconColor: '#06b6d4'
            }).then(() => {
                window.location.href = 'wallet.html';
            });
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            paymentAmount = parseInt(getUrlParameter('amount')) || 0;
            const totalAmount = paymentAmount + 10; // Add $10 for fees
            
            document.getElementById('paymentAmount').textContent = `$${paymentAmount}`;
            document.getElementById('totalAmount').textContent = `$${totalAmount}`;
        });
    </script>
</body>

</html>
