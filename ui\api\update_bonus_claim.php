<?php
session_start();

header('Content-Type: application/json');

if (!isset($_SESSION['user_id']) || !$_SESSION['is_admin']) {
    echo json_encode(['error' => 'Unauthorized']);
    exit();
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST' || !isset($_POST['claim_id']) || !isset($_POST['status'])) {
    echo json_encode(['error' => 'Invalid request']);
    exit();
}

$servername = "localhost";
$username = "root";
$password = "";
$dbname = "crypto";

$conn = new mysqli($servername, $username, $password, $dbname);
if ($conn->connect_error) {
    echo json_encode(['error' => 'Database connection failed']);
    exit();
}

$claim_id = (int)$_POST['claim_id'];
$new_status = $_POST['status'];
$admin_id = $_SESSION['user_id'];

if (!in_array($new_status, ['approved', 'rejected'])) {
    echo json_encode(['error' => 'Invalid status']);
    exit();
}

$conn->begin_transaction();

try {
    // Fetch claim details
    $claim_sql = "SELECT bc.user_id, b.bonus_fund AS amount, b.bonus_name FROM bonus_claims bc JOIN bonuses b ON bc.bonus_id = b.id WHERE bc.id = ? AND bc.status = 'pending'";
    $stmt = $conn->prepare($claim_sql);
    $stmt->bind_param('i', $claim_id);
    $stmt->execute();
    $result = $stmt->get_result();
    if ($result->num_rows === 0) {
        throw new Exception('Claim not found or already processed.');
    }
    $claim = $result->fetch_assoc();
    $user_id_to_credit = $claim['user_id'];
    $bonus_amount = $claim['amount'];
    $bonus_name = $claim['bonus_name'];
    $stmt->close();

    // Update claim status
    $update_sql = "UPDATE bonus_claims SET status = ?, reviewed_by = ?, reviewed_at = NOW() WHERE id = ?";
    $stmt = $conn->prepare($update_sql);
    $stmt->bind_param('sii', $new_status, $admin_id, $claim_id);
    $stmt->execute();
    $stmt->close();

    // If approved, add bonus amount to user's wallet and log the transaction
    if ($new_status === 'approved') {
        // 1. Update user's total wallet balance
        $wallet_sql = "UPDATE users SET wallet_balance = wallet_balance + ? WHERE id = ?";
        $stmt = $conn->prepare($wallet_sql);
        $stmt->bind_param('di', $bonus_amount, $user_id_to_credit);
        $stmt->execute();
        $stmt->close();

        // 2. Insert a transaction record into the wallet table
        $description = "Approved bonus: " . $bonus_name;
        $wallet_insert_sql = "INSERT INTO wallet (user_id, created_by, amount, type, description) VALUES (?, ?, ?, 'bonus', ?)";
        $stmt = $conn->prepare($wallet_insert_sql);
        $stmt->bind_param('iids', $user_id_to_credit, $admin_id, $bonus_amount, $description);
        $stmt->execute();
        $stmt->close();
    }

    $conn->commit();
    echo json_encode(['success' => true]);

} catch (Exception $e) {
    $conn->rollback();
    echo json_encode(['error' => $e->getMessage()]);
}

$conn->close();
?>
