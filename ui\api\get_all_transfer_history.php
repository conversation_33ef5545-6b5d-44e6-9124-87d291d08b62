<?php
ini_set('display_errors', 0);
ini_set('log_errors', 1);
ini_set('error_log', 'c:/xampp/htdocs/netvis/php_errors.log');
error_reporting(E_ALL);
session_start();
header('Content-Type: application/json');

// Admin check - assumes a 'role' session variable is set to 'admin' upon login
if (!isset($_SESSION['user_id']) || !isset($_SESSION['role']) || $_SESSION['role'] !== 'admin') {
    http_response_code(403);  // Forbidden
    echo json_encode(['error' => 'Access denied.']);
    exit();
}

include '../dbcon.php';

// Fetch all fund transfer debit records, joining to get sender and recipient names
$sql = "SELECT 
            wh.amount, 
            wh.created_at, 
            sender.name AS sender_name,
            recipient.name AS recipient_name
        FROM 
            wallet_history wh
        JOIN 
            users sender ON wh.user_id = sender.id
        LEFT JOIN 
            users recipient ON wh.related_user_id = recipient.id
        WHERE 
            wh.type = 'fund_transfer_debit'
        ORDER BY 
            wh.created_at DESC";

$stmt = $conn->prepare($sql);
if ($stmt === false) {
    echo json_encode([]);
    exit();
}

if (!$stmt->execute()) {
    echo json_encode([]);
    exit();
}

$stmt->store_result();
$stmt->bind_result($amount, $created_at, $sender_name, $recipient_name);

$history = [];
while ($stmt->fetch()) {
    $history[] = [
        'amount' => abs($amount),
        'created_at' => $created_at,
        'sender_name' => $sender_name,
        'recipient_name' => $recipient_name ?? 'N/A'
    ];
}

$stmt->close();
$conn->close();

echo json_encode($history);
?>
