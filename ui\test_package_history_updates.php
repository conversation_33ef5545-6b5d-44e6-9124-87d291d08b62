<?php
session_start();

// Check if user is admin
if (!isset($_SESSION['user_id']) || !$_SESSION['is_admin']) {
    header("Location: login.php");
    exit();
}

include 'dbcon.php';

echo "<h2>Test Package History Updates</h2>";
echo "<p>This script tests if package history is being updated correctly with daily profits and network income.</p>";

// 1. Show current package history before running earnings
echo "<h3>1. Current Package History (Before Earnings)</h3>";
$before_sql = "SELECT ph.id, ph.user_id, u.name, u.user_id as user_identifier, ph.package_name, 
               ph.total_earnings_received, ph.status, u.total_earnings_received as user_total_earnings
               FROM package_history ph 
               JOIN users u ON ph.user_id = u.id 
               WHERE ph.status = 'active' 
               ORDER BY ph.user_id 
               LIMIT 10";

$before_result = $conn->query($before_sql);
if ($before_result->num_rows > 0) {
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>History ID</th><th>User</th><th>Package</th><th>Package History Earnings</th><th>User Total Earnings</th><th>Status</th></tr>";
    while ($row = $before_result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>{$row['id']}</td>";
        echo "<td>{$row['name']} ({$row['user_identifier']})</td>";
        echo "<td>{$row['package_name']}</td>";
        echo "<td>$" . number_format($row['total_earnings_received'], 2) . "</td>";
        echo "<td>$" . number_format($row['user_total_earnings'], 2) . "</td>";
        echo "<td>{$row['status']}</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>No active package history records found.</p>";
}

// 2. Button to run earnings calculation
echo "<h3>2. Run Earnings Calculation</h3>";
if (isset($_POST['run_earnings'])) {
    echo "<div style='background: #f0f8ff; padding: 10px; border: 1px solid #ccc; margin: 10px 0;'>";
    echo "<h4>Running Earnings Calculation...</h4>";

    // Include and run the earnings calculation
    ob_start();
    include 'calculate_earnings.php';
    $earnings_output = ob_get_clean();

    if (!empty($earnings_output)) {
        echo "<pre>$earnings_output</pre>";
    } else {
        echo "<p>✅ Earnings calculation completed successfully!</p>";
    }
    echo "</div>";
}

echo "<form method='POST'>";
echo "<button type='submit' name='run_earnings' style='background: green; color: white; padding: 10px; border: none; cursor: pointer; margin: 10px 0;'>Run Daily Earnings Calculation</button>";
echo "</form>";

// 3. Show package history after running earnings (if earnings were run)
if (isset($_POST['run_earnings'])) {
    echo "<h3>3. Package History After Earnings Calculation</h3>";
    $after_result = $conn->query($before_sql); // Same query as before
    if ($after_result->num_rows > 0) {
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>History ID</th><th>User</th><th>Package</th><th>Package History Earnings</th><th>User Total Earnings</th><th>Status</th><th>Sync Status</th></tr>";
        while ($row = $after_result->fetch_assoc()) {
            // Check if package history matches user earnings (should be identical now)
            $sync_status = "✅ In Sync";
            if (abs($row['total_earnings_received'] - $row['user_total_earnings']) > 0.01) {
                $sync_status = "❌ Out of Sync";
            }

            echo "<tr>";
            echo "<td>{$row['id']}</td>";
            echo "<td>{$row['name']} ({$row['user_identifier']})</td>";
            echo "<td>{$row['package_name']}</td>";
            echo "<td>$" . number_format($row['total_earnings_received'], 2) . "</td>";
            echo "<td>$" . number_format($row['user_total_earnings'], 2) . "</td>";
            echo "<td>{$row['status']}</td>";
            echo "<td>$sync_status</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>No active package history records found after earnings calculation.</p>";
    }
}

// 4. Show recent wallet history to verify earnings were distributed
echo "<h3>4. Recent Wallet History (Last 20 transactions)</h3>";
$wallet_sql = "SELECT wh.*, u.name, u.user_id as user_identifier 
               FROM wallet_history wh 
               JOIN users u ON wh.user_id = u.id 
               WHERE wh.type IN ('daily_profit', 'network_commission') 
               ORDER BY wh.created_at DESC 
               LIMIT 20";

$wallet_result = $conn->query($wallet_sql);
if ($wallet_result->num_rows > 0) {
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>User</th><th>Amount</th><th>Type</th><th>Description</th><th>Date</th></tr>";
    while ($row = $wallet_result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>{$row['name']} ({$row['user_identifier']})</td>";
        echo "<td>$" . number_format($row['amount'], 2) . "</td>";
        echo "<td>{$row['type']}</td>";
        echo "<td>{$row['description']}</td>";
        echo "<td>" . date('M j, Y H:i:s', strtotime($row['created_at'])) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>No recent profit/commission transactions found.</p>";
}

// 5. Sync check - compare user earnings with package history
echo "<h3>5. Sync Check - User vs Package History Earnings</h3>";
$sync_check_sql = "SELECT u.id, u.name, u.user_id as user_identifier, u.total_earnings_received as user_earnings,
                   ph.total_earnings_received as history_earnings, ph.status,
                   (u.total_earnings_received - ph.total_earnings_received) as difference
                   FROM users u 
                   JOIN package_history ph ON u.id = ph.user_id 
                   WHERE ph.status = 'active' AND u.plan_status = 'active'
                   ORDER BY difference DESC";

$sync_result = $conn->query($sync_check_sql);
if ($sync_result->num_rows > 0) {
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>User</th><th>User Earnings</th><th>History Earnings</th><th>Difference</th><th>Status</th></tr>";
    while ($row = $sync_result->fetch_assoc()) {
        $diff_color = abs($row['difference']) < 0.01 ? 'green' : 'red';
        $status_icon = abs($row['difference']) < 0.01 ? '✅' : '❌';

        echo "<tr>";
        echo "<td>{$row['name']} ({$row['user_identifier']})</td>";
        echo "<td>$" . number_format($row['user_earnings'], 2) . "</td>";
        echo "<td>$" . number_format($row['history_earnings'], 2) . "</td>";
        echo "<td style='color: $diff_color;'>$" . number_format($row['difference'], 2) . "</td>";
        echo "<td>$status_icon</td>";
        echo "</tr>";
    }
    echo "</table>";

    echo "<p><strong>Legend:</strong></p>";
    echo "<ul>";
    echo "<li>✅ = Earnings are in sync (difference < $0.01)</li>";
    echo "<li>❌ = Earnings are out of sync - package history needs update</li>";
    echo "</ul>";
} else {
    echo "<p>No active users with package history found for sync check.</p>";
}

// 6. Manual sync button
echo "<h3>6. Manual Sync (if needed)</h3>";
if (isset($_POST['manual_sync'])) {
    $sync_sql = "UPDATE package_history ph 
                 JOIN users u ON ph.user_id = u.id 
                 SET ph.total_earnings_received = u.total_earnings_received 
                 WHERE ph.status = 'active' AND u.plan_status = 'active'";

    if ($conn->query($sync_sql)) {
        $affected = $conn->affected_rows;
        echo "<p style='color: green;'>✅ Manually synced $affected package history records with user earnings.</p>";
    } else {
        echo "<p style='color: red;'>❌ Manual sync failed: " . $conn->error . "</p>";
    }
}

echo "<form method='POST'>";
echo "<button type='submit' name='manual_sync' style='background: orange; color: white; padding: 10px; border: none; cursor: pointer;'>Manual Sync Package History</button>";
echo "</form>";

echo "<hr>";
echo "<h3>Summary</h3>";
echo "<p>This test page helps you verify that:</p>";
echo "<ul>";
echo "<li><strong>Daily profits</strong> are being added to both user earnings and package history</li>";
echo "<li><strong>Network commissions</strong> are being added to sponsor package history</li>";
echo "<li><strong>Package history</strong> stays in sync with user total earnings</li>";
echo "<li><strong>Plan expiration</strong> updates package history correctly</li>";
echo "</ul>";

$conn->close();
