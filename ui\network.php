<?php
session_start();

if (!isset($_SESSION['user_id']) || !$_SESSION['is_admin']) {
    header('Location: login.php');
    exit();
}

include 'dbcon.php';

// Get current view parameters
$current_user_id = $_GET['user_id'] ?? null;
$search = $_GET['search'] ?? '';

// Helper function to get user data with downline info
function getUserWithDownline($conn, $user_id = null, $search = '')
{
    $where_conditions = ["u.is_active = 1"];
    $params = [];
    $types = '';

    if ($user_id) {
        $where_conditions[] = "u.sponser_id = ?";
        $params[] = $user_id;
        $types .= 'i';
    } else {
        $where_conditions[] = "u.sponser_id IS NULL";
    }

    if ($search) {
        $where_conditions[] = "(u.name LIKE ? OR u.user_id LIKE ?)";
        $search_term = "%$search%";
        $params = array_merge($params, [$search_term, $search_term]);
        $types .= 'ss';
    }

    $where_clause = implode(' AND ', $where_conditions);

    $sql = "SELECT u.id, u.name, u.user_id, u.sponser_id, u.package, u.deposit_wallet_balance, u.withdrawal_wallet_balance,
                   (SELECT COUNT(*) FROM users WHERE sponser_id = u.id AND is_active = 1) as direct_count,
                   (SELECT COUNT(*) FROM users WHERE sponser_id = u.id AND is_active = 1 AND package IS NOT NULL AND package != '') as active_count
            FROM users u
            WHERE $where_clause
            ORDER BY u.id DESC";

    $stmt = $conn->prepare($sql);
    if ($params) {
        $stmt->bind_param($types, ...$params);
    }
    $stmt->execute();
    return $stmt->get_result()->fetch_all(MYSQLI_ASSOC);
}

// Get current level users
$users = getUserWithDownline($conn, $current_user_id, $search);

// Get breadcrumb path
$breadcrumb = [];
if ($current_user_id) {
    $temp_id = $current_user_id;
    while ($temp_id) {
        $stmt = $conn->prepare("SELECT id, name, user_id, sponser_id FROM users WHERE id = ?");
        $stmt->bind_param('i', $temp_id);
        $stmt->execute();
        $user_data = $stmt->get_result()->fetch_assoc();
        if ($user_data) {
            array_unshift($breadcrumb, $user_data);
            $temp_id = $user_data['sponser_id'];
        } else {
            break;
        }
    }
}

// Page configuration
$page_title = "Network Management";
$additional_css = '
<style>
.network-card { background: white; border: 1px solid #e5e7eb; border-radius: 0.75rem; padding: 1rem; margin-bottom: 1rem; transition: all 0.2s ease; cursor: pointer; }
.network-card:hover { transform: translateY(-1px); box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); border-color: #3b82f6; }
.user-avatar { width: 2.5rem; height: 2.5rem; background: linear-gradient(135deg, #3b82f6, #6366f1); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-weight: 600; font-size: 0.875rem; }
.stat-badge { display: inline-flex; align-items: center; padding: 0.25rem 0.5rem; border-radius: 0.375rem; font-size: 0.75rem; font-weight: 500; }
.stat-badge.primary { background: #dbeafe; color: #1d4ed8; }
.stat-badge.success { background: #dcfce7; color: #166534; }
.stat-badge.warning { background: #fef3c7; color: #d97706; }
.breadcrumb { display: flex; align-items: center; space-x: 0.5rem; margin-bottom: 1rem; padding: 0.75rem 1rem; background: #f8fafc; border-radius: 0.5rem; }
.breadcrumb-item { color: #6b7280; font-size: 0.875rem; }
.breadcrumb-item.active { color: #374151; font-weight: 500; }
.breadcrumb-separator { color: #9ca3af; margin: 0 0.5rem; }
.search-container { background: white; border: 1px solid #e5e7eb; border-radius: 0.75rem; padding: 1rem; margin-bottom: 1rem; }
.search-input { width: 100%; padding: 0.5rem 0.75rem; border: 1px solid #d1d5db; border-radius: 0.375rem; font-size: 0.875rem; }
.search-input:focus { outline: none; border-color: #3b82f6; box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1); }
.empty-state { text-align: center; padding: 3rem 1rem; color: #6b7280; }
</style>
';

// Helper function to get user initials
function getUserInitials($name)
{
    $words = explode(' ', trim($name));
    if (count($words) >= 2) {
        return strtoupper(substr($words[0], 0, 1) . substr($words[1], 0, 1));
    }
    return strtoupper(substr($name, 0, 2));
}

// Start output buffering
ob_start();
?>

<!-- Page Header -->
<div class="flex justify-between items-center mb-6">
    <div>
        <h1 class="text-2xl font-bold text-gray-900">Network Management</h1>
        <p class="text-gray-600">Browse and manage user network hierarchy</p>
    </div>
    <div class="flex items-center space-x-2 bg-blue-50 px-3 py-2 rounded-lg">
        <i class="fas fa-sitemap text-blue-600"></i>
        <span class="text-sm font-medium text-blue-700">Network Tree</span>
    </div>
</div>

<!-- Breadcrumb Navigation -->
<?php if (!empty($breadcrumb)): ?>
    <div class="breadcrumb">
        <a href="network.php" class="breadcrumb-item">Root</a>
        <?php foreach ($breadcrumb as $crumb): ?>
            <span class="breadcrumb-separator">/</span>
            <?php if ($crumb['id'] == $current_user_id): ?>
                <span class="breadcrumb-item active"><?= htmlspecialchars($crumb['name']) ?></span>
            <?php else: ?>
                <a href="network.php?user_id=<?= $crumb['id'] ?>" class="breadcrumb-item"><?= htmlspecialchars($crumb['name']) ?></a>
            <?php endif; ?>
        <?php endforeach; ?>
    </div>
<?php endif; ?>

<!-- Search Section -->
<div class="search-container">
    <form method="GET" class="flex items-center gap-3">
        <?php if ($current_user_id): ?>
            <input type="hidden" name="user_id" value="<?= $current_user_id ?>">
        <?php endif; ?>
        <div class="flex-1">
            <input type="text" name="search" value="<?= htmlspecialchars($search) ?>"
                class="search-input" placeholder="Search by name or user ID...">
        </div>
        <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
            <i class="fas fa-search mr-2"></i>Search
        </button>
        <?php if ($search || $current_user_id): ?>
            <a href="network.php" class="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors">
                <i class="fas fa-undo mr-2"></i>Reset
            </a>
        <?php endif; ?>
    </form>
</div>

<!-- Users Grid -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
    <?php if (empty($users)): ?>
        <div class="col-span-full empty-state">
            <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <i class="fas fa-users text-gray-400 text-2xl"></i>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mb-2">No Users Found</h3>
            <p class="text-gray-500">
                <?= $search ? 'No users match your search criteria.' : 'No users found at this level.' ?>
            </p>
        </div>
    <?php else: ?>
        <?php foreach ($users as $user): ?>
            <div class="network-card" onclick="viewDownline(<?= $user['id'] ?>)">
                <div class="flex items-start space-x-3">
                    <div class="user-avatar">
                        <?= getUserInitials($user['name']) ?>
                    </div>
                    <div class="flex-1 min-w-0">
                        <div class="flex items-center justify-between mb-2">
                            <h3 class="font-medium text-gray-900 truncate"><?= htmlspecialchars($user['name']) ?></h3>
                            <?php if ($user['direct_count'] > 0): ?>
                                <span class="stat-badge primary">
                                    <i class="fas fa-users mr-1"></i><?= $user['direct_count'] ?>
                                </span>
                            <?php endif; ?>
                        </div>
                        <p class="text-sm text-gray-500 mb-2">ID: <?= htmlspecialchars($user['user_id']) ?></p>

                        <div class="flex items-center space-x-2 text-xs">
                            <?php if ($user['package']): ?>
                                <span class="stat-badge success">Active Package</span>
                            <?php else: ?>
                                <span class="stat-badge warning">No Package</span>
                            <?php endif; ?>

                            <?php if ($user['active_count'] > 0): ?>
                                <span class="stat-badge primary"><?= $user['active_count'] ?> Active</span>
                            <?php endif; ?>
                        </div>

                        <div class="mt-2 text-xs text-gray-500">
                            Deposit: $<?= number_format($user['deposit_wallet_balance'], 2) ?> |
                            Withdrawal: $<?= number_format($user['withdrawal_wallet_balance'], 2) ?>
                        </div>
                    </div>
                </div>
            </div>
        <?php endforeach; ?>
    <?php endif; ?>
</div>

<script>
    function viewDownline(userId) {
        window.location.href = 'network.php?user_id=' + userId;
    }

    // Add keyboard navigation
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && window.location.search.includes('user_id')) {
            window.location.href = 'network.php';
        }
    });

    // Add loading state for cards
    document.querySelectorAll('.network-card').forEach(card => {
        card.addEventListener('click', function() {
            this.style.opacity = '0.7';
            this.style.transform = 'scale(0.98)';
        });
    });
</script>

<?php
// Capture the page content
$page_content = ob_get_clean();

// Close database connection
$conn->close();

// Include the admin layout
include 'admin/includes/layout.php';
?>