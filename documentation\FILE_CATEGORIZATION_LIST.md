# NetVis Crypto Platform - Complete File Categorization

## Overview
This document categorizes all files in the NetVis Crypto Platform by their purpose and target users.

---

## 🔵 USER SIDE FILES (Frontend for Regular Users)
*Files accessible to regular users for their daily operations*

### Core User Pages
- `ui/account.php` - User account management and profile
- `ui/invest.php` - Package purchase and investment page
- `ui/wallet.php` - User wallet dashboard and balance view
- `ui/package_history.php` - User's package purchase history
- `ui/network.php` - User's network structure and referrals
- `ui/team.php` - Team management and overview
- `ui/staking.php` - Staking interface with dynamic pricing
- `ui/deposit_fund.php` - Deposit request submission
- `ui/withdrawal.php` - Withdrawal request submission
- `ui/fund_transfer.php` - Transfer funds to sponsor/downline
- `ui/bonus_claims.php` - View and claim available bonuses
- `ui/change-password.php` - User password change

### User Authentication
- `ui/login.php` - User login page
- `ui/register.html` - User registration page
- `ui/register.php` - User registration processing
- `ui/login_process.php` - Login authentication processing
- `ui/logout.php` - User logout functionality

### User Navigation & Assets
- `ui/user_nav.php` - User navigation menu component
- `ui/js/` - JavaScript files for user interface

**Total User Files: 16**

---

## 🔴 ADMIN SIDE FILES (Backend Administration)
*Files accessible only to administrators for system management*

### Core Admin Pages
- `ui/admin.php` - Main admin dashboard
- `ui/admin_deposit_requests.php` - Manage deposit requests
- `ui/admin_withdrawal_requests.php` - Manage withdrawal requests
- `ui/admin_staking.php` - Staking management with dynamic pricing
- `ui/admin_staking_claims.php` - Approve/reject staking claims
- `ui/admin_package_history.php` - View all package history
- `ui/admin_coin_price.php` - Dynamic coin price management
- `ui/coin_price_graph.php` - Price trajectory visualization
- `ui/package_manager.php` - Package CRUD operations
- `ui/level_management.php` - Network level management
- `ui/bonus_management.php` - Bonus system management
- `ui/manage_investments.php` - Investment oversight

### Admin User Management
- `ui/create_user.php` - Create new users
- `ui/edit_user.php` - Edit existing users
- `ui/edit_package.php` - Edit package details
- `ui/admin_change_password.php` - Admin password change

### Admin Financial Operations
- `ui/admin_add_fund.php` - Add funds to user accounts
- `ui/admin_fund_transfer.php` - Admin fund transfers
- `ui/admin_fund_transfer_log.php` - Transfer history log
- `ui/admin_manual_transfer.php` - Manual transfer operations

### Admin Navigation & Components
- `ui/admin_nav.php` - Admin navigation menu component

**Total Admin Files: 20**

---

## 🟡 API FILES (Backend Services)
*RESTful API endpoints for frontend-backend communication*

### User API Endpoints
- `ui/api/purchase_package.php` - Package purchase API
- `ui/api/submit_deposit_request.php` - Deposit request API
- `ui/api/submit_withdrawal_request.php` - Withdrawal request API
- `ui/api/submit_staking_request.php` - Staking submission API
- `ui/api/claim_staking.php` - Staking claim API
- `ui/api/execute_transfer.php` - Fund transfer execution
- `ui/api/get_wallet_history.php` - Wallet transaction history
- `ui/api/get_withdrawal_history.php` - Withdrawal history
- `ui/api/get_transfer_history.php` - Transfer history
- `ui/api/get_user_name.php` - User name lookup
- `ui/api/get_users_for_select2.php` - User autocomplete
- `ui/api/validate_transfer_recipient.php` - Transfer validation
- `ui/api/get_coin_price.php` - Current coin price API

### Admin API Endpoints
- `ui/api/admin_add_fund.php` - Admin add funds API
- `ui/api/admin_execute_transfer.php` - Admin transfer API
- `ui/api/admin_update_package.php` - Package update API
- `ui/api/review_deposit_request.php` - Deposit approval API
- `ui/api/process_withdrawal_request.php` - Withdrawal approval API
- `ui/api/process_staking_claim.php` - Staking claim approval API
- `ui/api/update_bonus_claim.php` - Bonus claim processing
- `ui/api/level_handler.php` - Level management API
- `ui/api/get_all_transfer_history.php` - Complete transfer history

**Total API Files: 22**

---

## 🟢 BACKEND LOGIC FILES (Core Business Logic)
*Server-side processing and business rule implementation*

### Core Business Logic
- `ui/calculate_earnings.php` - Daily earnings calculation engine
- `ui/settings_helper.php` - Dynamic settings management
- `ui/dbcon.php` - Database connection management
- `ui/get_team_investment.php` - Team investment calculations
- `ui/submit.php` - Form submission processing

### Database Management
- `ui/create_package_history_table.php` - Package history table creation
- `ui/sync_package_history.php` - Package history synchronization
- `ui/create_settings_table.php` - Settings table creation
- `ui/create_coin_price_history.php` - Price history table creation
- `ui/update_staking_decimals.php` - Database precision updates
- `ui/backfill_user_ids.php` - User ID migration script
- `ui/add_plan_expiration_fields.php` - Database schema updates

### Staking System Backend
- `ui/check_staking_db.php` - Staking database verification
- `ui/fix_staking_database.php` - Staking database repairs
- `ui/create_test_staking.php` - Test staking data creation
- `ui/fix_test_staking.php` - Test staking fixes

**Total Backend Files: 16**

---

## 🟣 TEST & DEBUG FILES (Development & Troubleshooting)
*Files for testing, debugging, and development purposes*

### System Testing
- `ui/test_balance_sync.php` - Balance synchronization testing
- `ui/test_dynamic_coin_price.php` - Dynamic pricing system test
- `ui/test_earnings_with_qualification.php` - Earnings calculation test
- `ui/test_network_qualification.php` - Network qualification test
- `ui/test_package_history_updates.php` - Package history test
- `ui/test_plan_expiration.php` - Plan expiration testing
- `ui/test_purchase_api.php` - Package purchase API test
- `ui/test_user_links.php` - User link validation test
- `ui/test_wallet_history.php` - Wallet history testing
- `ui/test_withdrawal_db.php` - Withdrawal database test

### Debug Tools
- `ui/debug_package_history.php` - Package history debugging
- `ui/debug_purchase_api.php` - Purchase API debugging
- `ui/debug_withdrawal.php` - Withdrawal system debugging

**Total Test/Debug Files: 13**

---

## 📁 DATABASE FILES (Schema & Structure)
*Database schema, migrations, and seed data*

### Schema Files
- `database/schema.sql` - Complete database schema with all tables
- `database/FINAL_SCHEMA_DOCUMENTATION.md` - Schema documentation
- `database/seed_levels.php` - Level data seeding

### SQL Scripts
- `ui/sql/create_withdrawal_requests_table.sql` - Withdrawal table creation

**Total Database Files: 4**

---

## 📚 DOCUMENTATION FILES (Project Documentation)
*Comprehensive documentation for the entire system*

### Technical Documentation
- `documentation/BACKEND_LOGIC_DOCUMENTATION.md` - Backend logic documentation
- `documentation/API_DOCUMENTATION.md` - API endpoints documentation
- `documentation/FILE_CATEGORIZATION_LIST.md` - This file

### Feature Documentation
- `ui/DYNAMIC_COIN_PRICE_SYSTEM_SUMMARY.md` - Dynamic pricing documentation
- `ui/NETWORK_QUALIFICATION_SYSTEM.md` - Network qualification documentation
- `ui/PACKAGE_HISTORY_UPDATES_SUMMARY.md` - Package history documentation

**Total Documentation Files: 6**

---

## 📊 SUMMARY BY CATEGORY

| Category | File Count | Percentage |
|----------|------------|------------|
| 🔵 User Side | 16 | 16.3% |
| 🔴 Admin Side | 20 | 20.4% |
| 🟡 API Files | 22 | 22.4% |
| 🟢 Backend Logic | 16 | 16.3% |
| 🟣 Test & Debug | 13 | 13.3% |
| 📁 Database | 4 | 4.1% |
| 📚 Documentation | 6 | 6.1% |
| **TOTAL** | **98** | **100%** |

---

## 🎯 KEY INSIGHTS

### User Experience (16.3%)
- Clean separation between user and admin interfaces
- Comprehensive user functionality for all operations
- Intuitive navigation and user-friendly design

### Administration (20.4%)
- Robust admin panel with complete system control
- Financial operation management
- User and system administration tools

### API Architecture (22.4%)
- RESTful API design with clear separation
- Comprehensive endpoint coverage
- Both user and admin API functionality

### Backend Robustness (16.3%)
- Solid business logic implementation
- Dynamic configuration system
- Database management and migration tools

### Quality Assurance (13.3%)
- Extensive testing and debugging tools
- System validation and verification
- Development support utilities

### Data Management (4.1%)
- Complete database schema
- Migration and seeding scripts
- Proper data structure documentation

### Documentation (6.1%)
- Comprehensive technical documentation
- Feature-specific documentation
- Clear categorization and organization

This categorization shows a well-balanced system with strong separation of concerns, comprehensive testing, and thorough documentation.
