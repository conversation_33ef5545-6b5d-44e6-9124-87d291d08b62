<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Staking - Crypto Wallet</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <style>
        body {
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
            min-height: 100vh;
        }

        .stats-card {
            background: rgba(30, 41, 59, 0.4);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(59, 130, 246, 0.2);
            border-radius: 0.75rem;
            padding: 1rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }

        .staking-card {
            background: rgba(30, 41, 59, 0.4);
            border: 1px solid rgba(59, 130, 246, 0.15);
            border-radius: 0.75rem;
            padding: 1.5rem;
            transition: all 0.3s ease;
        }

        .staking-card:hover {
            border-color: rgba(6, 182, 212, 0.3);
            background: rgba(30, 41, 59, 0.6);
            transform: translateY(-2px);
        }

        .staking-btn {
            background: linear-gradient(135deg, #3b82f6, #06b6d4);
            border: none;
            border-radius: 0.75rem;
            padding: 0.75rem 1.5rem;
            color: white;
            font-weight: 600;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .staking-btn:hover {
            background: linear-gradient(135deg, #2563eb, #0891b2);
            transform: translateY(-1px);
            box-shadow: 0 10px 25px rgba(59, 130, 246, 0.3);
        }

        .staking-btn:disabled {
            background: rgba(100, 116, 139, 0.5);
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .progress-bar {
            background: rgba(100, 116, 139, 0.3);
            border-radius: 9999px;
            height: 0.5rem;
            overflow: hidden;
        }

        .progress-fill {
            background: linear-gradient(90deg, #3b82f6, #06b6d4);
            height: 100%;
            border-radius: 9999px;
            transition: width 0.3s ease;
        }

        /* Bottom Navigation Styles */
        .accent {
            color: #06b6d4 !important;
        }

        .hover\:accent:hover {
            color: #06b6d4 !important;
        }
    </style>
</head>

<body class="text-white">
    <div class="min-h-screen flex flex-col">

        <!-- Header -->
        <div class="sticky top-0 z-50 bg-slate-800/95 backdrop-blur-sm border-b border-slate-700">
            <div class="flex items-center justify-between px-6 py-4">
                <div class="flex items-center gap-4">
                    <button onclick="goBack()"
                        class="w-10 h-10 rounded-full bg-slate-700 hover:bg-slate-600 flex items-center justify-center transition-colors">
                        <i class="fas fa-arrow-left text-slate-300"></i>
                    </button>
                    <div>
                        <h1 class="text-xl font-bold text-white">CYT Staking</h1>
                        <p class="text-sm text-slate-400">Buy & stake CYT coins</p>
                    </div>
                </div>
                <div class="text-right">
                    <p class="text-lg font-bold text-cyan-400" id="totalCYT">0 CYT</p>
                    <p class="text-xs text-slate-400">Total CYT Coins</p>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-grow overflow-y-auto px-4 sm:px-6 py-4">

            <!-- CYT Coin Info -->
            <div class="stats-card mb-4">
                <div class="flex items-center justify-between mb-4">
                    <h2 class="text-lg font-bold text-white">CYT Coin Info</h2>
                    <span class="text-sm text-slate-400">6-month staking</span>
                </div>
                <div class="grid grid-cols-3 gap-4 mb-4">
                    <div class="text-center">
                        <p class="text-2xl font-bold text-blue-400">$0.45</p>
                        <p class="text-xs text-slate-400">Per CYT</p>
                    </div>
                    <div class="text-center">
                        <p class="text-2xl font-bold text-cyan-400" id="myCYT">0</p>
                        <p class="text-xs text-slate-400">Staked CYT</p>
                    </div>
                    <div class="text-center">
                        <p class="text-2xl font-bold text-cyan-400" id="cytValue">$0</p>
                        <p class="text-xs text-slate-400">Staked Value</p>
                    </div>
                </div>
                <div class="bg-blue-900/30 p-3 rounded-lg border border-blue-500/20">
                    <div class="flex items-center gap-2 mb-2">
                        <i class="fas fa-lock text-blue-400"></i>
                        <span class="text-sm font-semibold text-blue-400">Staking Terms</span>
                    </div>
                    <ul class="text-xs text-blue-300 space-y-1">
                        <li>• CYT coins are frozen for 6 months from deposit date</li>
                        <li>• Coins become available after staking period completes</li>
                        <li>• Purchase in multiples of $10 only</li>
                        <li>• No early withdrawal allowed during staking period</li>
                    </ul>
                </div>
            </div>

            <!-- Wallet Balances -->
            <div class="stats-card mb-4">
                <div class="flex items-center justify-between mb-4">
                    <h2 class="text-lg font-bold text-white">Available Balances</h2>
                    <span class="text-sm text-slate-400">Choose funding source</span>
                </div>
                <div class="grid grid-cols-2 gap-4">
                    <div class="text-center p-3 bg-slate-800/50 rounded-lg border border-blue-500/20">
                        <p class="text-lg font-bold text-blue-400" id="topupBalance">$250.00</p>
                        <p class="text-xs text-slate-400">Top-up Wallet</p>
                    </div>
                    <div class="text-center p-3 bg-slate-800/50 rounded-lg border border-cyan-500/20">
                        <p class="text-lg font-bold text-cyan-400" id="withdrawalBalance">$18.50</p>
                        <p class="text-xs text-slate-400">Withdrawal Wallet</p>
                    </div>
                </div>
            </div>

            <!-- Stake CYT Coins -->
            <div class="stats-card mb-4">
                <div class="flex items-center justify-between mb-4">
                    <h2 class="text-lg font-bold text-white">Stake CYT Coins</h2>
                    <span class="text-sm text-slate-400">6-month lock period</span>
                </div>

                <!-- Purchase Form -->
                <div class="space-y-4">
                    <!-- Amount Input -->
                    <div class="staking-card">
                        <div class="mb-4">
                            <label class="block text-sm font-semibold text-white mb-2">Purchase Amount (USD)</label>
                            <div class="relative">
                                <input type="number" id="purchaseAmount"
                                    class="w-full bg-slate-800/50 border border-slate-600 rounded-lg px-4 py-3 text-white text-lg font-semibold focus:border-cyan-400 focus:outline-none"
                                    placeholder="Enter any amount" min="1" step="1" value="10" oninput="calculateCYT()">
                                <span class="absolute right-3 top-3 text-slate-400 text-lg">USD</span>
                            </div>
                            <p class="text-xs text-slate-400 mt-1">Enter any amount • Will be converted to multiples of
                                $10</p>
                        </div>

                        <div class="mb-4">
                            <div class="flex justify-between items-center mb-2">
                                <span class="text-sm text-slate-400">You will receive:</span>
                                <span class="text-lg font-bold text-cyan-400" id="cytAmount">22.22 CYT</span>
                            </div>
                            <div class="bg-orange-900/30 p-3 rounded-lg border border-orange-500/20">
                                <p class="text-xs text-orange-300">⏳ CYT coins will be frozen for 6 months from deposit
                                    date</p>
                            </div>
                        </div>

                        <!-- Wallet Selection -->
                        <div class="mb-4">
                            <label class="block text-sm font-semibold text-white mb-2">Payment Source</label>
                            <div class="grid grid-cols-2 gap-3">
                                <label class="cursor-pointer">
                                    <input type="radio" name="walletSource" value="topup" checked class="sr-only">
                                    <div
                                        class="wallet-option border-2 border-blue-500 bg-blue-500/10 p-3 rounded-lg text-center">
                                        <i class="fas fa-wallet text-blue-400 mb-1"></i>
                                        <p class="text-sm font-semibold text-white">Top-up Wallet</p>
                                        <p class="text-xs text-blue-400" id="topupAvailable">$250.00 available</p>
                                    </div>
                                </label>
                                <label class="cursor-pointer">
                                    <input type="radio" name="walletSource" value="withdrawal" class="sr-only">
                                    <div
                                        class="wallet-option border-2 border-slate-600 bg-slate-800/30 p-3 rounded-lg text-center">
                                        <i class="fas fa-piggy-bank text-cyan-400 mb-1"></i>
                                        <p class="text-sm font-semibold text-white">Withdrawal Wallet</p>
                                        <p class="text-xs text-cyan-400" id="withdrawalAvailable">$18.50 available</p>
                                    </div>
                                </label>
                            </div>
                        </div>

                        <button class="staking-btn w-full" onclick="stakeCYT()" id="stakeButton">
                            <i class="fas fa-lock mr-2"></i>
                            Stake CYT Coins
                        </button>
                    </div>


                </div>
            </div>
        </div>

        <!-- Sticky Bottom Navigation Bar -->
        <div class="sticky bottom-0 z-40 flex-shrink-0">
            <div class="bg-slate-800/95 backdrop-blur-sm mx-4 mb-4 rounded-2xl border border-slate-700">
                <div class="flex justify-around items-center h-16">
                    <a href="1.html" class="flex flex-col items-center gap-1 text-slate-400 hover:accent">
                        <i class="fas fa-home"></i>
                        <span class="text-xs">Home</span>
                    </a>
                    <a href="wallet.html" class="flex flex-col items-center gap-1 text-slate-400 hover:accent">
                        <i class="fas fa-wallet"></i>
                        <span class="text-xs">Wallet</span>
                    </a>
                    <a href="team.html" class="flex flex-col items-center gap-1 text-slate-400 hover:accent">
                        <i class="fas fa-users"></i>
                        <span class="text-xs">Team</span>
                    </a>
                    <a href="profile.html" class="flex flex-col items-center gap-1 text-slate-400 hover:accent">
                        <i class="fas fa-user"></i>
                        <span class="text-xs">Profile</span>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script>
        function goBack() {
            window.history.back();
        }

        // CYT coin price
        const CYT_PRICE = 0.45;

        // Sample wallet balances
        let topupWalletBalance = 250.00;
        let withdrawalWalletBalance = 18.50;
        let myCYTCoins = 0;

        function calculateCYT() {
            const inputAmount = parseFloat(document.getElementById('purchaseAmount').value) || 0;

            // Get selected wallet balance
            const selectedWallet = document.querySelector('input[name="walletSource"]:checked').value;
            const availableBalance = selectedWallet === 'topup' ? topupWalletBalance : withdrawalWalletBalance;

            // Convert to floor multiple of 10, but don't exceed wallet balance
            let amount = Math.floor(inputAmount / 10) * 10;

            // Ensure amount doesn't exceed available balance
            if (amount > availableBalance) {
                amount = Math.floor(availableBalance / 10) * 10;
            }

            // Ensure minimum amount is 10
            if (amount < 10) {
                amount = Math.min(10, Math.floor(availableBalance / 10) * 10);
            }

            // Update the input field with the corrected amount
            if (amount !== inputAmount) {
                document.getElementById('purchaseAmount').value = amount;
            }

            // Calculate CYT amount
            const cytAmount = amount > 0 ? (amount / CYT_PRICE).toFixed(2) : '0.00';
            document.getElementById('cytAmount').textContent = `${cytAmount} CYT`;

            // Show conversion info if amount was adjusted
            const conversionInfo = document.getElementById('conversionInfo');
            if (inputAmount !== amount && inputAmount > 0) {
                if (!conversionInfo) {
                    const infoDiv = document.createElement('div');
                    infoDiv.id = 'conversionInfo';
                    infoDiv.className = 'text-xs text-orange-400 mt-1';
                    document.querySelector('.mb-4').appendChild(infoDiv);
                }
                if (amount > availableBalance) {
                    document.getElementById('conversionInfo').textContent = `Amount adjusted to $${amount} (wallet balance limit)`;
                } else {
                    document.getElementById('conversionInfo').textContent = `Amount adjusted to $${amount} (nearest multiple of $10)`;
                }
            } else if (conversionInfo) {
                conversionInfo.remove();
            }

            // Update button state based on available balance
            updateButtonState(amount);
        }

        function updateButtonState(amount) {
            const selectedWallet = document.querySelector('input[name="walletSource"]:checked').value;
            const availableBalance = selectedWallet === 'topup' ? topupWalletBalance : withdrawalWalletBalance;
            const stakeButton = document.getElementById('stakeButton');

            if (amount > availableBalance) {
                stakeButton.disabled = true;
                stakeButton.textContent = 'Insufficient Balance';
                stakeButton.style.background = 'rgba(100, 116, 139, 0.5)';
            } else {
                stakeButton.disabled = false;
                stakeButton.innerHTML = '<i class="fas fa-lock mr-2"></i>Stake CYT Coins';
                stakeButton.style.background = 'linear-gradient(135deg, #3b82f6, #06b6d4)';
            }
        }

        function stakeCYT() {
            const amount = parseFloat(document.getElementById('purchaseAmount').value);
            const selectedWallet = document.querySelector('input[name="walletSource"]:checked').value;
            const cytAmount = (amount / CYT_PRICE).toFixed(2);

            // Basic validation
            if (amount < 10) {
                Swal.fire({
                    title: 'Invalid Amount',
                    text: 'Minimum staking amount is $10',
                    icon: 'error',
                    confirmButtonColor: '#06b6d4',
                    background: '#1e293b',
                    color: '#ffffff'
                });
                return;
            }

            const availableBalance = selectedWallet === 'topup' ? topupWalletBalance : withdrawalWalletBalance;
            if (amount > availableBalance) {
                Swal.fire({
                    title: 'Insufficient Balance',
                    text: `You don't have enough balance in your ${selectedWallet} wallet.`,
                    icon: 'error',
                    confirmButtonColor: '#06b6d4',
                    background: '#1e293b',
                    color: '#ffffff'
                });
                return;
            }

            Swal.fire({
                title: 'Confirm CYT Staking',
                html: `
                    <div class="text-left space-y-3">
                        <div class="bg-slate-800 p-4 rounded-lg">
                            <h4 class="text-cyan-400 font-semibold mb-2">Staking Details:</h4>
                            <p class="text-slate-300 text-sm">• Stake Amount: $${amount.toFixed(2)}</p>
                            <p class="text-slate-300 text-sm">• CYT Coins: ${cytAmount} CYT</p>
                            <p class="text-slate-300 text-sm">• Price per CYT: $${CYT_PRICE}</p>
                            <p class="text-slate-300 text-sm">• Payment from: ${selectedWallet.charAt(0).toUpperCase() + selectedWallet.slice(1)} Wallet</p>
                            <p class="text-slate-300 text-sm">• Lock Period: 6 months from deposit</p>
                        </div>
                        <div class="bg-orange-900/30 p-3 rounded-lg border border-orange-500/20">
                            <p class="text-orange-300 text-sm">⚠️ CYT coins will be frozen for 6 months and cannot be used until staking period completes</p>
                        </div>
                    </div>
                `,
                showCancelButton: true,
                confirmButtonText: 'Start Staking',
                cancelButtonText: 'Cancel',
                confirmButtonColor: '#06b6d4',
                cancelButtonColor: '#64748b',
                background: '#1e293b',
                color: '#ffffff'
            }).then((result) => {
                if (result.isConfirmed) {
                    // Process staking
                    processCYTStaking(amount, cytAmount, selectedWallet);
                }
            });
        }

        function processCYTStaking(amount, cytAmount, walletSource) {
            // Deduct from selected wallet
            if (walletSource === 'topup') {
                topupWalletBalance -= amount;
                document.getElementById('topupBalance').textContent = `$${topupWalletBalance.toFixed(2)}`;
                document.getElementById('topupAvailable').textContent = `$${topupWalletBalance.toFixed(2)} available`;
            } else {
                withdrawalWalletBalance -= amount;
                document.getElementById('withdrawalBalance').textContent = `$${withdrawalWalletBalance.toFixed(2)}`;
                document.getElementById('withdrawalAvailable').textContent = `$${withdrawalWalletBalance.toFixed(2)} available`;
            }

            // Add staked CYT coins
            myCYTCoins += parseFloat(cytAmount);
            document.getElementById('myCYT').textContent = myCYTCoins.toFixed(2);
            document.getElementById('totalCYT').textContent = `${myCYTCoins.toFixed(2)} CYT`;
            document.getElementById('cytValue').textContent = `$${(myCYTCoins * CYT_PRICE).toFixed(2)}`;

            // Reset form
            document.getElementById('purchaseAmount').value = '10';
            calculateCYT();

            // Calculate unlock date (6 months from now)
            const unlockDate = new Date();
            unlockDate.setMonth(unlockDate.getMonth() + 6);
            const unlockDateString = unlockDate.toLocaleDateString();

            // Show success message
            Swal.fire({
                title: 'Staking Started!',
                html: `
                    <div class="text-center">
                        <i class="fas fa-lock text-cyan-400 text-4xl mb-4"></i>
                        <p class="text-slate-300">You have successfully staked</p>
                        <p class="text-2xl font-bold text-cyan-400">${cytAmount} CYT Coins</p>
                        <p class="text-slate-400 text-sm mt-2">Total Staked: ${myCYTCoins.toFixed(2)} CYT</p>
                        <div class="bg-orange-900/30 p-3 rounded-lg border border-orange-500/20 mt-4">
                            <p class="text-orange-300 text-sm">🔒 Coins will be available on: ${unlockDateString}</p>
                        </div>
                    </div>
                `,
                confirmButtonText: 'Great!',
                confirmButtonColor: '#06b6d4',
                background: '#1e293b',
                color: '#ffffff'
            });
        }

        // Handle wallet selection
        document.addEventListener('change', function (e) {
            if (e.target.name === 'walletSource') {
                // Update visual selection
                document.querySelectorAll('.wallet-option').forEach(option => {
                    option.classList.remove('border-blue-500', 'bg-blue-500/10', 'border-cyan-500', 'bg-cyan-500/10');
                    option.classList.add('border-slate-600', 'bg-slate-800/30');
                });

                const selectedOption = e.target.parentElement.querySelector('.wallet-option');
                if (e.target.value === 'topup') {
                    selectedOption.classList.remove('border-slate-600', 'bg-slate-800/30');
                    selectedOption.classList.add('border-blue-500', 'bg-blue-500/10');
                } else {
                    selectedOption.classList.remove('border-slate-600', 'bg-slate-800/30');
                    selectedOption.classList.add('border-cyan-500', 'bg-cyan-500/10');
                }

                // Recalculate CYT amount with new wallet balance
                calculateCYT();
            }
        });

        // Initialize page data
        document.addEventListener('DOMContentLoaded', function () {
            // Initialize balances
            document.getElementById('topupBalance').textContent = `$${topupWalletBalance.toFixed(2)}`;
            document.getElementById('withdrawalBalance').textContent = `$${withdrawalWalletBalance.toFixed(2)}`;
            document.getElementById('topupAvailable').textContent = `$${topupWalletBalance.toFixed(2)} available`;
            document.getElementById('withdrawalAvailable').textContent = `$${withdrawalWalletBalance.toFixed(2)} available`;

            // Initialize CYT data
            document.getElementById('myCYT').textContent = myCYTCoins.toFixed(2);
            document.getElementById('totalCYT').textContent = `${myCYTCoins.toFixed(2)} CYT`;
            document.getElementById('cytValue').textContent = `$${(myCYTCoins * CYT_PRICE).toFixed(2)}`;

            // Calculate initial CYT amount
            calculateCYT();
        });
    </script>

</body>

</html>