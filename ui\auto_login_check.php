<?php
// Auto-login check for remember me functionality
// Include this file at the top of pages that require authentication

if (!isset($_SESSION)) {
    session_start();
}

// If user is not logged in but has a remember token cookie
if (!isset($_SESSION['user_id']) && isset($_COOKIE['remember_token'])) {
    include_once 'dbcon.php';
    
    $remember_token = $_COOKIE['remember_token'];
    
    // Check if the token exists and is not expired
    $stmt = $conn->prepare('
        SELECT u.id, u.name, u.is_admin 
        FROM user_sessions us 
        JOIN users u ON us.user_id = u.id 
        WHERE us.session_token = ? AND us.expires_at > NOW() AND u.is_active = 1
    ');
    $stmt->bind_param('s', $remember_token);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        $user = $result->fetch_assoc();
        
        // Auto-login the user
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['user_name'] = $user['name'];
        $_SESSION['is_admin'] = $user['is_admin'];
        
        // Extend the session token expiry
        $new_expires_at = date('Y-m-d H:i:s', strtotime('+30 days'));
        $update_stmt = $conn->prepare('UPDATE user_sessions SET expires_at = ? WHERE session_token = ?');
        $update_stmt->bind_param('ss', $new_expires_at, $remember_token);
        $update_stmt->execute();
        $update_stmt->close();
        
        // Extend the cookie expiry
        setcookie('remember_token', $remember_token, [
            'expires' => strtotime('+30 days'),
            'path' => '/',
            'secure' => false, // Set to true in production with HTTPS
            'httponly' => true,
            'samesite' => 'Strict'
        ]);
    } else {
        // Invalid or expired token, remove the cookie
        setcookie('remember_token', '', [
            'expires' => time() - 3600,
            'path' => '/',
            'secure' => false,
            'httponly' => true,
            'samesite' => 'Strict'
        ]);
    }
    
    $stmt->close();
    $conn->close();
}
?>
