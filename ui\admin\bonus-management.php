<?php
// session_start();
// require_once 'includes/config.php';

// Check if admin is logged in
// if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
//     header('Location: login.php');
//     exit();
// }

$page_title = "Bonus Management";
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - CryptoApp Admin</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
</head>

<body class="bg-gray-50">
    <?php include 'includes/sidebar.php'; ?>

    <div class="flex-1 ml-0 lg:ml-72 transition-all duration-300" id="mainContent">
        <?php include 'includes/header.php'; ?>

        <main class="p-6">
            <!-- Page Header -->
            <div class="mb-8">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-3xl font-bold text-gray-900">Bonus Management</h1>
                        <p class="text-gray-600 mt-2">Create and manage bonus structures and rewards</p>
                    </div>
                    <div class="flex items-center space-x-3">
                        <div class="bg-white rounded-lg px-4 py-2 shadow-sm border border-gray-200">
                            <span class="text-sm text-gray-500">Total Bonuses:</span>
                            <span class="font-semibold text-gray-900 ml-2" id="totalBonuses">5</span>
                        </div>
                        <div class="bg-white rounded-lg px-4 py-2 shadow-sm border border-gray-200">
                            <span class="text-sm text-gray-500">Active:</span>
                            <span class="font-semibold text-green-600 ml-2" id="activeBonuses">5</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Create New Bonus Card -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-200 mb-8">
                <div class="p-6 border-b border-gray-200">
                    <h2 class="text-xl font-semibold text-gray-900 flex items-center">
                        <i class="fas fa-gift text-purple-500 mr-3"></i>
                        Create New Bonus
                    </h2>
                </div>
                <div class="p-6">
                    <form id="bonusForm" class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Bonus Name</label>
                            <input type="text" id="bonusName" name="bonusName"
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200"
                                placeholder="Enter bonus name" required>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Required Investment ($)</label>
                            <input type="number" id="requiredInvestment" name="requiredInvestment"
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200"
                                placeholder="Enter required investment" min="0" step="0.01" required>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Reward Amount ($)</label>
                            <input type="number" id="rewardAmount" name="rewardAmount"
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200"
                                placeholder="Enter reward amount" min="0" step="0.01" required>
                        </div>
                        <div class="md:col-span-3">
                            <button type="submit"
                                class="bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 text-white px-8 py-3 rounded-lg font-medium transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl">
                                <i class="fas fa-plus mr-2"></i>
                                Create Bonus
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Manage Bonuses Card -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-200">
                <div class="p-6 border-b border-gray-200">
                    <h2 class="text-xl font-semibold text-gray-900 flex items-center">
                        <i class="fas fa-cogs text-green-500 mr-3"></i>
                        Manage Bonuses
                    </h2>
                </div>
                <div class="overflow-x-auto">
                    <table class="w-full">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                                <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Required Investment</th>
                                <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Reward Amount</th>
                                <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created At</th>
                                <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Last Updated</th>
                                <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200" id="bonusesTableBody">
                            <!-- Bonus rows will be populated here -->
                        </tbody>
                    </table>
                </div>
            </div>
        </main>
    </div>

    <!-- Edit Bonus Modal -->
    <div id="editBonusModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center">
        <div class="bg-white rounded-xl shadow-2xl max-w-lg w-full mx-4">
            <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">Edit Bonus</h3>
            </div>
            <div class="p-6">
                <form id="editBonusForm">
                    <input type="hidden" id="editBonusId">
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Bonus Name</label>
                            <input type="text" id="editBonusName"
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                                required>
                        </div>
                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Required Investment ($)</label>
                                <input type="number" id="editRequiredInvestment"
                                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                                    min="0" step="0.01" required>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Reward Amount ($)</label>
                                <input type="number" id="editRewardAmount"
                                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                                    min="0" step="0.01" required>
                            </div>
                        </div>
                    </div>
                    <div class="flex justify-end space-x-3 mt-6">
                        <button type="button" onclick="closeEditModal()"
                            class="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors">
                            Cancel
                        </button>
                        <button type="submit"
                            class="bg-purple-500 hover:bg-purple-600 text-white px-6 py-2 rounded-lg transition-colors">
                            Update Bonus
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        // Sample bonus data
        let bonuses = [{
                id: 1,
                name: 'sdvcs',
                requiredInvestment: 500.00,
                rewardAmount: 10000.00,
                status: 'Inactive',
                createdAt: '2025-06-28 19:12',
                lastUpdated: '2025-06-28 19:12'
            },
            {
                id: 2,
                name: 'International Tour Fund',
                requiredInvestment: 4000.00,
                rewardAmount: 400.00,
                status: 'Active',
                createdAt: '2025-06-28 17:26',
                lastUpdated: '2025-06-28 17:26'
            },
            {
                id: 3,
                name: 'Laptop Fund',
                requiredInvestment: 2500.00,
                rewardAmount: 200.00,
                status: 'Active',
                createdAt: '2025-06-28 17:25',
                lastUpdated: '2025-06-28 17:25'
            },
            {
                id: 4,
                name: 'Mobile Fund',
                requiredInvestment: 1000.00,
                rewardAmount: 100.00,
                status: 'Active',
                createdAt: '2025-06-28 17:25',
                lastUpdated: '2025-06-28 17:25'
            }
        ];

        // Render bonuses table
        function renderBonusesTable() {
            const tbody = document.getElementById('bonusesTableBody');
            tbody.innerHTML = '';

            bonuses.forEach(bonus => {
                const row = document.createElement('tr');
                row.className = 'hover:bg-gray-50 transition-colors';

                row.innerHTML = `
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="font-medium text-gray-900">${bonus.name}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-gray-900 font-semibold">$${bonus.requiredInvestment.toFixed(2)}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-gray-900 font-semibold">$${bonus.rewardAmount.toFixed(2)}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="inline-flex px-3 py-1 text-xs font-semibold rounded-full ${bonus.status === 'Active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}">
                            ${bonus.status}
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-gray-600">${bonus.createdAt}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-gray-600">${bonus.lastUpdated}</td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="flex space-x-2">
                            <button onclick="editBonus(${bonus.id})"
                                    class="text-blue-600 hover:text-blue-800 font-medium text-sm transition-colors">
                                Edit
                            </button>
                            <button onclick="toggleBonusStatus(${bonus.id})"
                                    class="text-red-600 hover:text-red-800 font-medium text-sm transition-colors">
                                ${bonus.status === 'Active' ? 'Deactivate' : 'Activate'}
                            </button>
                        </div>
                    </td>
                `;
                tbody.appendChild(row);
            });

            // Update counters
            document.getElementById('totalBonuses').textContent = bonuses.length;
            document.getElementById('activeBonuses').textContent = bonuses.filter(b => b.status === 'Active').length;
        }

        // Create new bonus
        document.getElementById('bonusForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const name = document.getElementById('bonusName').value;
            const requiredInvestment = parseFloat(document.getElementById('requiredInvestment').value);
            const rewardAmount = parseFloat(document.getElementById('rewardAmount').value);

            // Add new bonus
            const newBonus = {
                id: bonuses.length + 1,
                name: name,
                requiredInvestment: requiredInvestment,
                rewardAmount: rewardAmount,
                status: 'Active',
                createdAt: new Date().toLocaleDateString() + ' ' + new Date().toLocaleTimeString(),
                lastUpdated: new Date().toLocaleDateString() + ' ' + new Date().toLocaleTimeString()
            };

            bonuses.push(newBonus);
            renderBonusesTable();

            // Reset form
            this.reset();

            // Show success message
            Swal.fire({
                icon: 'success',
                title: 'Bonus Created!',
                text: `${name} bonus has been created successfully.`,
                timer: 2000,
                showConfirmButton: false
            });
        });

        // Edit bonus
        function editBonus(id) {
            const bonus = bonuses.find(b => b.id === id);
            if (bonus) {
                document.getElementById('editBonusId').value = bonus.id;
                document.getElementById('editBonusName').value = bonus.name;
                document.getElementById('editRequiredInvestment').value = bonus.requiredInvestment;
                document.getElementById('editRewardAmount').value = bonus.rewardAmount;
                document.getElementById('editBonusModal').classList.remove('hidden');
            }
        }

        // Close edit modal
        function closeEditModal() {
            document.getElementById('editBonusModal').classList.add('hidden');
        }

        // Update bonus
        document.getElementById('editBonusForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const id = parseInt(document.getElementById('editBonusId').value);
            const name = document.getElementById('editBonusName').value;
            const requiredInvestment = parseFloat(document.getElementById('editRequiredInvestment').value);
            const rewardAmount = parseFloat(document.getElementById('editRewardAmount').value);

            const bonusIndex = bonuses.findIndex(b => b.id === id);
            if (bonusIndex !== -1) {
                bonuses[bonusIndex] = {
                    ...bonuses[bonusIndex],
                    name: name,
                    requiredInvestment: requiredInvestment,
                    rewardAmount: rewardAmount,
                    lastUpdated: new Date().toLocaleDateString() + ' ' + new Date().toLocaleTimeString()
                };

                renderBonusesTable();
                closeEditModal();

                Swal.fire({
                    icon: 'success',
                    title: 'Bonus Updated!',
                    text: `${name} bonus has been updated successfully.`,
                    timer: 2000,
                    showConfirmButton: false
                });
            }
        });

        // Toggle bonus status
        function toggleBonusStatus(id) {
            const bonus = bonuses.find(b => b.id === id);
            if (bonus) {
                const newStatus = bonus.status === 'Active' ? 'Inactive' : 'Active';
                const action = newStatus === 'Active' ? 'activate' : 'deactivate';

                Swal.fire({
                    title: `${action.charAt(0).toUpperCase() + action.slice(1)} Bonus?`,
                    text: `Are you sure you want to ${action} the ${bonus.name} bonus?`,
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: newStatus === 'Active' ? '#10b981' : '#ef4444',
                    cancelButtonColor: '#6b7280',
                    confirmButtonText: `Yes, ${action} it!`
                }).then((result) => {
                    if (result.isConfirmed) {
                        bonus.status = newStatus;
                        bonus.lastUpdated = new Date().toLocaleDateString();
                        renderBonusesTable();

                        Swal.fire({
                            icon: 'success',
                            title: `Bonus ${action}d!`,
                            text: `${bonus.name} bonus has been ${action}d.`,
                            timer: 2000,
                            showConfirmButton: false
                        });
                    }
                });
            }
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            renderBonusesTable();
        });

        // Close modal when clicking outside
        document.getElementById('editBonusModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeEditModal();
            }
        });
    </script>
</body>

</html>