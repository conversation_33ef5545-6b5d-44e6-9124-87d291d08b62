document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('levels-form');
    const container = document.getElementById('levels-container');
    const toast = document.getElementById('toast-message');
    const API_URL = '/netvis/ui/api/level_handler.php';

    // Fetch levels and build the form
    const loadLevels = async () => {
        try {
            const response = await fetch(API_URL);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            const levels = await response.json();

            container.innerHTML = ''; // Clear loading text

            levels.forEach(level => {
                const div = document.createElement('div');
                div.className = 'grid grid-cols-1 md:grid-cols-3 gap-4 items-center';
                div.innerHTML = `
                    <label for="level-${level.level_number}" class="md:col-span-1 text-sm font-medium text-gray-700">Level ${level.level_number} Percentage</label>
                    <div class="md:col-span-2">
                        <input type="number" step="0.01" id="level-${level.level_number}" name="level_percentage_${level.level_number}" 
                               data-level-number="${level.level_number}" value="${level.distribution_percentage}" 
                               class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm" 
                               placeholder="e.g., 5.0">
                    </div>
                `;
                container.appendChild(div);
            });

        } catch (error) {
            console.error('Error loading levels:', error);
            container.innerHTML = '<div class="text-center text-red-500">Failed to load level settings. Please try again later.</div>';
        }
    };

    // Handle form submission
    form.addEventListener('submit', async (e) => {
        e.preventDefault();
        const inputs = container.querySelectorAll('input[type="number"]');
        const levels_data = Array.from(inputs).map(input => ({
            number: parseInt(input.dataset.levelNumber, 10),
            percentage: parseFloat(input.value)
        }));

        try {
            const response = await fetch(API_URL, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ levels: levels_data })
            });

            const result = await response.json();

            if (response.ok && result.success) {
                showToast('Levels updated successfully!', 'success');
            } else {
                throw new Error(result.error || 'An unknown error occurred.');
            }
        } catch (error) {
            console.error('Error saving levels:', error);
            showToast(`Error: ${error.message}`, 'error');
        }
    });

    // Show toast message
    function showToast(message, type = 'success') {
        toast.textContent = message;
        toast.className = `fixed top-5 right-5 text-white py-2 px-4 rounded-lg shadow-md ${type === 'success' ? 'bg-green-500' : 'bg-red-500'}`;
        toast.classList.remove('hidden');
        setTimeout(() => {
            toast.classList.add('hidden');
        }, 3000);
    }

    // Initial load
    loadLevels();
});

