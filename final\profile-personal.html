<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Personal Information - Crypto Wallet</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <style>
        body {
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
            min-height: 100vh;
        }

        .stats-card {
            background: rgba(30, 41, 59, 0.4);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(59, 130, 246, 0.2);
            border-radius: 0.75rem;
            padding: 1rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }

        .input-field {
            background: rgba(15, 23, 42, 0.8);
            border: 1px solid rgba(59, 130, 246, 0.3);
            border-radius: 0.5rem;
            padding: 0.75rem;
            color: white;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .input-field:focus {
            outline: none;
            border-color: rgba(6, 182, 212, 0.6);
            box-shadow: 0 0 0 3px rgba(6, 182, 212, 0.15);
            background: rgba(15, 23, 42, 0.9);
        }

        .input-field:hover {
            border-color: rgba(6, 182, 212, 0.4);
            background: rgba(15, 23, 42, 0.85);
        }

        .input-field::placeholder {
            color: rgba(148, 163, 184, 0.5);
        }

        .btn-primary {
            background: linear-gradient(135deg, #3b82f6, #06b6d4);
            border: none;
            border-radius: 0.5rem;
            padding: 0.75rem 1.5rem;
            color: white;
            font-weight: 600;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #2563eb, #0891b2);
            transform: translateY(-1px);
        }

        .btn-secondary {
            background: rgba(30, 41, 59, 0.6);
            border: 1px solid rgba(59, 130, 246, 0.3);
            border-radius: 0.5rem;
            padding: 0.75rem 1.5rem;
            color: white;
            font-weight: 600;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .btn-secondary:hover {
            border-color: rgba(6, 182, 212, 0.6);
            background: rgba(30, 41, 59, 0.8);
        }

        /* Bottom Navigation Styles */
        .accent {
            color: #06b6d4 !important;
        }

        .hover\:accent:hover {
            color: #06b6d4 !important;
        }
    </style>
</head>

<body class="text-white">
    <div class="min-h-screen flex flex-col">
        <!-- Header -->
        <div class="sticky top-0 z-50 bg-slate-800/95 backdrop-blur-sm border-b border-slate-700">
            <div class="flex items-center justify-between px-6 py-4">
                <div class="flex items-center gap-4">
                    <button onclick="goBack()"
                        class="w-10 h-10 rounded-full bg-slate-700 hover:bg-slate-600 flex items-center justify-center transition-colors">
                        <i class="fas fa-arrow-left text-slate-300"></i>
                    </button>
                    <div>
                        <h1 class="text-xl font-bold text-white">Personal Information</h1>
                        <p class="text-sm text-slate-400">Edit your personal details</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-grow overflow-y-auto px-4 sm:px-6 py-4">
            <!-- Personal Information -->
            <div class="stats-card mb-4">
                <!-- Header Section -->
                <div class="flex items-center gap-3 mb-4">
                    <div
                        class="w-10 h-10 rounded-lg bg-gradient-to-r from-blue-600 to-cyan-600 flex items-center justify-center">
                        <i class="fas fa-user text-white"></i>
                    </div>
                    <div class="flex-1">
                        <h2 class="text-lg font-bold text-white">Personal Information</h2>
                        <p class="text-xs text-slate-400">Update your profile details</p>
                    </div>
                </div>

                <!-- Profile Photo Section -->
                <div
                    class="relative bg-gradient-to-r from-slate-800/60 to-slate-700/60 rounded-2xl p-4 mb-4 border border-blue-500/20 backdrop-blur-sm overflow-hidden">
                    <!-- Subtle background accent -->
                    <div
                        class="absolute top-0 right-0 w-20 h-20 bg-gradient-to-bl from-cyan-500/5 to-transparent rounded-full blur-xl">
                    </div>

                    <div class="relative flex items-center gap-4">
                        <!-- Compact profile picture -->
                        <div class="relative group/avatar cursor-pointer" onclick="changeProfilePicture()">
                            <div
                                class="w-16 h-16 rounded-2xl bg-gradient-to-br from-blue-500 to-cyan-500 flex items-center justify-center text-white font-bold text-lg shadow-lg ring-1 ring-blue-500/20 transition-all duration-300 group-hover/avatar:scale-105">
                                JD
                            </div>
                            <!-- Sleek hover overlay -->
                            <div
                                class="absolute inset-0 rounded-2xl bg-black/60 opacity-0 group-hover/avatar:opacity-100 transition-all duration-300 flex items-center justify-center backdrop-blur-sm">
                                <i class="fas fa-camera text-white text-lg"></i>
                            </div>
                        </div>

                        <!-- Compact info and button -->
                        <div class="flex-1 min-w-0">
                            <div class="flex items-center justify-between mb-2">
                                <div>
                                    <h3 class="text-sm font-bold text-white">Profile Photo</h3>
                                    <p class="text-xs text-slate-400">JPG, PNG • Max 5MB</p>
                                </div>
                                <button onclick="changeProfilePicture()"
                                    class="bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-500 hover:to-cyan-500 text-white font-medium text-xs py-2 px-4 rounded-xl transition-all duration-300 shadow-md hover:shadow-lg hover:scale-105">
                                    <i class="fas fa-upload mr-1"></i>
                                    Upload
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <form id="personalForm" class="space-y-4">
                    <!-- Name Fields -->
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-slate-300 mb-2">First Name</label>
                            <input type="text" id="firstName" class="input-field w-full" value="John"
                                placeholder="First name">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-slate-300 mb-2">Last Name</label>
                            <input type="text" id="lastName" class="input-field w-full" value="Doe"
                                placeholder="Last name">
                        </div>
                    </div>

                    <!-- Email Address -->
                    <div>
                        <label class="block text-sm font-medium text-slate-300 mb-2">Email Address</label>
                        <input type="email" id="email" class="input-field w-full" value="<EMAIL>"
                            placeholder="Email address">
                        <p class="text-xs text-slate-500 mt-1 flex items-center gap-1">
                            <i class="fas fa-info-circle text-cyan-400"></i>
                            <span>Email changes require verification</span>
                        </p>
                    </div>

                    <!-- Phone and Date -->
                    <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-slate-300 mb-2">Phone Number</label>
                            <input type="tel" id="phone" class="input-field w-full" value="+****************"
                                placeholder="Phone number">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-slate-300 mb-2">Date of Birth</label>
                            <input type="date" id="dateOfBirth" class="input-field w-full" value="1990-01-15">
                        </div>
                    </div>

                    <!-- Country -->
                    <div>
                        <label class="block text-sm font-medium text-slate-300 mb-2">Country</label>
                        <select id="country" class="input-field w-full">
                            <option value="US" selected>United States</option>
                            <option value="CA">Canada</option>
                            <option value="UK">United Kingdom</option>
                            <option value="AU">Australia</option>
                            <option value="DE">Germany</option>
                            <option value="FR">France</option>
                            <option value="JP">Japan</option>
                            <option value="SG">Singapore</option>
                        </select>
                    </div>

                    <!-- Action Buttons -->
                    <div class="flex gap-3 pt-2">
                        <button type="button" onclick="saveChanges()" class="btn-primary flex-1">
                            <i class="fas fa-save mr-2"></i>
                            Save Changes
                        </button>
                        <button type="button" onclick="cancelChanges()" class="btn-secondary">
                            <i class="fas fa-times mr-2"></i>
                            Cancel
                        </button>
                    </div>
                </form>
            </div>


        </div>

        <!-- Sticky Bottom Navigation Bar -->
        <div class="sticky bottom-0 z-40 flex-shrink-0">
            <div class="bg-slate-800/95 backdrop-blur-sm mx-4 mb-4 rounded-2xl border border-slate-700">
                <div class="flex justify-around items-center h-16">
                    <a href="1.html" class="flex flex-col items-center gap-1 text-slate-400 hover:accent">
                        <i class="fas fa-home"></i>
                        <span class="text-xs">Home</span>
                    </a>
                    <a href="wallet.html" class="flex flex-col items-center gap-1 text-slate-400 hover:accent">
                        <i class="fas fa-wallet"></i>
                        <span class="text-xs">Wallet</span>
                    </a>
                    <a href="team.html" class="flex flex-col items-center gap-1 text-slate-400 hover:accent">
                        <i class="fas fa-users"></i>
                        <span class="text-xs">Team</span>
                    </a>
                    <a href="profile.html" class="flex flex-col items-center gap-1 accent">
                        <i class="fas fa-user"></i>
                        <span class="text-xs">Profile</span>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script>
        function goBack() {
            window.history.back();
        }

        function changeProfilePicture() {
            Swal.fire({
                title: '<span style="background: linear-gradient(135deg, #3b82f6, #06b6d4); -webkit-background-clip: text; -webkit-text-fill-color: transparent; font-weight: 700;">Upload Profile Photo</span>',
                html: `
                    <div class="space-y-6">
                        <!-- Premium upload area -->
                        <div class="relative group">
                            <div class="border-2 border-dashed border-slate-600/50 rounded-2xl p-8 text-center hover:border-cyan-400/70 transition-all duration-500 bg-gradient-to-br from-slate-800/30 to-slate-700/30 backdrop-blur-sm group-hover:from-blue-600/10 group-hover:to-cyan-600/10">
                                <!-- Animated upload icon -->
                                <div class="relative mb-4">
                                    <div class="w-16 h-16 mx-auto rounded-2xl bg-gradient-to-br from-blue-600/20 to-cyan-600/20 flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                                        <i class="fas fa-cloud-upload-alt text-cyan-400 text-2xl group-hover:text-cyan-300 transition-colors duration-300"></i>
                                    </div>
                                    <!-- Floating particles effect -->
                                    <div class="absolute -top-1 -right-1 w-3 h-3 bg-cyan-400/60 rounded-full animate-ping"></div>
                                    <div class="absolute -bottom-1 -left-1 w-2 h-2 bg-blue-400/60 rounded-full animate-pulse"></div>
                                </div>

                                <div class="space-y-2">
                                    <p class="text-slate-200 font-semibold">Drop your photo here</p>
                                    <p class="text-slate-400 text-sm">or <span class="text-cyan-400 font-medium cursor-pointer hover:text-cyan-300">browse files</span></p>
                                    <p class="text-slate-500 text-xs">JPG, PNG • Max 5MB • Recommended: 400x400px</p>
                                </div>
                                <input type="file" id="profilePictureInput" class="hidden" accept="image/*">
                            </div>
                        </div>

                        <!-- Premium preview section -->
                        <div class="bg-gradient-to-r from-slate-800/40 to-slate-700/40 rounded-2xl p-4 border border-slate-600/30">
                            <p class="text-slate-300 text-sm mb-3 font-medium">Preview</p>
                            <div class="flex items-center justify-center gap-4">
                                <!-- Current photo -->
                                <div class="text-center">
                                    <div class="w-16 h-16 rounded-2xl bg-gradient-to-br from-blue-600 to-cyan-600 flex items-center justify-center text-white font-bold text-lg shadow-xl mb-2">
                                        JD
                                    </div>
                                    <p class="text-xs text-slate-400">Current</p>
                                </div>

                                <!-- Arrow -->
                                <div class="flex items-center">
                                    <i class="fas fa-arrow-right text-cyan-400 text-lg"></i>
                                </div>

                                <!-- New photo preview -->
                                <div class="text-center">
                                    <div id="previewContainer" class="w-16 h-16 rounded-2xl bg-slate-700/50 border-2 border-dashed border-slate-600/50 flex items-center justify-center mb-2 transition-all duration-300">
                                        <i class="fas fa-image text-slate-500"></i>
                                    </div>
                                    <p class="text-xs text-slate-400">New</p>
                                </div>
                            </div>
                        </div>
                    </div>
                `,
                background: 'linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%)',
                color: '#ffffff',
                confirmButtonColor: '#06b6d4',
                cancelButtonColor: '#64748b',
                confirmButtonText: '<i class="fas fa-upload mr-2"></i>Upload Photo',
                cancelButtonText: '<i class="fas fa-times mr-2"></i>Cancel',
                showCancelButton: true,
                buttonsStyling: false,
                customClass: {
                    confirmButton: 'bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-500 hover:to-cyan-500 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-300 shadow-lg hover:shadow-xl mr-3',
                    cancelButton: 'bg-slate-600 hover:bg-slate-500 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-300',
                    popup: 'rounded-3xl border border-blue-500/20 backdrop-blur-xl',
                    title: 'text-xl mb-4',
                    htmlContainer: 'text-left'
                },
                didOpen: () => {
                    const uploadArea = document.querySelector('.border-dashed');
                    const fileInput = document.getElementById('profilePictureInput');

                    uploadArea.addEventListener('click', () => fileInput.click());

                    // Drag and drop functionality
                    uploadArea.addEventListener('dragover', (e) => {
                        e.preventDefault();
                        uploadArea.classList.add('border-cyan-400', 'bg-cyan-400/10');
                    });

                    uploadArea.addEventListener('dragleave', (e) => {
                        e.preventDefault();
                        uploadArea.classList.remove('border-cyan-400', 'bg-cyan-400/10');
                    });

                    uploadArea.addEventListener('drop', (e) => {
                        e.preventDefault();
                        uploadArea.classList.remove('border-cyan-400', 'bg-cyan-400/10');
                        const files = e.dataTransfer.files;
                        if (files.length > 0) {
                            handleFileSelect(files[0]);
                        }
                    });

                    fileInput.addEventListener('change', (e) => {
                        if (e.target.files.length > 0) {
                            handleFileSelect(e.target.files[0]);
                        }
                    });

                    function handleFileSelect(file) {
                        if (file.size > 5 * 1024 * 1024) {
                            Swal.showValidationMessage('File size must be less than 5MB');
                            return;
                        }

                        const reader = new FileReader();
                        reader.onload = (e) => {
                            document.getElementById('previewContainer').innerHTML =
                                `<img src="${e.target.result}" class="w-16 h-16 rounded-2xl object-cover border-2 border-cyan-400/50">`;
                        };
                        reader.readAsDataURL(file);
                    }
                }
            }).then((result) => {
                if (result.isConfirmed) {
                    Swal.fire({
                        title: '<span style="background: linear-gradient(135deg, #10b981, #06b6d4); -webkit-background-clip: text; -webkit-text-fill-color: transparent; font-weight: 700;">Photo Updated!</span>',
                        html: '<p class="text-slate-300">Your profile picture has been updated successfully</p>',
                        icon: 'success',
                        background: 'linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%)',
                        color: '#ffffff',
                        confirmButtonColor: '#06b6d4',
                        iconColor: '#10b981',
                        timer: 3000,
                        showConfirmButton: false,
                        customClass: {
                            popup: 'rounded-3xl border border-green-500/20 backdrop-blur-xl'
                        }
                    });
                }
            });
        }

        function saveChanges() {
            const firstName = document.getElementById('firstName').value;
            const lastName = document.getElementById('lastName').value;
            const email = document.getElementById('email').value;
            const phone = document.getElementById('phone').value;

            if (!firstName || !lastName || !email) {
                Swal.fire({
                    icon: 'error',
                    title: 'Missing Information',
                    text: 'Please fill in all required fields',
                    background: '#1e293b',
                    color: '#ffffff',
                    confirmButtonColor: '#3b82f6',
                    iconColor: '#3b82f6'
                });
                return;
            }

            Swal.fire({
                icon: 'success',
                title: 'Changes Saved!',
                text: 'Your personal information has been updated successfully',
                background: '#1e293b',
                color: '#ffffff',
                confirmButtonColor: '#06b6d4',
                iconColor: '#06b6d4',
                timer: 2000,
                showConfirmButton: false
            });
        }

        function cancelChanges() {
            Swal.fire({
                title: 'Discard Changes?',
                text: 'Any unsaved changes will be lost',
                icon: 'warning',
                showCancelButton: true,
                background: '#1e293b',
                color: '#ffffff',
                confirmButtonColor: '#3b82f6',
                cancelButtonColor: '#64748b',
                confirmButtonText: 'Discard',
                cancelButtonText: 'Keep Editing',
                iconColor: '#f59e0b'
            }).then((result) => {
                if (result.isConfirmed) {
                    window.history.back();
                }
            });
        }
    </script>
</body>

</html>