<?php
session_start();
header('Content-Type: application/json');

if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['error' => 'User not authenticated.']);
    exit();
}

include '../dbcon.php';
// Set proper error response for API
if ($conn->connect_error) {
    http_response_code(500);
    die(json_encode(['error' => 'Database connection failed: ' . $conn->connect_error]));
}

$user_id = $_SESSION['user_id'];
$wallet_type = $_GET['wallet_type'] ?? 'deposit';
$start_date = $_GET['start_date'] ?? null;
$end_date = $_GET['end_date'] ?? null;
$transaction_type = $_GET['type'] ?? null;

$allowed_wallet_types = ['deposit', 'withdrawal'];
if (!in_array($wallet_type, $allowed_wallet_types)) {
    http_response_code(400);
    echo json_encode(['error' => 'Invalid wallet type specified.']);
    exit();
}

$params = [$user_id, $wallet_type];
$types = 'is';

$sql_conditions = [];

if ($start_date) {
    $sql_conditions[] = "created_at >= ?";
    $params[] = $start_date . ' 00:00:00';
    $types .= 's';
}

if ($end_date) {
    $sql_conditions[] = "created_at <= ?";
    $params[] = $end_date . ' 23:59:59';
    $types .= 's';
}

if ($transaction_type && $transaction_type !== 'all') {
    $sql_conditions[] = "type = ?";
    $params[] = $transaction_type;
    $types .= 's';
}

$sql_where = '';
if (!empty($sql_conditions)) {
    $sql_where = 'AND ' . implode(' AND ', $sql_conditions);
}

$sql = "SELECT amount, type, description, wallet_type, created_at FROM wallet_history WHERE user_id = ? AND wallet_type = ? {$sql_where} ORDER BY created_at DESC";

$stmt = $conn->prepare($sql);

if ($stmt === false) {
    http_response_code(500);
    die(json_encode(['error' => 'Failed to prepare statement: ' . $conn->error]));
}

$stmt->bind_param($types, ...$params);

$stmt->execute();
$result = $stmt->get_result();

$transactions = [];
if ($result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        $transactions[] = $row;
    }
}

$stmt->close();
$conn->close();

echo json_encode($transactions);
?>
