<?php
ini_set('display_errors', 0);
ini_set('log_errors', 1);
ini_set('error_log', 'c:/xampp/htdocs/netvis/php_errors.log');
error_reporting(E_ALL);

session_start();
header('Content-Type: application/json');

// Admin check
if (!isset($_SESSION['user_id']) || !isset($_SESSION['is_admin']) || !$_SESSION['is_admin']) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Access Denied: Admins only.']);
    exit();
}

$admin_id = $_SESSION['user_id'];
$sender_user_id = $_POST['sender_user_id'] ?? '';
$recipient_user_id = $_POST['recipient_user_id'] ?? '';
$amount = (float) ($_POST['amount'] ?? 0);

if (empty($sender_user_id) || empty($recipient_user_id) || $amount <= 0) {
    echo json_encode(['success' => false, 'message' => 'Sender ID, Recipient ID, and Amount are required.']);
    exit();
}

if ($sender_user_id === $recipient_user_id) {
    echo json_encode(['success' => false, 'message' => 'Sender and Recipient cannot be the same user.']);
    exit();
}

include '../dbcon.php';

// Fetch sender and recipient details in one query
$sql = 'SELECT id, name, sponser_id, deposit_wallet_balance FROM users WHERE id = ? OR id = ?';
$stmt = $conn->prepare($sql);
$stmt->bind_param('ii', $sender_user_id, $recipient_user_id);
$stmt->execute();
$result = $stmt->get_result();
$users = [];
while ($row = $result->fetch_assoc()) {
    $users[$row['id']] = $row;
}
$stmt->close();

$sender = $users[$sender_user_id] ?? null;
$recipient = $users[$recipient_user_id] ?? null;

if (!$sender) {
    echo json_encode(['success' => false, 'message' => 'Sender not found.']);
    exit();
}
if (!$recipient) {
    echo json_encode(['success' => false, 'message' => 'Recipient not found.']);
    exit();
}

// Validate sender's balance
if ($sender['deposit_wallet_balance'] < $amount) {
    echo json_encode(['success' => false, 'message' => 'Sender has insufficient funds in their deposit wallet.']);
    exit();
}

// Validate relationship: recipient must be direct sponsor or in the sender's entire downline
$is_valid_recipient = false;

// Case 1: Recipient is the sender's direct sponsor
if ($sender['sponser_id'] == $recipient['id']) {
    $is_valid_recipient = true;
} else {
    // Case 2: Recipient is in the sender's downline (any level)
    $sql_downline_check = '
        WITH RECURSIVE downline_check (id) AS (
            SELECT id FROM users WHERE sponser_id = ?
            UNION ALL
            SELECT u.id FROM users u JOIN downline_check d ON u.sponser_id = d.id
        )
        SELECT COUNT(*) as count FROM downline_check WHERE id = ?
    ';
    $stmt_check = $conn->prepare($sql_downline_check);
    $stmt_check->bind_param('ii', $sender['id'], $recipient['id']);
    $stmt_check->execute();
    $is_downline = $stmt_check->get_result()->fetch_assoc()['count'] > 0;
    $stmt_check->close();

    if ($is_downline) {
        $is_valid_recipient = true;
    }
}

if (!$is_valid_recipient) {
    echo json_encode(['success' => false, 'message' => "Invalid transfer: Recipient must be a direct sponsor or be in the sender's downline."]);
    exit();
}

// Proceed with transaction
$conn->begin_transaction();

try {
    // 1. Debit from sender's deposit wallet
    $stmt_debit = $conn->prepare('UPDATE users SET deposit_wallet_balance = deposit_wallet_balance - ? WHERE id = ?');
    $stmt_debit->bind_param('di', $amount, $sender['id']);
    $stmt_debit->execute();
    $stmt_debit->close();

    // 2. Credit to recipient's deposit wallet
    $stmt_credit = $conn->prepare('UPDATE users SET deposit_wallet_balance = deposit_wallet_balance + ? WHERE id = ?');
    $stmt_credit->bind_param('di', $amount, $recipient['id']);
    $stmt_credit->execute();
    $stmt_credit->close();

    // 3. Log the debit transaction for the sender
    $description_debit = "Admin-initiated fund transfer to user {$recipient['name']} (ID: {$recipient['id']})";
    $negative_amount = -$amount;
    $stmt_log_debit = $conn->prepare("INSERT INTO wallet_history (user_id, type, wallet_type, amount, description, related_user_id) VALUES (?, 'fund_transfer_debit', 'deposit', ?, ?, ?)");
    $stmt_log_debit->bind_param('idsi', $sender['id'], $negative_amount, $description_debit, $recipient['id']);
    $stmt_log_debit->execute();
    $stmt_log_debit->close();

    // 4. Log the credit transaction for the recipient
    $description_credit = "Admin-initiated fund transfer from user {$sender['name']} (ID: {$sender['id']})";
    $stmt_log_credit = $conn->prepare("INSERT INTO wallet_history (user_id, type, wallet_type, amount, description, related_user_id) VALUES (?, 'fund_transfer_credit', 'deposit', ?, ?, ?)");
    $stmt_log_credit->bind_param('idsi', $recipient['id'], $amount, $description_credit, $sender['id']);
    $stmt_log_credit->execute();
    $stmt_log_credit->close();

    $conn->commit();
    echo json_encode(['success' => true, 'message' => 'Transfer completed successfully!']);
} catch (mysqli_sql_exception $exception) {
    $conn->rollback();
    error_log('Admin Transfer Failed: ' . $exception->getMessage());
    echo json_encode(['success' => false, 'message' => 'Transaction failed. Please try again.']);
}

$conn->close();
?>
