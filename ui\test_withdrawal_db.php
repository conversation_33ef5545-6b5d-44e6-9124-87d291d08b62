<?php
// Test script to check if withdrawal_requests table exists and create it if needed
include 'dbcon.php';

// Check if withdrawal_requests table exists
$check_table = "SHOW TABLES LIKE 'withdrawal_requests'";
$result = $conn->query($check_table);

if ($result->num_rows == 0) {
    echo "withdrawal_requests table does not exist. Creating it...<br>";
    
    // Create the table
    $create_table = "
    CREATE TABLE withdrawal_requests (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        amount DECIMAL(15, 4) NOT NULL,
        binance_address VARCHAR(255) NOT NULL,
        status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
        requested_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        reviewed_at TIMESTAMP NULL,
        reviewed_by INT NULL,
        admin_notes TEXT NULL,
        transaction_hash VARCHAR(255) NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";
    
    if ($conn->query($create_table) === TRUE) {
        echo "✅ withdrawal_requests table created successfully!<br>";
    } else {
        echo "❌ Error creating table: " . $conn->error . "<br>";
    }
} else {
    echo "✅ withdrawal_requests table already exists!<br>";
}

// Test database connection
echo "✅ Database connection successful!<br>";
echo "Database: " . $conn->get_server_info() . "<br>";

$conn->close();
?>
