<?php
session_start();

if (!isset($_SESSION['user_id']) || !$_SESSION['is_admin']) {
    header("Location: login.html");
    exit();
}

include 'dbcon.php';

// Handle form submissions for CRUD operations
$action = $_POST['action'] ?? '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $admin_id = $_SESSION['user_id'];

    switch ($action) {
        case 'create':
            $bonus_name = $_POST['bonus_name'];
            $bonus_fund = $_POST['bonus_fund'];
            $team_investment = $_POST['team_investment'];

            $stmt = $conn->prepare("INSERT INTO bonuses (bonus_name, bonus_fund, team_investment) VALUES (?, ?, ?)");
            $stmt->bind_param("sdd", $bonus_name, $bonus_fund, $team_investment);
            $stmt->execute();
            $stmt->close();
            break;

        case 'toggle_active':
            $id = $_POST['id'];
            $current_status = $_POST['current_status'];
            $new_status = $current_status ? 0 : 1;

            $stmt = $conn->prepare("UPDATE bonuses SET is_active = ? WHERE id = ?");
            $stmt->bind_param("ii", $new_status, $id);
            $stmt->execute();
            $stmt->close();
            break;
    }
    header("Location: bonus_management.php");
    exit();
}

// Fetch all bonuses
$sql = "SELECT * FROM bonuses ORDER BY id DESC";
$result = $conn->query($sql);

// Page configuration
$page_title = "Bonus Management";
$additional_css = "
<style>
    /* Enhanced form styling */
    .bonus-form {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.98) 100%);
        backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 1.5rem;
        padding: 2.5rem;
        margin-bottom: 2rem;
        color: #1f2937;
        box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.1);
        animation: slideInUp 0.6s ease-out;
    }

    .form-input {
        background: rgba(255, 255, 255, 0.8);
        border: 2px solid rgba(59, 130, 246, 0.2);
        color: #1f2937;
        border-radius: 0.75rem;
        padding: 0.875rem 1rem;
        width: 100%;
        transition: all 0.3s ease;
        font-size: 0.95rem;
    }

    .form-input::placeholder {
        color: rgba(107, 114, 128, 0.7);
    }

    .form-input:focus {
        outline: none;
        border-color: rgba(59, 130, 246, 0.6);
        background: rgba(255, 255, 255, 0.95);
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        transform: translateY(-1px);
    }

    .form-label {
        color: #374151;
        font-weight: 600;
        margin-bottom: 0.5rem;
        display: block;
        font-size: 0.9rem;
    }

    /* Enhanced submit button */
    .submit-btn {
        background: linear-gradient(135deg, #3b82f6 0%, #6366f1 50%, #8b5cf6 100%);
        color: white;
        border: none;
        padding: 1rem 2.5rem;
        border-radius: 1rem;
        font-weight: 700;
        font-size: 1rem;
        letter-spacing: 0.025em;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        cursor: pointer;
        position: relative;
        overflow: hidden;
        box-shadow: 0 10px 25px -5px rgba(59, 130, 246, 0.4), 0 4px 6px -2px rgba(59, 130, 246, 0.05);
        transform: perspective(1000px) rotateX(0deg);
    }

    .submit-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
        transition: left 0.6s ease;
    }

    .submit-btn:hover::before {
        left: 100%;
    }

    .submit-btn:hover {
        background: linear-gradient(135deg, #2563eb 0%, #4f46e5 50%, #7c3aed 100%);
        transform: perspective(1000px) rotateX(-5deg) translateY(-3px);
        box-shadow: 0 20px 40px -10px rgba(59, 130, 246, 0.6), 0 10px 20px -5px rgba(59, 130, 246, 0.2);
    }

    .submit-btn:active {
        transform: perspective(1000px) rotateX(0deg) translateY(-1px);
        box-shadow: 0 5px 15px -3px rgba(59, 130, 246, 0.4);
        transition: all 0.1s ease;
    }

    .submit-btn span {
        position: relative;
        z-index: 1;
    }

    /* Enhanced animations */
    @keyframes slideInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* Enhanced table styling */
    .admin-table {
        width: 100%;
        border-collapse: separate;
        border-spacing: 0;
        background: white;
        border-radius: 1rem;
        overflow: hidden;
        box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
    }

    .admin-table thead {
        background: linear-gradient(135deg, #3b82f6 0%, #6366f1 50%, #8b5cf6 100%);
    }

    .admin-table thead th {
        color: white;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.05em;
        padding: 1rem 1.5rem;
        font-size: 0.75rem;
        border: none;
        position: relative;
        text-align: left;
    }

    .admin-table thead th:last-child {
        text-align: center;
    }

    .admin-table tbody tr {
        transition: all 0.2s ease;
        border-bottom: 1px solid #e2e8f0;
    }

    .admin-table tbody tr:hover {
        background-color: rgba(59, 130, 246, 0.05);
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    .admin-table tbody tr:last-child {
        border-bottom: none;
    }

    .admin-table tbody td {
        padding: 1rem 1.5rem;
        vertical-align: middle;
        border: none;
        background-color: white;
    }

    .admin-table tbody tr:hover td {
        background-color: rgba(59, 130, 246, 0.05);
    }

    /* Status badges */
    .status-badge {
        display: inline-flex;
        align-items: center;
        padding: 0.25rem 0.75rem;
        border-radius: 9999px;
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.05em;
    }

    .status-active {
        background-color: #d1fae5;
        color: #065f46;
        border: 1px solid #10b981;
    }

    .status-inactive {
        background-color: #fee2e2;
        color: #991b1b;
        border: 1px solid #ef4444;
    }

    /* Action buttons */
    .action-btn {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        padding: 0.5rem 1rem;
        border-radius: 0.75rem;
        font-weight: 500;
        font-size: 0.875rem;
        transition: all 0.3s ease;
        border: none;
        cursor: pointer;
        text-decoration: none;
    }

    .action-btn-toggle {
        background: rgba(239, 68, 68, 0.1);
        color: #ef4444;
        border: 1px solid rgba(239, 68, 68, 0.2);
    }

    .action-btn-toggle:hover {
        background: rgba(239, 68, 68, 0.2);
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
    }

    .action-btn-toggle.activate {
        background: rgba(34, 197, 94, 0.1);
        color: #22c55e;
        border: 1px solid rgba(34, 197, 94, 0.2);
    }

    .action-btn-toggle.activate:hover {
        background: rgba(34, 197, 94, 0.2);
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(34, 197, 94, 0.3);
    }
</style>
";

// Start output buffering to capture the page content
ob_start();

?>

<!-- Enhanced Page Header -->
<div class="relative overflow-hidden bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 rounded-2xl p-8 mb-8 border border-blue-100">
    <div class="absolute inset-0 bg-gradient-to-br from-blue-500/5 via-indigo-500/5 to-purple-500/5"></div>

    <!-- Subtle decorative elements -->
    <div class="absolute top-0 right-0 -mt-8 -mr-8 w-32 h-32 bg-gradient-to-br from-blue-200/20 to-indigo-200/20 rounded-full blur-3xl"></div>
    <div class="absolute bottom-0 left-0 -mb-8 -ml-8 w-40 h-40 bg-gradient-to-br from-indigo-200/20 to-purple-200/20 rounded-full blur-3xl"></div>

    <div class="relative flex flex-col lg:flex-row justify-between items-start lg:items-center space-y-4 lg:space-y-0">
        <div>
            <div class="flex items-center space-x-4 mb-4">
                <div class="w-14 h-14 bg-gradient-to-br from-purple-500 to-pink-600 rounded-2xl flex items-center justify-center shadow-lg">
                    <i class="fas fa-gift text-white text-xl"></i>
                </div>
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">Bonus Manager</h1>
                    <p class="text-gray-600 mt-1">Create and manage bonus rewards for team investments</p>
                </div>
            </div>
            <div class="flex items-center space-x-6 text-sm text-gray-600">
                <div class="flex items-center space-x-2">
                    <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span>Active Rewards</span>
                </div>
                <div class="flex items-center space-x-2">
                    <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
                    <span>Team Investment</span>
                </div>
                <div class="flex items-center space-x-2">
                    <div class="w-2 h-2 bg-purple-500 rounded-full"></div>
                    <span>Admin Control</span>
                </div>
            </div>
        </div>
        <div class="flex items-center space-x-3">
            <a href="level_management.php" class="inline-flex items-center px-4 py-2 bg-white border border-gray-200 text-gray-700 rounded-lg hover:bg-gray-50 transition-all duration-200 shadow-sm">
                <i class="fas fa-layer-group mr-2 text-indigo-600"></i>
                Level Management
            </a>
            <div class="flex items-center space-x-2 bg-blue-50 px-4 py-2 rounded-lg border border-blue-200">
                <i class="fas fa-gift text-blue-600"></i>
                <span class="text-sm font-medium text-blue-700">Bonus Manager</span>
            </div>
        </div>
    </div>
</div>
<!-- Statistics Section -->
<?php
// Calculate bonus statistics
$total_bonuses = $result->num_rows;
$active_bonuses = 0;
$inactive_bonuses = 0;
$total_reward_value = 0;

// Reset result pointer to count statistics
$result->data_seek(0);
while ($row = $result->fetch_assoc()) {
    if ($row['is_active']) {
        $active_bonuses++;
    } else {
        $inactive_bonuses++;
    }
    $total_reward_value += $row['bonus_fund'];
}
// Reset result pointer for table display
$result->data_seek(0);
?>

<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
    <div class="admin-card p-6">
        <div class="flex items-center">
            <div class="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">
                <i class="fas fa-gift text-blue-600 text-xl"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">Total Bonuses</p>
                <p class="text-2xl font-bold text-gray-900"><?php echo number_format($total_bonuses); ?></p>
            </div>
        </div>
    </div>

    <div class="admin-card p-6">
        <div class="flex items-center">
            <div class="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center">
                <i class="fas fa-check-circle text-green-600 text-xl"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">Active Bonuses</p>
                <p class="text-2xl font-bold text-gray-900"><?php echo number_format($active_bonuses); ?></p>
            </div>
        </div>
    </div>

    <div class="admin-card p-6">
        <div class="flex items-center">
            <div class="w-12 h-12 bg-red-100 rounded-xl flex items-center justify-center">
                <i class="fas fa-times-circle text-red-600 text-xl"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">Inactive Bonuses</p>
                <p class="text-2xl font-bold text-gray-900"><?php echo number_format($inactive_bonuses); ?></p>
            </div>
        </div>
    </div>

    <div class="admin-card p-6">
        <div class="flex items-center">
            <div class="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center">
                <i class="fas fa-dollar-sign text-purple-600 text-xl"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">Total Rewards</p>
                <p class="text-2xl font-bold text-gray-900">$<?php echo number_format($total_reward_value, 2); ?></p>
            </div>
        </div>
    </div>
</div>

<!-- Create Bonus Form -->
<div class="bonus-form">
    <div class="flex items-center space-x-4 mb-8">
        <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-600 rounded-xl flex items-center justify-center shadow-lg">
            <i class="fas fa-plus text-white text-lg"></i>
        </div>
        <div>
            <h2 class="text-2xl font-bold text-gray-900">Create New Bonus</h2>
            <p class="text-gray-600">Add a new bonus reward for team investments</p>
        </div>
    </div>

    <form action="bonus_management.php" method="POST">
        <input type="hidden" name="action" value="create">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
                <label for="bonus_name" class="form-label">
                    <i class="fas fa-tag mr-2"></i>Bonus Name
                </label>
                <input type="text" name="bonus_name" id="bonus_name" required class="form-input" placeholder="Enter bonus name">
            </div>
            <div>
                <label for="team_investment" class="form-label">
                    <i class="fas fa-users mr-2"></i>Required Investment ($)
                </label>
                <input type="number" name="team_investment" id="team_investment" step="0.01" required class="form-input" placeholder="0.00">
            </div>
            <div>
                <label for="bonus_fund" class="form-label">
                    <i class="fas fa-award mr-2"></i>Reward Amount ($)
                </label>
                <input type="number" name="bonus_fund" id="bonus_fund" step="0.01" required class="form-input" placeholder="0.00">
            </div>
        </div>
        <div class="pt-6">
            <button type="submit" class="submit-btn">
                <span>
                    <i class="fas fa-plus mr-3"></i>
                    Create Bonus
                </span>
            </button>
            <p class="text-center text-xs text-gray-500 mt-3">
                <i class="fas fa-info-circle mr-1"></i>
                Bonus will be available for team investment rewards
            </p>
        </div>
    </form>
</div>

<!-- Bonuses Table -->
<div class="admin-card p-0 overflow-hidden">
    <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center space-x-3">
            <div class="w-8 h-8 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-lg flex items-center justify-center">
                <i class="fas fa-list text-white text-sm"></i>
            </div>
            <div>
                <h3 class="text-lg font-semibold text-gray-900">Manage Bonuses</h3>
                <p class="text-sm text-gray-600">View and manage all bonus rewards</p>
            </div>
        </div>
    </div>

    <div class="overflow-x-auto">
        <table class="admin-table">
            <thead>
                <tr>
                    <th>Name</th>
                    <th>Required Investment</th>
                    <th>Reward Amount</th>
                    <th>Status</th>
                    <th>Created At</th>
                    <th>Last Updated</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                <?php if ($result->num_rows > 0): ?>
                    <?php while ($row = $result->fetch_assoc()): ?>
                        <tr class="<?php echo $row['is_active'] ? '' : 'opacity-60'; ?>">
                            <td>
                                <div class="flex items-center space-x-3">
                                    <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                        <i class="fas fa-gift text-blue-600 text-xs"></i>
                                    </div>
                                    <div class="text-sm font-medium text-gray-900">
                                        <?php echo htmlspecialchars($row['bonus_name']); ?>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div class="flex items-center space-x-2">
                                    <div class="w-6 h-6 bg-green-100 rounded flex items-center justify-center">
                                        <i class="fas fa-dollar-sign text-green-600 text-xs"></i>
                                    </div>
                                    <span class="text-lg font-bold text-green-600">
                                        $<?php echo number_format($row['team_investment'], 2); ?>
                                    </span>
                                </div>
                            </td>
                            <td>
                                <div class="flex items-center space-x-2">
                                    <div class="w-6 h-6 bg-purple-100 rounded flex items-center justify-center">
                                        <i class="fas fa-award text-purple-600 text-xs"></i>
                                    </div>
                                    <span class="text-lg font-bold text-purple-600">
                                        $<?php echo number_format($row['bonus_fund'], 2); ?>
                                    </span>
                                </div>
                            </td>
                            <td>
                                <span class="status-badge <?php echo $row['is_active'] ? 'status-active' : 'status-inactive'; ?>">
                                    <?php echo $row['is_active'] ? 'Active' : 'Inactive'; ?>
                                </span>
                            </td>
                            <td>
                                <div class="text-sm text-gray-900">
                                    <div class="font-medium"><?php echo date('M j, Y', strtotime($row['created_at'])); ?></div>
                                    <div class="text-xs text-gray-500"><?php echo date('g:i A', strtotime($row['created_at'])); ?></div>
                                </div>
                            </td>
                            <td>
                                <div class="text-sm text-gray-900">
                                    <div class="font-medium"><?php echo date('M j, Y', strtotime($row['updated_at'])); ?></div>
                                    <div class="text-xs text-gray-500"><?php echo date('g:i A', strtotime($row['updated_at'])); ?></div>
                                </div>
                            </td>
                            <td>
                                <div class="flex items-center justify-center">
                                    <form action="bonus_management.php" method="POST" class="inline-block">
                                        <input type="hidden" name="action" value="toggle_active">
                                        <input type="hidden" name="id" value="<?php echo $row['id']; ?>">
                                        <input type="hidden" name="current_status" value="<?php echo $row['is_active']; ?>">
                                        <button type="submit" class="action-btn action-btn-toggle <?php echo $row['is_active'] ? '' : 'activate'; ?>">
                                            <i class="fas fa-<?php echo $row['is_active'] ? 'times' : 'check'; ?> mr-1"></i>
                                            <?php echo $row['is_active'] ? 'Deactivate' : 'Activate'; ?>
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                    <?php endwhile; ?>
                <?php else: ?>
                    <tr>
                        <td colspan="7" style="text-align: center; padding: 3rem;">
                            <div class="flex flex-col items-center">
                                <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                                    <i class="fas fa-gift text-gray-400 text-2xl"></i>
                                </div>
                                <h3 class="text-lg font-medium text-gray-900 mb-2">No Bonuses Found</h3>
                                <p class="text-sm text-gray-500 mb-4">Create your first bonus using the form above.</p>
                            </div>
                        </td>
                    </tr>
                <?php endif; ?>
            </tbody>
        </table>
    </div>
</div>

<?php
// Capture the page content
$page_content = ob_get_clean();

// Close database connection
$conn->close();

// Include the admin layout
include 'admin/includes/layout.php';
?>