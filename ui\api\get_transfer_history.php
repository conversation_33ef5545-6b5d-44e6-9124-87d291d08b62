<?php
ini_set('display_errors', 0);
ini_set('log_errors', 1);
ini_set('error_log', 'c:/xampp/htdocs/netvis/php_errors.log');
error_reporting(E_ALL);
session_start();
header('Content-Type: application/json');

if (!isset($_SESSION['user_id'])) {
    echo json_encode([]);
    exit();
}

include '../dbcon.php';

$user_id = $_SESSION['user_id'];

// Fetch transfer history where the user was the sender
$sql = "SELECT 
            wh.amount, 
            wh.created_at, 
            wh.description, 
            u.name AS recipient_name
        FROM 
            wallet_history wh
        LEFT JOIN 
            users u ON wh.related_user_id = u.id
        WHERE 
            wh.user_id = ? AND wh.type = 'fund_transfer_debit'
        ORDER BY 
            wh.created_at DESC";

$stmt = $conn->prepare($sql);
if ($stmt === false) {
    echo json_encode([]);
    exit();
}
$stmt->bind_param('i', $user_id);
if (!$stmt->execute()) {
    echo json_encode([]);
    exit();
}
$stmt->store_result();
$stmt->bind_result($amount, $created_at, $description, $recipient_name);

$history = [];
while ($stmt->fetch()) {
    $history[] = [
        'amount' => abs($amount),
        'created_at' => $created_at,
        'description' => $description,
        'recipient_name' => $recipient_name ?? 'N/A'
    ];
}

$stmt->close();
$conn->close();

echo json_encode($history);
?>
