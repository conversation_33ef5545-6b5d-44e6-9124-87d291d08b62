<?php
session_start();

if (!isset($_SESSION['user_id']) || !$_SESSION['is_admin']) {
    header('Location: login.php');
    exit();
}

include 'dbcon.php';

// SQL to create packages table if it doesn't exist
$sql = 'CREATE TABLE IF NOT EXISTS `packages` (
  `id` int(6) UNSIGNED NOT NULL AUTO_INCREMENT,
  `package_name` varchar(255) NOT NULL,
  `package_amount` decimal(10,2) NOT NULL,
  `profit_percentage` decimal(5,2) NOT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `created_by` int(6) UNSIGNED NOT NULL,
  `updated_by` int(6) UNSIGNED DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `deleted_at` timestamp NULL DEFAULT NULL,
  `is_deleted` tinyint(1) NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`),
  <PERSON><PERSON><PERSON> `created_by` (`created_by`),
  KEY `updated_by` (`updated_by`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;';
$conn->query($sql);

// Add columns if they don't exist (for existing installations)
$conn->query("ALTER TABLE packages ADD COLUMN IF NOT EXISTS deleted_at TIMESTAMP NULL DEFAULT NULL");
$conn->query("ALTER TABLE packages ADD COLUMN IF NOT EXISTS is_deleted TINYINT(1) NOT NULL DEFAULT 0");

// Ensure default admin user exists
$check_admin = $conn->query("SELECT COUNT(*) as count FROM users WHERE id = 1");
if ($check_admin) {
    $admin_count = $check_admin->fetch_assoc()['count'];
    if ($admin_count == 0) {
        // Create default admin user if it doesn't exist
        $default_password = password_hash('admin123', PASSWORD_DEFAULT);
        $conn->query("INSERT IGNORE INTO users (id, user_id, name, email, password, is_admin, is_active) VALUES
                     (1, 'ADMIN001', 'System Administrator', '<EMAIL>', '$default_password', 1, 1)");
    } else {
        // Ensure user ID 1 is an admin
        $conn->query("UPDATE users SET is_admin = 1, is_active = 1 WHERE id = 1");
    }
}

// Handle form submissions for CRUD operations
$action = $_POST['action'] ?? '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $admin_id = $_SESSION['user_id'];

    // Ensure we have a valid admin user ID
    if (!$admin_id) {
        // Fallback to default admin if session is invalid
        $admin_id = 1;
    }

    switch ($action) {
        case 'create':
            $package_name = $_POST['package_name'];
            $package_amount = $_POST['package_amount'];
            $profit_percentage = $_POST['profit_percentage'];

            // Verify that the admin user exists in the users table
            $check_user = $conn->prepare('SELECT id FROM users WHERE id = ? AND is_admin = 1');
            $check_user->bind_param('i', $admin_id);
            $check_user->execute();
            $user_result = $check_user->get_result();

            if ($user_result->num_rows === 0) {
                // Admin user doesn't exist, use default admin (ID = 1) or create error
                $admin_id = 1; // Fallback to default admin
            }
            $check_user->close();

            $stmt = $conn->prepare('INSERT INTO packages (package_name, package_amount, profit_percentage, created_by) VALUES (?, ?, ?, ?)');
            $stmt->bind_param('sddi', $package_name, $package_amount, $profit_percentage, $admin_id);
            $stmt->execute();
            $stmt->close();
            break;

        case 'toggle_active':
            $id = $_POST['id'];
            $current_status = $_POST['current_status'];
            $new_status = $current_status ? 0 : 1;

            // Verify that the admin user exists in the users table
            $check_user = $conn->prepare('SELECT id FROM users WHERE id = ?');
            $check_user->bind_param('i', $admin_id);
            $check_user->execute();
            $user_result = $check_user->get_result();

            if ($user_result->num_rows === 0) {
                // Admin user doesn't exist, set updated_by to NULL
                $stmt = $conn->prepare('UPDATE packages SET is_active = ?, updated_by = NULL WHERE id = ?');
                $stmt->bind_param('ii', $new_status, $id);
            } else {
                // Admin user exists, use it
                $stmt = $conn->prepare('UPDATE packages SET is_active = ?, updated_by = ? WHERE id = ?');
                $stmt->bind_param('iii', $new_status, $admin_id, $id);
            }
            $check_user->close();

            $stmt->execute();
            $stmt->close();
            break;

        case 'delete':
            $id = $_POST['id'];

            // Check if package is being used by any user in package_history
            $check_history = $conn->prepare('SELECT COUNT(*) as count FROM package_history ph JOIN packages p ON ph.package_name = p.package_name WHERE p.id = ? AND p.is_deleted = 0');
            $check_history->bind_param('i', $id);
            $check_history->execute();
            $history_result = $check_history->get_result();
            $history_count = $history_result->fetch_assoc()['count'];
            $check_history->close();

            // Check if package is being used by any user in users table
            $check_users = $conn->prepare('SELECT COUNT(*) as count FROM users u JOIN packages p ON u.package = p.package_name WHERE p.id = ? AND p.is_deleted = 0');
            $check_users->bind_param('i', $id);
            $check_users->execute();
            $users_result = $check_users->get_result();
            $users_count = $users_result->fetch_assoc()['count'];
            $check_users->close();

            if ($history_count > 0 || $users_count > 0) {
                // Package is in use, redirect with error
                header('Location: package_manager.php?error=package_in_use');
                exit();
            } else {
                // Soft delete - set deleted_at timestamp and is_deleted flag
                $check_user = $conn->prepare('SELECT id FROM users WHERE id = ?');
                $check_user->bind_param('i', $admin_id);
                $check_user->execute();
                $user_result = $check_user->get_result();

                if ($user_result->num_rows === 0) {
                    // Admin user doesn't exist, set updated_by to NULL
                    $stmt = $conn->prepare('UPDATE packages SET deleted_at = NOW(), is_deleted = 1, updated_by = NULL WHERE id = ?');
                    $stmt->bind_param('i', $id);
                } else {
                    // Admin user exists, use it
                    $stmt = $conn->prepare('UPDATE packages SET deleted_at = NOW(), is_deleted = 1, updated_by = ? WHERE id = ?');
                    $stmt->bind_param('ii', $admin_id, $id);
                }
                $check_user->close();

                $stmt->execute();
                $stmt->close();
                header('Location: package_manager.php?success=package_deleted');
                exit();
            }
            break;

        case 'restore':
            $id = $_POST['id'];

            // Restore package by setting deleted_at to NULL and is_deleted to 0
            $check_user = $conn->prepare('SELECT id FROM users WHERE id = ?');
            $check_user->bind_param('i', $admin_id);
            $check_user->execute();
            $user_result = $check_user->get_result();

            if ($user_result->num_rows === 0) {
                // Admin user doesn't exist, set updated_by to NULL
                $stmt = $conn->prepare('UPDATE packages SET deleted_at = NULL, is_deleted = 0, updated_by = NULL WHERE id = ?');
                $stmt->bind_param('i', $id);
            } else {
                // Admin user exists, use it
                $stmt = $conn->prepare('UPDATE packages SET deleted_at = NULL, is_deleted = 0, updated_by = ? WHERE id = ?');
                $stmt->bind_param('ii', $admin_id, $id);
            }
            $check_user->close();

            $stmt->execute();
            $stmt->close();
            header('Location: package_manager.php?success=package_restored');
            exit();
            break;
    }
    header('Location: package_manager.php');
    exit();
}

// Check if showing deleted packages
$show_deleted = isset($_GET['show_deleted']) && $_GET['show_deleted'] == '1';
$deleted_condition = $show_deleted ? 'p.is_deleted = 1' : 'p.is_deleted = 0';

// Fetch packages with usage information
$sql = "SELECT p.*,
        creator.name as creator_name,
        updater.name as updater_name,
        COALESCE(ph.history_count, 0) as history_usage,
        COALESCE(u.user_count, 0) as user_usage,
        (COALESCE(ph.history_count, 0) + COALESCE(u.user_count, 0)) as total_usage
        FROM packages p
        JOIN users creator ON p.created_by = creator.id
        LEFT JOIN users updater ON p.updated_by = updater.id
        LEFT JOIN (
            SELECT p2.id, COUNT(*) as history_count
            FROM packages p2
            JOIN package_history ph ON ph.package_name = p2.package_name
            GROUP BY p2.id
        ) ph ON p.id = ph.id
        LEFT JOIN (
            SELECT p3.id, COUNT(*) as user_count
            FROM packages p3
            JOIN users u ON u.package = p3.package_name
            GROUP BY p3.id
        ) u ON p.id = u.id
        WHERE $deleted_condition
        ORDER BY p.id DESC";
$result = $conn->query($sql);

// Page configuration
$page_title = "Package Management";

// Add SweetAlert2 to the page
$additional_js = '
<!-- SweetAlert2 -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
';

$additional_css = '
<style>
    .form-input {
        border: 1px solid #d1d5db;
        border-radius: 0.5rem;
        padding: 0.5rem 0.75rem;
        width: 100%;
        transition: border-color 0.2s ease;
    }

    .form-input:focus {
        outline: none;
        border-color: #3b82f6;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }

    .submit-btn {
        background: #3b82f6;
        color: white;
        border: none;
        padding: 0.5rem 1rem;
        border-radius: 0.5rem;
        font-weight: 500;
        transition: background-color 0.2s ease;
        cursor: pointer;
    }

    .submit-btn:hover {
        background: #2563eb;
    }

    .admin-table {
        width: 100%;
        border-collapse: separate;
        border-spacing: 0;
        background: white;
        border-radius: 0.75rem;
        overflow: hidden;
        box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
    }

    .admin-table thead {
        background: linear-gradient(135deg, #3b82f6 0%, #6366f1 100%);
    }

    .admin-table thead th {
        color: white;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.05em;
        padding: 1rem 0.75rem;
        text-align: left;
        font-size: 0.75rem;
        border: none;
        position: relative;
    }

    .admin-table thead th:first-child {
        padding-left: 1.5rem;
    }

    .admin-table thead th:last-child {
        text-align: center;
        padding-right: 1.5rem;
    }

    .admin-table tbody tr {
        border-bottom: 1px solid #e5e7eb;
        transition: all 0.2s ease;
    }

    .admin-table tbody tr:hover {
        background-color: #f9fafb;
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    }

    .admin-table tbody tr:last-child {
        border-bottom: none;
    }

    .admin-table tbody td {
        padding: 1rem 0.75rem;
        vertical-align: middle;
        background-color: white;
        border: none;
    }

    .admin-table tbody td:first-child {
        padding-left: 1.5rem;
    }

    .admin-table tbody td:last-child {
        text-align: center;
        padding-right: 1.5rem;
    }

    .admin-table tbody tr:hover td {
        background-color: #f9fafb;
    }

    /* Status badges */
    .status-badge {
        display: inline-flex;
        align-items: center;
        padding: 0.25rem 0.75rem;
        border-radius: 9999px;
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.05em;
    }

    .status-active {
        background-color: #d1fae5;
        color: #065f46;
        border: 1px solid #10b981;
    }

    .status-inactive {
        background-color: #fee2e2;
        color: #991b1b;
        border: 1px solid #ef4444;
    }

    .action-btn {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        padding: 0.375rem;
        border-radius: 0.375rem;
        font-size: 0.875rem;
        transition: all 0.2s ease;
        border: 1px solid;
        cursor: pointer;
        text-decoration: none;
        margin: 0 0.125rem;
        width: 2rem;
        height: 2rem;
    }

    .action-btn-edit {
        background: #eff6ff;
        color: #3b82f6;
        border-color: #dbeafe;
    }

    .action-btn-edit:hover {
        background: #dbeafe;
    }

    .action-btn-toggle {
        background: #fef2f2;
        color: #ef4444;
        border-color: #fecaca;
    }

    .action-btn-toggle:hover {
        background: #fecaca;
    }

    .action-btn-toggle.activate {
        background: #f0fdf4;
        color: #22c55e;
        border-color: #bbf7d0;
    }

    .action-btn-toggle.activate:hover {
        background: #bbf7d0;
    }

    .action-btn-delete {
        background: #fef2f2;
        color: #dc2626;
        border-color: #fecaca;
    }

    .action-btn-delete:hover {
        background: #fecaca;
    }

    .action-btn-delete.disabled {
        background: #f3f4f6;
        color: #9ca3af;
        border-color: #e5e7eb;
        cursor: not-allowed;
        opacity: 0.5;
    }

    .action-btn-delete.disabled:hover {
        background: #f3f4f6;
    }

    .action-btn-restore {
        background: #f0f9ff;
        color: #0284c7;
        border-color: #bae6fd;
    }

    .action-btn-restore:hover {
        background: #bae6fd;
    }
</style>
';

// Start output buffering to capture the page content
ob_start();

// Handle alert messages
$alert_message = '';
$should_clean_url = false;

if (isset($_GET['error']) && $_GET['error'] == 'package_in_use') {
    $alert_message = '<div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4" role="alert">
        <strong>Cannot Delete Package!</strong> This package is currently being used by users and cannot be deleted.
    </div>';
    $should_clean_url = true;
}
if (isset($_GET['success'])) {
    if ($_GET['success'] == 'package_deleted') {
        $alert_message = '<div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4" role="alert">
            <strong>Success!</strong> Package has been deleted successfully.
        </div>';
        $should_clean_url = true;
    } elseif ($_GET['success'] == 'package_restored') {
        $alert_message = '<div class="bg-blue-100 border border-blue-400 text-blue-700 px-4 py-3 rounded mb-4" role="alert">
            <strong>Success!</strong> Package has been restored successfully.
        </div>';
        $should_clean_url = true;
    }
}
?>

<!-- Page Header -->
<div class="flex flex-col lg:flex-row justify-between items-start lg:items-center space-y-4 lg:space-y-0 mb-6">
    <div>
        <h1 class="text-3xl font-bold text-gray-900">Package Management</h1>
        <p class="text-gray-600 mt-1">Create and manage investment packages</p>
    </div>
    <div class="flex items-center space-x-3">
        <?php if ($show_deleted): ?>
            <a href="package_manager.php" class="inline-flex items-center px-3 py-2 bg-green-100 text-green-700 rounded-lg hover:bg-green-200 transition-all duration-200 text-sm">
                <i class="fas fa-arrow-left mr-2"></i>
                Back to Active
            </a>
            <div class="flex items-center space-x-2 bg-red-50 px-4 py-2 rounded-lg border border-red-200">
                <i class="fas fa-trash text-red-600"></i>
                <span class="text-sm font-medium text-red-700">Deleted Packages</span>
            </div>
        <?php else: ?>
            <a href="?show_deleted=1" class="inline-flex items-center px-3 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-all duration-200 text-sm">
                <i class="fas fa-trash-restore mr-2"></i>
                View Deleted
            </a>
            <div class="flex items-center space-x-2 bg-blue-50 px-4 py-2 rounded-lg border border-blue-200">
                <i class="fas fa-box text-blue-600"></i>
                <span class="text-sm font-medium text-blue-700">Package Manager</span>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Alert Messages -->
<?php echo $alert_message; ?>

<!-- Package Statistics -->
<?php
// Calculate package statistics
$total_packages = $result->num_rows;
$active_packages = 0;
$inactive_packages = 0;
$total_value = 0;

// Reset result pointer to count statistics
$result->data_seek(0);
while ($row = $result->fetch_assoc()) {
    if ($row['is_active']) {
        $active_packages++;
    } else {
        $inactive_packages++;
    }
    $total_value += $row['package_amount'];
}
// Reset result pointer for table display
$result->data_seek(0);
?>

<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
    <div class="admin-card p-4">
        <div class="flex items-center">
            <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                <i class="fas fa-box text-blue-600"></i>
            </div>
            <div class="ml-3">
                <p class="text-sm text-gray-600">Total Packages</p>
                <p class="text-xl font-bold text-gray-900"><?php echo $total_packages; ?></p>
            </div>
        </div>
    </div>

    <div class="admin-card p-4">
        <div class="flex items-center">
            <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                <i class="fas fa-check-circle text-green-600"></i>
            </div>
            <div class="ml-3">
                <p class="text-sm text-gray-600">Active Packages</p>
                <p class="text-xl font-bold text-gray-900"><?php echo $active_packages; ?></p>
            </div>
        </div>
    </div>

    <div class="admin-card p-4">
        <div class="flex items-center">
            <div class="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center">
                <i class="fas fa-times-circle text-red-600"></i>
            </div>
            <div class="ml-3">
                <p class="text-sm text-gray-600">Inactive Packages</p>
                <p class="text-xl font-bold text-gray-900"><?php echo $inactive_packages; ?></p>
            </div>
        </div>
    </div>

    <div class="admin-card p-4">
        <div class="flex items-center">
            <div class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                <i class="fas fa-dollar-sign text-purple-600"></i>
            </div>
            <div class="ml-3">
                <p class="text-sm text-gray-600">Total Value</p>
                <p class="text-xl font-bold text-gray-900">$<?php echo number_format($total_value, 2); ?></p>
            </div>
        </div>
    </div>
</div>

<!-- Create Package Form -->
<div class="admin-card p-4 mb-6">
    <h3 class="text-lg font-semibold text-gray-900 mb-4">
        <i class="fas fa-plus mr-2 text-blue-600"></i>Create New Package
    </h3>

    <form action="package_manager.php" method="POST">
        <input type="hidden" name="action" value="create">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
                <input type="text" name="package_name" required class="form-input" placeholder="Package Name">
            </div>
            <div>
                <input type="number" name="package_amount" step="0.01" required class="form-input" placeholder="Amount ($)">
            </div>
            <div>
                <input type="number" name="profit_percentage" step="0.01" required class="form-input" placeholder="Profit (%)">
            </div>
            <div>
                <button type="submit" class="submit-btn w-full">
                    <i class="fas fa-plus mr-1"></i>Create
                </button>
            </div>
        </div>
    </form>
</div>

<!-- Packages Table -->
<div class="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
    <!-- <div class="px-6 py-4 bg-gradient-to-r from-gray-50 to-gray-100 border-b border-gray-200">
        <div class="flex items-center space-x-3">
            <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                <i class="fas fa-list text-blue-600 text-sm"></i>
            </div>
            <div>
                <h3 class="text-lg font-semibold text-gray-900">Manage Packages</h3>
                <p class="text-sm text-gray-600">View and manage all investment packages</p>
            </div>
        </div>
    </div> -->

    <div class="overflow-x-auto">
        <table class="admin-table">
            <thead>
                <tr>
                    <th>Package</th>
                    <th>Amount</th>
                    <th>Profit %</th>
                    <th>Status</th>
                    <th>Created</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                <?php if ($result->num_rows > 0): ?>
                    <?php while ($row = $result->fetch_assoc()): ?>
                        <tr class="<?php echo $row['is_active'] ? '' : 'opacity-60'; ?>">
                            <td>
                                <div class="font-medium text-gray-900">
                                    <?php echo htmlspecialchars($row['package_name']); ?>
                                </div>
                            </td>
                            <td>
                                <span class="font-bold text-green-600">
                                    $<?php echo number_format($row['package_amount'], 2); ?>
                                </span>
                            </td>
                            <td>
                                <span class="font-medium text-purple-600">
                                    <?php echo number_format($row['profit_percentage'], 2); ?>%
                                </span>
                            </td>
                            <td>
                                <span class="status-badge <?php echo $row['is_active'] ? 'status-active' : 'status-inactive'; ?>">
                                    <?php echo $row['is_active'] ? 'Active' : 'Inactive'; ?>
                                </span>
                            </td>
                            <td>
                                <div class="text-sm text-gray-900">
                                    <div><?php echo htmlspecialchars($row['creator_name']); ?></div>
                                    <div class="text-xs text-gray-500"><?php echo date('M j, Y', strtotime($row['created_at'])); ?></div>
                                </div>
                            </td>
                            <td>
                                <div class="flex items-center space-x-1">
                                    <?php if ($show_deleted): ?>
                                        <!-- Actions for deleted packages -->
                                        <form action="package_manager.php" method="POST" class="inline-block">
                                            <input type="hidden" name="action" value="restore">
                                            <input type="hidden" name="id" value="<?php echo $row['id']; ?>">
                                            <button type="submit" class="action-btn action-btn-restore" title="Restore Package">
                                                <i class="fas fa-undo"></i>
                                            </button>
                                        </form>
                                        <span class="text-xs text-gray-500">Deleted: <?php echo date('M j, Y', strtotime($row['deleted_at'])); ?></span>
                                    <?php else: ?>
                                        <!-- Actions for active packages -->
                                        <a href="edit_package.php?id=<?php echo $row['id']; ?>" class="action-btn action-btn-edit" title="Edit Package">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <form action="package_manager.php" method="POST" class="inline-block">
                                            <input type="hidden" name="action" value="toggle_active">
                                            <input type="hidden" name="id" value="<?php echo $row['id']; ?>">
                                            <input type="hidden" name="current_status" value="<?php echo $row['is_active']; ?>">
                                            <button type="submit" class="action-btn action-btn-toggle <?php echo $row['is_active'] ? '' : 'activate'; ?>" title="<?php echo $row['is_active'] ? 'Deactivate' : 'Activate'; ?> Package">
                                                <i class="fas fa-<?php echo $row['is_active'] ? 'times' : 'check'; ?>"></i>
                                            </button>
                                        </form>
                                        <?php if ($row['total_usage'] > 0): ?>
                                            <button class="action-btn action-btn-delete disabled" disabled title="Cannot delete - Package is being used by <?php echo $row['total_usage']; ?> user(s)">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        <?php else: ?>
                                            <button type="button" class="action-btn action-btn-delete" title="Delete Package"
                                                onclick="confirmDelete(<?php echo $row['id']; ?>, '<?php echo htmlspecialchars($row['package_name']); ?>')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        <?php endif; ?>
                                    <?php endif; ?>
                                </div>
                            </td>
                        </tr>
                    <?php endwhile; ?>
                <?php else: ?>
                    <tr>
                        <td colspan="6" style="text-align: center; padding: 2rem;">
                            <div class="text-gray-500">
                                <i class="fas fa-box text-2xl mb-2"></i>
                                <p>No packages found. Create your first package above.</p>
                            </div>
                        </td>
                    </tr>
                <?php endif; ?>
            </tbody>
        </table>
    </div>
</div>

<script>
    function confirmDelete(packageId, packageName) {
        Swal.fire({
            title: 'Delete Package?',
            html: `Are you sure you want to delete the <strong>"${packageName}"</strong> package?<br><br><span style="color: #f59e0b;">The package will be hidden from the system but data will be preserved.</span>`,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#ef4444',
            cancelButtonColor: '#6b7280',
            confirmButtonText: '<i class="fas fa-trash mr-2"></i>Yes, delete it!',
            cancelButtonText: '<i class="fas fa-times mr-2"></i>Cancel',
            reverseButtons: true,
            focusCancel: true,
            customClass: {
                confirmButton: 'font-semibold',
                cancelButton: 'font-semibold'
            }
        }).then((result) => {
            if (result.isConfirmed) {
                // Create and submit form
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = 'package_manager.php';

                const actionInput = document.createElement('input');
                actionInput.type = 'hidden';
                actionInput.name = 'action';
                actionInput.value = 'delete';

                const idInput = document.createElement('input');
                idInput.type = 'hidden';
                idInput.name = 'id';
                idInput.value = packageId;

                form.appendChild(actionInput);
                form.appendChild(idInput);
                document.body.appendChild(form);
                form.submit();
            }
        });
    }

    // Clean URL parameters after showing alert messages
    <?php if ($should_clean_url): ?>
        document.addEventListener('DOMContentLoaded', function() {
            // Wait a moment for the alert to be visible, then clean the URL
            setTimeout(function() {
                // Get current URL without parameters
                const url = new URL(window.location);
                url.searchParams.delete('success');
                url.searchParams.delete('error');

                // Update URL without refreshing the page
                window.history.replaceState({}, document.title, url.toString());
            }, 100); // Small delay to ensure alert is visible
        });
    <?php endif; ?>
</script>

<?php
// Capture the page content
$page_content = ob_get_clean();

// Close database connection
$conn->close();

// Include the admin layout
include 'admin/includes/layout.php';
?>