<?php
session_start();

// Check if user is admin
if (!isset($_SESSION['user_id']) || !$_SESSION['is_admin']) {
    header("Location: login.html");
    exit();
}

include 'dbcon.php';

echo "<h2>Update Staking Tables to 4 Decimal Places</h2>";
echo "<p>This will update the staking_records table to use 4 decimal places instead of 2 for better precision.</p>";

$success_count = 0;
$error_count = 0;
$messages = [];

// Update staking_records table
$alter_staking_sql = "ALTER TABLE staking_records 
                     MODIFY COLUMN amount_usd decimal(20,4) NOT NULL,
                     MODIFY COLUMN profit_earned decimal(20,4) DEFAULT 0.0000";

if ($conn->query($alter_staking_sql)) {
    $messages[] = "✅ Updated staking_records table to use 4 decimal places";
    $success_count++;
} else {
    $messages[] = "❌ Error updating staking_records table: " . $conn->error;
    $error_count++;
}

// Update users table wallet balances to 4 decimal places (if not already)
$alter_users_sql = "ALTER TABLE users 
                    MODIFY COLUMN deposit_wallet_balance decimal(20,4) NOT NULL DEFAULT 0.0000,
                    MODIFY COLUMN withdrawal_wallet_balance decimal(20,4) NOT NULL DEFAULT 0.0000,
                    MODIFY COLUMN total_earnings_received decimal(20,4) DEFAULT 0.0000";

if ($conn->query($alter_users_sql)) {
    $messages[] = "✅ Updated users table wallet balances to use 4 decimal places";
    $success_count++;
} else {
    $messages[] = "❌ Error updating users table: " . $conn->error;
    $error_count++;
}

// Update wallet_history table to 4 decimal places
$alter_wallet_history_sql = "ALTER TABLE wallet_history 
                             MODIFY COLUMN amount decimal(20,4) NOT NULL";

if ($conn->query($alter_wallet_history_sql)) {
    $messages[] = "✅ Updated wallet_history table to use 4 decimal places";
    $success_count++;
} else {
    $messages[] = "❌ Error updating wallet_history table: " . $conn->error;
    $error_count++;
}

// Update packages table to 4 decimal places
$alter_packages_sql = "ALTER TABLE packages 
                       MODIFY COLUMN package_amount decimal(10,4) NOT NULL,
                       MODIFY COLUMN profit_percentage decimal(5,4) NOT NULL";

if ($conn->query($alter_packages_sql)) {
    $messages[] = "✅ Updated packages table to use 4 decimal places";
    $success_count++;
} else {
    $messages[] = "❌ Error updating packages table: " . $conn->error;
    $error_count++;
}

// Update package_history table to 4 decimal places
$alter_package_history_sql = "ALTER TABLE package_history 
                              MODIFY COLUMN package_amount decimal(10,4) NOT NULL,
                              MODIFY COLUMN profit_percentage decimal(5,4) NOT NULL,
                              MODIFY COLUMN total_earnings_received decimal(20,4) DEFAULT 0.0000,
                              MODIFY COLUMN earnings_limit decimal(20,4) NOT NULL";

if ($conn->query($alter_package_history_sql)) {
    $messages[] = "✅ Updated package_history table to use 4 decimal places";
    $success_count++;
} else {
    $messages[] = "❌ Error updating package_history table: " . $conn->error;
    $error_count++;
}

// Display results
echo "<h3>Update Results:</h3>";
foreach ($messages as $message) {
    echo "<p>$message</p>";
}

echo "<hr>";
echo "<h3>Summary:</h3>";
echo "<p><strong>Successful updates:</strong> $success_count</p>";
echo "<p><strong>Failed updates:</strong> $error_count</p>";

if ($error_count == 0) {
    echo "<div style='background: #e8f5e8; padding: 15px; border: 1px solid #4CAF50; margin: 20px 0;'>";
    echo "<h4>✅ All Updates Successful!</h4>";
    echo "<p>All database tables have been updated to use 4 decimal places for better precision.</p>";
    echo "</div>";
} else {
    echo "<div style='background: #ffe8e8; padding: 15px; border: 1px solid #ff0000; margin: 20px 0;'>";
    echo "<h4>⚠️ Some Updates Failed</h4>";
    echo "<p>Please check the error messages above and try again.</p>";
    echo "</div>";
}

// Show current table structures
echo "<h3>Current Table Structures:</h3>";

$tables_to_check = [
    'staking_records' => ['amount_usd', 'profit_earned'],
    'users' => ['deposit_wallet_balance', 'withdrawal_wallet_balance', 'total_earnings_received'],
    'wallet_history' => ['amount'],
    'packages' => ['package_amount', 'profit_percentage'],
    'package_history' => ['package_amount', 'profit_percentage', 'total_earnings_received', 'earnings_limit']
];

foreach ($tables_to_check as $table => $columns) {
    echo "<h4>$table:</h4>";
    
    $describe_sql = "DESCRIBE $table";
    $result = $conn->query($describe_sql);
    
    if ($result) {
        echo "<table border='1' style='border-collapse: collapse; margin-bottom: 20px;'>";
        echo "<tr style='background: #f0f0f0;'><th>Column</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
        
        while ($row = $result->fetch_assoc()) {
            if (in_array($row['Field'], $columns)) {
                $highlight = strpos($row['Type'], 'decimal') !== false && strpos($row['Type'], ',4)') !== false ? 
                           'background: #e8f5e8;' : 'background: #ffe8e8;';
                echo "<tr style='$highlight'>";
                echo "<td><strong>{$row['Field']}</strong></td>";
                echo "<td>{$row['Type']}</td>";
                echo "<td>{$row['Null']}</td>";
                echo "<td>{$row['Key']}</td>";
                echo "<td>{$row['Default']}</td>";
                echo "</tr>";
            }
        }
        echo "</table>";
    } else {
        echo "<p>❌ Error describing table $table: " . $conn->error . "</p>";
    }
}

echo "<hr>";
echo "<h3>What Changed:</h3>";
echo "<div style='background: #f0f8ff; padding: 15px; border: 1px solid #4169E1; margin: 10px 0;'>";
echo "<h4>🔧 Decimal Precision Updates:</h4>";
echo "<ul>";
echo "<li><strong>staking_records</strong> - amount_usd and profit_earned now use 4 decimal places</li>";
echo "<li><strong>users</strong> - All wallet balances now use 4 decimal places</li>";
echo "<li><strong>wallet_history</strong> - Amount field now uses 4 decimal places</li>";
echo "<li><strong>packages</strong> - Package amounts and profit percentages now use 4 decimal places</li>";
echo "<li><strong>package_history</strong> - All monetary fields now use 4 decimal places</li>";
echo "</ul>";
echo "<br>";
echo "<h4>✅ Benefits:</h4>";
echo "<ul>";
echo "<li><strong>Higher Precision</strong> - Support for prices like $0.4567 instead of $0.46</li>";
echo "<li><strong>Better Calculations</strong> - More accurate staking calculations</li>";
echo "<li><strong>Consistent Format</strong> - All monetary fields use same precision</li>";
echo "<li><strong>Future-proof</strong> - Ready for micro-pricing scenarios</li>";
echo "</ul>";
echo "</div>";

echo "<h3>Next Steps:</h3>";
echo "<ol>";
echo "<li><strong>Test staking functionality</strong> - Verify 4 decimal places work correctly</li>";
echo "<li><strong>Update coin price</strong> - Use the new 4 decimal precision</li>";
echo "<li><strong>Check calculations</strong> - Ensure all math still works correctly</li>";
echo "<li><strong>Update display formats</strong> - Make sure UI shows 4 decimals where appropriate</li>";
echo "</ol>";

$conn->close();
?>
