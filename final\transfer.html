<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Transfer Funds - Crypto Wallet</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
            min-height: 100vh;
        }

        .stats-card {
            background: rgba(30, 41, 59, 0.4);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(59, 130, 246, 0.2);
            border-radius: 1rem;
            padding: 1.5rem;
            box-shadow: 0 20px 50px rgba(0, 0, 0, 0.5);
        }

        .amount-btn {
            background: rgba(30, 41, 59, 0.4);
            border: 2px solid rgba(59, 130, 246, 0.3);
            border-radius: 0.75rem;
            padding: 1rem;
            color: white;
            font-weight: 600;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .amount-btn:hover {
            border-color: rgba(6, 182, 212, 0.6);
            background: rgba(30, 41, 59, 0.6);
            transform: translateY(-2px);
        }

        .amount-btn.selected {
            background: linear-gradient(135deg, #3b82f6, #06b6d4);
            border-color: rgba(6, 182, 212, 0.8);
            box-shadow: 0 8px 25px rgba(6, 182, 212, 0.3);
            color: white !important;
        }

        .amount-btn.selected .text-slate-400 {
            color: rgba(255, 255, 255, 0.8) !important;
        }

        .custom-input {
            background: rgba(30, 41, 59, 0.4);
            border: 2px solid rgba(59, 130, 246, 0.3);
            border-radius: 0.75rem;
            padding: 1rem;
            color: white;
            font-size: 1.125rem;
            font-weight: 600;
            text-align: center;
            transition: all 0.3s ease;
        }

        .custom-input:focus {
            outline: none;
            border-color: rgba(6, 182, 212, 0.6);
            box-shadow: 0 0 0 3px rgba(6, 182, 212, 0.2);
        }

        .user-input {
            background: rgba(30, 41, 59, 0.4);
            border: 2px solid rgba(59, 130, 246, 0.3);
            border-radius: 0.75rem;
            padding: 1rem;
            color: white;
            transition: all 0.3s ease;
        }

        .user-input:focus {
            outline: none;
            border-color: rgba(6, 182, 212, 0.6);
            box-shadow: 0 0 0 3px rgba(6, 182, 212, 0.2);
        }

        /* Bottom Navigation Styles */
        .accent {
            color: #06b6d4 !important;
        }

        .hover\:accent:hover {
            color: #06b6d4 !important;
        }
    </style>
</head>

<body class="text-white">
    <div class="min-h-screen flex flex-col">
        <!-- Header -->
        <div class="sticky top-0 z-50 bg-slate-800/95 backdrop-blur-sm border-b border-slate-700">
            <div class="flex items-center justify-between px-6 py-4">
                <div class="flex items-center gap-4">
                    <button onclick="goBack()"
                        class="w-10 h-10 rounded-full bg-slate-700 hover:bg-slate-600 flex items-center justify-center transition-colors">
                        <i class="fas fa-arrow-left text-slate-300"></i>
                    </button>
                    <div>
                        <h1 class="text-xl font-bold text-white">Transfer Funds</h1>
                        <p class="text-sm text-slate-400" id="walletSource">Send money to other users</p>
                    </div>
                </div>
                <div class="flex items-center gap-2">
                    <div class="text-right">
                        <p class="text-xs text-slate-400">Available Balance</p>
                        <p class="text-lg font-bold text-cyan-400" id="availableBalance">$150.25</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-grow overflow-y-auto px-4 sm:px-6 py-6">
            <!-- Recipient Selection -->
            <div class="stats-card mb-6">
                <div class="flex items-center gap-3 mb-6">
                    <div
                        class="w-12 h-12 rounded-lg bg-gradient-to-r from-blue-600 to-cyan-600 flex items-center justify-center">
                        <i class="fas fa-user text-white text-xl"></i>
                    </div>
                    <div>
                        <h2 class="text-xl font-bold text-white">Select Recipient</h2>
                        <p class="text-sm text-slate-400">Enter username or user ID</p>
                    </div>
                </div>

                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-slate-400 mb-2">Username or User ID</label>
                        <div class="relative">
                            <span class="absolute left-4 top-1/2 transform -translate-y-1/2 text-slate-400">@</span>
                            <input type="text" id="recipientUser" class="user-input w-full pl-12"
                                placeholder="username or ID" oninput="validateRecipient()">
                        </div>
                    </div>

                    <!-- Recent Recipients -->
                    <div>
                        <p class="text-sm text-slate-400 mb-3">Recent Recipients</p>
                        <div class="grid grid-cols-1 sm:grid-cols-2 gap-3">
                            <button onclick="selectRecipient('john_doe')"
                                class="text-left p-3 bg-slate-700/30 hover:bg-slate-700/50 rounded-lg border border-slate-600/30 transition-all">
                                <div class="flex items-center gap-3">
                                    <div
                                        class="w-8 h-8 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full flex items-center justify-center">
                                        <span class="text-xs font-bold">JD</span>
                                    </div>
                                    <div>
                                        <p class="text-sm font-medium text-white">@john_doe</p>
                                        <p class="text-xs text-slate-400">Last transfer: $50</p>
                                    </div>
                                </div>
                            </button>
                            <button onclick="selectRecipient('alice_crypto')"
                                class="text-left p-3 bg-slate-700/30 hover:bg-slate-700/50 rounded-lg border border-slate-600/30 transition-all">
                                <div class="flex items-center gap-3">
                                    <div
                                        class="w-8 h-8 bg-gradient-to-r from-cyan-500 to-blue-500 rounded-full flex items-center justify-center">
                                        <span class="text-xs font-bold">AC</span>
                                    </div>
                                    <div>
                                        <p class="text-sm font-medium text-white">@alice_crypto</p>
                                        <p class="text-xs text-slate-400">Last transfer: $25</p>
                                    </div>
                                </div>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Amount Selection -->
            <div class="stats-card mb-6">
                <div class="flex items-center gap-3 mb-6">
                    <div
                        class="w-12 h-12 rounded-lg bg-gradient-to-r from-cyan-600 to-blue-600 flex items-center justify-center">
                        <i class="fas fa-dollar-sign text-white text-xl"></i>
                    </div>
                    <div>
                        <h2 class="text-xl font-bold text-white">Transfer Amount</h2>
                        <p class="text-sm text-slate-400">Choose amount in multiples of $10</p>
                    </div>
                </div>

                <!-- Quick Amount Buttons -->
                <div class="grid grid-cols-2 sm:grid-cols-3 gap-4 mb-6">
                    <button class="amount-btn" onclick="selectAmount(10)">
                        <div class="text-center">
                            <div class="text-xl font-bold">$10</div>
                            <div class="text-xs text-slate-400">Minimum</div>
                        </div>
                    </button>
                    <button class="amount-btn" onclick="selectAmount(20)">
                        <div class="text-center">
                            <div class="text-xl font-bold">$20</div>
                            <div class="text-xs text-slate-400">Quick</div>
                        </div>
                    </button>
                    <button class="amount-btn" onclick="selectAmount(50)">
                        <div class="text-center">
                            <div class="text-xl font-bold">$50</div>
                            <div class="text-xs text-slate-400">Popular</div>
                        </div>
                    </button>
                    <button class="amount-btn" onclick="selectAmount(100)">
                        <div class="text-center">
                            <div class="text-xl font-bold">$100</div>
                            <div class="text-xs text-slate-400">Standard</div>
                        </div>
                    </button>
                    <button class="amount-btn" onclick="selectAmount(250)">
                        <div class="text-center">
                            <div class="text-xl font-bold">$250</div>
                            <div class="text-xs text-slate-400">Large</div>
                        </div>
                    </button>
                    <button class="amount-btn" onclick="selectAmount(500)">
                        <div class="text-center">
                            <div class="text-xl font-bold">$500</div>
                            <div class="text-xs text-slate-400">Maximum</div>
                        </div>
                    </button>
                </div>

                <!-- Custom Amount Input -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-slate-400 mb-3">Or enter custom amount (multiples of
                        $10)</label>
                    <div class="relative">
                        <span class="absolute left-4 top-1/2 transform -translate-y-1/2 text-slate-400 text-xl">$</span>
                        <input type="number" id="customAmount" class="custom-input w-full pl-12" placeholder="0"
                            min="10" step="10" oninput="validateCustomAmount()">
                    </div>
                    <p class="text-xs text-slate-500 mt-2">Minimum: $10 • Maximum: Available Balance</p>
                </div>

                <!-- Transfer Note -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-slate-400 mb-3">Transfer Note (Optional)</label>
                    <textarea id="transferNote" class="user-input w-full h-20 resize-none"
                        placeholder="Add a note for this transfer..."></textarea>
                </div>

                <!-- Selected Amount Display -->
                <div
                    class="bg-gradient-to-r from-slate-800/50 to-slate-700/50 rounded-lg p-4 border border-blue-500/20">
                    <div class="flex items-center justify-between">
                        <span class="text-slate-400">Transfer Amount:</span>
                        <span class="text-2xl font-bold text-cyan-400" id="selectedAmount">$0</span>
                    </div>
                    <div class="flex items-center justify-between mt-3 text-sm">
                        <span class="text-slate-400">Amount Deducted:</span>
                        <span class="text-lg font-bold text-cyan-400" id="totalDeducted">$0</span>
                    </div>
                </div>
            </div>

            <!-- Transfer Button -->
            <div class="stats-card">
                <button onclick="processTransfer()" id="transferBtn" disabled
                    class="w-full bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-500 hover:to-cyan-500 disabled:from-slate-600 disabled:to-slate-700 disabled:cursor-not-allowed text-white font-bold py-4 px-6 rounded-lg text-lg transition-all duration-300 shadow-lg flex items-center justify-center gap-3">
                    <i class="fas fa-paper-plane"></i>
                    <span>Send Transfer</span>
                    <span class="text-sm opacity-75" id="transferAmount">$0</span>
                </button>
                <p class="text-xs text-slate-500 text-center mt-3">🔒 Secure transfer • No fees</p>
            </div>
        </div>

        <!-- Sticky Bottom Navigation Bar -->
        <div class="sticky bottom-0 z-40 flex-shrink-0">
            <div class="bg-slate-800/95 backdrop-blur-sm mx-4 mb-4 rounded-2xl border border-slate-700">
                <div class="flex justify-around items-center h-16">
                    <a href="1.html" class="flex flex-col items-center gap-1 text-slate-400 hover:accent">
                        <i class="fas fa-home"></i>
                        <span class="text-xs">Home</span>
                    </a>
                    <a href="wallet.html" class="flex flex-col items-center gap-1 accent">
                        <i class="fas fa-wallet"></i>
                        <span class="text-xs">Wallet</span>
                    </a>
                    <a href="team.html" class="flex flex-col items-center gap-1 text-slate-400 hover:accent">
                        <i class="fas fa-users"></i>
                        <span class="text-xs">Team</span>
                    </a>
                    <a href="profile.html" class="flex flex-col items-center gap-1 text-slate-400 hover:accent">
                        <i class="fas fa-user"></i>
                        <span class="text-xs">Profile</span>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script>
        let selectedAmountValue = 0;
        let selectedRecipient = '';
        let availableBalance = 150.25; // This would come from the wallet

        function goBack() {
            window.history.back();
        }

        // Check if coming from withdrawal wallet
        function checkSource() {
            const urlParams = new URLSearchParams(window.location.search);
            const from = urlParams.get('from');

            if (from === 'withdrawal') {
                document.getElementById('walletSource').textContent = 'Transfer from Withdrawal Wallet';
                document.getElementById('availableBalance').textContent = '$45.75'; // Withdrawal wallet balance
                availableBalance = 45.75;
            }
        }

        function selectRecipient(username) {
            selectedRecipient = username;
            document.getElementById('recipientUser').value = username;
            validateRecipient();
        }

        function validateRecipient() {
            const input = document.getElementById('recipientUser');
            selectedRecipient = input.value.trim();
            updateTransferButton();
        }

        function selectAmount(amount) {
            selectedAmountValue = amount;

            // Clear custom input
            document.getElementById('customAmount').value = '';

            // Update UI
            updateSelectedAmount();
            updateAmountButtons();
            updateTransferButton();
        }

        function validateCustomAmount() {
            const input = document.getElementById('customAmount');
            let value = parseInt(input.value) || 0;

            // Ensure it's a multiple of 10
            if (value % 10 !== 0) {
                value = Math.floor(value / 10) * 10;
                input.value = value || '';
            }

            // Set limits
            if (value > availableBalance) {
                value = Math.floor(availableBalance / 10) * 10;
                input.value = value;
            }

            selectedAmountValue = value;

            // Clear amount button selections
            document.querySelectorAll('.amount-btn').forEach(btn => btn.classList.remove('selected'));

            updateSelectedAmount();
            updateTransferButton();
        }

        function updateSelectedAmount() {
            document.getElementById('selectedAmount').textContent = `$${selectedAmountValue}`;
            document.getElementById('totalDeducted').textContent = `$${selectedAmountValue}`;
        }

        function updateAmountButtons() {
            document.querySelectorAll('.amount-btn').forEach(btn => {
                btn.classList.remove('selected');
            });

            // Find and select the matching button
            const buttons = document.querySelectorAll('.amount-btn');
            buttons.forEach(btn => {
                const amount = parseInt(btn.textContent.replace('$', ''));
                if (amount === selectedAmountValue) {
                    btn.classList.add('selected');
                }
            });
        }

        function updateTransferButton() {
            const btn = document.getElementById('transferBtn');
            const amountSpan = document.getElementById('transferAmount');

            if (selectedAmountValue >= 10 && selectedRecipient.length > 0) {
                btn.disabled = false;
                amountSpan.textContent = `$${selectedAmountValue}`;
            } else {
                btn.disabled = true;
                amountSpan.textContent = '$0';
            }
        }

        function processTransfer() {
            if (selectedAmountValue < 10) {
                alert('Please select an amount of at least $10');
                return;
            }

            if (selectedAmountValue % 10 !== 0) {
                alert('Please select an amount in multiples of $10');
                return;
            }

            if (!selectedRecipient) {
                alert('Please enter a recipient username');
                return;
            }

            const note = document.getElementById('transferNote').value;

            let confirmMessage = `Transfer Details:\n\n`;
            confirmMessage += `Recipient: @${selectedRecipient}\n`;
            confirmMessage += `Amount: $${selectedAmountValue}\n`;
            confirmMessage += `Transfer Fee: FREE\n`;
            confirmMessage += `Amount Deducted: $${selectedAmountValue}\n`;
            if (note) confirmMessage += `Note: ${note}\n`;
            confirmMessage += `\nConfirm this transfer?`;

            if (confirm(confirmMessage)) {
                alert(`Transfer of $${selectedAmountValue} to @${selectedRecipient} has been processed successfully!`);
                // In a real app, this would redirect back to wallet
                window.location.href = 'wallet.html';
            }
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function () {
            checkSource();
            updateTransferButton();
        });
    </script>
</body>

</html>