document.addEventListener("DOMContentLoaded", function () {
  const form = document.getElementById("levels-form");
  const container = document.getElementById("levels-container");
  const toast = document.getElementById("toast-message");
  const API_URL = "/netvis/ui/api/level_handler.php";

  // Validate required elements
  if (!form) {
    console.error("Critical: Form element 'levels-form' not found!");
    return;
  }

  if (!toast) {
    console.error("Critical: Toast element 'toast-message' not found!");
    return;
  }

  // Fetch levels and build the form
  const loadLevels = async () => {
    try {
      const response = await fetch(API_URL);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const levels = await response.json();

      container.innerHTML = ""; // Clear loading text

      levels.forEach((level) => {
        const div = document.createElement("div");
        div.className = "mb-4";
        div.innerHTML = `
                    <div class="level-card">
                        <div class="flex items-center justify-between mb-4">
                            <div class="flex items-center space-x-3">
                                <div class="w-10 h-10 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-lg flex items-center justify-center">
                                    <span class="text-white font-bold">${level.level_number}</span>
                                </div>
                                <div>
                                    <label for="level-${level.level_number}" class="form-label mb-0">
                                        <i class="fas fa-percentage mr-2"></i>Level ${level.level_number} Commission
                                    </label>
                                    <p class="text-xs text-gray-500">Distribution percentage for level ${level.level_number}</p>
                                </div>
                            </div>
                        </div>
                        <div class="relative">
                            <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                <span class="text-gray-400">%</span>
                            </div>
                            <input type="number" step="0.01" min="0" max="100" id="level-${level.level_number}"
                                   name="level_percentage_${level.level_number}"
                                   data-level-number="${level.level_number}" value="${level.distribution_percentage}"
                                   class="form-input pr-8"
                                   placeholder="0.00">
                        </div>
                    </div>
                `;
        container.appendChild(div);
      });

      // Add real-time percentage calculation
      addPercentageCalculator();
    } catch (error) {
      console.error("Error loading levels:", error);
      container.innerHTML =
        '<div class="text-center text-red-500">Failed to load level settings. Please try again later.</div>';
    }
  };

  // Handle form submission
  if (form) {
    form.addEventListener("submit", async (e) => {
      e.preventDefault();
      e.stopPropagation();

      // Get submit button and show loading state
      const submitBtn = form.querySelector('button[type="submit"]');
      const originalText = submitBtn.innerHTML;
      submitBtn.disabled = true;
      submitBtn.innerHTML = `
        <span>
            <i class="fas fa-spinner fa-spin mr-3"></i>
            Saving Changes...
        </span>
    `;

      const inputs = container.querySelectorAll('input[type="number"]');
      const levels_data = Array.from(inputs).map((input) => ({
        number: parseInt(input.dataset.levelNumber, 10),
        percentage: parseFloat(input.value) || 0,
      }));

      // Validate total percentage
      const totalPercentage = levels_data.reduce(
        (sum, level) => sum + level.percentage,
        0
      );
      if (totalPercentage > 100) {
        submitBtn.disabled = false;
        submitBtn.innerHTML = originalText;
        showToast(
          `Total percentage (${totalPercentage.toFixed(
            2
          )}%) cannot exceed 100%`,
          "error"
        );
        return;
      }

      try {
        const response = await fetch(API_URL, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ levels: levels_data }),
        });

        const result = await response.json();

        if (response.ok && result.success) {
          showToast("Changes saved successfully!", "success");
        } else {
          throw new Error(result.error || "An unknown error occurred.");
        }
      } catch (error) {
        console.error("Error saving levels:", error);
        showToast(`Error: ${error.message}`, "error");
      } finally {
        // Restore button state
        submitBtn.disabled = false;
        submitBtn.innerHTML = originalText;
      }
    });
  } else {
    console.error("Form element not found!");
  }

  // Show toast message
  function showToast(message, type = "success") {
    if (!toast) {
      console.error("Toast element not found! Using alert fallback.");
      alert(`${type.toUpperCase()}: ${message}`);
      return;
    }

    toast.innerHTML = `
            <div class="flex items-center space-x-2">
                <i class="fas fa-${
                  type === "success" ? "check-circle" : "exclamation-circle"
                }"></i>
                <span>${message}</span>
            </div>
        `;
    toast.className = type === "success" ? "toast-success" : "toast-error";
    toast.classList.remove("hidden");
    toast.style.display = "flex"; // Force display
    toast.style.visibility = "visible"; // Force visibility
    toast.style.opacity = "1"; // Force opacity

    // Simple auto-hide after 4 seconds
    setTimeout(() => {
      toast.classList.add("hidden");
      toast.style.display = "none";
    }, 4000);
  }

  // Add real-time percentage calculator
  function addPercentageCalculator() {
    // Add total percentage display
    const totalDisplay = document.createElement("div");
    totalDisplay.id = "total-percentage";
    totalDisplay.className =
      "mt-4 p-4 bg-gray-50 border border-gray-200 rounded-lg";
    totalDisplay.innerHTML = `
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-2">
          <i class="fas fa-calculator text-gray-600"></i>
          <span class="text-sm font-medium text-gray-700">Total Percentage:</span>
        </div>
        <div id="total-value" class="text-lg font-bold text-gray-900">0.00%</div>
      </div>
      <div class="mt-2">
        <div class="w-full bg-gray-200 rounded-full h-2">
          <div id="total-bar" class="bg-blue-600 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
        </div>
      </div>
    `;
    container.appendChild(totalDisplay);

    // Add event listeners to all inputs
    function updateTotal() {
      const inputs = container.querySelectorAll('input[type="number"]');
      const total = Array.from(inputs).reduce((sum, input) => {
        return sum + (parseFloat(input.value) || 0);
      }, 0);

      const totalValueEl = document.getElementById("total-value");
      const totalBarEl = document.getElementById("total-bar");

      totalValueEl.textContent = total.toFixed(2) + "%";
      totalBarEl.style.width = Math.min(total, 100) + "%";

      // Change color based on total
      if (total > 100) {
        totalValueEl.className = "text-lg font-bold text-red-600";
        totalBarEl.className =
          "bg-red-600 h-2 rounded-full transition-all duration-300";
      } else if (total > 90) {
        totalValueEl.className = "text-lg font-bold text-yellow-600";
        totalBarEl.className =
          "bg-yellow-600 h-2 rounded-full transition-all duration-300";
      } else {
        totalValueEl.className = "text-lg font-bold text-green-600";
        totalBarEl.className =
          "bg-green-600 h-2 rounded-full transition-all duration-300";
      }
    }

    // Add event listeners
    container.addEventListener("input", updateTotal);
    updateTotal(); // Initial calculation
  }

  // Initial load
  loadLevels();
});
