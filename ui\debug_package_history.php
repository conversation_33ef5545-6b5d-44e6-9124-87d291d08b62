<?php
session_start();

// Check if user is admin
if (!isset($_SESSION['user_id']) || !$_SESSION['is_admin']) {
    header("Location: login.html");
    exit();
}

include 'dbcon.php';

echo "<h2>Package History Debug Information</h2>";

// 1. Check if package_history table exists
echo "<h3>1. Table Existence Check</h3>";
$checkTable = "SHOW TABLES LIKE 'package_history'";
$result = $conn->query($checkTable);
if ($result->num_rows > 0) {
    echo "✅ package_history table EXISTS<br>";
} else {
    echo "❌ package_history table DOES NOT EXIST<br>";
    exit();
}

// 2. Check table structure
echo "<h3>2. Table Structure</h3>";
$structure = $conn->query("DESCRIBE package_history");
echo "<table border='1'><tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
while ($row = $structure->fetch_assoc()) {
    echo "<tr><td>{$row['Field']}</td><td>{$row['Type']}</td><td>{$row['Null']}</td><td>{$row['Key']}</td><td>{$row['Default']}</td></tr>";
}
echo "</table>";

// 3. Count records in package_history
echo "<h3>3. Current Records in package_history</h3>";
$count = $conn->query("SELECT COUNT(*) as total FROM package_history");
$total = $count->fetch_assoc()['total'];
echo "Total records: <strong>$total</strong><br>";

// 4. Show sample records if any
if ($total > 0) {
    echo "<h4>Sample Records:</h4>";
    $sample = $conn->query("SELECT * FROM package_history LIMIT 5");
    echo "<table border='1'>";
    echo "<tr><th>ID</th><th>User ID</th><th>Package</th><th>Amount</th><th>Status</th><th>Notes</th></tr>";
    while ($row = $sample->fetch_assoc()) {
        echo "<tr><td>{$row['id']}</td><td>{$row['user_id']}</td><td>{$row['package_name']}</td><td>{$row['package_amount']}</td><td>{$row['status']}</td><td>{$row['notes']}</td></tr>";
    }
    echo "</table>";
}

// 5. Check users with packages
echo "<h3>4. Users with Packages (who need history records)</h3>";
$users_with_packages = $conn->query("SELECT u.id, u.name, u.user_id, u.package, u.total_earnings_received, u.plan_status 
                                    FROM users u 
                                    WHERE u.package IS NOT NULL 
                                    AND u.package != '' 
                                    AND u.is_admin = 0 
                                    LIMIT 10");

if ($users_with_packages->num_rows > 0) {
    echo "<table border='1'>";
    echo "<tr><th>ID</th><th>Name</th><th>User ID</th><th>Package</th><th>Earnings</th><th>Plan Status</th></tr>";
    while ($row = $users_with_packages->fetch_assoc()) {
        echo "<tr><td>{$row['id']}</td><td>{$row['name']}</td><td>{$row['user_id']}</td><td>{$row['package']}</td><td>{$row['total_earnings_received']}</td><td>{$row['plan_status']}</td></tr>";
    }
    echo "</table>";
} else {
    echo "No users with packages found.<br>";
}

// 6. Check packages table
echo "<h3>5. Available Packages</h3>";
$packages = $conn->query("SELECT * FROM packages WHERE is_active = 1");
if ($packages->num_rows > 0) {
    echo "<table border='1'>";
    echo "<tr><th>ID</th><th>Package Name</th><th>Amount</th><th>Profit %</th></tr>";
    while ($row = $packages->fetch_assoc()) {
        echo "<tr><td>{$row['id']}</td><td>{$row['package_name']}</td><td>{$row['package_amount']}</td><td>{$row['profit_percentage']}</td></tr>";
    }
    echo "</table>";
} else {
    echo "No active packages found.<br>";
}

// 7. Test the sync query manually
echo "<h3>6. Manual Sync Test</h3>";
echo "<p>Testing the sync query that should create missing records...</p>";

$test_query = "SELECT 
    u.id,
    u.package,
    COALESCE(p.package_amount, 0) as package_amount,
    COALESCE(p.profit_percentage, 0) as profit_percentage,
    'withdrawal' as wallet_type,
    NOW() as purchased_at,
    COALESCE(u.total_earnings_received, 0) as total_earnings_received,
    COALESCE(p.package_amount * 2, 0) as earnings_limit,
    CASE 
        WHEN u.plan_status = 'expired' THEN 'expired'
        ELSE 'active'
    END as status,
    'Manual test query' as notes
FROM users u
LEFT JOIN packages p ON u.package = p.package_name
WHERE u.package IS NOT NULL 
AND u.package != ''
AND u.is_admin = 0
AND NOT EXISTS (
    SELECT 1 FROM package_history ph 
    WHERE ph.user_id = u.id 
    AND ph.package_name = u.package
)
LIMIT 5";

$test_result = $conn->query($test_query);
if ($test_result->num_rows > 0) {
    echo "<p><strong>Query would create {$test_result->num_rows} records:</strong></p>";
    echo "<table border='1'>";
    echo "<tr><th>User ID</th><th>Package</th><th>Amount</th><th>Profit %</th><th>Status</th></tr>";
    while ($row = $test_result->fetch_assoc()) {
        echo "<tr><td>{$row['id']}</td><td>{$row['package']}</td><td>{$row['package_amount']}</td><td>{$row['profit_percentage']}</td><td>{$row['status']}</td></tr>";
    }
    echo "</table>";
} else {
    echo "<p>No records would be created by the sync query.</p>";
}

// 8. Manual insert button
echo "<h3>7. Manual Insert Test</h3>";
if (isset($_POST['manual_insert'])) {
    try {
        $insert_sql = "INSERT INTO package_history (user_id, package_name, package_amount, profit_percentage, wallet_type, purchased_at, total_earnings_received, earnings_limit, status, notes)
                      SELECT 
                          u.id,
                          u.package,
                          COALESCE(p.package_amount, 0),
                          COALESCE(p.profit_percentage, 0),
                          'withdrawal' as wallet_type,
                          NOW() as purchased_at,
                          COALESCE(u.total_earnings_received, 0),
                          COALESCE(p.package_amount * 2, 0) as earnings_limit,
                          CASE 
                              WHEN u.plan_status = 'expired' THEN 'expired'
                              ELSE 'active'
                          END as status,
                          'Manual debug insert' as notes
                      FROM users u
                      LEFT JOIN packages p ON u.package = p.package_name
                      WHERE u.package IS NOT NULL 
                      AND u.package != ''
                      AND u.is_admin = 0
                      AND NOT EXISTS (
                          SELECT 1 FROM package_history ph 
                          WHERE ph.user_id = u.id 
                          AND ph.package_name = u.package
                      )";
        
        if ($conn->query($insert_sql)) {
            $affected = $conn->affected_rows;
            echo "<p style='color: green;'>✅ Successfully inserted $affected records!</p>";
        } else {
            echo "<p style='color: red;'>❌ Insert failed: " . $conn->error . "</p>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Exception: " . $e->getMessage() . "</p>";
    }
}

echo "<form method='POST'>";
echo "<button type='submit' name='manual_insert' style='background: blue; color: white; padding: 10px; border: none; cursor: pointer;'>Run Manual Insert</button>";
echo "</form>";

$conn->close();
?>
