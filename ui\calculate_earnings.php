<?php
// Include database connection
include 'dbcon.php';

// Fetch active level percentages
$levels_sql = "SELECT level_number, distribution_percentage FROM levels WHERE is_active = 1 AND level_number IN (1, 2, 3)";
$levels_result = $conn->query($levels_sql);
$level_percentages = [];
if ($levels_result->num_rows > 0) {
    while ($level_row = $levels_result->fetch_assoc()) {
        $level_percentages[$level_row['level_number']] = $level_row['distribution_percentage'];
    }
}

// Prepare statements for reuse
$update_withdrawal_balance_stmt = $conn->prepare("UPDATE users SET withdrawal_wallet_balance = withdrawal_wallet_balance + ? WHERE id = ?");
$insert_wallet_history_stmt = $conn->prepare("INSERT INTO wallet_history (user_id, amount, type, wallet_type, description, related_user_id) VALUES (?, ?, ?, 'withdrawal', ?, ?)");
$update_earnings_stmt = $conn->prepare("UPDATE users SET total_earnings_received = total_earnings_received + ? WHERE id = ?");
$expire_plan_stmt = $conn->prepare("UPDATE users SET plan_status = 'expired', plan_expired_at = NOW() WHERE id = ?");
$update_package_history_stmt = $conn->prepare("UPDATE package_history SET status = 'expired', expired_at = NOW(), total_earnings_received = ?, duration_days = DATEDIFF(NOW(), purchased_at), expiry_reason = 'limit_reached' WHERE user_id = ? AND status = 'active'");
$update_package_history_balance_stmt = $conn->prepare("UPDATE package_history SET total_earnings_received = (SELECT total_earnings_received FROM users WHERE id = ?) WHERE user_id = ? AND status = 'active'");
$update_sponsor_package_history_balance_stmt = $conn->prepare("UPDATE package_history SET total_earnings_received = (SELECT total_earnings_received FROM users WHERE id = ?) WHERE user_id = ? AND status = 'active'");
$count_direct_referrals_stmt = $conn->prepare("SELECT COUNT(*) as direct_count FROM users WHERE sponser_id = ? AND is_active = 1 AND deleted_at IS NULL AND package IS NOT NULL AND package != ''");

// Get all active users and their packages, including plan status and earnings tracking
$sql = 'SELECT u.id, u.user_id AS public_user_id, u.sponser_id, u.total_earnings_received, u.plan_status, p.package_amount, p.profit_percentage
        FROM users u
        JOIN packages p ON u.package = p.package_name
        WHERE u.is_active = 1 AND u.is_admin = 0 AND u.deleted_at IS NULL AND u.plan_status = "active"';

$result = $conn->query($sql);

if ($result === false) {
    die("Error executing query: " . $conn->error);
}

if ($result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        $user_int_id = $row['id'];
        $user_public_id = $row['public_user_id'];

        $conn->begin_transaction();
        try {
            $sponsor_int_id_from_user = $row['sponser_id']; // This is the sponsor's integer ID
            $packageAmount = $row['package_amount'];
            $profitPercentage = $row['profit_percentage'];
            $currentEarnings = $row['total_earnings_received'];
            $planLimit = $packageAmount * 2; // 2x package amount limit

            // 1. Calculate daily profit
            $dailyProfit = ($packageAmount * $profitPercentage) / 100;

            // Check if adding this profit would exceed the 2x limit
            if (($currentEarnings + $dailyProfit) >= $planLimit) {
                // Calculate remaining amount before hitting limit
                $remainingAmount = $planLimit - $currentEarnings;

                if ($remainingAmount > 0) {
                    // Give partial profit up to the limit
                    $type_profit = 'daily_profit';
                    $profit_description = 'Final daily profit (plan limit reached)';
                    $insert_wallet_history_stmt->bind_param("idssi", $user_int_id, $remainingAmount, $type_profit, $profit_description, $user_int_id);
                    $insert_wallet_history_stmt->execute();
                    $update_withdrawal_balance_stmt->bind_param("di", $remainingAmount, $user_int_id);
                    $update_withdrawal_balance_stmt->execute();
                    $update_earnings_stmt->bind_param("di", $remainingAmount, $user_int_id);
                    $update_earnings_stmt->execute();

                    // Update package history with current user balance
                    $update_package_history_balance_stmt->bind_param("ii", $user_int_id, $user_int_id);
                    $update_package_history_balance_stmt->execute();
                }

                // Expire the plan
                $expire_plan_stmt->bind_param("i", $user_int_id);
                $expire_plan_stmt->execute();

                // Update package history
                $final_earnings = $currentEarnings + $remainingAmount;
                $update_package_history_stmt->bind_param("di", $final_earnings, $user_int_id);
                $update_package_history_stmt->execute();

                // Log plan expiration
                $expiry_description = "Plan expired - reached 2x package limit ($" . number_format($planLimit, 2) . ")";
                $expiry_type = 'plan_expired';
                $zero_amount = 0;
                $insert_wallet_history_stmt->bind_param("idssi", $user_int_id, $zero_amount, $expiry_type, $expiry_description, $user_int_id);
                $insert_wallet_history_stmt->execute();

                // Skip network commission distribution for expired plans
                $conn->commit();
                continue;
            }

            // Normal profit distribution (plan still active)
            $type_profit = 'daily_profit';
            $profit_description = 'Daily profit from package';
            $insert_wallet_history_stmt->bind_param("idssi", $user_int_id, $dailyProfit, $type_profit, $profit_description, $user_int_id);
            $insert_wallet_history_stmt->execute();
            $update_withdrawal_balance_stmt->bind_param("di", $dailyProfit, $user_int_id);
            $update_withdrawal_balance_stmt->execute();
            $update_earnings_stmt->bind_param("di", $dailyProfit, $user_int_id);
            $update_earnings_stmt->execute();

            // Update package history with current user balance
            $update_package_history_balance_stmt->bind_param("ii", $user_int_id, $user_int_id);
            $update_package_history_balance_stmt->execute();

            // 2. Distribute network commission to upline
            $level = 1;
            $current_sponsor_int_id = $sponsor_int_id_from_user;

            while ($current_sponsor_int_id && $level <= 3) {
                // Find the sponsor's details using their integer ID, including plan status
                $sponsor_details_sql = "SELECT u.id, u.sponser_id, u.total_earnings_received, u.plan_status, p.package_amount
                                       FROM users u
                                       LEFT JOIN packages p ON u.package = p.package_name
                                       WHERE u.id = ? AND u.is_active = 1 AND u.deleted_at IS NULL";
                $sponsor_details_stmt = $conn->prepare($sponsor_details_sql);
                $sponsor_details_stmt->bind_param("i", $current_sponsor_int_id);
                $sponsor_details_stmt->execute();
                $sponsor_details_result = $sponsor_details_stmt->get_result();

                if ($sponsor_details_row = $sponsor_details_result->fetch_assoc()) {
                    $sponsor_id_for_wallet = $sponsor_details_row['id'];
                    $next_sponsor_int_id = $sponsor_details_row['sponser_id'];
                    $sponsor_earnings = $sponsor_details_row['total_earnings_received'];
                    $sponsor_plan_status = $sponsor_details_row['plan_status'];
                    $sponsor_package_amount = $sponsor_details_row['package_amount'];

                    // Only give commission if sponsor's plan is still active
                    if ($sponsor_plan_status === 'active') {
                        // Check qualification based on direct referrals
                        $count_direct_referrals_stmt->bind_param("i", $sponsor_id_for_wallet);
                        $count_direct_referrals_stmt->execute();
                        $direct_referrals_result = $count_direct_referrals_stmt->get_result();
                        $direct_referrals_count = $direct_referrals_result->fetch_assoc()['direct_count'];

                        // Define qualification requirements for each level
                        $qualification_requirements = [
                            1 => 0,  // Level 1: No qualification needed
                            2 => 3,  // Level 2: Need at least 3 direct referrals
                            3 => 5,  // Level 3: Need at least 5 direct referrals
                            4 => 7,  // Level 4: Need at least 7 direct referrals (if you extend to more levels)
                            5 => 10  // Level 5: Need at least 10 direct referrals
                        ];

                        $required_referrals = $qualification_requirements[$level] ?? 0;
                        $is_qualified = $direct_referrals_count >= $required_referrals;

                        $commissionPercentage = $level_percentages[$level] ?? 0;
                        if ($commissionPercentage > 0 && $is_qualified) {
                            $commissionAmount = ($dailyProfit * $commissionPercentage) / 100;
                            $sponsor_limit = $sponsor_package_amount * 2;

                            // Check if commission would exceed sponsor's plan limit
                            if (($sponsor_earnings + $commissionAmount) >= $sponsor_limit) {
                                // Calculate remaining amount before hitting sponsor's limit
                                $sponsor_remaining = $sponsor_limit - $sponsor_earnings;

                                if ($sponsor_remaining > 0) {
                                    // Give partial commission up to the limit
                                    $type_commission = 'network_commission';
                                    $commission_description = "Final network commission from user $user_public_id (Level $level) - plan limit reached";
                                    $insert_wallet_history_stmt->bind_param("idssi", $sponsor_id_for_wallet, $sponsor_remaining, $type_commission, $commission_description, $user_int_id);
                                    $insert_wallet_history_stmt->execute();
                                    $update_withdrawal_balance_stmt->bind_param("di", $sponsor_remaining, $sponsor_id_for_wallet);
                                    $update_withdrawal_balance_stmt->execute();
                                    $update_earnings_stmt->bind_param("di", $sponsor_remaining, $sponsor_id_for_wallet);
                                    $update_earnings_stmt->execute();

                                    // Update sponsor's package history with current sponsor balance
                                    $update_sponsor_package_history_balance_stmt->bind_param("ii", $sponsor_id_for_wallet, $sponsor_id_for_wallet);
                                    $update_sponsor_package_history_balance_stmt->execute();
                                }

                                // Expire sponsor's plan
                                $expire_plan_stmt->bind_param("i", $sponsor_id_for_wallet);
                                $expire_plan_stmt->execute();

                                // Log sponsor plan expiration
                                $sponsor_expiry_description = "Plan expired - reached 2x package limit ($" . number_format($sponsor_limit, 2) . ")";
                                $sponsor_expiry_type = 'plan_expired';
                                $sponsor_zero_amount = 0;
                                $insert_wallet_history_stmt->bind_param("idssi", $sponsor_id_for_wallet, $sponsor_zero_amount, $sponsor_expiry_type, $sponsor_expiry_description, $sponsor_id_for_wallet);
                                $insert_wallet_history_stmt->execute();
                            } else {
                                // Normal commission distribution
                                $type_commission = 'network_commission';
                                $commission_description = "Network commission from user $user_public_id (Level $level)";
                                $insert_wallet_history_stmt->bind_param("idssi", $sponsor_id_for_wallet, $commissionAmount, $type_commission, $commission_description, $user_int_id);
                                $insert_wallet_history_stmt->execute();
                                $update_withdrawal_balance_stmt->bind_param("di", $commissionAmount, $sponsor_id_for_wallet);
                                $update_withdrawal_balance_stmt->execute();
                                $update_earnings_stmt->bind_param("di", $commissionAmount, $sponsor_id_for_wallet);
                                $update_earnings_stmt->execute();

                                // Update sponsor's package history with current sponsor balance
                                $update_sponsor_package_history_balance_stmt->bind_param("ii", $sponsor_id_for_wallet, $sponsor_id_for_wallet);
                                $update_sponsor_package_history_balance_stmt->execute();
                            }
                        } elseif ($commissionPercentage > 0 && !$is_qualified) {
                            // Log when sponsor doesn't qualify for this level
                            $qualification_description = "Network commission skipped - Level $level requires $required_referrals direct referrals, sponsor has $direct_referrals_count";
                            $qualification_type = 'qualification_failed';
                            $zero_amount = 0;
                            $insert_wallet_history_stmt->bind_param("idssi", $sponsor_id_for_wallet, $zero_amount, $qualification_type, $qualification_description, $user_int_id);
                            $insert_wallet_history_stmt->execute();
                        }
                    }
                    $current_sponsor_int_id = $next_sponsor_int_id;
                } else {
                    $current_sponsor_int_id = null; // Sponsor not found or inactive, stop climbing the upline
                }
                $sponsor_details_stmt->close();
                $level++;
            }

            $conn->commit();
        } catch (Exception $e) {
            $conn->rollback();
            error_log("Failed to process earnings for user ID: $user_int_id. Error: " . $e->getMessage());
        }
    }
}

$update_withdrawal_balance_stmt->close();
$insert_wallet_history_stmt->close();
$update_earnings_stmt->close();
$expire_plan_stmt->close();
$update_package_history_stmt->close();
$update_package_history_balance_stmt->close();
$update_sponsor_package_history_balance_stmt->close();
$count_direct_referrals_stmt->close();
$conn->close();

header('Location: admin.php?success=earnings_calculated');
exit();
