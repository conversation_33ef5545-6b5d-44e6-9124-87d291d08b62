<?php
session_start();

// Check if user is admin
if (!isset($_SESSION['user_id']) || !$_SESSION['is_admin']) {
    header("Location: login.html");
    exit();
}

include 'dbcon.php';

$message = '';
$error = '';

if ($_POST) {
    try {
        // Create package_history table
        $createTableSQL = "
        CREATE TABLE IF NOT EXISTS `package_history` (
            `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
            `user_id` int(11) UNSIGNED NOT NULL,
            `package_name` varchar(255) NOT NULL,
            `package_amount` decimal(20,8) NOT NULL,
            `profit_percentage` decimal(5,2) NOT NULL,
            `wallet_type` enum('deposit','withdrawal') NOT NULL,
            `purchased_at` timestamp NOT NULL DEFAULT current_timestamp(),
            `expired_at` timestamp NULL DEFAULT NULL,
            `total_earnings_received` decimal(20,8) DEFAULT 0.00000000,
            `earnings_limit` decimal(20,8) NOT NULL COMMENT '2x package amount',
            `status` enum('active','expired','upgraded') NOT NULL DEFAULT 'active',
            `duration_days` int(11) NULL COMMENT 'Days the package was active',
            `expiry_reason` enum('limit_reached','upgraded','manual') NULL,
            `notes` text NULL,
            PRIMARY KEY (`id`),
            KEY `fk_package_history_user_id` (`user_id`),
            KEY `idx_package_history_status` (`status`),
            KEY `idx_package_history_purchased_at` (`purchased_at`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
        ";

        if ($conn->query($createTableSQL)) {
            $message .= "✅ Package history table created successfully!<br>";
        } else {
            if (strpos($conn->error, 'already exists') !== false) {
                $message .= "ℹ️ Package history table already exists.<br>";
            } else {
                $error .= "❌ Error creating table: " . $conn->error . "<br>";
            }
        }

        // Migrate existing user packages to history
        if (empty($error)) {
            $migrateSQL = "
            INSERT INTO package_history (user_id, package_name, package_amount, profit_percentage, wallet_type, purchased_at, total_earnings_received, earnings_limit, status, notes)
            SELECT
                u.id,
                u.package,
                COALESCE(p.package_amount, 0),
                COALESCE(p.profit_percentage, 0),
                'withdrawal' as wallet_type,
                NOW() as purchased_at,
                COALESCE(u.total_earnings_received, 0),
                COALESCE(p.package_amount * 2, 0) as earnings_limit,
                CASE
                    WHEN u.plan_status = 'expired' THEN 'expired'
                    ELSE 'active'
                END as status,
                'Migrated from existing user data' as notes
            FROM users u
            LEFT JOIN packages p ON u.package = p.package_name
            WHERE u.package IS NOT NULL
            AND u.package != ''
            AND u.is_admin = 0
            AND NOT EXISTS (
                SELECT 1 FROM package_history ph
                WHERE ph.user_id = u.id
                AND ph.package_name = u.package
                AND ph.notes = 'Migrated from existing user data'
            )
            ";

            if ($conn->query($migrateSQL)) {
                $affected_rows = $conn->affected_rows;
                $message .= "✅ Migrated $affected_rows existing user packages to history.<br>";
            } else {
                $error .= "❌ Error migrating data: " . $conn->error . "<br>";
            }
        }

        if (empty($error)) {
            $message .= "<br>🎯 <strong>Package History System Ready!</strong><br>";
            $message .= "📊 Features available:<br>";
            $message .= "• Complete purchase history tracking<br>";
            $message .= "• Package expiration details<br>";
            $message .= "• Duration calculations<br>";
            $message .= "• Earnings tracking per package<br>";
        }
    } catch (Exception $e) {
        $error = "Error: " . $e->getMessage();
    }
}

// Check if table exists and get sample data
$tableExists = false;
$sampleData = [];

try {
    $checkTable = "SHOW TABLES LIKE 'package_history'";
    $result = $conn->query($checkTable);
    $tableExists = $result->num_rows > 0;

    if ($tableExists) {
        $sampleSQL = "SELECT ph.*, u.name as user_name, u.user_id as user_identifier 
                      FROM package_history ph 
                      JOIN users u ON ph.user_id = u.id 
                      ORDER BY ph.purchased_at DESC 
                      LIMIT 10";
        $sampleResult = $conn->query($sampleSQL);
        $sampleData = $sampleResult->fetch_all(MYSQLI_ASSOC);
    }
} catch (Exception $e) {
    // Table doesn't exist yet
}

$conn->close();
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Create Package History System - NetVis</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>

<body class="bg-gray-100">
    <?php include 'admin_nav.php'; ?>

    <main class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div class="bg-white shadow-lg rounded-lg p-8">
            <div class="mb-6">
                <h2 class="text-3xl font-bold text-gray-800">Package History Tracking System</h2>
                <p class="text-gray-600 mt-2">Create a comprehensive system to track all user package purchases and their expiration history.</p>
            </div>

            <?php if ($message): ?>
                <div class="bg-green-50 border border-green-200 text-green-800 px-4 py-3 rounded-lg mb-6">
                    <?php echo $message; ?>
                </div>
            <?php endif; ?>

            <?php if ($error): ?>
                <div class="bg-red-50 border border-red-200 text-red-800 px-4 py-3 rounded-lg mb-6">
                    <?php echo $error; ?>
                </div>
            <?php endif; ?>

            <!-- System Overview -->
            <div class="bg-blue-50 p-6 rounded-lg mb-6">
                <h3 class="text-blue-800 font-semibold mb-3">📊 Package History System Features</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-blue-700 text-sm">
                    <div>
                        <h4 class="font-semibold">📈 Purchase Tracking:</h4>
                        <ul class="list-disc list-inside space-y-1">
                            <li>Complete purchase history</li>
                            <li>Package details and amounts</li>
                            <li>Purchase dates and wallet types</li>
                            <li>Profit percentages</li>
                        </ul>
                    </div>
                    <div>
                        <h4 class="font-semibold">⏰ Expiration Tracking:</h4>
                        <ul class="list-disc list-inside space-y-1">
                            <li>Expiration dates and reasons</li>
                            <li>Duration calculations</li>
                            <li>Total earnings per package</li>
                            <li>Status progression</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Table Schema -->
            <div class="mb-6">
                <h3 class="text-xl font-semibold text-gray-800 mb-4">Database Schema</h3>

                <div class="bg-gray-50 p-4 rounded-lg">
                    <h4 class="font-semibold text-gray-800 mb-2">package_history Table Fields:</h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                        <div>
                            <p><strong>id:</strong> Primary key</p>
                            <p><strong>user_id:</strong> User reference</p>
                            <p><strong>package_name:</strong> Package purchased</p>
                            <p><strong>package_amount:</strong> Amount paid</p>
                            <p><strong>profit_percentage:</strong> Daily profit %</p>
                            <p><strong>wallet_type:</strong> Payment wallet</p>
                        </div>
                        <div>
                            <p><strong>purchased_at:</strong> Purchase timestamp</p>
                            <p><strong>expired_at:</strong> Expiration timestamp</p>
                            <p><strong>total_earnings_received:</strong> Total earned</p>
                            <p><strong>earnings_limit:</strong> 2x package limit</p>
                            <p><strong>status:</strong> active/expired/upgraded</p>
                            <p><strong>duration_days:</strong> Active duration</p>
                        </div>
                    </div>
                </div>
            </div>

            <?php if (!$tableExists): ?>
                <!-- Create System Button -->
                <form method="POST" class="mb-6">
                    <button type="submit" class="w-full bg-indigo-600 text-white py-3 px-4 rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 font-medium">
                        Create Package History System
                    </button>
                </form>
            <?php else: ?>
                <!-- Sample Data Display -->
                <div class="mb-6">
                    <h3 class="text-xl font-semibold text-gray-800 mb-4">Recent Package History (Sample)</h3>

                    <?php if (empty($sampleData)): ?>
                        <div class="bg-gray-50 p-6 rounded-lg text-center">
                            <p class="text-gray-500">No package history data found. The system is ready to track new purchases.</p>
                        </div>
                    <?php else: ?>
                        <div class="overflow-x-auto border border-gray-200 rounded-lg">
                            <table class="min-w-full bg-white">
                                <thead class="bg-gray-100">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-600 uppercase">User</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-600 uppercase">Package</th>
                                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-600 uppercase">Amount</th>
                                        <th class="px-6 py-3 text-center text-xs font-medium text-gray-600 uppercase">Purchased</th>
                                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-600 uppercase">Earnings</th>
                                        <th class="px-6 py-3 text-center text-xs font-medium text-gray-600 uppercase">Status</th>
                                    </tr>
                                </thead>
                                <tbody class="divide-y divide-gray-200">
                                    <?php foreach ($sampleData as $record): ?>
                                        <tr class="hover:bg-gray-50">
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div>
                                                    <div class="text-sm font-medium text-gray-900"><?php echo htmlspecialchars($record['user_name']); ?></div>
                                                    <div class="text-sm text-gray-500">ID: <?php echo htmlspecialchars($record['user_identifier']); ?></div>
                                                </div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                <?php echo htmlspecialchars($record['package_name']); ?>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right">
                                                $<?php echo number_format($record['package_amount'], 2); ?>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">
                                                <?php echo date('M j, Y', strtotime($record['purchased_at'])); ?>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right">
                                                $<?php echo number_format($record['total_earnings_received'], 2); ?>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-center">
                                                <?php
                                                $status_classes = [
                                                    'active' => 'bg-green-100 text-green-800',
                                                    'expired' => 'bg-red-100 text-red-800',
                                                    'upgraded' => 'bg-blue-100 text-blue-800'
                                                ];
                                                $status_class = $status_classes[$record['status']] ?? 'bg-gray-100 text-gray-800';
                                                ?>
                                                <span class="px-2 py-1 text-xs font-medium rounded-full <?php echo $status_class; ?>">
                                                    <?php echo ucfirst($record['status']); ?>
                                                </span>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            <?php endif; ?>

            <!-- Next Steps -->
            <div class="bg-yellow-50 p-6 rounded-lg mb-6">
                <h4 class="text-yellow-800 font-semibold mb-2">📋 Next Steps:</h4>
                <ol class="text-yellow-700 text-sm space-y-1 list-decimal list-inside">
                    <li>Create the package history table and migrate existing data</li>
                    <li>Update package purchase logic to record history</li>
                    <li>Update plan expiration logic to record expiration details</li>
                    <li>Create user interface to view package history</li>
                    <li>Add admin interface for comprehensive history management</li>
                </ol>
            </div>

            <!-- Quick Links -->
            <div class="flex space-x-4">
                <a href="package_manager.php" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
                    Package Manager
                </a>
                <a href="test_plan_expiration.php" class="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700">
                    Test Plan Expiration
                </a>
                <a href="admin.php" class="bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700">
                    Admin Dashboard
                </a>
            </div>
        </div>
    </main>
</body>

</html>