<?php
// Set page title
$page_title = "Dashboard";

// Start output buffering for page content
ob_start();
?>

<!-- Dashboard Content -->
<div class="fade-in">
    <!-- Page Header -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-800 mb-2">Dashboard</h1>
        <p class="text-gray-600">Welcome back! Here's what's happening with your crypto platform.</p>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <!-- Total Users -->
        <div class="admin-card p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600">Total Users</p>
                    <p class="text-3xl font-bold text-gray-800">1,234</p>
                    <p class="text-sm text-green-600 mt-1">
                        <i class="fas fa-arrow-up"></i> +12% from last month
                    </p>
                </div>
                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-users text-blue-600 text-xl"></i>
                </div>
            </div>
        </div>

        <!-- Total Volume -->
        <div class="admin-card p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600">Total Volume</p>
                    <p class="text-3xl font-bold text-gray-800">$2.5M</p>
                    <p class="text-sm text-green-600 mt-1">
                        <i class="fas fa-arrow-up"></i> +8% from last month
                    </p>
                </div>
                <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-dollar-sign text-green-600 text-xl"></i>
                </div>
            </div>
        </div>

        <!-- Active Packages -->
        <div class="admin-card p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600">Active Packages</p>
                    <p class="text-3xl font-bold text-gray-800">856</p>
                    <p class="text-sm text-orange-600 mt-1">
                        <i class="fas fa-arrow-down"></i> -3% from last month
                    </p>
                </div>
                <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-box text-orange-600 text-xl"></i>
                </div>
            </div>
        </div>

        <!-- Pending Withdrawals -->
        <div class="admin-card p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600">Pending Withdrawals</p>
                    <p class="text-3xl font-bold text-gray-800">23</p>
                    <p class="text-sm text-red-600 mt-1">
                        <i class="fas fa-exclamation-triangle"></i> Requires attention
                    </p>
                </div>
                <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-arrow-up text-red-600 text-xl"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <!-- Revenue Chart -->
        <div class="admin-card p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-800">Revenue Overview</h3>
                <select class="form-input text-sm py-2">
                    <option>Last 7 days</option>
                    <option>Last 30 days</option>
                    <option>Last 3 months</option>
                </select>
            </div>
            <div class="h-64">
                <canvas id="revenueChart"></canvas>
            </div>
        </div>

        <!-- User Growth Chart -->
        <div class="admin-card p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-800">User Growth</h3>
                <select class="form-input text-sm py-2">
                    <option>Last 7 days</option>
                    <option>Last 30 days</option>
                    <option>Last 3 months</option>
                </select>
            </div>
            <div class="h-64">
                <canvas id="userChart"></canvas>
            </div>
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Recent Transactions -->
        <div class="admin-card p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-800">Recent Transactions</h3>
                <a href="transactions.php" class="text-blue-600 hover:text-blue-800 text-sm font-medium">View All</a>
            </div>
            <div class="space-y-4">
                <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                            <i class="fas fa-arrow-down text-green-600 text-sm"></i>
                        </div>
                        <div>
                            <p class="font-medium text-gray-800">John Doe</p>
                            <p class="text-sm text-gray-600">Deposit</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <p class="font-semibold text-gray-800">+$500.00</p>
                        <p class="text-xs text-gray-500">2 min ago</p>
                    </div>
                </div>

                <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                            <i class="fas fa-arrow-up text-red-600 text-sm"></i>
                        </div>
                        <div>
                            <p class="font-medium text-gray-800">Jane Smith</p>
                            <p class="text-sm text-gray-600">Withdrawal</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <p class="font-semibold text-gray-800">-$250.00</p>
                        <p class="text-xs text-gray-500">5 min ago</p>
                    </div>
                </div>

                <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                            <i class="fas fa-coins text-blue-600 text-sm"></i>
                        </div>
                        <div>
                            <p class="font-medium text-gray-800">Mike Johnson</p>
                            <p class="text-sm text-gray-600">Staking Reward</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <p class="font-semibold text-gray-800">+$75.50</p>
                        <p class="text-xs text-gray-500">10 min ago</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Users -->
        <div class="admin-card p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-800">Recent Users</h3>
                <a href="users.php" class="text-blue-600 hover:text-blue-800 text-sm font-medium">View All</a>
            </div>
            <div class="space-y-4">
                <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                            <span class="text-white text-sm font-semibold">JD</span>
                        </div>
                        <div>
                            <p class="font-medium text-gray-800">John Doe</p>
                            <p class="text-sm text-gray-600"><EMAIL></p>
                        </div>
                    </div>
                    <span class="badge badge-success">Active</span>
                </div>

                <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center">
                            <span class="text-white text-sm font-semibold">JS</span>
                        </div>
                        <div>
                            <p class="font-medium text-gray-800">Jane Smith</p>
                            <p class="text-sm text-gray-600"><EMAIL></p>
                        </div>
                    </div>
                    <span class="badge badge-warning">Pending</span>
                </div>

                <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                            <span class="text-white text-sm font-semibold">MJ</span>
                        </div>
                        <div>
                            <p class="font-medium text-gray-800">Mike Johnson</p>
                            <p class="text-sm text-gray-600"><EMAIL></p>
                        </div>
                    </div>
                    <span class="badge badge-success">Active</span>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// Get the page content
$page_content = ob_get_clean();

// Additional JavaScript for charts
$additional_js = '
<script>
// Revenue Chart
const revenueCtx = document.getElementById("revenueChart").getContext("2d");
const revenueChart = new Chart(revenueCtx, {
    type: "line",
    data: {
        labels: ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"],
        datasets: [{
            label: "Revenue",
            data: [1200, 1900, 3000, 5000, 2000, 3000, 4500],
            borderColor: "#3b82f6",
            backgroundColor: "rgba(59, 130, 246, 0.1)",
            borderWidth: 3,
            fill: true,
            tension: 0.4
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: false
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                grid: {
                    color: "rgba(0, 0, 0, 0.05)"
                }
            },
            x: {
                grid: {
                    display: false
                }
            }
        }
    }
});

// User Growth Chart
const userCtx = document.getElementById("userChart").getContext("2d");
const userChart = new Chart(userCtx, {
    type: "bar",
    data: {
        labels: ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"],
        datasets: [{
            label: "New Users",
            data: [12, 19, 30, 50, 20, 30, 45],
            backgroundColor: "rgba(16, 185, 129, 0.8)",
            borderColor: "#10b981",
            borderWidth: 1,
            borderRadius: 8
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: false
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                grid: {
                    color: "rgba(0, 0, 0, 0.05)"
                }
            },
            x: {
                grid: {
                    display: false
                }
            }
        }
    }
});
</script>
';

// Include the layout
include 'includes/layout.php';
?>