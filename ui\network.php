<?php
session_start();

if (!isset($_SESSION['user_id']) || !$_SESSION['is_admin']) {
    header('Location: login.php');
    exit();
}

include 'dbcon.php';

// Fetch all active users to build the tree in memory
$sql = "SELECT id, name, user_id, sponser_id FROM users WHERE is_active = 1 AND deleted_at IS NULL";
$result = $conn->query($sql);

$users_by_id = [];
if ($result) {
    while ($row = $result->fetch_assoc()) {
        $users_by_id[$row['id']] = $row;
        $users_by_id[$row['id']]['children'] = [];
    }
}

// Build the hierarchy
$tree = [];
foreach ($users_by_id as $id => &$user) {
    if ($user['sponser_id'] !== null && isset($users_by_id[$user['sponser_id']])) {
        $users_by_id[$user['sponser_id']]['children'][] = &$user;
    } else {
        // This is a root node (has no sponsor)
        $tree[] = &$user;
    }
}
unset($user); // Unset reference to avoid bugs

// Page configuration
$page_title = "Network Tree Management";
$additional_css = '
<style>
    /* Network Tree Styling */
    .tree-container {
        text-align: center;
        padding: 2rem;
        overflow-x: auto;
        white-space: nowrap;
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        border-radius: 1rem;
        margin: 1rem 0;
    }

    .tree {
        display: inline-block;
    }

    .tree ul {
        padding-top: 20px;
        position: relative;
        transition: all 0.5s;
    }

    .tree li {
        float: left;
        text-align: center;
        list-style-type: none;
        position: relative;
        padding: 20px 5px 0 5px;
        transition: all 0.5s;
    }

    .tree li::before,
    .tree li::after {
        content: "";
        position: absolute;
        top: 0;
        right: 50%;
        border-top: 2px solid #667eea;
        width: 50%;
        height: 20px;
    }

    .tree li::after {
        right: auto;
        left: 50%;
        border-left: 2px solid #667eea;
    }

    .tree li:only-child::after,
    .tree li:only-child::before {
        display: none;
    }

    .tree li:only-child {
        padding-top: 0;
    }

    .tree li:first-child::before {
        border: 0 none;
    }

    .tree li:last-child::after {
        border: 0 none;
    }

    .tree li:last-child::before {
        border-right: 2px solid #667eea;
        border-radius: 0 5px 0 0;
    }

    .tree li:first-child::after {
        border-radius: 5px 0 0 0;
    }

    .tree ul ul::before {
        content: "";
        position: absolute;
        top: 0;
        left: 50%;
        border-left: 2px solid #667eea;
        width: 0;
        height: 20px;
    }

    .tree li .node-content {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        padding: 1rem;
        text-decoration: none;
        font-family: sans-serif;
        display: inline-block;
        border-radius: 12px;
        transition: all 0.3s ease;
        min-width: 140px;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        cursor: pointer;
        position: relative;
        overflow: hidden;
    }

    .tree li .node-content::before {
        content: "";
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
        transition: left 0.5s;
    }

    .tree li .node-content:hover::before {
        left: 100%;
    }

    .tree li .node-content:hover {
        transform: translateY(-2px) scale(1.05);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
    }

    .tree li .node-content .user-name {
        font-weight: 600;
        font-size: 0.9rem;
        margin-bottom: 0.25rem;
    }

    .tree li .node-content .user-id {
        font-size: 0.75rem;
        opacity: 0.9;
        font-weight: 400;
    }

    .tree li .node-content .user-icon {
        width: 2rem;
        height: 2rem;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 0.5rem;
    }

    /* Network Statistics Cards */
    .network-stats {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;
    }

    .stat-card {
        background: white;
        border-radius: 1rem;
        padding: 1.5rem;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        border: 1px solid #e2e8f0;
        transition: all 0.3s ease;
    }

    .stat-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
    }

    .stat-icon {
        width: 3rem;
        height: 3rem;
        border-radius: 0.75rem;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 1rem;
    }

    /* Search and Filter Section */
    .search-container {
        background: white;
        border-radius: 1rem;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    }

    .search-input {
        width: 100%;
        padding: 0.75rem 1rem;
        border: 2px solid #e2e8f0;
        border-radius: 0.5rem;
        font-size: 0.875rem;
        transition: all 0.3s ease;
    }

    .search-input:focus {
        outline: none;
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }
</style>
';

// Recursive function to render the tree view
function render_tree_node($nodes)
{
    if (empty($nodes)) {
        return;
    }
    echo '<ul>';
    foreach ($nodes as $node) {
        echo '<li>';
        echo '<div class="node-content">';
        echo '<div class="user-icon">';
        echo '<i class="fas fa-user text-white text-sm"></i>';
        echo '</div>';
        echo '<div class="user-name">' . htmlspecialchars($node['name']) . '</div>';
        echo '<div class="user-id">ID: ' . htmlspecialchars($node['user_id'] ?? 'N/A') . '</div>';
        echo '</div>';
        if (!empty($node['children'])) {
            render_tree_node($node['children']);
        }
        echo '</li>';
    }
    echo '</ul>';
}

// Start output buffering to capture the page content
ob_start();
?>

<!-- Page Header -->
<div class="flex flex-col lg:flex-row justify-between items-start lg:items-center space-y-4 lg:space-y-0 mb-6">
    <div>
        <h1 class="text-3xl font-bold text-gray-900">Network Tree Management</h1>
        <p class="text-gray-600 mt-1">Visualize and manage the user network hierarchy</p>
    </div>
    <div class="flex items-center space-x-2 bg-blue-50 px-4 py-2 rounded-lg border border-blue-200">
        <i class="fas fa-sitemap text-blue-600"></i>
        <span class="text-sm font-medium text-blue-700">Network Overview</span>
    </div>
</div>

<!-- Network Statistics -->
<?php
// Calculate network statistics
$total_users = count($users_by_id);
$root_users = count($tree);
$max_depth = 0;

function calculate_depth($nodes, $current_depth = 0)
{
    global $max_depth;
    $max_depth = max($max_depth, $current_depth);

    foreach ($nodes as $node) {
        if (!empty($node['children'])) {
            calculate_depth($node['children'], $current_depth + 1);
        }
    }
}

if (!empty($tree)) {
    calculate_depth($tree);
}

// Count users by level
$level_counts = [];
function count_by_level($nodes, $level = 0)
{
    global $level_counts;

    if (!isset($level_counts[$level])) {
        $level_counts[$level] = 0;
    }

    $level_counts[$level] += count($nodes);

    foreach ($nodes as $node) {
        if (!empty($node['children'])) {
            count_by_level($node['children'], $level + 1);
        }
    }
}

if (!empty($tree)) {
    count_by_level($tree);
}
?>

<div class="network-stats">
    <div class="stat-card">
        <div class="flex items-center">
            <div class="stat-icon bg-blue-100">
                <i class="fas fa-users text-blue-600 text-xl"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">Total Users</p>
                <p class="text-2xl font-bold text-gray-900"><?php echo number_format($total_users); ?></p>
            </div>
        </div>
    </div>

    <div class="stat-card">
        <div class="flex items-center">
            <div class="stat-icon bg-green-100">
                <i class="fas fa-seedling text-green-600 text-xl"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">Root Users</p>
                <p class="text-2xl font-bold text-gray-900"><?php echo number_format($root_users); ?></p>
            </div>
        </div>
    </div>

    <div class="stat-card">
        <div class="flex items-center">
            <div class="stat-icon bg-purple-100">
                <i class="fas fa-layer-group text-purple-600 text-xl"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">Max Depth</p>
                <p class="text-2xl font-bold text-gray-900"><?php echo $max_depth + 1; ?> Levels</p>
            </div>
        </div>
    </div>

    <div class="stat-card">
        <div class="flex items-center">
            <div class="stat-icon bg-orange-100">
                <i class="fas fa-chart-line text-orange-600 text-xl"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">Network Health</p>
                <p class="text-2xl font-bold text-gray-900"><?php echo $total_users > 0 ? 'Active' : 'Empty'; ?></p>
            </div>
        </div>
    </div>
</div>

<!-- Search and Filter Section -->
<div class="search-container">
    <div class="flex items-center space-x-4">
        <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center">
            <i class="fas fa-search text-white text-lg"></i>
        </div>
        <div class="flex-1">
            <h3 class="text-lg font-semibold text-gray-900 mb-2">Search Network</h3>
            <input type="text" id="network-search" class="search-input" placeholder="Search by user name or ID...">
        </div>
    </div>
</div>

<!-- Network Tree Container -->
<div class="admin-card p-0 overflow-hidden">
    <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center space-x-3">
            <div class="w-8 h-8 bg-gradient-to-br from-green-500 to-teal-600 rounded-lg flex items-center justify-center">
                <i class="fas fa-sitemap text-white text-sm"></i>
            </div>
            <div>
                <h3 class="text-lg font-semibold text-gray-900">Network Hierarchy</h3>
                <p class="text-sm text-gray-600">Interactive network tree visualization</p>
            </div>
        </div>
    </div>

    <div class="tree-container">
        <div class="tree">
            <?php
            if (!empty($tree)) {
                render_tree_node($tree);
            } else {
                echo '<div class="flex flex-col items-center py-12">';
                echo '<div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">';
                echo '<i class="fas fa-sitemap text-gray-400 text-2xl"></i>';
                echo '</div>';
                echo '<h3 class="text-lg font-medium text-gray-900 mb-2">No Network Data</h3>';
                echo '<p class="text-sm text-gray-500">No network hierarchy data available to display.</p>';
                echo '</div>';
            }
            ?>
        </div>
    </div>
</div>

<script>
    // Search functionality
    document.getElementById('network-search').addEventListener('input', function(e) {
        const searchTerm = e.target.value.toLowerCase();
        const nodes = document.querySelectorAll('.node-content');

        nodes.forEach(node => {
            const userName = node.querySelector('.user-name').textContent.toLowerCase();
            const userId = node.querySelector('.user-id').textContent.toLowerCase();

            if (userName.includes(searchTerm) || userId.includes(searchTerm)) {
                node.style.opacity = '1';
                node.style.transform = 'scale(1)';
            } else {
                node.style.opacity = '0.3';
                node.style.transform = 'scale(0.9)';
            }
        });
    });
</script>

<?php
// Capture the page content
$page_content = ob_get_clean();

// Close database connection
$conn->close();

// Include the admin layout
include 'admin/includes/layout.php';
?>