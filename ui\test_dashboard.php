<?php
// Test dashboard access
session_start();

// Set admin session for testing
$_SESSION['admin_logged_in'] = true;
$_SESSION['admin_id'] = 1;

echo "<h2>🎯 Admin Dashboard Access Methods</h2>";

echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin: 20px 0;'>";

// Method 1: Direct Access
echo "<div style='background: linear-gradient(135deg, #10b981 0%, #059669 100%); color: white; padding: 20px; border-radius: 12px;'>";
echo "<h3 style='margin-top: 0; color: white;'>✅ Method 1: Quick Access</h3>";
echo "<p>Direct login bypass (current session set)</p>";
echo "<a href='admin_dashboard.php' style='background: rgba(255,255,255,0.2); color: white; padding: 10px 20px; text-decoration: none; border-radius: 8px; border: 1px solid rgba(255,255,255,0.3);'>🚀 Open Dashboard</a>";
echo "</div>";

// Method 2: Login Bypass Page
echo "<div style='background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%); color: white; padding: 20px; border-radius: 12px;'>";
echo "<h3 style='margin-top: 0; color: white;'>🔐 Method 2: Login Bypass</h3>";
echo "<p>Comprehensive admin session setup</p>";
echo "<a href='admin_login_bypass.php' style='background: rgba(255,255,255,0.2); color: white; padding: 10px 20px; text-decoration: none; border-radius: 8px; border: 1px solid rgba(255,255,255,0.3);'>🎯 Setup Admin Access</a>";
echo "</div>";

// Method 3: Quick Redirect
echo "<div style='background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%); color: white; padding: 20px; border-radius: 12px;'>";
echo "<h3 style='margin-top: 0; color: white;'>⚡ Method 3: Quick Redirect</h3>";
echo "<p>Instant admin login and redirect</p>";
echo "<a href='quick_admin_access.php' style='background: rgba(255,255,255,0.2); color: white; padding: 10px 20px; text-decoration: none; border-radius: 8px; border: 1px solid rgba(255,255,255,0.3);'>⚡ Instant Access</a>";
echo "</div>";

echo "</div>";

echo "<div style='background: #fef3c7; border: 2px solid #f59e0b; border-radius: 12px; padding: 20px; margin: 20px 0;'>";
echo "<h3 style='color: #92400e; margin-top: 0;'>⚠️ Session Status</h3>";
echo "<p style='color: #b45309;'>Current admin session: <strong>" . (isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in'] ? 'ACTIVE ✅' : 'INACTIVE ❌') . "</strong></p>";
if (isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in']) {
    echo "<p style='color: #b45309;'>You should be able to access the dashboard directly.</p>";
} else {
    echo "<p style='color: #b45309;'>Use one of the methods above to set admin session.</p>";
}
echo "</div>";

echo "<h3>📊 Dashboard Features:</h3>";
echo "<ul>";
echo "<li>✅ <strong>Live Statistics:</strong> Real-time user, deposit, withdrawal data</li>";
echo "<li>✅ <strong>Interactive Charts:</strong> User growth and deposit trends</li>";
echo "<li>✅ <strong>Recent Activities:</strong> Latest users, deposits, withdrawals</li>";
echo "<li>✅ <strong>Quick Actions:</strong> Direct links to pending requests</li>";
echo "<li>✅ <strong>Platform Overview:</strong> System status and health</li>";
echo "<li>✅ <strong>Responsive Design:</strong> Works on desktop and mobile</li>";
echo "<li>✅ <strong>Auto-refresh:</strong> Updates every 5 minutes</li>";
echo "<li>✅ <strong>Real-time Clock:</strong> Live server time display</li>";
echo "</ul>";

echo "<h3>🔧 Technical Features:</h3>";
echo "<ul>";
echo "<li>✅ <strong>Professional UI:</strong> Modern admin panel design</li>";
echo "<li>✅ <strong>Chart.js Integration:</strong> Beautiful interactive charts</li>";
echo "<li>✅ <strong>Tailwind CSS:</strong> Responsive and modern styling</li>";
echo "<li>✅ <strong>Font Awesome Icons:</strong> Professional iconography</li>";
echo "<li>✅ <strong>Error Handling:</strong> Graceful fallbacks for database issues</li>";
echo "<li>✅ <strong>Security:</strong> Admin session validation</li>";
echo "<li>✅ <strong>Performance:</strong> Optimized database queries</li>";
echo "</ul>";

echo "<h3>📈 Data Displayed:</h3>";
echo "<ul>";
echo "<li>📊 <strong>User Statistics:</strong> Total, active, new users today</li>";
echo "<li>💰 <strong>Financial Data:</strong> Deposits, withdrawals, pending amounts</li>";
echo "<li>📦 <strong>Investment Data:</strong> Total investments, packages</li>";
echo "<li>🪙 <strong>Staking Data:</strong> Active stakes, total staked amount</li>";
echo "<li>💳 <strong>Wallet Balances:</strong> Total deposit and withdrawal wallet balances</li>";
echo "<li>📅 <strong>Growth Charts:</strong> 6-month user and deposit trends</li>";
echo "<li>🕒 <strong>Recent Activities:</strong> Latest 5 users, deposits, withdrawals</li>";
echo "<li>⚡ <strong>Quick Stats:</strong> Platform summary with live data</li>";
echo "</ul>";

echo "<h3>🎨 Design Features:</h3>";
echo "<ul>";
echo "<li>🎯 <strong>Modern Cards:</strong> Glass-morphism effect with hover animations</li>";
echo "<li>🌈 <strong>Color Coding:</strong> Intuitive color scheme for different data types</li>";
echo "<li>📱 <strong>Responsive Grid:</strong> Adapts to all screen sizes</li>";
echo "<li>✨ <strong>Smooth Animations:</strong> Fade-in effects and transitions</li>";
echo "<li>🔔 <strong>Status Indicators:</strong> Live system status with pulse animations</li>";
echo "<li>🎪 <strong>Interactive Elements:</strong> Hover effects and clickable cards</li>";
echo "</ul>";

echo "<div style='background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%); border: 2px solid #0ea5e9; border-radius: 12px; padding: 20px; margin: 20px 0;'>";
echo "<h3 style='color: #0c4a6e; margin-top: 0;'>🚀 Dashboard Ready!</h3>";
echo "<p style='color: #0369a1;'>Your comprehensive admin dashboard is now ready with:</p>";
echo "<ul style='color: #0369a1;'>";
echo "<li>✅ Live data from your crypto platform database</li>";
echo "<li>✅ Professional admin panel layout with sidebar navigation</li>";
echo "<li>✅ Interactive charts showing growth trends</li>";
echo "<li>✅ Real-time statistics and recent activity feeds</li>";
echo "<li>✅ Quick action buttons for pending requests</li>";
echo "<li>✅ Responsive design that works on all devices</li>";
echo "</ul>";
echo "</div>";

echo "<p style='text-align: center; margin-top: 30px;'>";
echo "<a href='admin_dashboard.php' style='background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%); color: white; padding: 15px 30px; text-decoration: none; border-radius: 12px; font-weight: 600; box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);'>🎯 Launch Admin Dashboard</a>";
echo "</p>";
?>

<style>
    body {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        margin: 20px;
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        min-height: 100vh;
    }

    h2,
    h3 {
        color: #1f2937;
    }

    p {
        margin: 10px 0;
        line-height: 1.6;
    }

    ul {
        margin-left: 20px;
    }

    li {
        margin: 8px 0;
        line-height: 1.6;
    }

    a:hover {
        transform: translateY(-2px);
        transition: all 0.3s ease;
    }
</style>