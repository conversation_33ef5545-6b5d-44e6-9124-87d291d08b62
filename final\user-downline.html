<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Downline - Crypto Wallet</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <style>
        body {
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
            min-height: 100vh;
        }

        .stats-card {
            background: rgba(30, 41, 59, 0.4);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(59, 130, 246, 0.2);
            border-radius: 0.75rem;
            padding: 1rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }

        .member-card {
            background: rgba(30, 41, 59, 0.4);
            border: 1px solid rgba(59, 130, 246, 0.15);
            border-radius: 0.75rem;
            padding: 1rem;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .member-card:hover {
            border-color: rgba(6, 182, 212, 0.3);
            background: rgba(30, 41, 59, 0.6);
            transform: translateY(-1px);
        }

        .member-card.cursor-default {
            cursor: default;
        }

        .member-card.cursor-default:hover {
            transform: none;
        }



        /* Bottom Navigation Styles */
        .accent {
            color: #06b6d4 !important;
        }

        .hover\:accent:hover {
            color: #06b6d4 !important;
        }
    </style>
</head>

<body class="text-white">
    <div class="min-h-screen flex flex-col">

        <!-- Header -->
        <div class="sticky top-0 z-50 bg-slate-800/95 backdrop-blur-sm border-b border-slate-700">
            <div class="flex items-center justify-between px-6 py-4">
                <div class="flex items-center gap-4">
                    <button onclick="goBack()"
                        class="w-10 h-10 rounded-full bg-slate-700 hover:bg-slate-600 flex items-center justify-center transition-colors">
                        <i class="fas fa-arrow-left text-slate-300"></i>
                    </button>
                    <div>
                        <h1 class="text-xl font-bold text-white" id="userName">User Network</h1>
                        <p class="text-sm text-slate-400" id="userLevel">Team Structure</p>
                    </div>
                </div>
                <div class="text-right">
                    <p class="text-lg font-bold text-cyan-400" id="totalEarnings">$0</p>
                    <p class="text-xs text-slate-400">Total Earned</p>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-grow overflow-y-auto px-4 sm:px-6 py-4">

            <!-- User Summary -->
            <div class="stats-card mb-4">
                <div class="flex items-center justify-between">
                    <div class="flex items-center gap-3">
                        <div class="w-12 h-12 rounded-full bg-gradient-to-r from-blue-600 to-cyan-600 flex items-center justify-center text-white font-bold text-lg"
                            id="userAvatar">
                            JD
                        </div>
                        <div>
                            <h2 class="text-lg font-bold text-white" id="userFullName">John Doe</h2>
                            <p class="text-sm text-slate-400" id="userPackage">Premium Package</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <p class="text-xl font-bold text-cyan-400" id="userEarnings">$0</p>
                        <p class="text-xs text-slate-400">Total Earned</p>
                    </div>
                </div>
            </div>

            <!-- Team Members -->
            <div class="stats-card mb-4" id="downlineContainer">
                <div class="flex items-center justify-between mb-4">
                    <h2 class="text-lg font-bold text-white" id="downlineDescription">Team Members</h2>
                    <span class="text-sm text-slate-400" id="memberCount">0 members</span>
                </div>

                <div class="space-y-3" id="downlineContent">
                    <!-- Dynamic content will be loaded here -->
                </div>
            </div>
        </div>

        <!-- Sticky Bottom Navigation Bar -->
        <div class="sticky bottom-0 z-40 flex-shrink-0">
            <div class="bg-slate-800/95 backdrop-blur-sm mx-4 mb-4 rounded-2xl border border-slate-700">
                <div class="flex justify-around items-center h-16">
                    <a href="1.html" class="flex flex-col items-center gap-1 text-slate-400 hover:accent">
                        <i class="fas fa-home"></i>
                        <span class="text-xs">Home</span>
                    </a>
                    <a href="wallet.html" class="flex flex-col items-center gap-1 text-slate-400 hover:accent">
                        <i class="fas fa-wallet"></i>
                        <span class="text-xs">Wallet</span>
                    </a>
                    <a href="team.html" class="flex flex-col items-center gap-1 accent">
                        <i class="fas fa-users"></i>
                        <span class="text-xs">Team</span>
                    </a>
                    <a href="profile.html" class="flex flex-col items-center gap-1 text-slate-400 hover:accent">
                        <i class="fas fa-user"></i>
                        <span class="text-xs">Profile</span>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Sample user data structure
        const userData = {
            'john-doe': {
                name: 'John Doe',
                avatar: 'JD',
                joinDate: '2 days ago',
                package: 'Standard ($100)',
                level: 1,
                earnings: 485,
                directReferrals: 4,
                totalDownline: 18,
                downline: {
                    level1: [
                        {
                            id: 'alice-smith',
                            name: 'Alice Smith',
                            avatar: 'AS',
                            joinDate: '5 days ago',
                            package: 'Premium ($250)',
                            earnings: 78,
                            status: 'Active',
                            directReferrals: 3
                        },
                        {
                            id: 'mike-brown',
                            name: 'Mike Brown',
                            avatar: 'MB',
                            joinDate: '1 week ago',
                            package: 'Large ($500)',
                            earnings: 92,
                            status: 'Active',
                            directReferrals: 4
                        },
                        {
                            id: 'sarah-johnson',
                            name: 'Sarah Johnson',
                            avatar: 'SJ',
                            joinDate: '2 weeks ago',
                            package: 'Standard ($100)',
                            earnings: 65,
                            status: 'Active',
                            directReferrals: 2
                        },
                        {
                            id: 'robert-davis',
                            name: 'Robert Davis',
                            avatar: 'RD',
                            joinDate: '3 weeks ago',
                            package: 'Premium ($250)',
                            earnings: 125,
                            status: 'Active',
                            directReferrals: 3
                        }
                    ],
                    level2: [
                        {
                            id: 'kevin-lee',
                            name: 'Kevin Lee',
                            avatar: 'KL',
                            joinDate: '3 days ago',
                            package: 'Standard ($100)',
                            earnings: 15,
                            status: 'Active',
                            referredBy: 'Alice Smith'
                        },
                        {
                            id: 'jessica-chen',
                            name: 'Jessica Chen',
                            avatar: 'JC',
                            joinDate: '1 week ago',
                            package: 'Premium ($250)',
                            earnings: 28,
                            status: 'Active',
                            referredBy: 'Alice Smith'
                        },
                        {
                            id: 'lisa-wang',
                            name: 'Lisa Wang',
                            avatar: 'LW',
                            joinDate: '4 days ago',
                            package: 'Trial ($10)',
                            earnings: 8,
                            status: 'Active',
                            referredBy: 'Alice Smith'
                        },
                        {
                            id: 'chris-wang',
                            name: 'Chris Wang',
                            avatar: 'CW',
                            joinDate: '1 week ago',
                            package: 'Premium ($250)',
                            earnings: 65,
                            status: 'Active',
                            referredBy: 'Mike Brown'
                        },
                        {
                            id: 'sophie-brown',
                            name: 'Sophie Brown',
                            avatar: 'SB',
                            joinDate: '2 weeks ago',
                            package: 'Large ($500)',
                            earnings: 85,
                            status: 'Active',
                            referredBy: 'Mike Brown'
                        },
                        {
                            id: 'james-martinez',
                            name: 'James Martinez',
                            avatar: 'JM',
                            joinDate: '3 weeks ago',
                            package: 'Standard ($100)',
                            earnings: 18,
                            status: 'Active',
                            referredBy: 'Mike Brown'
                        },
                        {
                            id: 'daniel-garcia',
                            name: 'Daniel Garcia',
                            avatar: 'DG',
                            joinDate: '5 days ago',
                            package: 'Premium ($250)',
                            earnings: 42,
                            status: 'Active',
                            referredBy: 'Mike Brown'
                        },
                        {
                            id: 'emily-taylor',
                            name: 'Emily Taylor',
                            avatar: 'ET',
                            joinDate: '1 week ago',
                            package: 'Standard ($100)',
                            earnings: 22,
                            status: 'Active',
                            referredBy: 'Sarah Johnson'
                        },
                        {
                            id: 'mark-wilson',
                            name: 'Mark Wilson',
                            avatar: 'MW',
                            joinDate: '2 weeks ago',
                            package: 'Trial ($10)',
                            earnings: 5,
                            status: 'Active',
                            referredBy: 'Sarah Johnson'
                        },
                        {
                            id: 'anna-lopez',
                            name: 'Anna Lopez',
                            avatar: 'AL',
                            joinDate: '6 days ago',
                            package: 'Large ($500)',
                            earnings: 95,
                            status: 'Active',
                            referredBy: 'Robert Davis'
                        },
                        {
                            id: 'tom-anderson',
                            name: 'Tom Anderson',
                            avatar: 'TA',
                            joinDate: '1 week ago',
                            package: 'Premium ($250)',
                            earnings: 58,
                            status: 'Active',
                            referredBy: 'Robert Davis'
                        },
                        {
                            id: 'nina-clark',
                            name: 'Nina Clark',
                            avatar: 'NC',
                            joinDate: '10 days ago',
                            package: 'Standard ($100)',
                            earnings: 35,
                            status: 'Active',
                            referredBy: 'Robert Davis'
                        }
                    ]
                }
            },
            'alice-smith': {
                name: 'Alice Smith',
                avatar: 'AS',
                joinDate: '5 days ago',
                package: 'Premium ($250)',
                level: 1,
                earnings: 158,
                directReferrals: 3,
                totalDownline: 9,
                downline: {
                    level1: [
                        {
                            id: 'kevin-lee',
                            name: 'Kevin Lee',
                            avatar: 'KL',
                            joinDate: '3 days ago',
                            package: 'Standard ($100)',
                            earnings: 15,
                            status: 'Active',
                            directReferrals: 2
                        },
                        {
                            id: 'jessica-chen',
                            name: 'Jessica Chen',
                            avatar: 'JC',
                            joinDate: '1 week ago',
                            package: 'Premium ($250)',
                            earnings: 28,
                            status: 'Active',
                            directReferrals: 2
                        },
                        {
                            id: 'lisa-wang',
                            name: 'Lisa Wang',
                            avatar: 'LW',
                            joinDate: '4 days ago',
                            package: 'Trial ($10)',
                            earnings: 8,
                            status: 'Active',
                            directReferrals: 2
                        }
                    ],
                    level2: [
                        {
                            id: 'david-kim',
                            name: 'David Kim',
                            avatar: 'DK',
                            joinDate: '2 days ago',
                            package: 'Trial ($10)',
                            earnings: 5,
                            status: 'Active',
                            referredBy: 'Kevin Lee'
                        },
                        {
                            id: 'peter-jones',
                            name: 'Peter Jones',
                            avatar: 'PJ',
                            joinDate: '1 day ago',
                            package: 'Standard ($100)',
                            earnings: 8,
                            status: 'Active',
                            referredBy: 'Kevin Lee'
                        },
                        {
                            id: 'emma-wilson',
                            name: 'Emma Wilson',
                            avatar: 'EW',
                            joinDate: '4 days ago',
                            package: 'Standard ($100)',
                            earnings: 12,
                            status: 'Active',
                            referredBy: 'Jessica Chen'
                        },
                        {
                            id: 'ryan-miller',
                            name: 'Ryan Miller',
                            avatar: 'RM',
                            joinDate: '3 days ago',
                            package: 'Premium ($250)',
                            earnings: 25,
                            status: 'Active',
                            referredBy: 'Jessica Chen'
                        },
                        {
                            id: 'sophia-white',
                            name: 'Sophia White',
                            avatar: 'SW',
                            joinDate: '2 days ago',
                            package: 'Trial ($10)',
                            earnings: 3,
                            status: 'Active',
                            referredBy: 'Lisa Wang'
                        },
                        {
                            id: 'alex-green',
                            name: 'Alex Green',
                            avatar: 'AG',
                            joinDate: '1 day ago',
                            package: 'Standard ($100)',
                            earnings: 6,
                            status: 'Active',
                            referredBy: 'Lisa Wang'
                        }
                    ]
                }
            },
            'kevin-lee': {
                name: 'Kevin Lee',
                avatar: 'KL',
                joinDate: '3 days ago',
                package: 'Standard ($100)',
                level: 2,
                earnings: 15,
                directReferrals: 1,
                totalDownline: 1,
                downline: {
                    level1: [
                        {
                            id: 'david-kim',
                            name: 'David Kim',
                            avatar: 'DK',
                            joinDate: '2 days ago',
                            package: 'Trial ($10)',
                            earnings: 5,
                            status: 'Active',
                            directReferrals: 0
                        }
                    ]
                }
            },
            'mike-brown': {
                name: 'Mike Brown',
                avatar: 'MB',
                joinDate: '1 week ago',
                package: 'Large ($500)',
                level: 1,
                earnings: 192,
                directReferrals: 4,
                totalDownline: 8,
                downline: {
                    level1: [
                        {
                            id: 'chris-wang',
                            name: 'Chris Wang',
                            avatar: 'CW',
                            joinDate: '1 week ago',
                            package: 'Premium ($250)',
                            earnings: 65,
                            status: 'Active',
                            directReferrals: 2
                        },
                        {
                            id: 'sophie-brown',
                            name: 'Sophie Brown',
                            avatar: 'SB',
                            joinDate: '2 weeks ago',
                            package: 'Large ($500)',
                            earnings: 85,
                            status: 'Active',
                            directReferrals: 1
                        },
                        {
                            id: 'james-martinez',
                            name: 'James Martinez',
                            avatar: 'JM',
                            joinDate: '3 weeks ago',
                            package: 'Standard ($100)',
                            earnings: 18,
                            status: 'Active',
                            directReferrals: 1
                        },
                        {
                            id: 'daniel-garcia',
                            name: 'Daniel Garcia',
                            avatar: 'DG',
                            joinDate: '5 days ago',
                            package: 'Premium ($250)',
                            earnings: 42,
                            status: 'Active',
                            directReferrals: 0
                        }
                    ],
                    level2: [
                        {
                            id: 'lucy-davis',
                            name: 'Lucy Davis',
                            avatar: 'LD',
                            joinDate: '1 month ago',
                            package: 'Trial ($10)',
                            earnings: 12,
                            status: 'Active',
                            referredBy: 'Chris Wang'
                        },
                        {
                            id: 'carlos-rodriguez',
                            name: 'Carlos Rodriguez',
                            avatar: 'CR',
                            joinDate: '3 days ago',
                            package: 'Standard ($100)',
                            earnings: 15,
                            status: 'Active',
                            referredBy: 'Chris Wang'
                        },
                        {
                            id: 'maya-patel',
                            name: 'Maya Patel',
                            avatar: 'MP',
                            joinDate: '1 week ago',
                            package: 'Premium ($250)',
                            earnings: 32,
                            status: 'Active',
                            referredBy: 'Sophie Brown'
                        },
                        {
                            id: 'oliver-smith',
                            name: 'Oliver Smith',
                            avatar: 'OS',
                            joinDate: '2 weeks ago',
                            package: 'Trial ($10)',
                            earnings: 8,
                            status: 'Active',
                            referredBy: 'James Martinez'
                        }
                    ]
                }
            },
            'chris-wang': {
                name: 'Chris Wang',
                avatar: 'CW',
                joinDate: '1 week ago',
                package: 'Premium ($250)',
                level: 2,
                earnings: 65,
                directReferrals: 2,
                totalDownline: 2,
                downline: {
                    level1: [
                        {
                            id: 'lucy-davis',
                            name: 'Lucy Davis',
                            avatar: 'LD',
                            joinDate: '1 month ago',
                            package: 'Trial ($10)',
                            earnings: 12,
                            status: 'Active',
                            directReferrals: 0
                        },
                        {
                            id: 'carlos-rodriguez',
                            name: 'Carlos Rodriguez',
                            avatar: 'CR',
                            joinDate: '3 days ago',
                            package: 'Standard ($100)',
                            earnings: 15,
                            status: 'Active',
                            directReferrals: 0
                        }
                    ]
                }
            },
            'sarah-johnson': {
                name: 'Sarah Johnson',
                avatar: 'SJ',
                joinDate: '2 weeks ago',
                package: 'Standard ($100)',
                level: 1,
                earnings: 65,
                directReferrals: 2,
                totalDownline: 3,
                downline: {
                    level1: [
                        {
                            id: 'emily-taylor',
                            name: 'Emily Taylor',
                            avatar: 'ET',
                            joinDate: '1 week ago',
                            package: 'Standard ($100)',
                            earnings: 22,
                            status: 'Active',
                            directReferrals: 1
                        },
                        {
                            id: 'mark-wilson',
                            name: 'Mark Wilson',
                            avatar: 'MW',
                            joinDate: '2 weeks ago',
                            package: 'Trial ($10)',
                            earnings: 5,
                            status: 'Active',
                            directReferrals: 0
                        }
                    ],
                    level2: [
                        {
                            id: 'grace-lee',
                            name: 'Grace Lee',
                            avatar: 'GL',
                            joinDate: '3 days ago',
                            package: 'Trial ($10)',
                            earnings: 2,
                            status: 'Active',
                            referredBy: 'Emily Taylor'
                        }
                    ]
                }
            },
            'robert-davis': {
                name: 'Robert Davis',
                avatar: 'RD',
                joinDate: '3 weeks ago',
                package: 'Premium ($250)',
                level: 1,
                earnings: 225,
                directReferrals: 3,
                totalDownline: 6,
                downline: {
                    level1: [
                        {
                            id: 'anna-lopez',
                            name: 'Anna Lopez',
                            avatar: 'AL',
                            joinDate: '6 days ago',
                            package: 'Large ($500)',
                            earnings: 95,
                            status: 'Active',
                            directReferrals: 1
                        },
                        {
                            id: 'tom-anderson',
                            name: 'Tom Anderson',
                            avatar: 'TA',
                            joinDate: '1 week ago',
                            package: 'Premium ($250)',
                            earnings: 58,
                            status: 'Active',
                            directReferrals: 1
                        },
                        {
                            id: 'nina-clark',
                            name: 'Nina Clark',
                            avatar: 'NC',
                            joinDate: '10 days ago',
                            package: 'Standard ($100)',
                            earnings: 35,
                            status: 'Active',
                            directReferrals: 1
                        }
                    ],
                    level2: [
                        {
                            id: 'victor-kim',
                            name: 'Victor Kim',
                            avatar: 'VK',
                            joinDate: '2 days ago',
                            package: 'Premium ($250)',
                            earnings: 18,
                            status: 'Active',
                            referredBy: 'Anna Lopez'
                        },
                        {
                            id: 'zoe-martinez',
                            name: 'Zoe Martinez',
                            avatar: 'ZM',
                            joinDate: '4 days ago',
                            package: 'Standard ($100)',
                            earnings: 12,
                            status: 'Active',
                            referredBy: 'Tom Anderson'
                        },
                        {
                            id: 'ian-brown',
                            name: 'Ian Brown',
                            avatar: 'IB',
                            joinDate: '5 days ago',
                            package: 'Trial ($10)',
                            earnings: 4,
                            status: 'Active',
                            referredBy: 'Nina Clark'
                        }
                    ]
                }
            }
        };

        function getUrlParams() {
            const params = new URLSearchParams(window.location.search);
            return {
                userId: params.get('user') || 'john-doe',
                targetLevel: params.get('target') || 'level-2',
                fromPage: params.get('from') || 'team'
            };
        }

        function loadUserDownline() {
            const { userId, targetLevel, fromPage } = getUrlParams();
            const user = userData[userId];

            if (!user) {
                // Handle user not found
                document.getElementById('userName').textContent = 'User Not Found';
                return;
            }

            // Determine what to show based on target level
            let levelToShow, levelName, nextLevel;
            switch (targetLevel) {
                case 'level-2':
                    levelToShow = 1;
                    levelName = 'Level 2 Referrals';
                    nextLevel = 'level-3';
                    break;
                case 'level-3':
                    levelToShow = 1;
                    levelName = 'Level 3 Referrals';
                    nextLevel = null; // No further levels
                    break;
                default:
                    levelToShow = 1;
                    levelName = 'Direct Referrals';
                    nextLevel = 'level-3';
            }

            // Update header information
            document.getElementById('userName').textContent = `${user.name}'s Team`;
            document.getElementById('userLevel').textContent = levelName;
            document.getElementById('totalEarnings').textContent = `$${user.earnings}`;

            // Update user summary
            document.getElementById('userAvatar').textContent = user.avatar;
            document.getElementById('userFullName').textContent = user.name;
            const packageType = user.package.split(' ')[0]; // Get just "Trial", "Standard", etc.
            document.getElementById('userPackage').textContent = `${packageType} Package`;
            document.getElementById('userEarnings').textContent = `$${user.earnings}`;

            // Update section title and count
            document.getElementById('downlineDescription').textContent = levelName;

            // Generate downline content
            generateProgressiveDownlineContent(user, targetLevel, nextLevel);
        }

        function generateProgressiveDownlineContent(user, targetLevel, nextLevel) {
            const container = document.getElementById('downlineContent');
            container.innerHTML = '';

            let membersToShow = [];
            let sectionTitle = '';
            let isClickable = nextLevel !== null;

            switch (targetLevel) {
                case 'level-2':
                    // Show Level 2 referrals (which are in level1 array for this user's perspective)
                    membersToShow = user.downline.level1 || [];
                    sectionTitle = 'Level 2 Referrals';
                    break;
                case 'level-3':
                    // Show Level 3 referrals
                    membersToShow = user.downline.level2 || [];
                    sectionTitle = 'Level 3 Referrals';
                    break;
                default:
                    membersToShow = user.downline.level1 || [];
                    sectionTitle = 'Direct Referrals';
            }

            // Update member count
            document.getElementById('memberCount').textContent = `${membersToShow.length} member${membersToShow.length !== 1 ? 's' : ''}`;

            if (membersToShow.length === 0) {
                container.innerHTML = `
                    <div class="text-center py-8">
                        <i class="fas fa-users text-slate-600 text-4xl mb-4"></i>
                        <p class="text-slate-400">No team members found</p>
                    </div>
                `;
                return;
            }

            const section = createProgressiveLevelSection(sectionTitle, membersToShow, isClickable, nextLevel);
            container.appendChild(section);
        }

        function generateDownlineContent(user, maxLevels) {
            const container = document.getElementById('downlineContent');
            container.innerHTML = '';

            if (maxLevels >= 1 && user.downline.level1) {
                // Level 1 (Direct Referrals)
                const level1Section = createLevelSection('Direct Referrals (Level 1)', user.downline.level1, 1, maxLevels > 1);
                container.appendChild(level1Section);
            }

            if (maxLevels >= 2 && user.downline.level2) {
                // Level 2
                const level2Section = createLevelSection('Second Level Referrals', user.downline.level2, 2, false);
                container.appendChild(level2Section);
            }
        }

        function createProgressiveLevelSection(title, members, isClickable, nextLevel) {
            const section = document.createElement('div');

            const membersList = document.createElement('div');
            membersList.className = 'space-y-3';

            members.forEach(member => {
                const memberCard = createProgressiveMemberCard(member, isClickable, nextLevel);
                membersList.appendChild(memberCard);
            });

            section.appendChild(membersList);
            return section;
        }

        function createLevelSection(title, members, level, isClickable) {
            const section = document.createElement('div');
            section.className = 'mb-6';

            const header = document.createElement('h3');
            header.className = 'text-base font-semibold text-cyan-400 mb-3 flex items-center gap-2';
            header.innerHTML = `
                <span class="w-6 h-6 rounded-full bg-gradient-to-r from-blue-600 to-cyan-600 flex items-center justify-center text-white text-xs font-bold">
                    ${level}
                </span>
                ${title} (${members.length})
            `;
            section.appendChild(header);

            const membersList = document.createElement('div');
            membersList.className = 'space-y-3';

            members.forEach(member => {
                const memberCard = createMemberCard(member, isClickable);
                membersList.appendChild(memberCard);
            });

            section.appendChild(membersList);
            return section;
        }

        function createProgressiveMemberCard(member, isClickable, nextLevel) {
            const card = document.createElement('div');
            card.className = `member-card ${isClickable ? 'cursor-pointer' : 'cursor-default'}`;

            if (isClickable) {
                card.onclick = () => viewUserDownline(member.id, nextLevel);
            }

            // Simplify package display
            const packageType = member.package.split(' ')[0]; // Get just "Trial", "Standard", etc.

            card.innerHTML = `
                <div class="flex items-center justify-between">
                    <div class="flex items-center gap-3">
                        <div class="w-10 h-10 rounded-full bg-gradient-to-r from-blue-600 to-cyan-600 flex items-center justify-center text-white font-bold text-sm">
                            ${member.avatar}
                        </div>
                        <div>
                            <p class="text-base font-semibold text-white">${member.name}</p>
                            <p class="text-sm text-slate-400">${packageType} • ${member.status}</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <p class="text-lg font-bold text-cyan-400">$${member.earnings}</p>
                        ${isClickable && member.directReferrals > 0 ? `<p class="text-xs text-slate-500">${member.directReferrals} referrals</p>` : ''}
                    </div>
                </div>
            `;

            return card;
        }

        function createMemberCard(member, isClickable) {
            const card = document.createElement('div');
            card.className = `member-card ${isClickable ? 'cursor-pointer' : 'cursor-default'}`;

            if (isClickable) {
                card.onclick = () => viewUserDownline(member.id, 1);
            }

            card.innerHTML = `
                <div class="flex items-center justify-between">
                    <div class="flex items-center gap-3">
                        <div class="w-12 h-12 rounded-full bg-gradient-to-r from-blue-600 to-cyan-600 flex items-center justify-center text-white font-bold">
                            ${member.avatar}
                        </div>
                        <div>
                            <p class="text-base font-semibold text-white">${member.name}</p>
                            <p class="text-xs text-slate-400">Joined: ${member.joinDate}</p>
                            <p class="text-xs text-cyan-400">Package: ${member.package}</p>
                            ${member.referredBy ? `<p class="text-xs text-slate-500">Referred by: ${member.referredBy}</p>` : ''}
                        </div>
                    </div>
                    <div class="text-right">
                        <p class="text-lg font-bold text-cyan-400">$${member.earnings}</p>
                        <p class="text-xs text-slate-400">Earned</p>
                        <p class="text-xs text-cyan-400">${member.status}</p>
                        ${member.directReferrals !== undefined ? `<p class="text-xs text-slate-500">${member.directReferrals} referrals</p>` : ''}
                    </div>
                </div>
                ${isClickable ? '<div class="mt-2 text-xs text-slate-500 flex items-center gap-1"><i class="fas fa-mouse-pointer"></i> Click to view downline</div>' : ''}
            `;

            return card;
        }

        function viewUserDownline(userId, targetLevel) {
            window.location.href = `user-downline.html?user=${userId}&target=${targetLevel}&from=downline`;
        }

        function goBack() {
            window.history.back();
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', loadUserDownline);
    </script>

</body>

</html>