<?php
session_start();

// Check if user is admin
if (!isset($_SESSION['user_id']) || !$_SESSION['is_admin']) {
    header("Location: login.html");
    exit();
}

include 'dbcon.php';

echo "<h2>Create Settings Table for Dynamic Coin Price</h2>";

// Create settings table
$create_table_sql = "
CREATE TABLE IF NOT EXISTS `system_settings` (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
  `setting_key` varchar(100) NOT NULL UNIQUE,
  `setting_value` text NOT NULL,
  `setting_type` enum('string','number','boolean','json') NOT NULL DEFAULT 'string',
  `description` text DEFAULT NULL,
  `updated_by` int(11) UNSIGNED DEFAULT NULL,
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_setting_key` (`setting_key`),
  KEY `fk_settings_updated_by` (`updated_by`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
";

if ($conn->query($create_table_sql)) {
    echo "<p>✅ Settings table created successfully!</p>";
    
    // Insert default coin price setting
    $insert_default_sql = "
    INSERT INTO system_settings (setting_key, setting_value, setting_type, description, updated_by) 
    VALUES ('staking_coin_price', '0.45', 'number', 'Price per staking coin in USD', ?) 
    ON DUPLICATE KEY UPDATE 
    setting_value = VALUES(setting_value),
    description = VALUES(description),
    updated_by = VALUES(updated_by)
    ";
    
    $stmt = $conn->prepare($insert_default_sql);
    $admin_id = $_SESSION['user_id'];
    $stmt->bind_param("i", $admin_id);
    
    if ($stmt->execute()) {
        echo "<p>✅ Default coin price setting inserted/updated!</p>";
    } else {
        echo "<p>❌ Error inserting default setting: " . $stmt->error . "</p>";
    }
    $stmt->close();
    
    // Insert other staking-related settings
    $other_settings = [
        ['staking_min_coins', '10', 'number', 'Minimum coins required for staking'],
        ['staking_freeze_months', '6', 'number', 'Number of months for staking freeze period'],
        ['staking_min_usd_amount', '10', 'number', 'Minimum USD amount for staking (must be multiple of this)']
    ];
    
    foreach ($other_settings as $setting) {
        $insert_sql = "
        INSERT INTO system_settings (setting_key, setting_value, setting_type, description, updated_by) 
        VALUES (?, ?, ?, ?, ?) 
        ON DUPLICATE KEY UPDATE 
        setting_value = VALUES(setting_value),
        description = VALUES(description),
        updated_by = VALUES(updated_by)
        ";
        
        $stmt = $conn->prepare($insert_sql);
        $stmt->bind_param("ssssi", $setting[0], $setting[1], $setting[2], $setting[3], $admin_id);
        
        if ($stmt->execute()) {
            echo "<p>✅ Setting '{$setting[0]}' inserted/updated!</p>";
        } else {
            echo "<p>❌ Error inserting setting '{$setting[0]}': " . $stmt->error . "</p>";
        }
        $stmt->close();
    }
    
} else {
    echo "<p>❌ Error creating settings table: " . $conn->error . "</p>";
}

// Show current settings
echo "<h3>Current System Settings:</h3>";
$settings_sql = "SELECT s.*, u.name as updated_by_name FROM system_settings s LEFT JOIN users u ON s.updated_by = u.id ORDER BY s.setting_key";
$result = $conn->query($settings_sql);

if ($result && $result->num_rows > 0) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #f0f0f0;'>";
    echo "<th>Setting Key</th><th>Value</th><th>Type</th><th>Description</th><th>Updated By</th><th>Updated At</th>";
    echo "</tr>";
    
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>{$row['setting_key']}</td>";
        echo "<td><strong>{$row['setting_value']}</strong></td>";
        echo "<td>{$row['setting_type']}</td>";
        echo "<td>{$row['description']}</td>";
        echo "<td>" . ($row['updated_by_name'] ?? 'System') . "</td>";
        echo "<td>" . date('M j, Y H:i:s', strtotime($row['updated_at'])) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>No settings found.</p>";
}

echo "<hr>";
echo "<h3>Next Steps:</h3>";
echo "<ol>";
echo "<li><strong>Settings table created</strong> - system_settings table is ready</li>";
echo "<li><strong>Default values inserted</strong> - Current coin price and other staking settings</li>";
echo "<li><strong>Visit the admin coin price manager</strong> - <a href='admin_coin_price.php' style='color: blue;'>admin_coin_price.php</a></li>";
echo "<li><strong>Test the user staking page</strong> - Prices should update dynamically</li>";
echo "</ol>";

echo "<div style='background: #e8f5e8; padding: 15px; border: 1px solid #4CAF50; margin: 20px 0;'>";
echo "<h4>✅ What This Creates:</h4>";
echo "<ul>";
echo "<li><strong>Dynamic Settings Storage</strong> - All staking settings stored in database</li>";
echo "<li><strong>Admin Control</strong> - Admins can update coin price and other settings</li>";
echo "<li><strong>Real-time Updates</strong> - Changes reflect immediately on user side</li>";
echo "<li><strong>Audit Trail</strong> - Track who updated settings and when</li>";
echo "<li><strong>Flexible System</strong> - Easy to add more settings in the future</li>";
echo "</ul>";
echo "</div>";

$conn->close();
?>
