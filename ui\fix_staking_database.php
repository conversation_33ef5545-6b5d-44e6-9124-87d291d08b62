<?php
session_start();

if (!isset($_SESSION['user_id'])) {
    header("Location: login.html");
    exit();
}

include 'dbcon.php';

$message = '';
$error = '';

if ($_POST) {
    $action = $_POST['action'] ?? '';
    
    try {
        if ($action === 'add_claim_pending') {
            // Add claim_pending to the status ENUM
            $sql = "ALTER TABLE staking_records MODIFY status enum('active','completed','cancelled','claim_pending') NOT NULL DEFAULT 'active'";
            if ($conn->query($sql)) {
                $message .= "✅ Added 'claim_pending' status to staking_records table.<br>";
            } else {
                $error .= "❌ Failed to add claim_pending status: " . $conn->error . "<br>";
            }
        }
        
        if ($action === 'fix_test_record') {
            $user_id = $_SESSION['user_id'];
            $staking_id = intval($_POST['staking_id'] ?? 0);
            
            if ($staking_id > 0) {
                // Update the test record to be unlocked
                $past_date = date('Y-m-d H:i:s', strtotime('-1 week'));
                $sql = "UPDATE staking_records SET freeze_end_date = ? WHERE id = ? AND user_id = ?";
                $stmt = $conn->prepare($sql);
                $stmt->bind_param("sii", $past_date, $staking_id, $user_id);
                
                if ($stmt->execute()) {
                    $message .= "✅ Updated staking record #$staking_id to be unlocked.<br>";
                } else {
                    $error .= "❌ Failed to update staking record: " . $stmt->error . "<br>";
                }
                $stmt->close();
            }
        }
        
    } catch (Exception $e) {
        $error .= "❌ Error: " . $e->getMessage() . "<br>";
    }
}

// Check current status
$enum_sql = "SHOW COLUMNS FROM staking_records LIKE 'status'";
$enum_result = $conn->query($enum_sql);
$enum_row = $enum_result->fetch_assoc();
$has_claim_pending = strpos($enum_row['Type'], 'claim_pending') !== false;

// Get user's staking records
$user_id = $_SESSION['user_id'];
$records_sql = "SELECT * FROM staking_records WHERE user_id = ? ORDER BY created_at DESC";
$stmt = $conn->prepare($records_sql);
$stmt->bind_param("i", $user_id);
$stmt->execute();
$records_result = $stmt->get_result();
$records = $records_result->fetch_all(MYSQLI_ASSOC);
$stmt->close();

$conn->close();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fix Staking Database - NetVis</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100">
    <?php include 'user_nav.php'; ?>

    <main class="max-w-6xl mx-auto py-6 sm:px-6 lg:px-8">
        <div class="bg-white shadow-lg rounded-lg p-8">
            <div class="mb-6">
                <h2 class="text-3xl font-bold text-gray-800">Fix Staking Database</h2>
                <p class="text-gray-600 mt-2">Fix database issues for staking claim functionality.</p>
            </div>

            <?php if ($message): ?>
                <div class="bg-green-50 border border-green-200 text-green-800 px-4 py-3 rounded-lg mb-6">
                    <?php echo $message; ?>
                </div>
            <?php endif; ?>

            <?php if ($error): ?>
                <div class="bg-red-50 border border-red-200 text-red-800 px-4 py-3 rounded-lg mb-6">
                    <?php echo $error; ?>
                </div>
            <?php endif; ?>

            <!-- Database Status -->
            <div class="mb-8">
                <h3 class="text-xl font-semibold text-gray-800 mb-4">Database Status</h3>
                
                <div class="bg-gray-50 p-4 rounded-lg">
                    <div class="flex items-center justify-between">
                        <div>
                            <h4 class="font-semibold">Status ENUM Values</h4>
                            <p class="text-sm text-gray-600"><?php echo $enum_row['Type']; ?></p>
                        </div>
                        <div>
                            <?php if ($has_claim_pending): ?>
                                <span class="px-3 py-1 bg-green-100 text-green-800 text-sm rounded-full">✅ claim_pending Available</span>
                            <?php else: ?>
                                <span class="px-3 py-1 bg-red-100 text-red-800 text-sm rounded-full">❌ claim_pending Missing</span>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Fix Actions -->
            <div class="mb-8">
                <h3 class="text-xl font-semibold text-gray-800 mb-4">Fix Actions</h3>
                
                <?php if (!$has_claim_pending): ?>
                    <div class="bg-yellow-50 border border-yellow-200 p-4 rounded-lg mb-4">
                        <h4 class="text-yellow-800 font-semibold mb-2">⚠️ Missing claim_pending Status</h4>
                        <p class="text-yellow-700 text-sm mb-3">The staking table is missing the 'claim_pending' status required for the claim functionality.</p>
                        
                        <form method="POST" style="display: inline;">
                            <input type="hidden" name="action" value="add_claim_pending">
                            <button type="submit" class="px-4 py-2 bg-yellow-600 text-white rounded-md hover:bg-yellow-700">
                                Add claim_pending Status
                            </button>
                        </form>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Current Records -->
            <div class="mb-8">
                <h3 class="text-xl font-semibold text-gray-800 mb-4">Your Staking Records</h3>
                
                <?php if (empty($records)): ?>
                    <div class="bg-gray-50 p-6 rounded-lg text-center">
                        <p class="text-gray-500">No staking records found.</p>
                        <a href="create_test_staking.php" class="text-blue-600 underline">Create a test staking record</a>
                    </div>
                <?php else: ?>
                    <div class="overflow-x-auto border border-gray-200 rounded-lg">
                        <table class="min-w-full bg-white">
                            <thead class="bg-gray-100">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-600 uppercase">ID</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-600 uppercase">Amount</th>
                                    <th class="px-6 py-3 text-center text-xs font-medium text-gray-600 uppercase">Freeze End</th>
                                    <th class="px-6 py-3 text-center text-xs font-medium text-gray-600 uppercase">Status</th>
                                    <th class="px-6 py-3 text-center text-xs font-medium text-gray-600 uppercase">Unlocked?</th>
                                    <th class="px-6 py-3 text-center text-xs font-medium text-gray-600 uppercase">Action</th>
                                </tr>
                            </thead>
                            <tbody class="divide-y divide-gray-200">
                                <?php foreach ($records as $record): ?>
                                    <?php
                                    $is_unlocked = strtotime($record['freeze_end_date']) <= time();
                                    $can_claim = $record['status'] === 'active' && $is_unlocked;
                                    ?>
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                            #<?php echo $record['id']; ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right">
                                            $<?php echo number_format($record['amount_usd'], 2); ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">
                                            <?php echo date('M j, Y H:i', strtotime($record['freeze_end_date'])); ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-center">
                                            <span class="px-2 py-1 text-xs font-medium rounded-full <?php echo $record['status'] === 'active' ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800'; ?>">
                                                <?php echo ucfirst($record['status']); ?>
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-center">
                                            <?php if ($is_unlocked): ?>
                                                <span class="text-green-600 font-semibold">✅ YES</span>
                                            <?php else: ?>
                                                <span class="text-red-600 font-semibold">❌ NO</span>
                                            <?php endif; ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-center">
                                            <?php if ($record['status'] === 'active' && !$is_unlocked): ?>
                                                <form method="POST" style="display: inline;">
                                                    <input type="hidden" name="action" value="fix_test_record">
                                                    <input type="hidden" name="staking_id" value="<?php echo $record['id']; ?>">
                                                    <button type="submit" class="px-3 py-1 bg-blue-600 text-white text-xs rounded-md hover:bg-blue-700">
                                                        Make Unlocked
                                                    </button>
                                                </form>
                                            <?php elseif ($can_claim): ?>
                                                <span class="px-3 py-1 bg-green-100 text-green-800 text-xs rounded-md">
                                                    Ready to Claim!
                                                </span>
                                            <?php else: ?>
                                                <span class="px-3 py-1 bg-gray-100 text-gray-600 text-xs rounded-md">
                                                    <?php echo ucfirst($record['status']); ?>
                                                </span>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Instructions -->
            <div class="bg-blue-50 p-6 rounded-lg mb-6">
                <h4 class="text-blue-800 font-semibold mb-2">📋 Fix Instructions:</h4>
                <ol class="text-blue-700 text-sm space-y-1 list-decimal list-inside">
                    <li>First, add the 'claim_pending' status if it's missing</li>
                    <li>Make sure at least one staking record is unlocked</li>
                    <li>Go to the staking page to test the claim functionality</li>
                    <li>The record should show a green "Claim" button</li>
                </ol>
            </div>

            <!-- Quick Links -->
            <div class="flex space-x-4">
                <a href="staking.php" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
                    Go to Staking Page
                </a>
                <a href="check_staking_db.php" class="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700">
                    Check Database
                </a>
                <a href="create_test_staking.php" class="bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700">
                    Create Test Record
                </a>
            </div>
        </div>
    </main>
</body>
</html>
